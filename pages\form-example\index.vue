<template>
  <view class="container">
    <view class="form-container">
      <!-- 渲染表单组件 -->
      <component :is="FormComponent"></component>
      
      <!-- 操作按钮 -->
      <view class="button-group">
        <uv-button type="primary" @click="submitForm">提交表单</uv-button>
        <uv-button type="info" @click="resetForm" style="margin-left: 20rpx;">重置表单</uv-button>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import useForm from '../../hooks/useForm';

// 表单配置
const formConfig = {
  // 标签配置
  labelOptions: {
    position: 'left',
    width: 100
  },
  // 表单项配置
  formItems: [
    {
      type: 'input',
      label: '用户名',
      prop: 'username',
      placeholder: '请输入用户名',
      props: {
        borderBottom: true
      },
      rules: {
        required: true,
        message: '请输入用户名',
        trigger: ['blur']
      }
    },
    {
      type: 'input',
      label: '密码',
      prop: 'password',
      placeholder: '请输入密码',
      props: {
        password: true,
        labelWidth: 150,
        borderBottom: true
      },
      rules: {
        required: true,
        message: '请输入密码',
        trigger: ['blur']
      }
    },
    {
      type: 'radio',
      label: '性别',
      prop: 'gender',
      props: {
        borderBottom: true
      },
      options: [
        { label: '男', value: 'male' },
        { label: '女', value: 'female' }
      ],
      rules: {
        required: true,
        message: '请选择性别',
        trigger: ['change']
      }
    },
    // {
    //   type: 'checkbox',
    //   label: '爱好',
    //   prop: 'hobbies',
    //   borderBottom: true,
    //   options: [
    //     { label: '阅读', value: 'reading' },
    //     { label: '音乐', value: 'music' },
    //     { label: '运动', value: 'sports' }
    //   ]
    // },
    // {
    //   type: 'textarea',
    //   label: '简介',
    //   prop: 'introduction',
    //   placeholder: '请输入个人简介',
    //   borderBottom: true,
    //   props: {
    //     maxlength: 200
    //   }
    // }
  ],
  // 初始值
  initialValues: {
    username: '',
    password: '',
    gender: 'male',
    hobbies: [],
    introduction: ''
  }
};

// 使用表单钩子
const {
  FormComponent,
  formModel,
  validate,
  resetForm,
  setFormValues
} = useForm(formConfig);

// 提交表单
const submitForm = async () => {
  try {
    const values = await validate();
    console.log('表单验证成功:', values);
    uni.showToast({
      title: '提交成功',
      icon: 'success'
    });
  } catch (error) {
    console.error('表单验证失败:', error);
    uni.showToast({
      title: '请检查表单',
      icon: 'error'
    });
  }
};

onMounted(() => {
  // 可以在这里设置初始值
  // setFormValues({
  //   username: '张三',
  //   gender: 'male'
  // });
});
</script>

<style lang="scss" scoped>
.container {
  padding: 30rpx;
}

.form-container {
  background-color: #fff;
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.button-group {
  display: flex;
  justify-content: center;
  padding: 40rpx 20rpx;
}
</style> 