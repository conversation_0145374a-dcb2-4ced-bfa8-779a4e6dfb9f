import { ref, reactive, nextTick } from 'vue';
import { getSoftInfo } from '@/api/monitorReport';

// 状态定义
export const isBuy = ref(true);
export const version = ref(0);
export const min_version = ref(0);
export const currentIndex = ref(1);
export const selectDay = ref('');
export const swiperHeight = ref(0);
export const scrollLeft = ref(0);
export const setInter = ref(null);
export const tabCurrentIndex = ref(0);

// 组件引用
export const overview = ref(null);
export const siteList = ref(null);
export const ipStatistics = ref(null);
export const spiderStatistics = ref(null);

// 导航列表
export const showNavList = reactive([
    {
        title: '概览',
        active: true,
        index: 1,
    },
    {
        title: '网站列表',
        active: true,
        index: 2,
    },
    {
        title: 'IP统计',
        active: true,
        index: 3,
    },
    {
        title: '蜘蛛统计',
        active: true,
        index: 4,
    },
]);

// 查询插件信息
export const selectPreventInfo = async () => {
    try {
        const res = await getSoftInfo('monitor');
        uni.hideLoading();
        if ((res.setup != undefined && !res.status) || res.endtime == -1) {
            isBuy.value = false;
            if (res.versions && res.versions.length) {
                version.value = res.versions[0].m_version;
                min_version.value = res.versions[0].version;
            }
            return;
        } else {
            isBuy.value = true;
        }
    } catch (err) {
        uni.hideLoading();
        uni.showToast({
            title: '获取插件信息失败',
            icon: 'none',
        });
        console.error(err);
    }
};

// 切换类型
export const tabChange = (index) => {
    if (setInter.value) {
        clearInterval(setInter.value);
    }
    tabCurrentIndex.value = index;
    currentIndex.value = index + 1;
    cutType(index);
};

// swiper切换事件
export const swiperChange = (e) => {
    if (setInter.value) {
        clearInterval(setInter.value);
    }
    if (e.currentTarget && e.currentTarget.dataset && e.currentTarget.dataset.index != undefined) {
        let index = parseInt(e.currentTarget.dataset.index);
        currentIndex.value = index;
        tabCurrentIndex.value = index;
    } else {
        let index = e.detail.current + 1;
        currentIndex.value = index;
        tabCurrentIndex.value = e.detail.current;
    }
};

// 切换类型
export const cutType = (index) => {
    scrollLeft.value = index * 93 - 186;

    nextTick(() => {
        if (currentIndex.value == 1 && overview.value) {
            overview.value.getData();
        } else if (currentIndex.value == 2 && siteList.value) {
            // siteList.value.getList()
        } else if (currentIndex.value == 3 && ipStatistics.value) {
            ipStatistics.value.getSiteList();
        } else if (currentIndex.value == 4 && spiderStatistics.value) {
            spiderStatistics.value.getSiteList();
        }
    });
};