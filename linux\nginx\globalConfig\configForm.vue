<template>
	<page-container ref="formPageContainer" :is-back="true" :title="title">
		<cc-config v-if="type === 'cc'" />
		<cc-tolerate v-if="type === 'cc_tolerate'" />
		<cc-uri-frequency v-if="type === 'cc_uri_frequency'" />
		<url-cc-param v-if="type === 'url_cc_param'" />
		<golbls-cc v-if="type === 'golbls_cc'" />
	</page-container>
</template>

<script setup>
	import { onLoad } from '@dcloudio/uni-app';
	import PageContainer from '@/components/PageContainer/index.vue';
	import ccConfig from './ccConfig.vue';
	import ccTolerate from './ccTolerate.vue';
	import ccUriFrequency from './ccUriFrequency.vue';
	import urlCcParam from './urlCcParam.vue';
	import golblsCc from './golblsCc.vue';
	import { ref } from 'vue';
	import { formPageContainer } from './useController.js';

	const title = ref('');
	const type = ref(null);

	const handleTitle = (type) => {
		if (type === 'cc') {
			title.value = '设置CC规则';
		} else if (type === 'cc_tolerate') {
			title.value = '设置攻击次数拦截';
		} else if (type === 'cc_uri_frequency') {
			title.value = '单URL CC防御';
		} else if (type === 'url_cc_param') {
			title.value = 'URL增强模式';
		} else if (type === 'golbls_cc') {
			title.value = '人机验证白名单';
		}
	};

	onLoad((options) => {
		type.value = options.type;
		handleTitle(options.type);
	});
</script>
