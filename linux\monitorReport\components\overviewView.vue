<template>
	<view class="status-box">
		<view class="form">
			<view class="report-switch row" :prop="siteIndex" :change:prop="monitorOverview.changeSitePathByCharts">
				<view class="label text-secondary">网站：</view>
				<uni-data-select
					v-model="siteIndex"
					:localdata="siteList"
					:clear="false"
					@change="changeSitePath"
					emptyTips="暂无网站"
				></uni-data-select>
			</view>

			<view class="report-switch row" :prop="currentButton" :change:prop="monitorOverview.changeDateByCharts">
				<view class="label text-secondary">时间：</view>
				<view class="" style="width: 75%">
					<uni-segmented-control
						:current="currentButton"
						:values="items"
						@clickItem="onClickItem"
						styleType="button"
						activeColor="#20a53a"
					></uni-segmented-control>
				</view>
				<canvas canvas-id="countdownCanvas" id="countdownCanvas" style="width: 60rpx; height: 60rpx"></canvas>
			</view>

			<!-- 概览数据 -->
			<view class="firewall-details interval row">
				<view class="details-query-li rank" v-for="(item, index) in fenceList" :key="index">
					<view class="details-title">{{ item.name }}</view>
					<view class="details-count">{{ formatCurrency(item.value) }}</view>
				</view>
			</view>
		</view>

		<view class="report-switch column-layout" :prop="compare_date" :change:prop="monitorOverview.changeCompareData">
			<view
				class="label full-width text-secondary"
				:prop="trendIndicators"
				:change:prop="monitorOverview.changeTrendByCharts"
				>趋势指标：</view
			>
			<uni-data-checkbox
				v-model="trendIndicators"
				selectedColor="#20a53a"
				:localdata="trendIndicatorsList"
				class="full-width-checkbox"
			></uni-data-checkbox>
			<view
				class="label full-width text-secondary"
				:prop="comparison"
				:change:prop="monitorOverview.changeComparison"
				>对比：</view
			>
			<uni-data-checkbox
				multiple
				@change="changeComparison"
				:value="comparison"
				selectedColor="#20a53a"
				:localdata="comparisonList"
				class="full-width-checkbox"
			></uni-data-checkbox>
		</view>

		<view class="flow-charts row">
			<view
				class="charts"
				id="chartsview"
				:prop="chartsData"
				:change:prop="monitorOverview.changeChartsData"
			></view>
		</view>
	</view>
</template>

<script setup>
	import { ref, onMounted, onBeforeUnmount, getCurrentInstance } from 'vue';
	import { getSiteNames, setDefaultSite, getSiteTotalListCustom } from '@/api/monitorReport';

	const { proxy } = getCurrentInstance();

	const totalTime = ref(60);
	const remainingTime = ref(60);
	const intervalId = ref(null);
	const siteList = ref([]);
	const siteIndex = ref(''); // 将持有选定网站的名称（字符串）
	const theme = ref(uni.getStorageSync('app_theme'));

	const currentButton = ref(0);
	const trendIndicatorsList = ref([
		{ text: '浏览量(PV)', value: 'pv_number' },
		{ text: '访客量(UV)', value: 'uv_number' },
		{ text: 'IP数', value: 'ip_number' },
		{ text: '请求', value: 'request' },
		{ text: '流量', value: 'sent_bytes' },
		{ text: '蜘蛛爬虫', value: 'spider_count' },
	]);
	const trendIndicators = ref('pv_number');
	const comparisonList = ref([
		{ text: '前一天', value: 'yesterday', disable: false },
		{ text: '上周同期', value: 'lw', disable: false },
	]);
	const comparison = ref([]); // v-model 会处理这个 ref
	const items = ref(['今天', '昨天', '近7天', '近30天']);
	const fenceList = ref([
		{ name: '浏览量(PV)', value: 0, type: 'pv_number' },
		{ name: '访客量(UV)', value: 0, type: 'uv_number' },
		{ name: 'IP数', value: 0, type: 'ip_number' },
		{ name: '请求', value: 0, type: 'request' },
		{ name: '流量', value: 0, type: 'sent_bytes' },
		{ name: '蜘蛛爬虫', value: 0, type: 'spider_count' },
	]);
	const chartsData = ref({});
	const compare_date = ref([]);

	const startCountdown = () => {
		if (intervalId.value) {
			clearInterval(intervalId.value);
		}
		remainingTime.value = totalTime.value;
		drawProgressCircle();
		intervalId.value = setInterval(() => {
			remainingTime.value--;
			drawProgressCircle();
			if (remainingTime.value <= 0) {
				clearInterval(intervalId.value);
				onCountdownComplete();
			}
		}, 1000);
	};

	const onCountdownComplete = async () => {
		try {
			await getOverviewData();
			startCountdown();
		} catch (e) {
			console.log(e);
		}
	};

	const drawProgressCircle = () => {
		const ctx = uni.createCanvasContext('countdownCanvas', proxy); // 第二个参数 proxy 在 Vue 3 中是必要的

		const canvasWidth = uni.upx2px(60);
		const canvasHeight = uni.upx2px(60);

		const radius = uni.upx2px(25);
		const lineWidth = uni.upx2px(4);
		const centerX = canvasWidth / 2;
		const centerY = canvasHeight / 2;
		const startAngle = -Math.PI / 2;
		const progress = (totalTime.value - remainingTime.value) / totalTime.value;

		ctx.clearRect(0, 0, canvasWidth, canvasHeight);

		ctx.beginPath();
		ctx.arc(centerX, centerY, radius, 0, 2 * Math.PI);
		ctx.setLineWidth(lineWidth);
		ctx.setStrokeStyle('#e5e5e5');
		ctx.stroke();

		const endAngle = startAngle + 2 * Math.PI * progress;
		ctx.beginPath();
		ctx.arc(centerX, centerY, radius, startAngle, endAngle);
		ctx.setLineWidth(lineWidth);
		ctx.setStrokeStyle('#4caf50');
		ctx.stroke();

		ctx.setFontSize(uni.upx2px(28));
		if (theme.value === 'dark') {
			ctx.setFillStyle('#fff');
		} else {
			ctx.setFillStyle('#000');
		}
		ctx.setTextAlign('center');
		ctx.setTextBaseline('middle');
		ctx.fillText(remainingTime.value.toString(), centerX, centerY);

		ctx.draw();
	};

	const getData = () => {
		getSiteList();
	};

	const getOverviewData = async () => {
		const dateObj = getNowDateType(currentButton.value);
		let params = {
			SiteName: siteIndex.value,
			part_type: currentButton.value < 2 ? 'hour' : 'date',
			start_date: dateObj.start_date,
			end_date: dateObj.end_date,
			is_verify: false,
		};
		try {
			const res = await getSiteTotalListCustom(params);
			const { list, total } = res;
			chartsData.value = list;
			// let chartsDataCopyLocal = JSON.parse(JSON.stringify(data.list)); // 本地副本，如果需要

			fenceList.value.forEach((item) => {
				item.value = total[item.type];
			});
			const bytesItem = fenceList.value.find((item) => item.type === 'sent_bytes');
			if (bytesItem) {
				bytesItem.value = formatBytes(total.sent_bytes); // 确保传递原始数值
			}

			if (comparison.value.length) {
				const isYesterday = comparison.value.includes('yesterday');
				let dateOffset = 0;
				if (currentButton.value === 0) {
					// 今天
					dateOffset = isYesterday ? 1 : comparison.value.includes('lw') ? 7 : 0;
				} else if (currentButton.value === 1) {
					// 昨天
					dateOffset = isYesterday ? 2 : comparison.value.includes('lw') ? 14 : 0; // 保留原始逻辑的14
				}

				if (dateOffset > 0) {
					let compareStartDate = get_before_date(dateOffset);
					let comparisonParams = {
						SiteName: siteIndex.value,
						part_type: 'hour',
						start_date: compareStartDate,
						end_date: compareStartDate,
						is_verify: false,
					};
					const compRes = await getSiteTotalListCustom(comparisonParams);
					compare_date.value = compRes.list;
				} else {
					compare_date.value = [];
				}
			} else {
				compare_date.value = [];
			}
		} catch (error) {
			console.error('获取概览数据错误:', error);
			chartsData.value = {};
			compare_date.value = [];
			fenceList.value.forEach((item) => {
				item.value = item.type === 'sent_bytes' ? formatBytes(0) : 0;
			});
		}
	};

	const get_before_date = (days) => {
		let now = new Date();
		let beforeDaysDate = new Date(now.getTime() - days * 24 * 3600 * 1000);
		return formatDate(beforeDaysDate);
	};

	const getSiteList = async () => {
		try {
			const res = await getSiteNames();
			const remoteSiteList = res; // 重命名以避免与 ref 冲突
			siteList.value = remoteSiteList.map((item) => ({
				...item,
				text: item.name,
				value: item.name,
				disable: false,
			}));

			if (siteList.value.length) {
				const defaultSite = siteList.value.find((item) => item.default);
				if (defaultSite) {
					siteIndex.value = defaultSite.name;
				} else {
					siteIndex.value = siteList.value[0].name;
				}
				getOverviewData();
			}
		} catch (error) {
			console.error('获取网站列表错误:', error);
			siteList.value = [];
		}
	};

	const changeSitePath = (selectedValue) => {
		// v-model="siteIndex" 已经更新了 siteIndex.value
		setDefaultSite({ SiteName: siteIndex.value, is_verify: false })
			.then((res) => {
				getOverviewData();
			})
			.catch((error) => {
				console.error('设置默认网站错误:', error);
			});
	};

	const onClickItem = (e) => {
		const { currentIndex } = e;
		currentButton.value = currentIndex;
		comparisonList.value.forEach((item) => {
			item.disable = currentIndex > 1;
		});
		if (currentIndex > 1) {
			comparison.value = [];
			compare_date.value = [];
		}
		getOverviewData();
	};

	const changeComparison = (e) => {
		comparison.value = e.detail.value.filter(item => !comparison.value.includes(item));
		getOverviewData();
	};

	const formatCurrency = (num) => {
		let nums = Number(num);
		if (isNaN(nums)) {
			return num;
		}
		if (nums === 0) {
			return '0';
		}
		let sign = nums < 0 ? '-' : '';
		nums = Math.abs(Math.round(nums));
		let integerPart = nums.toString();
		integerPart = integerPart.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
		return sign + integerPart;
	};

	const getNowDateType = (type) => {
		let dateObj = { start_date: '', end_date: '' };
		const today = new Date();
		switch (type) {
			case 0:
				dateObj.start_date = formatDate(today);
				dateObj.end_date = formatDate(today);
				break;
			case 1:
				const yesterday = new Date(today);
				yesterday.setDate(today.getDate() - 1);
				dateObj.start_date = formatDate(yesterday);
				dateObj.end_date = formatDate(yesterday);
				break;
			case 2:
				const last7DaysStart = new Date(today);
				last7DaysStart.setDate(today.getDate() - 6);
				dateObj.start_date = formatDate(last7DaysStart);
				dateObj.end_date = formatDate(today);
				break;
			case 3:
				const last30DaysStart = new Date(today);
				last30DaysStart.setDate(today.getDate() - 29);
				dateObj.start_date = formatDate(last30DaysStart);
				dateObj.end_date = formatDate(today);
				break;
		}
		return dateObj;
	};

	const formatDate = (date) => {
		const year = date.getFullYear();
		const month = String(date.getMonth() + 1).padStart(2, '0');
		const day = String(date.getDate()).padStart(2, '0');
		return `${year}-${month}-${day}`;
	};

	const formatBytes = (bytes) => {
		const numBytes = Number(bytes);
		if (isNaN(numBytes) || numBytes === 0) return '0 B';

		const units = ['B', 'KB', 'MB', 'GB', 'TB'];
		const k = 1024;
		if (numBytes < 1) return `${numBytes.toFixed(2)} B`; // 处理小于1字节的情况（理论上字节数是整数）
		const i = Math.floor(Math.log(numBytes) / Math.log(k));
		const unitIndex = Math.min(i, units.length - 1);
		const value = parseFloat((numBytes / Math.pow(k, unitIndex)).toFixed(2));
		return `${value} ${units[unitIndex]}`;
	};

	onMounted(() => {
		getData();
		startCountdown();
	});

	onBeforeUnmount(() => {
		if (intervalId.value) {
			clearInterval(intervalId.value);
		}
	});
</script>

<script module="monitorOverview" lang="renderjs">
	var echarts = require("../../../static/echarts/echarts.min.js");

	var chartsView = null;

	export default {
		data() {
			return {
				chartsData: null,
				chartsOption: {},
				siteName: '',
				dateType: 0,
				trendIndicators: 'pv_number',
				trendIndicatorsList: [
					{ text: '浏览量(PV)', value: 'pv_number' },
					{ text: '访客量(UV)', value: 'uv_number' },
					{ text: 'IP数', value: 'ip_number' },
					{ text: '请求', value: 'request' },
					{ text: '流量', value: 'sent_bytes' },
					{ text: '蜘蛛爬虫', value: 'spider_count' }
				],
				comparison: [],
				compare_date: []
			}
		},
		methods: {
			changeComparison(newValue) {
				this.comparison = newValue;
				this.$nextTick(() => {
					this.chartsInit();
				});
			},
			changeCompareData(newValue) {
				this.compare_date = newValue;
	               this.$nextTick(() => {
					this.chartsInit();
				});
			},
			changeTrendByCharts(newValue) {
				this.trendIndicators = newValue;
	               this.$nextTick(() => {
					this.chartsInit();
				});
			},
			changeDateByCharts(newValue) {
				this.dateType = newValue;
	               this.$nextTick(() => {
					this.chartsInit();
				});
			},
			changeSitePathByCharts(newValue) {
	               this.siteName = typeof newValue === 'string' ? newValue : '';
	               this.$nextTick(() => {
					this.chartsInit();
				});
			},
			changeChartsData(newValue) {
				this.chartsData = newValue;
				this.chartsInit();
			},
			chartsInit() {
				const chartDom = document.getElementById("chartsview");
				if (!chartDom) {
	                   setTimeout(() => { this.chartsInit(); }, 100);
	                   return;
	               }
				if (chartsView == null) {
					chartsView = echarts.init(chartDom);
				}

	               if (!this.chartsData || (Array.isArray(this.chartsData) && this.chartsData.length === 0) || (typeof this.chartsData === 'object' && Object.keys(this.chartsData).length === 0)  ) {
	                   if (chartsView) {
	                       chartsView.clear();
	                       chartsView.setOption({
	                           title: { text: '暂无数据', left: 'center', top: 'center', textStyle: { color: '#888', fontSize: 14 } },
	                           graphic: []
	                       });
	                   }
	                   return;
	               }

				let trend_name_obj = this.trendIndicatorsList.find(item => item.value === this.trendIndicators);
	               let trend_name = trend_name_obj ? trend_name_obj.text : '';

				this.chartsOption = {
					tooltip: {
						trigger: 'axis',
						borderColor: '#eee',
						borderWidth: 1,
						padding: [5, 10],
						axisPointer: {
						    type: 'line',
						    lineStyle: { color: 'rgba(150,150,150,0.3)', width: 1 }
						},
						textStyle: { color: '#333', fontSize: 12 },
						extraCssText: 'box-shadow: 0 1px 5px rgba(0, 0, 0, 0.1); border-radius: 4px;',
						position: function(pt, params, dom, rect, size) {
							var y = pt[1];
							var tooltipWidth = size.contentSize[0];
							var viewWidth = size.viewSize[0];
							var x = pt[0];
							if (x + tooltipWidth + 20 > viewWidth) { // Add some margin
							    x = viewWidth - tooltipWidth - 20;
							}
	                           if (x < 10) x = 10;

	                           var tooltipHeight = size.contentSize[1];
	                           if (y + tooltipHeight + 20 > size.viewSize[1]) {
	                               y = size.viewSize[1] - tooltipHeight - 20;
	                           }
	                           if (y < 10) y = 10;
							return [x, y];
						},
						formatter: (params) => { // Use arrow function to retain 'this' context if needed, though not strictly here
						    var htmlStr = "";
	                           if (!params || params.length === 0) return '';

						    for (var i = 0; i < params.length; i++) {
						        var seriesParam = params[i];
	                               var val = seriesParam.value;
	                               if (Array.isArray(val)) {
	                                   val = val[1];
	                               }
						        htmlStr +=
						            `<div style="display: flex; justify-content: space-between; align-items: center; height:22px;line-height:22px;overflow:hidden;padding:2px 0;">
						              <span style="display: flex; align-items: center; max-width:140px;overflow:hidden;text-overflow: ellipsis;white-space: nowrap;">
						                <span style="margin-right:5px;display:inline-block;width:8px;height:8px;border-radius:4px;background-color:${seriesParam.color};"></span>
						                ${seriesParam.seriesName}
						              </span>
						              <span style="font-weight: bold;">${val}${(this.trendIndicators == 'sent_bytes' ? ' MB' : '')}</span>
						            </div>`;
						    }
						    var res = `<div style="min-width: 180px;">
						                <div style="height:30px;line-height:30px;padding:0 5px; margin-bottom: 5px; border-bottom: 1px solid #f0f0f0;">
						                  ${params[0].name}
						                  <span style="float:right; color: #666; font-size: 11px;">${(this.comparison && this.comparison.length ? trend_name : '')}</span>
						                </div>
						                ${htmlStr}
						              </div>`;
						    return res;
						}
					},
					grid: {
					    containLabel: true,
					    left: 10,
					    top: '12%',
					    right: '4%',
					    bottom: '18%',
					},
					xAxis: {
						type: 'category',
						boundaryGap: false,
						axisTick: { alignWithLabel: true, lineStyle: { color: '#ccc' } },
						axisLine: { lineStyle: { color: '#ccc' } },
						axisLabel: { interval: 'auto', color: '#666', fontSize: 10 },
	                       data: []
					},
					yAxis: {
						type: 'value',
						splitNumber: 4,
						axisLabel: {
							color: '#666',
							fontSize: 10,
						},
						axisLine: { show: false },
						axisTick: { show: false },
						splitLine: { show: true, lineStyle: { color: '#E5E9ED', type: 'dashed' } }
					},
					dataZoom: [{
						type: 'slider',
						start: 0,
						end: 100,
						handleIcon: 'M10.7,11.9v-1.3H9.3v1.3c-4.9,0.3-8.8,4.4-8.8,9.4c0,5,3.9,9.1,8.8,9.4v1.3h1.3v-1.3c4.9-0.3,8.8-4.4,8.8-9.4C19.5,16.3,15.6,12.2,10.7,11.9z M13.3,24.4H6.7V23h6.6V24.4z M13.3,19.6H6.7v-1.4h6.6V19.6z',
						handleSize: '80%',
						handleStyle: {
							color: '#fff',
							shadowBlur: 3,
							shadowColor: 'rgba(0, 0, 0, 0.6)',
							shadowOffsetX: 2,
							shadowOffsetY: 2
						},
						left: '5%',
						right: '5%',
					}],
					graphic: [{
					    type: 'text',
						left: 'center',
						top: 5,
						z: 100,
						style: {
							fill: 'var(--text-color-primary)',
							text: this.siteName,
							font: 'bold 14px Arial'
						}
					}],
					series: []
				};

	               this.chartsOption.series = [];

				if(this.comparison && this.comparison.length) {
					this.chartsOption.series.push({
						type: 'line',
						smooth: true,
						symbol: 'circle',
						symbolSize: 6,
						itemStyle: {
						    color: '#20a53a',
						    lineStyle: { color: "#20a53a", width: 2 },
						    areaStyle: {
						        color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [{
						            offset: 0, color: 'rgba(32, 165, 58, 0)'
						        }, {
						            offset: 1, color: 'rgba(32, 165, 58, 0.2)'
						        }]),
						    }
						},
						name: this.dateType === 0 ? '今日' : (this.dateType === 1 ? '昨日' : '当前'),
						data: this.transformDataTotal(this.chartsData),
					});
	                   if (this.compare_date && this.compare_date.length > 0) {
	                       this.chartsOption.series.push({
	                           name: this.comparison.includes('yesterday') ? '前一天' : (this.comparison.includes('lw') ? '上周同期' : '对比'),
	                           type: 'line',
	                           smooth: true,
	                           symbol: 'circle',
						    symbolSize: 6,
	                           itemStyle: {
	                               color: '#5cadff', // Changed comparison color
	                               lineStyle: { color: "#5cadff", width: 2 },
	                               areaStyle: {
	                                   color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [{
	                                       offset: 0, color: 'rgba(92, 173, 255, 0)'
	                                   }, {
	                                       offset: 1, color: 'rgba(92, 173, 255, 0.2)'
	                                   }])
	                               }
	                           },
	                           data: this.transformDataTotal(this.compare_date)
	                       });
	                   }
				} else {
	                   let currentSeriesName = trend_name_obj ? trend_name_obj.text : '数据';
					this.chartsOption.series.push({
						type: 'line',
						smooth: true,
	                       symbol: 'circle',
						symbolSize: 6,
						itemStyle: {
	                           color: '#20a53a',
						    lineStyle: { color: "#20a53a", width: 2 },
						    areaStyle: {
						        color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [{
						            offset: 0, color: 'rgba(32, 165, 58, 0)'
						        }, {
						            offset: 1, color: 'rgba(32, 165, 58, 0.2)'
						        }]),
						    }
						},
						name: currentSeriesName,
						data: this.transformDataTotal(this.chartsData),
					});
				}

	               if (this.chartsData && this.chartsData.length > 0) {
	                   if (this.dateType === 0 || this.dateType === 1) {
	                       this.chartsOption.xAxis.data = this.chartsData.map(item => item.hour + ':00');
	                   } else {
	                       const reg = /^(\d{4})-?(\d{2})-?(\d{2})$/;
	                       this.chartsOption.xAxis.data = this.chartsData.map(item => {
	                           return String(item.date).replace(reg, "$2/$3");
	                       });
	                   }
	               } else {
	                    this.chartsOption.xAxis.data = []; // Ensure xAxis data is empty if chartsData is empty
	               }

				chartsView.setOption(this.chartsOption, true);
			},

			transformDataTotal(arr) {
				if (!arr || arr.length === 0) return [];
				let total = [];
				const trendType = this.trendIndicators;
				for (let i = 0; i < arr.length; i++) {
					let item = arr[i];
	                   if (!item) continue;

					let _time = '';
					let _value = item[trendType];

					switch(this.dateType){
						case 0:
						case 1:
							_time = item.hour + ':00';
							break;
						default:
	                           const regDate = /^(\d{4})-?(\d{2})-?(\d{2})$/;
							_time = String(item.date).replace(regDate, "$2/$3");
							break;
					}
					if(trendType === 'sent_bytes') {
	                       _value = this.formatBytesRenderJs(item[trendType]);
	                   }
					total.push([_time, _value]);
				}
				return total;
			},
			formatBytesRenderJs(bytes) {
				if (bytes === 0 || bytes === undefined || bytes === null) return 0;
	               const numBytes = Number(bytes);
	               if (isNaN(numBytes)) return 0;
				let mb = numBytes / (1024 * 1024);
				return Number(mb.toFixed(2));
			}
		},
	}
</script>

<style scoped>
	.status-box {
		/* padding: 16rpx; */
		height: calc(100vh - 280rpx);
		overflow: scroll;
		padding-bottom: 40rpx;
		background-color: var(--bg-color);
	}
	.form {
		/* background-color: #fff; Optional: if sections need distinct backgrounds */
	}
	.flow-charts {
		width: 100%;
		height: 650rpx; /* Fixed height for the chart container */
		background-color: var(--bg-color);
		padding: 20rpx; /* Add some padding around the chart */
		box-sizing: border-box;
	}

	.charts {
		width: 100%;
		height: 100%;
	}

	.report-switch {
		min-height: 100rpx;
		display: flex;
		align-items: center;
		/* justify-content: space-between; Default for row, adjust for column */
		border-bottom: 1rpx solid #f0f0f0; /* Lighter border */
		padding: 20rpx 30rpx; /* Adjusted padding */
		background-color: var(--bg-color); /* Add background to switches for better separation */
	}
	.report-switch.row {
		justify-content: space-between;
	}
	.report-switch.column-layout {
		flex-direction: column;
		align-items: flex-start; /* Align items to the start for column layout */
		padding-bottom: 20rpx; /* Original padding-bottom was 160rpx, seems large */
	}

	.report-switch .label {
		/* Style for labels like "网站：" */
		font-size: 28rpx; /* Slightly smaller font */
		margin-right: 15rpx;
		flex-shrink: 0;
	}
	.report-switch.column-layout .label {
		margin-bottom: 10rpx; /* Space below label in column layout */
	}
	.report-switch.column-layout .full-width {
		width: 100%;
	}
	.report-switch.column-layout .full-width-checkbox {
		width: 100%;
		margin-bottom: 15rpx; /* Space below checkbox group */
	}

	.report-switch uni-data-select,
	.report-switch uni-segmented-control {
		flex-grow: 1;
	}

	.firewall-details {
		margin: 20rpx 30rpx;
		padding: 20rpx 10rpx; /* Adjust padding */
		display: flex;
		justify-content: space-around;
		flex-wrap: wrap;
		background-color: var(--border-color); /* Lighter background */
		border: 1rpx solid #eee;
		border-radius: 12rpx; /* Softer radius */
	}

	.details-query-li {
		width: 32%; /* Adjust for spacing, approx 3 per row */
		padding: 15rpx 5rpx;
		box-sizing: border-box;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		text-align: center;
		margin-bottom: 10rpx; /* Space between rows of items */
	}

	.details-title {
		color: var(--text-color-secondary); /* Slightly darker */
		font-size: 24rpx;
		margin-bottom: 8rpx;
	}

	.details-count {
		color: var(--text-color-primary);
		font-size: 28rpx; /* Slightly smaller */
		font-weight: bold; /* Bolder */
	}
</style>
