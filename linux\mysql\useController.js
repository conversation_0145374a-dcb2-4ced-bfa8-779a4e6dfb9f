import { ref, reactive, computed } from 'vue';
import { getSoftStatus, serviceAdmin } from '@/api/config';
import { getMySQLInfo, setMySQLPort, setDataDir } from '@/api/database';

export const pageContainer = ref(null);

// 服务状态
export const serviceStatus = reactive({
	isRunning: false,
	version: '获取中...',
});

// 加载状态
export const serviceLoading = ref(false);

// 当前正在执行的快速操作ID
export const currentExecutingActionId = ref(null);

// 端口配置加载状态
export const portLoading = ref(false);

// 端口验证错误信息
export const portError = ref('');

// 存储位置配置加载状态
export const storageLoading = ref(false);

// MySQL信息
export const mysqlInfo = reactive({
	datadir: '获取中...',
	port: '获取中...',
});

// 临时存储路径（用于用户选择的路径，避免被API数据覆盖）
export const tempStoragePath = ref('');

// 标记是否有用户选择的临时路径
export const hasTempStoragePath = ref(false);

// 重置临时路径状态（当用户手动清空或重置路径时调用）
export const resetTempStoragePath = () => {
	tempStoragePath.value = '';
	hasTempStoragePath.value = false;
};

// 快速操作按钮
export const quickActions = computed(() => {
	const actions = [];

	if (serviceStatus.isRunning) {
		actions.push(
			{ id: 'stop', title: '停止', icon: 'stop-circle' },
			{ id: 'restart', title: '重启', icon: 'reload' },
			{ id: 'reload', title: '重载配置', icon: 'refresh' },
		);
	} else {
		actions.push({ id: 'start', title: '启动', icon: 'play-circle' });
	}

	return actions;
});

// 功能模块
export const functionModules = ref([
	{
		id: 'config',
		title: '配置文件',
		desc: '编辑my.cnf主配置文件',
		icon: 'file-text',
		showData: false,
		navigatable: true,
	},
	{
		id: 'errorLog',
		title: '错误日志',
		desc: '查看MySQL错误日志',
		icon: 'warning',
		showData: false,
		navigatable: true,
	},
]);

// MySQL安装状态
export const isMySQLInstalled = ref(true);

// 编辑状态管理
export const storageEditMode = ref(false);
export const portEditMode = ref(false);

// 确认对话框相关状态
export const showConfirmDialog = ref(false);
export const confirmDialogConfig = ref({
	title: '',
	content: '',
	confirmText: '确认',
	action: null,
});

// 检查MySQL安装状态
export const checkMySQLInstalled = async () => {
	try {
		const res = await getSoftStatus({ name: 'mysql' });
		isMySQLInstalled.value = res.s_status;
	} catch (error) {
		console.error('检查MySQL安装状态失败:', error);
		isMySQLInstalled.value = false;
	}
};

// 获取MySQL服务状态
export const getMySQLServiceStatus = async () => {
	try {
		const res = await getSoftStatus({ name: 'mysql' });
		serviceStatus.isRunning = res.s_status;

		if (res.version) {
			serviceStatus.version = 'MySQL ' + res.version;
		}
	} catch (error) {
		console.error('获取MySQL服务状态失败:', error);
		serviceStatus.isRunning = false;
	}
};

// 获取MySQL信息
export const getMySQLInfoData = async () => {
	try {
		const res = await getMySQLInfo();
		if (res.status !== false) {
			// 只有在没有用户选择的临时路径时才更新datadir
			if (!hasTempStoragePath.value) {
				mysqlInfo.datadir = res.datadir || '/www/server/data';
			}
			mysqlInfo.port = res.port || '3306';
		}
	} catch (error) {
		console.error('获取MySQL信息失败:', error);
		if (!hasTempStoragePath.value) {
			mysqlInfo.datadir = '获取失败';
		}
		mysqlInfo.port = '获取失败';
	}
};

// 初始化MySQL数据
export const initMySQLData = async () => {
	try {
		await checkMySQLInstalled();
		if (isMySQLInstalled.value) {
			await getMySQLServiceStatus();
			await getMySQLInfoData();
		}
	} catch (error) {
		console.error('初始化MySQL数据失败:', error);
	}
};

// 切换服务状态
export const toggleService = async (value) => {
	try {
		serviceLoading.value = true;

		const action = value ? 'start' : 'stop';
		const res = await serviceAdmin({ name: 'mysqld', type: action });

		if (res.status) {
			await getMySQLServiceStatus();

			if (serviceStatus.isRunning) {
				pageContainer.value?.notify?.success('MySQL服务启动成功');
			} else {
				pageContainer.value?.notify?.success('MySQL服务停止成功');
			}
		} else {
			const errorMsg = res.msg || (value ? '启动失败' : '停止失败');
			pageContainer.value?.notify?.error(errorMsg);
		}
	} catch (error) {
		console.error('服务状态切换失败:', error);
		const errorMsg = error.msg || error.message || '操作失败，请重试';
		pageContainer.value?.notify?.error(errorMsg);
	} finally {
		serviceLoading.value = false;
	}
};

// 显示确认对话框
const showActionConfirm = (action) => {
	const configs = {
		start: {
			title: '启动确认',
			content: '确定要启动MySQL服务吗？',
			confirmText: '确认启动',
		},
		stop: {
			title: '停止确认',
			content: '确定要停止MySQL服务吗？停止后数据库将无法访问。',
			confirmText: '确认停止',
		},
		restart: {
			title: '重启确认',
			content: '确定要重启MySQL服务吗？重启过程中数据库将短暂无法访问。',
			confirmText: '确认重启',
		},
		reload: {
			title: '重载确认',
			content: '确定要重载MySQL配置吗？',
			confirmText: '确认重载',
		},
	};

	const config = configs[action.id];
	if (config) {
		confirmDialogConfig.value = {
			...config,
			action: action,
		};
		showConfirmDialog.value = true;
	}
};

// 确认快速操作
export const confirmQuickAction = async () => {
	const action = confirmDialogConfig.value.action;
	// 立即关闭确认弹窗，提供更好的用户体验
	showConfirmDialog.value = false;

	if (action) {
		await executeQuickAction(action);
	}
};

// 取消快速操作
export const cancelQuickAction = () => {
	showConfirmDialog.value = false;
};

// 执行具体的快速操作
const executeQuickAction = async (action) => {
	// 统一的loading控制，防止重复操作
	if (serviceLoading.value || currentExecutingActionId.value) {
		return;
	}

	try {
		currentExecutingActionId.value = action.id;

		switch (action.id) {
			case 'start':
				pageContainer.value?.notify?.primary('正在启动MySQL服务...');
				const startRes = await serviceAdmin({ name: 'mysqld', type: 'start' });
				if (startRes.status) {
					await getMySQLServiceStatus();
					await getMySQLInfoData();
					pageContainer.value?.notify?.success('MySQL服务启动成功');
				} else {
					const errorMsg = startRes.msg || '启动失败';
					pageContainer.value?.notify?.error(errorMsg);
				}
				break;

			case 'stop':
				pageContainer.value?.notify?.primary('正在停止MySQL服务...');
				const stopRes = await serviceAdmin({ name: 'mysqld', type: 'stop' });
				if (stopRes.status) {
					await getMySQLServiceStatus();
					pageContainer.value?.notify?.success('MySQL服务停止成功');
				} else {
					const errorMsg = stopRes.msg || '停止失败';
					pageContainer.value?.notify?.error(errorMsg);
				}
				break;

			case 'restart':
				pageContainer.value?.notify?.primary('正在重启MySQL服务...');
				const restartRes = await serviceAdmin({ name: 'mysqld', type: 'restart' });
				if (restartRes.status) {
					await getMySQLServiceStatus();
					await getMySQLInfoData();
					pageContainer.value?.notify?.success('MySQL服务重启成功');
				} else {
					const errorMsg = restartRes.msg || '重启失败';
					pageContainer.value?.notify?.error(errorMsg);
				}
				break;

			case 'reload':
				pageContainer.value?.notify?.primary('正在重载配置...');
				const reloadRes = await serviceAdmin({ name: 'mysqld', type: 'reload' });
				if (reloadRes.status) {
					pageContainer.value?.notify?.success('配置重载成功');
				} else {
					const errorMsg = reloadRes.msg || '重载配置失败';
					pageContainer.value?.notify?.error(errorMsg);
				}
				break;
		}
	} catch (error) {
		console.error('快速操作失败:', error);
		const errorMsg = error.msg || error.message || '操作失败，请重试';
		pageContainer.value?.notify?.error(errorMsg);
	} finally {
		currentExecutingActionId.value = null;
	}
};

// 处理快速操作
export const handleQuickAction = async (action) => {
	// 如果有操作正在执行中，则禁止新的操作
	if (serviceLoading.value || currentExecutingActionId.value) {
		pageContainer.value?.notify?.warning('有操作正在执行中，请稍候...');
		return;
	}

	// 显示确认对话框而不是直接执行操作
	showActionConfirm(action);
};

// 处理模块点击
export const handleModuleClick = async (module) => {
	try {
		switch (module.id) {
			case 'config':
				openMySQLConfigEditor();
				break;
			case 'errorLog':
				uni.navigateTo({
					url: '/linux/mysql/errorLog/index',
				});
				break;
		}
	} catch (error) {
		console.error('模块操作失败:', error);
		pageContainer.value?.notify?.error('操作失败，请重试');
	}
};

// 打开MySQL配置文件编辑器
export const openMySQLConfigEditor = async () => {
	try {
		const configFileItem = {
			path: '/etc/my.cnf',
			title: 'my.cnf',
			size: 1024,
		};

		if (configFileItem.size > 3 * 1024 * 1024) {
			pageContainer.value?.notify?.warning('配置文件过大，无法编辑');
			return;
		}

		uni.navigateTo({
			url: `/linux/files/editor/index?fileItem=${JSON.stringify(configFileItem)}`,
			animationType: 'zoom-fade-out',
		});
	} catch (error) {
		console.error('打开配置文件编辑器失败:', error);
		pageContainer.value?.notify?.error('打开配置文件失败，请重试');
	}
};

// 切换存储位置编辑模式
export const toggleStorageEditMode = () => {
	storageEditMode.value = !storageEditMode.value;
	if (!storageEditMode.value) {
		// 退出编辑模式时，可以选择恢复原始值或保持当前值
		// 这里保持当前值
	}
};

// 切换端口编辑模式
export const togglePortEditMode = () => {
	portEditMode.value = !portEditMode.value;
	if (!portEditMode.value) {
		// 退出编辑模式时，可以选择恢复原始值或保持当前值
		// 这里保持当前值
	}
};

// 选择存储路径
export const selectStoragePath = () => {
	// 使用统一的文件选择器方法
	import('@/utils/common.js').then(({ openSelector }) => {
		// 设置文件选择器回调
		import('@/stores/fileSelector.js').then(({ setFileSelectorCallback }) => {
			setFileSelectorCallback((selectedPaths) => {
				if (selectedPaths && selectedPaths.length > 0) {
					// 更新显示的路径值
					mysqlInfo.datadir = selectedPaths[0];
					// 保存临时路径
					tempStoragePath.value = selectedPaths[0];
					hasTempStoragePath.value = true;
					// 移除成功提示，因为这只是选择路径，还没有执行迁移
				}
			});

			// 打开文件夹选择器（单选模式）
			openSelector('folder', false);
		});
	});
};

// 保存存储位置配置
export const saveStorageConfig = async () => {
	// 如果存储位置配置操作正在执行中，则禁止新的操作
	if (storageLoading.value) {
		pageContainer.value?.notify?.warning('存储位置迁移正在执行中，请稍候...');
		return;
	}

	try {
		// 验证存储路径
		if (!mysqlInfo.datadir || mysqlInfo.datadir.trim() === '') {
			pageContainer.value?.notify?.error('请输入有效的存储路径');
			return;
		}

		storageLoading.value = true;
		pageContainer.value?.notify?.primary('正在迁移存储位置...');

		// 调用设置MySQL数据目录的API
		const res = await setDataDir({ datadir: mysqlInfo.datadir.trim() });

		if (res.status) {
			pageContainer.value?.notify?.success('存储位置迁移成功');
			// 迁移成功后，清除临时路径状态，允许API数据更新
			hasTempStoragePath.value = false;
			tempStoragePath.value = '';
			// 重新获取MySQL信息以确保数据同步
			await getMySQLInfoData();
		} else {
			const errorMsg = res.msg || '存储位置迁移失败';
			pageContainer.value?.notify?.error(errorMsg);
		}

		// 保存成功后退出编辑模式
		storageEditMode.value = false;
	} catch (error) {
		console.error('保存存储位置配置失败:', error);
		const errorMsg = error.msg || error.message || '迁移失败，请重试';
		pageContainer.value?.notify?.error(errorMsg);
	} finally {
		storageLoading.value = false;
	}
};

// 处理端口输入，由于有watch监听器，这里主要用于清除错误信息
export const handlePortInput = (value) => {
	// 清除错误信息，让watch监听器处理验证
	portError.value = '';
};

// 处理端口输入框失去焦点
export const handlePortBlur = () => {
	// 失去焦点时不做任何处理，允许端口为空
	// 用户可以手动输入端口号，不强制填充默认值
};

// 验证端口号是否有效
export const validatePort = (port) => {
	const numPort = parseInt(port, 10);
	if (isNaN(numPort) || numPort < 1 || numPort > 65535) {
		return false;
	}
	return true;
};

// 保存端口配置
export const savePortConfig = async () => {
	// 如果端口配置操作正在执行中，则禁止新的操作
	if (portLoading.value) {
		pageContainer.value?.notify?.warning('端口配置正在执行中，请稍候...');
		return;
	}

	try {
		// 验证端口号
		const port = parseInt(mysqlInfo.port);
		if (!validatePort(mysqlInfo.port)) {
			pageContainer.value?.notify?.error('请输入有效的端口号(1-65535)');
			portError.value = '请输入有效的端口号(1-65535)';
			return;
		}

		// 清除错误信息
		portError.value = '';

		portLoading.value = true;
		pageContainer.value?.notify?.primary('正在保存端口配置...');

		// 调用设置MySQL端口的API
		const res = await setMySQLPort({ port: port });

		if (res.status) {
			pageContainer.value?.notify?.success('端口配置已保存');
			// 重新获取MySQL信息以确保数据同步
			await getMySQLInfoData();
		} else {
			const errorMsg = res.msg || '端口配置保存失败';
			pageContainer.value?.notify?.error(errorMsg);
		}

		// 保存成功后退出编辑模式
		portEditMode.value = false;
	} catch (error) {
		console.error('保存端口配置失败:', error);
		const errorMsg = error.msg || error.message || '保存失败，请重试';
		pageContainer.value?.notify?.error(errorMsg);
	} finally {
		portLoading.value = false;
	}
};

// 处理端口值变化（change事件），确保只包含数字字符并验证范围
export const handlePortChange = (value) => {
	// 处理输入值，兼容不同的事件格式
	let inputValue = '';
	if (typeof value === 'string' || typeof value === 'number') {
		inputValue = String(value);
	} else if (value && typeof value === 'object') {
		// 可能是事件对象
		inputValue = value.target?.value || value.detail?.value || String(value);
	}

	// 如果是空值，清除错误信息
	if (!inputValue || inputValue === '') {
		portError.value = '';
		return;
	}

	// 移除所有非数字字符
	const cleanValue = inputValue.replace(/[^\d]/g, '');

	// 如果清理后为空，设置为空字符串
	if (!cleanValue) {
		mysqlInfo.port = '';
		portError.value = '';
		return;
	}

	// 更新为清理后的值
	mysqlInfo.port = cleanValue;

	// 验证数字范围
	const numValue = parseInt(cleanValue, 10);
	if (numValue < 1) {
		portError.value = '端口号不能小于1';
	} else if (numValue > 65535) {
		portError.value = '端口号不能大于65535';
	} else {
		portError.value = '';
	}
};
