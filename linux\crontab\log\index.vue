<template>
	<page-container ref="pageContainer" :title="pageTitle" :is-back="true">
		<view class="log-container">
			<!-- 日志信息卡片 -->
			<view class="log-info-card">
				<view class="info-content flex flex-col gap-2">
					<view class="log-path-container">
						<text class="log-label">日志路径:</text>
						<text class="log-path">{{ logPath || '日志路径加载中...' }}</text>
					</view>
					<text class="log-size">大小: {{ logSize || '计算中...' }}</text>
				</view>
			</view>

			<!-- 操作栏 -->
			<view class="log-actions">
				<view class="action-btn" @tap="refreshLog" hover-class="action-hover" :class="{ loading: refreshing }">
					<uv-icon name="reload" size="16" :color="refreshing ? '#ccc' : '#20a50a'"></uv-icon>
					<text class="action-text">{{ refreshing ? '刷新中' : '刷新' }}</text>
				</view>

				<view class="action-btn" @tap="clearLog" hover-class="action-hover" :class="{ loading: clearing }">
					<uv-icon name="trash" size="16" :color="clearing ? '#ccc' : '#e74c3c'"></uv-icon>
					<text class="action-text">{{ clearing ? '清空中' : '清空' }}</text>
				</view>

				<view
					v-if="taskId"
					class="action-btn"
					@tap="executeTask"
					hover-class="action-hover"
					:class="{ loading: executing }"
				>
					<uv-icon name="play-circle" size="16" :color="executing ? '#ccc' : '#3498db'"></uv-icon>
					<text class="action-text">{{ executing ? '执行中' : '执行' }}</text>
				</view>
			</view>

			<!-- 时间范围选择 -->
			<view class="time-range-selector">
				<view
					v-for="range in timeRanges"
					:key="range.id"
					class="range-btn"
					:class="{ active: activeRange === range.id }"
					@tap="setActiveRange(range.id)"
					hover-class="range-hover"
				>
					<text class="range-text">{{ range.label }}</text>
				</view>
			</view>

			<!-- 日志内容 -->
			<view class="log-content">
				<uv-loading-icon
					v-if="loading"
					mode="circle"
					color="#20a50a"
					size="24"
					class="loading-center"
				></uv-loading-icon>
				<scroll-view
					v-else
					class="log-display"
					scroll-y="true"
					:scroll-top="scrollTop"
					ref="scrollView"
					@scroll="onScroll"
				>
					<text v-if="logContent" class="log-text">{{ logContent }}</text>
					<view v-else class="empty-log">
						<text class="empty-text">暂无日志内容</text>
					</view>
				</scroll-view>

				<!-- 滚动到底部按钮 -->
				<view
					v-if="showScrollToBottom"
					class="scroll-to-bottom"
					@tap="scrollToBottom"
					hover-class="scroll-hover"
				>
					<uv-icon name="arrow-down" size="16" color="#fff"></uv-icon>
				</view>
			</view>
		</view>

		<!-- 清空日志确认对话框 -->
		<CustomDialog
			v-model="showClearDialog"
			title="确认清空"
			confirmText="确认清空"
			cancelText="取消"
			contentHeight="200rpx"
			:confirmStyle="{
				backgroundColor: '#FF3B30',
			}"
			@confirm="confirmClearLog"
		>
			<view class="text-secondary flex justify-center items-center h-full">
				确定要清空日志吗？此操作不可恢复。
			</view>
		</CustomDialog>
	</page-container>
</template>

<script setup>
	import { onLoad, onUnload } from '@dcloudio/uni-app';
	import { computed } from 'vue';
	import PageContainer from '@/components/PageContainer/index.vue';
	import CustomDialog from '@/components/CustomDialog/index.vue';
	import { useLogViewer, pageContainer } from './useController.js';

	// 使用控制器
	const {
		logPath,
		logSize,
		logContent,
		activeRange,
		scrollTop,
		loading,
		refreshing,
		clearing,
		executing,
		taskId,
		taskName,
		timeRanges,
		showScrollToBottom,
		showClearDialog,
		initPageData,
		refreshLog,
		clearLog,
		confirmClearLog,
		executeTask,
		setActiveRange,
		scrollToBottom,
		onScroll,
		cleanup,
	} = useLogViewer();

	// 计算属性 - 动态标题
	const pageTitle = computed(() => {
		return taskName.value ? `查看【${taskName.value}】日志` : '计划任务日志查看';
	});

	// 页面加载
	onLoad((options) => {
		initPageData(options);
	});

	// 页面卸载
	onUnload(() => {
		cleanup();
	});
</script>

<style lang="scss" scoped>
	.log-container {
		padding: 32rpx 32rpx 0;
		display: flex;
		flex-direction: column;
		gap: 24rpx;
		height: 100%;
		background-color: var(--bg-color);
	}

	/* 日志信息卡片 */
	.log-info-card {
		background: var(--dialog-bg-color);
		border: 2rpx solid var(--border-color);
		border-radius: 12rpx;
		padding: 24rpx;
		box-shadow: var(--box-shadow);

		.info-content {
			.log-path-container {
				display: flex;
				flex-direction: column;
				gap: 8rpx;

				.log-label {
					font-size: 24rpx;
					color: var(--text-color-secondary);
					font-weight: 500;
					flex-shrink: 0;
				}

				.log-path {
					font-size: 22rpx;
					color: var(--text-color-primary);
					font-weight: 400;
					word-break: break-all;
					line-height: 1.4;
					background: var(--bg-color);
					padding: 12rpx 16rpx;
					border-radius: 8rpx;
					border: 1rpx solid var(--border-color);
					font-family: 'Courier New', monospace;
					max-height: 120rpx;
					overflow-y: auto;
					box-shadow: inset 0 2rpx 4rpx rgba(0, 0, 0, 0.05);
				}
			}

			.log-size {
				font-size: 24rpx;
				color: var(--text-color-secondary);
				font-weight: 500;
			}
		}
	}

	/* 操作栏 */
	.log-actions {
		background: var(--dialog-bg-color);
		border: 2rpx solid var(--border-color);
		border-radius: 12rpx;
		padding: 12rpx;
		display: flex;
		justify-content: space-between;
		gap: 16rpx;
		box-shadow: var(--box-shadow);
	}

	.action-btn {
		display: flex;
		align-items: center;
		justify-content: center;
		padding: 20rpx 24rpx;
		border-radius: 10rpx;
		background: var(--dialog-bg-color);
		border: 2rpx solid var(--border-color);
		transition: all 0.2s ease;
		gap: 8rpx;
		cursor: pointer;
		flex: 1;
		min-width: 0;
		box-shadow: var(--box-shadow);

		&.loading {
			opacity: 0.6;
			pointer-events: none;
		}

		.action-text {
			font-size: 24rpx;
			color: var(--text-color-primary);
			font-weight: 500;
			white-space: nowrap;
			overflow: hidden;
			text-overflow: ellipsis;
		}
	}

	.action-hover {
		background: var(--bg-color);
		border-color: var(--primary-color);
		transform: translateY(-2rpx);
		box-shadow:
			0 6rpx 12rpx rgba(32, 165, 10, 0.25),
			0 3rpx 6rpx rgba(0, 0, 0, 0.12);
	}

	/* 时间范围选择 */
	.time-range-selector {
		background: var(--dialog-bg-color);
		border: 2rpx solid var(--border-color);
		border-radius: 12rpx;
		padding: 12rpx;
		display: flex;
		justify-content: space-between;
		gap: 12rpx;
		box-shadow: var(--box-shadow);
	}

	.range-btn {
		display: flex;
		align-items: center;
		justify-content: center;
		padding: 18rpx 20rpx;
		border-radius: 10rpx;
		background: var(--dialog-bg-color);
		border: 2rpx solid var(--border-color);
		transition: all 0.2s ease;
		cursor: pointer;
		flex: 1;
		min-width: 0;
		box-shadow: var(--box-shadow);

		&.active {
			background: var(--primary-color);
			border-color: var(--primary-color);
			.range-text {
				color: #fff;
				font-weight: 600;
				text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.2);
			}
		}

		.range-text {
			font-size: 24rpx;
			color: var(--text-color-primary);
			font-weight: 500;
			white-space: nowrap;
			overflow: hidden;
			text-overflow: ellipsis;
		}
	}

	.range-hover {
		background: var(--bg-color);
		border-color: var(--primary-color);
		transform: translateY(-2rpx);
		box-shadow:
			0 6rpx 12rpx rgba(32, 165, 10, 0.25),
			0 3rpx 6rpx rgba(0, 0, 0, 0.12);
	}

	/* 日志内容 */
	.log-content {
		flex: 1;
		background: #000000;
		border: 2rpx solid var(--border-color);
		border-radius: 12rpx;
		overflow: hidden;
		display: flex;
		flex-direction: column;
		position: relative;
		box-shadow: var(--box-shadow);
	}

	.log-display {
		height: 0;
		flex: 1;
		background: #000;
		font-family: 'Courier New', monospace;
	}

	.log-text {
		font-size: 28rpx;
		font-weight: 600;
		line-height: 1.2;
		white-space: pre-wrap;
		word-break: break-all;
		display: block;
		padding: 32rpx;
		color: #fff;
		text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.9);
		letter-spacing: 0.5rpx;
	}

	.empty-log {
		display: flex;
		align-items: center;
		justify-content: center;
		height: 100%;
		min-height: 400rpx;
		padding: 32rpx;

		.empty-text {
			color: var(--text-color-tertiary);
			font-size: 32rpx;
			font-weight: 600;
			opacity: 0.8;
			letter-spacing: 0.5rpx;
		}
	}

	.loading-center {
		position: absolute !important;
		top: 50% !important;
		left: 50% !important;
		transform: translate(-50%, -50%) !important;
		z-index: 10 !important;
	}

	/* 滚动到底部按钮 */
	.scroll-to-bottom {
		position: absolute;
		bottom: 32rpx;
		right: 32rpx;
		width: 88rpx;
		height: 88rpx;
		background: var(--primary-color);
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
		cursor: pointer;
		box-shadow:
			0 10rpx 30rpx rgba(32, 165, 10, 0.5),
			0 4rpx 12rpx rgba(0, 0, 0, 0.2);
		border: 2rpx solid rgba(255, 255, 255, 0.1);
		transition: all 0.2s ease;
	}

	.scroll-hover {
		transform: translateY(-4rpx) scale(1.05);
		box-shadow:
			0 16rpx 40rpx rgba(32, 165, 10, 0.6),
			0 6rpx 16rpx rgba(0, 0, 0, 0.25);
	}

	/* 深色主题适配 */
	.theme-dark {
		.log-container {
			background-color: var(--bg-color);
		}

		.log-info-card {
			background: var(--bg-color-secondary);
			border-color: var(--border-color);
			box-shadow: var(--box-shadow);

			.info-content {
				.log-path-container {
					.log-label {
						color: var(--text-color-secondary);
					}

					.log-path {
						background: var(--bg-color);
						border-color: var(--border-color);
						color: var(--text-color-primary);
						box-shadow: inset 0 2rpx 4rpx rgba(0, 0, 0, 0.25);
					}
				}

				.log-size {
					color: var(--text-color-secondary);
				}
			}
		}

		.log-actions {
			background: var(--bg-color-secondary);
			border-color: var(--border-color);
			box-shadow: var(--box-shadow);

			.action-btn {
				background: var(--bg-color-secondary);
				border-color: var(--border-color);
				box-shadow: var(--box-shadow);

				.action-text {
					color: var(--text-color-primary);
				}
			}

			.action-hover {
				background: var(--bg-color);
				border-color: var(--primary-color);
			}
		}

		.time-range-selector {
			background: var(--bg-color-secondary);
			border-color: var(--border-color);
			box-shadow: var(--box-shadow);

			.range-btn {
				background: var(--bg-color-secondary);
				border-color: var(--border-color);
				box-shadow: var(--box-shadow);

				.range-text {
					color: var(--text-color-primary);
				}

				&.active {
					background: var(--primary-color);
					border-color: var(--primary-color);
				}
			}

			.range-hover {
				background: var(--bg-color);
				border-color: var(--primary-color);
			}
		}

		.log-content {
			border-color: var(--border-color);
			box-shadow: var(--box-shadow);

			.empty-text {
				color: var(--text-color-tertiary);
			}
		}

		.scroll-to-bottom {
			background: var(--primary-color);
		}
	}

	/* 加载动画优化 */
	:deep(.uv-loading-icon) {
		position: absolute !important;
		top: 50% !important;
		left: 50% !important;
		transform: translate(-50%, -50%) !important;
		z-index: 10 !important;
	}
</style>
