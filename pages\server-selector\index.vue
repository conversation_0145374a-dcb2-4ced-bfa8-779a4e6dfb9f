<template>
	<page-container ref="pageContainer" :title="$t('server.serverList')">
		<ServerSelector @select="onSelect" @delete="handleDelete" />
	</page-container>
</template>

<script setup>
	import { onLoad } from '@dcloudio/uni-app';
	import { ref } from 'vue';
	import PageContainer from '@/components/PageContainer/index.vue';
	import ServerSelector from '@/components/ServerSelector/index.vue';
	import { navigateServerInfo, onActionDelete } from '@/pages/index/serverList/useController';
	import { getNetwork } from '@/linux/index/useController';
	import { $t } from '@/locale/index.js';

	const serverIp = ref(null);
	const pageContainer = ref(null);

	const onSelect = (item) => {
		navigateServerInfo(item, async () => {
			uni.showLoading({
				title: $t('server.connecting'),
			});
			await getNetwork();
			uni.navigateBack();
		});
	};

	const handleDelete = async (item) => {
		if (serverIp.value === item.ip) {
			pageContainer.value.notify.error('不能删除当前打开的服务器');
			return;
		}
		await onActionDelete(item);
		pageContainer.value.notify.success($t('common.deleteSuccess'));
	};

	onLoad((options) => {
		serverIp.value = options.server;
	});
</script>

<style scoped></style>
