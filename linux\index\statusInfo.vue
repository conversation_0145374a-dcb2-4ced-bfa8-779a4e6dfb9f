<template>
	<view class="expandable-layout rd-32 p-32" :class="{ 'expandable-layout--disabled': disabled }">
		<!-- 标题区域 - 可选 -->
		<view v-if="title || $slots.title" class="expandable-layout__header">
			<view class="expandable-layout__header-content">
				<!-- 标题内容 -->
				<view class="expandable-layout__title">
					<slot name="title">
						<text v-if="title" class="title-text">{{ title }}</text>
					</slot>
				</view>

				<!-- 右侧操作区域 - 显示展开/收起状态 -->
				<view v-if="!disabled" class="expandable-layout__header-action" @click="handleToggle">
					<text class="action-text">{{ isExpanded ? expandedText : collapsedText }}</text>
					<uv-icon
						:name="isExpanded ? expandedIcon : collapsedIcon"
						size="16"
						color="#999999"
						class="action-icon"
					></uv-icon>
				</view>
			</view>
		</view>

		<!-- 基本信息区域 - 始终显示 -->
		<view class="expandable-layout__basic">
			<view class="expandable-layout__basic-content">
				<slot name="basic"></slot>
			</view>
		</view>

		<!-- 详情信息区域 - 可展开 -->
		<view
			class="expandable-layout__details-container"
			:class="{ 'expandable-layout__details-container--expanded': isExpanded }"
		>
			<view class="expandable-layout__details">
				<view class="expandable-layout__details-content">
					<slot name="details"></slot>
				</view>
			</view>
		</view>
	</view>
</template>

<script setup>
	import { ref, watch } from 'vue';

	// Props 定义
	const props = defineProps({
		// 标题文本
		title: {
			type: String,
			default: '',
		},
		// 是否默认展开
		defaultExpanded: {
			type: Boolean,
			default: false,
		},
		// 是否禁用展开功能
		disabled: {
			type: Boolean,
			default: false,
		},
		// 外部控制展开状态
		modelValue: {
			type: Boolean,
			default: undefined,
		},
		// 展开状态图标
		expandedIcon: {
			type: String,
			default: 'arrow-up',
		},
		// 收起状态图标
		collapsedIcon: {
			type: String,
			default: 'arrow-down',
		},
		// 展开状态文本
		expandedText: {
			type: String,
			default: '收起',
		},
		// 收起状态文本
		collapsedText: {
			type: String,
			default: '详情',
		},
		// 是否显示切换按钮文本
		showToggleText: {
			type: Boolean,
			default: false,
		},
	});

	// Emits 定义
	const emit = defineEmits(['update:modelValue', 'toggle']);

	// 内部展开状态
	const isExpanded = ref(props.defaultExpanded);

	// 监听外部传入的 modelValue
	watch(
		() => props.modelValue,
		(newVal) => {
			if (newVal !== undefined) {
				isExpanded.value = newVal;
			}
		},
		{ immediate: true },
	);

	// 监听内部状态变化，同步到外部
	watch(isExpanded, (newVal) => {
		emit('update:modelValue', newVal);
		emit('toggle', newVal);
	});

	// 切换展开状态
	const handleToggle = () => {
		if (props.disabled) return;

		// 如果外部有传入 modelValue，则通过 emit 通知外部更新
		if (props.modelValue !== undefined) {
			emit('update:modelValue', !props.modelValue);
		} else {
			// 否则直接更新内部状态
			isExpanded.value = !isExpanded.value;
		}
	};
</script>

<style lang="scss" scoped>
	.expandable-layout {
		overflow: hidden;
		transition: all 0.3s ease;
		box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
		background:
            /* 上层：高 200px 的渐变 */
			linear-gradient(180deg, rgba(32, 165, 58, 0.2), rgba(32, 165, 58, 0)) 0 0 / 100% 100rpx no-repeat,
			/* 下层：其余区域的底色 */ var(--bg-color);

		&--disabled {
			.expandable-layout__basic {
				cursor: default;
			}

			.expandable-layout__toggle-btn {
				opacity: 0.3;
			}
		}
	}

	// 标题区域样式
	.expandable-layout__header {
		margin-bottom: 28rpx;
	}

	.expandable-layout__header-content {
		display: flex;
		align-items: center;
		justify-content: space-between;
	}

	.expandable-layout__title {
		flex: 1;
		min-width: 0;
	}

	.title-text {
		font-size: 32rpx;
		font-weight: 500;
		color: var(--text-color-primary);
		line-height: 1.4;
	}

	.expandable-layout__actions {
		margin-left: 24rpx;
		display: flex;
		align-items: center;
		gap: 16rpx;
	}

	// 标题区域右侧操作样式
	.expandable-layout__header-action {
		display: flex;
		align-items: center;
		gap: 8rpx;
		padding: 8rpx 16rpx;
		border-radius: 8rpx;
	}

	.action-text {
		font-size: 28rpx;
		color: #999999;
	}

	.action-icon {
		transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
	}

	.expandable-layout__basic {
		display: flex;
		align-items: center;
		justify-content: space-between;
	}

	.expandable-layout__basic-content {
		flex: 1;
		min-width: 0; // 防止内容溢出
	}

	.expandable-layout__toggle-btn {
		margin-left: 16rpx;
		padding: 8rpx 12rpx;
		border-radius: 20rpx;
		transition: all 0.2s ease;
		display: flex;
		align-items: center;
		gap: 8rpx;
	}

	.toggle-icon {
		transition: transform 0.3s ease;
	}

	.toggle-text {
		font-size: 24rpx;
		color: #757575;
		font-weight: 500;
		white-space: nowrap;
	}

	// 详情区域容器 - 负责动画效果
	.expandable-layout__details-container {
		overflow: hidden;
		max-height: 0;
		opacity: 0;
		transition:
			max-height 0.35s cubic-bezier(0.4, 0, 0.2, 1),
			opacity 0.25s cubic-bezier(0.4, 0, 0.2, 1),
			padding 0.35s cubic-bezier(0.4, 0, 0.2, 1);

		&--expanded {
			max-height: 1000rpx; // 足够大的值以容纳内容
			opacity: 1;
			padding-top: 24rpx;
		}
	}

	.expandable-layout__details {
		border-top: 1rpx solid var(--border-color, #f0f0f0);
		background: var(--bg-color, #ffffff);
		transform: translateY(-10rpx);
		transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
	}

	.expandable-layout__details-container--expanded .expandable-layout__details {
		transform: translateY(0);
	}
</style>
