import { ref, reactive } from 'vue'
import { $t } from '@/locale/index.js'

export const controlEnabled = ref(true)

/**
 * @description	公共配置
 */
const publicConfig = {
    axisLine: { lineStyle: { color: '#666' } },
    splitLine: { lineStyle: { color: '#ddd' } },
    axisLabel: { formatter: (value) => formatTime_up(value / 1000) },
    nameTextStyle: { color: '#666', fontSize: 12, align: 'left' },
    series: { type: 'line', smooth: true, symbol: 'circle', showSymbol: false },
    table: [
        { title: 'PID', width: '40px', index: 1 },
        { title: $t('linux.control.processName'), index: 2 },
        { title: $t('linux.control.cpuUsage'), index: 0, unit: '%' },
        { title: $t('linux.control.startUser'), index: 4 },
    ],
}

/**
 * @description 秒数时间戳获取格式化时间
 * @param time 时间戳
 */
const formatTime_up = (timestamp) => {
    const date = new Date(timestamp * 1000)
    const month = date.getMonth() + 1
    const day = date.getDate()
    const hours = date.getHours()
    const minutes = date.getMinutes()
    return `${month.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}\n${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`
}

export const getLoadChartOpts = () => {
    // 当天时间
    const interval = 1000 * 60 * 60 * 24;
    return {
        xAxis: {
            type: 'time',
            boundaryGap: ['1%', '0%'],
            minInterval: interval,
            axisLine: publicConfig.axisLine,
            axisLabel: publicConfig.axisLabel,
        },
        yAxis: {
            scale: true,
            name: $t('linux.control.resourceUsagePercent'),
            min: 0,
            max: function (value) {
                if (value.max >= 100) return Math.ceil(value.max) // 最大值超过100
                if (value.max >= 80) return 100 // 最大值超过80
                return parseInt((value.max + 10).toString().slice(0, 1) + '0') // 小于80取当前最大值的首位数字
            },
            splitLine: { ...publicConfig.splitLine, show: true }, // y轴网格显示
            nameTextStyle: publicConfig.nameTextStyle, // 坐标轴名样式
            axisLine: publicConfig.axisLine,
        },
    }
}

export const getLoadChartData = reactive({
    series: [
        {
            ...publicConfig.series,
            lineStyle: {
                itemStyle: {
                    width: 2,
                    color: 'rgb(255, 140, 0)',
                },
            },
            itemStyle: { color: 'rgb(255, 140, 0)' },
            data: [10, 20, 30, 40, 50, 60, 70, 80, 90, 100],
        },
    ]
})

export const handleControlSwitch = (value) => {
    console.log(value, 'value');
}