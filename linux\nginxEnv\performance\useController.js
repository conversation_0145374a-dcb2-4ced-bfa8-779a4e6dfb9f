import { ref, reactive } from 'vue';
import { getNginxPerformanceConfig, saveNginxPerformanceConfig } from '@/api/nginx';

// 页面引用
export const pageContainer = ref(null);

// 表单数据
export const formData = reactive({
	// 服务配置
	worker_processes: 'auto',
	worker_connections: 51200,
	keepalive_timeout: 60,

	// 性能调整
	gzip: true,
	gzip_min_length: 1,
	gzip_comp_level: 2,
	client_max_body_size: 50,

	// 缓冲区配置
	server_names_hash_bucket_size: 512,
	client_header_buffer_size: 32,
	client_body_buffer_size: 512,
});

// 表单验证错误
export const errors = reactive({});

// 加载状态
export const loading = ref(false);

// 对话框状态
export const showResetDialog = ref(false);

// 验证规则
const validationRules = {
	worker_processes: {
		required: false,
		validator: (value) => {
			if (!value) return true; // 允许为空
			if (value === 'auto') return true;
			const num = parseInt(value);
			if (isNaN(num) || num < 1 || num > 64) {
				return '请输入1-64之间的数字或auto';
			}
			return true;
		},
	},
	worker_connections: {
		required: true,
		validator: (value) => {
			const num = parseInt(value);
			if (isNaN(num) || num < 1024 || num > 65535) {
				return '请输入1024-65535之间的数字';
			}
			return true;
		},
	},
	keepalive_timeout: {
		required: true,
		validator: (value) => {
			const num = parseInt(value);
			if (isNaN(num) || num < 1 || num > 3600) {
				return '请输入1-3600之间的数字';
			}
			return true;
		},
	},
	gzip_min_length: {
		required: false,
		validator: (value) => {
			if (!formData.gzip) return true; // gzip关闭时不验证
			const num = parseInt(value);
			if (isNaN(num) || num < 1 || num > 1024) {
				return '请输入1-1024之间的数字';
			}
			return true;
		},
	},
	gzip_comp_level: {
		required: false,
		validator: (value) => {
			if (!formData.gzip) return true; // gzip关闭时不验证
			const num = parseInt(value);
			if (isNaN(num) || num < 1 || num > 9) {
				return '请输入1-9之间的数字';
			}
			return true;
		},
	},
	client_max_body_size: {
		required: true,
		validator: (value) => {
			const num = parseInt(value);
			if (isNaN(num) || num < 1 || num > 1024) {
				return '请输入1-1024之间的数字';
			}
			return true;
		},
	},
	server_names_hash_bucket_size: {
		required: true,
		validator: (value) => {
			const num = parseInt(value);
			if (isNaN(num) || num < 32 || num > 1024) {
				return '请输入32-1024之间的数字';
			}
			// 检查是否为2的幂
			if ((num & (num - 1)) !== 0) {
				return '请输入2的幂次方数字(如32, 64, 128, 256, 512, 1024)';
			}
			return true;
		},
	},
	client_header_buffer_size: {
		required: true,
		validator: (value) => {
			const num = parseInt(value);
			if (isNaN(num) || num < 1 || num > 64) {
				return '请输入1-64之间的数字';
			}
			return true;
		},
	},
	client_body_buffer_size: {
		required: true,
		validator: (value) => {
			const num = parseInt(value);
			if (isNaN(num) || num < 8 || num > 1024) {
				return '请输入8-1024之间的数字';
			}
			return true;
		},
	},
};

// 字段验证
export const validateField = (fieldName) => {
	const rule = validationRules[fieldName];
	if (!rule) return true;

	const value = formData[fieldName];

	// 必填验证
	if (rule.required && (!value || value === '')) {
		errors[fieldName] = `${getFieldLabel(fieldName)}不能为空`;
		return false;
	}

	// 自定义验证
	if (rule.validator) {
		const result = rule.validator(value);
		if (result !== true) {
			errors[fieldName] = result;
			return false;
		}
	}

	// 验证通过，清除错误
	delete errors[fieldName];
	return true;
};

// 获取字段标签
const getFieldLabel = (fieldName) => {
	const labels = {
		worker_processes: 'Worker进程数',
		worker_connections: 'Worker连接数',
		keepalive_timeout: '保持连接超时',
		gzip_min_length: 'Gzip最小长度',
		gzip_comp_level: 'Gzip压缩率',
		client_max_body_size: '客户端最大请求体',
		server_names_hash_bucket_size: '服务器名称哈希桶大小',
		client_header_buffer_size: '客户端请求头缓冲区',
		client_body_buffer_size: '客户端请求体缓冲区',
	};
	return labels[fieldName] || fieldName;
};

// 验证所有字段
const validateAllFields = () => {
	let isValid = true;
	Object.keys(validationRules).forEach((fieldName) => {
		if (!validateField(fieldName)) {
			isValid = false;
		}
	});
	return isValid;
};

// 开关变化处理
export const handleSwitchChange = (fieldName, value) => {
	formData[fieldName] = value;

	// 如果关闭gzip，清除相关字段的错误
	if (fieldName === 'gzip' && !value) {
		delete errors.gzip_min_length;
		delete errors.gzip_comp_level;
	}
};

// 初始化性能数据
export const initPerformanceData = async () => {
	try {
		loading.value = true;
		// 调用封装的API获取当前配置
		await loadNginxPerformanceConfig();
	} catch (error) {
		console.error('初始化性能数据失败:', error);
		pageContainer.value?.notify?.error('获取配置失败，请重试');
	} finally {
		loading.value = false;
	}
};

// 获取Nginx性能配置
const loadNginxPerformanceConfig = async () => {
	try {
		const response = await getNginxPerformanceConfig();

		// 检查响应是否成功
		if (response.status === false) {
			throw new Error(response.msg || '获取配置失败');
		}

		// API返回的是数组格式，直接处理
		const configArray = Array.isArray(response) ? response : response.data || [];
		const configData = {};

		configArray.forEach((item) => {
			if (item.name === 'gzip') {
				// gzip字段转换为布尔值
				configData[item.name] = item.value === 'on';
			} else if (
				[
					'worker_connections',
					'keepalive_timeout',
					'gzip_min_length',
					'gzip_comp_level',
					'client_max_body_size',
					'server_names_hash_bucket_size',
					'client_header_buffer_size',
					'client_body_buffer_size',
				].includes(item.name)
			) {
				// 数值字段转换为数字
				configData[item.name] = parseInt(item.value);
			} else {
				// 其他字段保持原值
				configData[item.name] = item.value;
			}
		});

		// 更新表单数据
		Object.assign(formData, configData);
		return configData;
	} catch (error) {
		console.error('获取Nginx配置失败:', error);
		throw error;
	}
};

// 保存配置
export const handleSave = async () => {
	try {
		// 验证所有字段
		if (!validateAllFields()) {
			pageContainer.value?.notify?.error('请检查输入内容');
			return;
		}

		loading.value = true;

		// 调用封装的API保存配置
		await submitNginxPerformanceConfig();

		pageContainer.value?.notify?.success('配置保存成功');
	} catch (error) {
		console.error('保存配置失败:', error);
		pageContainer.value?.notify?.error(error.message || '保存失败，请重试');
	} finally {
		loading.value = false;
	}
};

// 保存Nginx性能配置
const submitNginxPerformanceConfig = async () => {
	try {
		// 准备保存的数据，将表单数据转换为API需要的格式
		const saveData = {
			worker_processes: formData.worker_processes,
			worker_connections: formData.worker_connections,
			keepalive_timeout: formData.keepalive_timeout,
			gzip: formData.gzip ? 'on' : 'off', // 布尔值转换为on/off
			gzip_min_length: formData.gzip_min_length,
			gzip_comp_level: formData.gzip_comp_level,
			client_max_body_size: formData.client_max_body_size,
			server_names_hash_bucket_size: formData.server_names_hash_bucket_size,
			client_header_buffer_size: formData.client_header_buffer_size,
			client_body_buffer_size: formData.client_body_buffer_size,
		};

		const response = await saveNginxPerformanceConfig(saveData);

		if (response.status === false) {
			throw new Error(response.msg || '保存配置失败');
		}

		return response;
	} catch (error) {
		console.error('保存Nginx配置失败:', error);
		throw error;
	}
};

// 重置配置到默认值
export const handleReset = () => {
	showResetDialog.value = true;
};

export const confirmReset = async () => {
	try {
		await resetToDefault();
		showResetDialog.value = false;
	} catch (error) {
		showResetDialog.value = false;
		// 错误处理已在 resetToDefault 中完成
	}
};

const resetToDefault = async () => {
	try {
		loading.value = true;

		const defaultConfig = {
			worker_processes: 'auto',
			worker_connections: 51200,
			keepalive_timeout: 60,
			gzip: true,
			gzip_min_length: 1,
			gzip_comp_level: 2,
			client_max_body_size: 50,
			server_names_hash_bucket_size: 512,
			client_header_buffer_size: 32,
			client_body_buffer_size: 512,
		};

		// 更新表单数据
		Object.assign(formData, defaultConfig);

		// 清除所有错误
		Object.keys(errors).forEach((key) => {
			delete errors[key];
		});

		// 自动保存到服务器
		await submitNginxPerformanceConfig();

		pageContainer.value?.notify?.success('已重置为默认配置并保存成功');
	} catch (error) {
		console.error('重置配置失败:', error);
		pageContainer.value?.notify?.error(error.message || '重置配置失败，请重试');
		throw error;
	} finally {
		loading.value = false;
	}
};
