import { ref, onMounted } from 'vue';
import {
	getNginxFirewallGlobalConfig,
	getNginxFirewallSiteConfig,
	setNginxFirewallSiteGlobalConfig,
	setNginxFirewallSiteObjOpen,
} from '@/api/nginx.js';

// 响应式状态变量
export const expandedSite = ref(null);
export const menuOpen = ref(false);
export const isLoading = ref(true);
export const siteStates = ref([]);
export const settings = ref({
	blockMaliciousIp: false,
	blockForeignIp: false,
	blockMaliciousRequests: false,
});

// 页面容器引用
export const pageContainer = ref(null);

// 各配置项的loading状态
export const loadingStates = ref({
	maliciousIp: false,
	foreignIp: false,
	maliciousRequests: false,
});

// 站点功能开关的loading状态
export const featureLoadingStates = ref({});

// 常量数据
export const featureLabels = {
	sql: 'SQL注入',
	xss: 'XSS防御',
	file: '文件上传',
	malicious: '恶意爬虫',
	foreign: '禁国外',
	cdn: 'CDN',
	cc: 'CC防御',
};

// Order of features to display in the quick toggle section
export const quickToggleFeatures = ['sql', 'xss', 'file', 'malicious'];

// 功能名称到API参数的映射
const featureToApiMap = {
	sql: 'sql_injection',
	xss: 'xss_injection',
	file: 'file_upload',
	malicious: 'user-agent',
	foreign: 'drop_abroad',
	cdn: 'cdn',
	cc: 'cc',
};

/**
 * 将接口返回的站点配置数据转换为组件所需的数据结构
 * @param {Array} apiSiteData - 从API获取的站点配置数据数组
 * @returns {Array} 转换后的站点数据数组，符合组件的sites结构
 */
export const transformSiteData = (apiSiteData) => {
	if (!Array.isArray(apiSiteData)) {
		console.warn('Invalid site data: expected array');
		return [];
	}

	return apiSiteData.map((siteConfig) => {
		// 安全获取统计数据的辅助函数
		const getCountFromTotal = (total, index) => {
			return total?.[index]?.value ?? 0;
		};

		// 获取功能开关状态的辅助函数
		const getFeatureEnabled = (feature) => {
			if (typeof feature === 'boolean') {
				return feature;
			}
			return feature?.open ?? false;
		};

		// 构建转换后的站点数据
		return {
			id: siteConfig.siteName || 'Unknown Site',
			status: siteConfig.open ?? false,
			features: {
				sql: {
					enabled: getFeatureEnabled(siteConfig.sql_injection),
					count: getCountFromTotal(siteConfig.total, 0),
					description: '防止SQL注入攻击',
				},
				xss: {
					enabled: getFeatureEnabled(siteConfig.xss_injection),
					count: getCountFromTotal(siteConfig.total, 1),
					description: '防止跨站脚本攻击',
				},
				file: {
					enabled: getFeatureEnabled(siteConfig.file_upload),
					count: getCountFromTotal(siteConfig.total, 6),
					description: '检测恶意文件上传',
				},
				malicious: {
					enabled: getFeatureEnabled(siteConfig['user-agent']),
					count: getCountFromTotal(siteConfig.total, 3),
					description: '识别恶意爬虫行为',
				},
				foreign: {
					enabled: getFeatureEnabled(siteConfig.drop_abroad),
					count: getCountFromTotal(siteConfig.total, 10),
					description: '阻止国外IP访问',
				},
				cdn: {
					enabled: getFeatureEnabled(siteConfig.cdn),
					count: 0, // CDN没有对应的统计数据
					description: '当网站开启CDN时候获取IP的开关',
				},
				cc: {
					enabled: getFeatureEnabled(siteConfig.cc),
					count: getCountFromTotal(siteConfig.total, 2),
					description: 'CC攻击防护',
				},
			},
		};
	});
};

// 业务逻辑函数
export const toggleSiteExpand = (siteId) => {
	if (expandedSite.value === siteId) {
		expandedSite.value = null;
	} else {
		expandedSite.value = siteId;
	}
};

export const toggleSiteStatus = async (siteId) => {
	try {
		const res = await setNginxFirewallSiteObjOpen({ obj: 'open', siteName: siteId });
		if (res.status) {
			pageContainer.value.notify.success(res.msg);
			siteStates.value = siteStates.value.map((site) =>
				site.id === siteId ? { ...site, status: !site.status } : site,
			);
		} else {
			pageContainer.value.notify.error(res.msg);
		}
	} catch (error) {
		console.log(error);
	}
};

export const toggleFeature = async (siteId, feature) => {
	// 生成loading状态的唯一key
	const loadingKey = `${siteId}_${feature}`;

	// 获取当前功能状态
	const currentSite = siteStates.value.find((site) => site.id === siteId);
	if (!currentSite) {
		console.error(`Site not found: ${siteId}`);
		return;
	}

	const currentFeature = currentSite.features[feature];
	if (!currentFeature) {
		console.error(`Feature not found: ${feature} for site ${siteId}`);
		return;
	}

	// 获取API参数名称
	const apiParam = featureToApiMap[feature];
	if (!apiParam) {
		console.error(`API parameter not found for feature: ${feature}`);
		return;
	}

	// 设置loading状态
	featureLoadingStates.value[loadingKey] = true;

	try {
		// 调用API设置功能开关
		const res = await setNginxFirewallSiteObjOpen({
			obj: apiParam,
			siteName: siteId,
		});

		if (res.status) {
			// API调用成功，更新本地状态
			// 使用更强制的方式来确保响应式更新
			const newSiteStates = [...siteStates.value];
			const siteIndex = newSiteStates.findIndex((site) => site.id === siteId);

			if (siteIndex !== -1) {
				// 创建新的 features 对象
				const newFeatures = { ...newSiteStates[siteIndex].features };
				newFeatures[feature] = {
					...newFeatures[feature],
					enabled: !newFeatures[feature].enabled,
				};

				// 创建新的 site 对象
				newSiteStates[siteIndex] = {
					...newSiteStates[siteIndex],
					features: newFeatures,
				};

				// 强制触发响应式更新
				siteStates.value = newSiteStates;
			}

			// 显示成功消息
			if (pageContainer.value?.notify?.success) {
				pageContainer.value.notify.success(res.msg || '设置成功');
			}
		} else {
			// API调用失败，显示错误消息
			if (pageContainer.value?.notify?.error) {
				pageContainer.value.notify.error(res.msg || '设置失败');
			}
		}
	} catch (error) {
		console.error('Toggle feature failed:', error);
		// 发生错误，显示错误消息
		if (pageContainer.value?.notify?.error) {
			pageContainer.value.notify.error('操作失败，请稍后重试');
		}
	} finally {
		// 清除loading状态
		featureLoadingStates.value[loadingKey] = false;
	}
};

export const getFeatureIcon = (feature) => {
	const iconMap = {
		sql: 'file-text',
		xss: 'lock-fill',
		file: 'attach',
		malicious: 'warning-fill',
		foreign: 'map',
		cdn: 'grid',
		cc: 'lock',
	};
	return iconMap[feature] || 'setting';
};

/**
 * 通用处理函数，用于设置Nginx防火墙站点全局配置
 * @param {string} objType - 配置对象类型
 * @param {string} loadingKey - loading状态的key
 * @param {string} settingKey - 设置项的key
 * @param {Object} pageContainer - 页面容器引用，用于显示通知
 * @returns {Promise<void>}
 */
export const handleFirewallConfig = async (objType, loadingKey, settingKey, pageContainer) => {
	// 保存当前状态，以便在请求失败时恢复
	const currentValue = settings.value[settingKey];

	// 设置对应的loading状态为true
	if (loadingKey) {
		loadingStates.value[loadingKey] = true;
	}

	try {
		const res = await setNginxFirewallSiteGlobalConfig({ obj: objType });
		if (res.status) {
			pageContainer.value.notify.success(res.msg);
			// 请求成功时，保持当前UI状态（已在UI中预先更新）
		} else {
			pageContainer.value.notify.error(res.msg);
			// 请求失败时，恢复原来的状态
			settings.value[settingKey] = !currentValue;
		}
	} catch (error) {
		console.log(error);
		pageContainer.value.notify.error('操作失败，请稍后重试');
		// 发生错误时，恢复原来的状态
		settings.value[settingKey] = !currentValue;
	} finally {
		// 无论成功失败，都将loading状态设为false
		if (loadingKey) {
			loadingStates.value[loadingKey] = false;
		}
	}
};

// 使用通用处理函数的特定配置处理函数
export const createHandleBlockMaliciousIp = (pageContainer) => () =>
	handleFirewallConfig('malicious_ip', 'maliciousIp', 'blockMaliciousIp', pageContainer);

export const createHandleBlockForeignIp = (pageContainer) => () =>
	handleFirewallConfig('system_black', 'foreignIp', 'blockForeignIp', pageContainer);

export const createHandleBlockMaliciousRequests = (pageContainer) => () =>
	handleFirewallConfig('btmalibrary', 'maliciousRequests', 'blockMaliciousRequests', pageContainer);

export const handleGlobalConfig = async () => {
	try {
		const res = await getNginxFirewallGlobalConfig();
		settings.value.blockMaliciousIp = res.malicious_ip;
		settings.value.blockForeignIp = res.system_black;
		settings.value.blockMaliciousRequests = res.btmalibrary;
	} catch (error) {
		console.log(error);
	}
};

export const handleSiteConfig = async () => {
	try {
		const res = await getNginxFirewallSiteConfig();

		// 使用转换函数处理接口数据
		const transformedSites = transformSiteData(res);

		// 更新站点状态数据
		if (transformedSites.length > 0) {
			siteStates.value = transformedSites;
		} else {
			console.warn('No valid site data received, using default data');
		}
	} catch (error) {
		console.error('Failed to fetch site config:', error);
		// 发生错误时保持使用默认数据
	} finally {
		isLoading.value = false;
	}
};

// 初始化函数
export const initializeController = () => {
	onMounted(() => {
		handleGlobalConfig();
		handleSiteConfig();
	});
};
