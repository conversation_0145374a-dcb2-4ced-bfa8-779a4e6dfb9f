<template>
  <view :class="['theme-provider', `theme-${currentTheme}`]">
    <slot :theme="currentTheme" :toggle-theme="handleToggleTheme"></slot>
  </view>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, provide } from 'vue'
import { getTheme, toggleTheme, THEME_TYPE } from '@/hooks/useTheme.js'

// 定义组件名称
defineOptions({
  name: 'ThemeProvider'
})

// 定义事件
const emit = defineEmits(['theme-changed'])

// 响应式状态
const currentTheme = ref(THEME_TYPE.LIGHT)

// 主题变化处理函数
const handleThemeChange = (event) => {
  currentTheme.value = event.theme
  
  // 向上传递事件
  emit('theme-changed', event.theme)
}

// 切换主题
const handleToggleTheme = () => {
  const newTheme = toggleTheme()
  currentTheme.value = newTheme
  
  // 向上传递事件
  emit('theme-changed', newTheme)
  
  return newTheme
}

// 更新主题状态
const updateThemeState = () => {
  const currentThemeValue = getTheme()
  currentTheme.value = currentThemeValue
}

// 组件挂载时初始化主题状态并添加监听
onMounted(() => {
  // 获取当前主题
  updateThemeState()
  
  // 监听主题变化
  uni.$on('themeChange', handleThemeChange)
})

// 组件销毁前移除监听
onBeforeUnmount(() => {
  // 解除监听
  uni.$off('themeChange', handleThemeChange)
})

// 提供主题相关的上下文给子组件
provide('themeContext', {
  currentTheme,
  toggleTheme: handleToggleTheme
})
</script>

<style lang="scss">
.theme-provider {
  /* 这个组件本身不提供样式，只提供逻辑 */
}
</style> 