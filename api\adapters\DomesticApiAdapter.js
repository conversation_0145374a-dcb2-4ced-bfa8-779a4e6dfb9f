import ApiAdapter from './ApiAdapter';

/**
 * 国内API适配器
 * 处理国内API的响应格式和错误
 */
export default class DomesticApiAdapter extends ApiAdapter {
  /**
   * 转换响应数据
   * 国内API响应格式已经是标准的，直接返回
   * @param {Object} response 原始响应数据
   * @returns {Object} 转换后的响应数据
   */
  transformResponse(response) {
    // 国内API响应格式假设为 { status: true/false, data: {}, msg: '' }
    return response;
  }

  /**
   * 处理错误
   * @param {Object} error 原始错误对象
   * @returns {Object} 处理后的错误对象
   */
  handleError(error) {
    return {
      status: false,
      msg: error.errMsg || '请求失败',
      error: error,
    };
  }
}
