import { ref } from 'vue';
import { isEmptyString } from '@/utils/check'
import { getDomainList as getDomainList<PERSON>pi, deleteDomain, getSSLInfo, setSSLInfo, closeSSL, setSiteHttps, closeSiteHttps, getCertList, deployCert, deleteCert } from '@/api/site';
import { $t } from '@/locale/index.js';

const DEFAULT_API_TYPE = import.meta.env.VITE_PANEL_OWNER || 'domestic';
export const currentItem = ref({})
export const currentTab = ref(0)
export const pageContainer = ref(null)
export const sslFolderPageContainer = ref(null)
export const domainPaging = ref(null)
export const domainLoading = ref(false)
export const currentDomain = ref({})
export const isShowDeleteDomain = ref(false)
export const switchHttps = ref(false);
export const infoKey = ref('');
export const infoPem = ref('');
export const cert_data = ref({
    deployment: $t('website.manage.certificateDetails.deploymentStatus')
})
export const sslPaging = ref(null)
export const isShowDeleteCert = ref(false)
export const currentCert = ref({})

export const handleUrlTitle = (url) => {
    if (!url) return '';
    return url.length > 20 ? url.substring(0, 20) + '...' : url;
};

export const getDomainList = async () => {
    const res = await getDomainListApi({
        table: 'domain',
        list: 'True',
        search: currentItem.value.id
    })
    return res
}

export const handleDeleteDomain = async (item) => {
    currentDomain.value = item
    isShowDeleteDomain.value = true
}

export const confirmDeleteDomain = async (close) => {
    domainLoading.value = true
    try {
        const res = await deleteDomain({
            id: currentItem.value.id,
            webname: currentItem.value.name,
            domain: currentDomain.value.name,
            port: currentDomain.value.port,
        })
        if (res.status) {
            close && close()
            domainPaging.value.reload()
            pageContainer.value.notify.success(res.msg)
        } else {
            pageContainer.value.notify.error(res.msg)
        }
    } catch (e) {
        console.log(e);
    } finally {
        domainLoading.value = false
    }
}

export const hanleSSLInfo = async () => {
    const res = await getSSLInfo({
        siteName: currentItem.value.name,
    })
    infoKey.value = res.key || ''
    infoPem.value = res.csr || ''
    if (res.status) {
        switchHttps.value = res.httpTohttps
        cert_data.value = res.cert_data
        cert_data.value.status = res.cert_data.issuer == "Let's Encrypt" ? $t('website.manage.certificateDetails.autoRenewal') :
            $t('website.manage.certificateDetails.renewalReminder')
        cert_data.value.deployment = $t('website.manage.certificateDetails.deployed')
    } else {
        cert_data.value = {}
        switchHttps.value = false
    }

}

export const saveDeployment = async () => {
    if (isEmptyString(infoKey.value)) {
        pageContainer.value.notify.error($t('website.errors.keyRequired'))
        return
    }
    if (isEmptyString(infoPem.value)) {
        pageContainer.value.notify.error($t('website.errors.certificateRequired'))
        return
    }
    uni.showLoading({
        title: $t('website.manage.certificateSaving')
    })
    try {
        const res = await setSSLInfo({
            type: 1,
            siteName: currentItem.value.name,
            key: infoKey.value,
            csr: infoPem.value
        })
        if (res.status) {
            pageContainer.value.notify.success(res.msg)
            hanleSSLInfo()
        } else {
            pageContainer.value.notify.error(res.msg)
        }
    } catch (e) {
        console.log(e);
    } finally {
        uni.hideLoading()
    }
}

export const closeOperation = async () => {
    uni.showLoading({
        title: $t('website.manage.closingCertificate')
    })
    try {
        const res = await closeSSL({
            updateOf: 1,
            siteName: currentItem.value.name
        })
        if (res.status) {
            pageContainer.value.notify.success($t('website.manage.closeSSLSuccess'))
            hanleSSLInfo()
        } else {
            pageContainer.value.notify.error((typeof res.msg) == "string" ? res.msg : res.msg[0])
        }
    } catch (e) {
        console.log(e);
    } finally {
        uni.hideLoading()
    }
}

export const handleHttps = async (value) => {
    const reqFn = value ? setSiteHttps : closeSiteHttps
    const res = await reqFn({
        siteName: currentItem.value.name
    })
    if (res.status) {
        switchHttps.value = value
        pageContainer.value.notify.success(res.msg)
    } else {
        pageContainer.value.notify.error(res.msg)
    }
}

export const skipSSLFolder = () => {
    uni.navigateTo({ url: '/linux/website/manage/sslFolder' })
}

export const handleCertList = async () => {
    const res = await getCertList({
        force_refresh: 1,
        search_limit: 0,
        search_name: '',
    })
    return res
}

export const handleDeployCert = async (item) => {
    uni.showLoading({
        title: $t('website.manage.sslFolder.deploying')
    })
    try {
        let params
        if (DEFAULT_API_TYPE === 'domestic') {
            params = {
                BatchInfo: JSON.stringify([
                    {
                        siteName: currentItem.value.name,
                        certName: item.subject,
                        ssl_hash: item.hash
                    }
                ])
            }
        } else {
            params = {
                siteName: currentItem.value.name,
                certName: item.subject
            }
        }

        const res = await deployCert(params)
        if (res.status) {
            sslFolderPageContainer.value.notify.success(res.msg)
            hanleSSLInfo()
            setTimeout(() => {
                uni.navigateBack()
            }, 1000)
        } else {
            sslFolderPageContainer.value.notify.error(res.msg)
        }
    } catch (e) {
        console.log(e);
    } finally {
        uni.hideLoading()
    }
}

export const handleDeleteCert = async (item) => {
    currentCert.value = item
    isShowDeleteCert.value = true
}

export const confirmDeleteCert = async (close) => {
    uni.showLoading({
        title: $t('website.manage.sslFolder.deleting')
    })
    try {
        const res = await deleteCert({
            certName: currentCert.value.subject,
            local: currentCert.value.cloud_id > 0 ? 0 : 1,
            ssl_hash: currentCert.value.hash,
        })
        if (res.status) {
            sslFolderPageContainer.value.notify.success(res.msg)
            sslPaging.value.reload()
            close && close()
        } else {
            sslFolderPageContainer.value.notify.error(res.msg)
        }
    } catch (e) {
        console.log(e);
    } finally {
        uni.hideLoading()
    }
}
