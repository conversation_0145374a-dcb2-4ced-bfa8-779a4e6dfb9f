<template>
  <page-container ref="pageContainer" :title="`${fileItem.title}${isUnsaved ? ' ('+$t('editor.unsaved')+')' : ''} `">
    <template #nav-right>
      <view class="nav-right-btn">
        <uni-icons @click="onSave" fontFamily="iconfont" class="icon-save mr-20" color="var(--text-color-secondary)" size="20"></uni-icons>
      </view>
    </template>
    <view class="editor-content">
      <AceEditor
        v-model="content"
        :height="editorHeight"
        :lang="lang"
        :theme="theme"
        :font-size="fontSize"
        :options="editorOptions"
        @change="onContentChange"
        @update:font-size="onFontSizeChange"
        @update:theme="onThemeChange"
        @update:lang="onLangChange"
      />
    </view>
    <CustomDialog
      contentHeight="120rpx"
      v-model="showSaveDialog"
      :z-index="9999999"
      :title="$t('common.tip')"
      :confirmText="$t('editor.save')"
      :cancelText="$t('editor.dontSave')"
      @confirm="onSave"
      @cancel="onCancel"
    >
      <view class="py-40 text-secondary flex justify-center">
        <text>{{ $t('editor.fileModified') }}</text>
      </view>
    </CustomDialog>
  </page-container>
</template>

<script setup>
  import { computed, ref } from 'vue';
  import { onLoad, onUnload, onBackPress } from '@dcloudio/uni-app';
  import CustomDialog from '@/components/CustomDialog/index.vue';
  import PageContainer from '@/components/PageContainer/index.vue';
  import AceEditor from '@/components/AceEditor/index.vue';
  import { $t } from '@/locale/index.js';
  import {
    content,
    editorHeight,
    lang,
    theme,
    fontSize,
    readonly,
    initEditorHeight,
    fileItem,
    initEditorOptions,
    getContent,
    pageContainer,
    isUnsaved,
    useSaveFiles,
  } from './useController';

  const editorOptions = computed(() => ({
    tabSize: 2,
    wrap: false,
  }));

  onLoad((options) => {
    fileItem.value = JSON.parse(options.fileItem);
    initEditorHeight();
    initEditorOptions();
  });

  const showSaveDialog = ref(false);

  const onContentChange = (value) => {
    if (value !== content.value) {
      isUnsaved.value = true;
      fileItem.value.data = value;
    } else {
      isUnsaved.value = false;
    }
  };

  const onFontSizeChange = (value) => {
    console.log(value, 'value');
  };

  const onThemeChange = (value) => {
    console.log(value, 'value');
  };

  const onLangChange = (value) => {
    console.log(value, 'value');
  };

  const onSave = async () => {
    await useSaveFiles({
      path: fileItem.value.path,
      data: fileItem.value.data,
      st_mtime: fileItem.value.stTime,
      encoding: fileItem.value.encoding,
    });
    showSaveDialog.value = false;
  };

  const confirmNavBack = ref(false);

  const onCancel = () => {
    confirmNavBack.value = true;
    isUnsaved.value = false;
    uni.navigateBack();
  };

  onBackPress(({ from }) => {
    if (from === 'navigateBack' && confirmNavBack.value) {
      return false;
    }
    if (isUnsaved.value) {
      showSaveDialog.value = true;
      return true;
    }
    return false;
  });

  onUnload(() => {
    showSaveDialog.value = false;
  });
</script>
