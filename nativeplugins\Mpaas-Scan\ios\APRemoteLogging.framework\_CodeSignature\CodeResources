<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>APRemoteLogging.bundle/baseline.json</key>
		<data>
		mKILCpuOT8RycIKWwxgz5nKUd7Y=
		</data>
		<key>Headers/APCrashLogParamHelper.h</key>
		<data>
		ShDtD+w/CSN4mV2g/BKlI/c5/Hw=
		</data>
		<key>Headers/APLogAddions.h</key>
		<data>
		AGuDWgwTnMF+CWgLpAeLL4oMGdg=
		</data>
		<key>Headers/APLogSpmUtils.h</key>
		<data>
		SqC4K7+gRrl9E0/RmjQMoCyVGw0=
		</data>
		<key>Headers/APMonitorPointDataDefines.h</key>
		<data>
		Cdn5cHCVeKK19lV5nzduUy10kEY=
		</data>
		<key>Headers/APMonitorPointManager.h</key>
		<data>
		QcKlCCpdt+CWwEs9zOzSV79FaIY=
		</data>
		<key>Headers/APRemoteLogger+AntLog.h</key>
		<data>
		G2U5R98rZQCETdIpl3OkAVDFQUw=
		</data>
		<key>Headers/APRemoteLogger+Internal.h</key>
		<data>
		7z/q1Wj4/XsSv5MFYDKjiWdqjUE=
		</data>
		<key>Headers/APRemoteLogger.h</key>
		<data>
		5M6DUOD1G/zulzTRfmRh+mHO2SY=
		</data>
		<key>Headers/APRemoteLogging.h</key>
		<data>
		eydCAonxKMlX4ojLoeW0hnuSgks=
		</data>
		<key>Headers/APRemoteMarco.h</key>
		<data>
		jgtnkQW+embj306KkREh+WYcGh4=
		</data>
		<key>Headers/ATActionMgr.h</key>
		<data>
		kx9ZyLDS/atD1qVkgjYuasIkqkQ=
		</data>
		<key>Headers/ATAppendAction.h</key>
		<data>
		R/hXio7peVwy3ZEKdFFVmyOf5QY=
		</data>
		<key>Headers/ATAppender.h</key>
		<data>
		aMy94ZMlPw0TAj6d9sq65ET9wuk=
		</data>
		<key>Headers/ATAppenderMgr.h</key>
		<data>
		S5KdTFr1X+zTHJnc6jx5pXXhByg=
		</data>
		<key>Headers/ATBehaviorLayout.h</key>
		<data>
		2OY5S0L+eOoDivZJlcsGdAXbAkk=
		</data>
		<key>Headers/ATBehaviorLogger.h</key>
		<data>
		eL/37w7mzdh85o+BqPGmO8UR20s=
		</data>
		<key>Headers/ATConfigMgr.h</key>
		<data>
		xwx/evZCKOk1tQsHB1Ox0z9NBLs=
		</data>
		<key>Headers/ATContext.h</key>
		<data>
		7zEOHyfwJ04Shp3AetAT8ax0sWU=
		</data>
		<key>Headers/ATCrash.h</key>
		<data>
		wEdupoW3LLfS0xx6ODV1RyNmhVw=
		</data>
		<key>Headers/ATCrashLayout.h</key>
		<data>
		N7aLLSpubBUsxAnRQ3u6SX8+stk=
		</data>
		<key>Headers/ATCrashLogger.h</key>
		<data>
		szSbmgaojnEqxWaZ4rrrV5ZGukY=
		</data>
		<key>Headers/ATDataFlow.h</key>
		<data>
		rEajPF98cWGxY4SxcN/rL2iVSOc=
		</data>
		<key>Headers/ATDataFlowLayout.h</key>
		<data>
		ol5svCz9CQK2hKk3f2ohAcOGq6g=
		</data>
		<key>Headers/ATDataFlowLogger.h</key>
		<data>
		uV1U7LRh59MgpdmEBQ1ZdY8dvMU=
		</data>
		<key>Headers/ATEvent.h</key>
		<data>
		8NR/VDuIH1BG6ma5n0qndBRo3ko=
		</data>
		<key>Headers/ATEventLogger.h</key>
		<data>
		ZLMnlOptWaMCyG6Lw+s8xRDTfYc=
		</data>
		<key>Headers/ATHTTPUploader.h</key>
		<data>
		rVwJccDuIyi2c2GFOMnklSXS8/4=
		</data>
		<key>Headers/ATLayout.h</key>
		<data>
		pVGhtsAM2Pi29xhyOmT7o0ua7Mg=
		</data>
		<key>Headers/ATLinkLogger.h</key>
		<data>
		TRD79PVNhJpLv2JvXBz3F2CQkds=
		</data>
		<key>Headers/ATLogger.h</key>
		<data>
		A5UhKG95+3tRCXK/4TxLfPIZgsM=
		</data>
		<key>Headers/ATMessage.h</key>
		<data>
		zRj/3s1e9imokXBn34oWSNGGr6Q=
		</data>
		<key>Headers/ATMigrator.h</key>
		<data>
		EKdiZqURwDPAwpxJ7MEUIx5zR7Q=
		</data>
		<key>Headers/ATMonitor.h</key>
		<data>
		UKvoRYtGVO5xUwkay40Ka0e4JY0=
		</data>
		<key>Headers/ATMonitorLayout.h</key>
		<data>
		fa3TezbJ4Slm+1t4SWbcRbacZ6s=
		</data>
		<key>Headers/ATMonitorLogger.h</key>
		<data>
		gt/vYeiMO6LGSkm/yUJ7ZnxRaRQ=
		</data>
		<key>Headers/ATNetEnvUtil.h</key>
		<data>
		wLvVUjuXOnWTVPxHR+uN9SWZ86k=
		</data>
		<key>Headers/ATParameters.h</key>
		<data>
		Wfij7pdVIp4rdZkTxXyzga49ZG0=
		</data>
		<key>Headers/ATPerformanceLayout.h</key>
		<data>
		5EnbzKeomEOkNuclNRrbr8O+rGc=
		</data>
		<key>Headers/ATPerformanceLogger.h</key>
		<data>
		m8EUl69bU9IUzPhNcr2SD6U08j0=
		</data>
		<key>Headers/ATUploadAction.h</key>
		<data>
		POCfVllConRKnaxAOv8jxFcvY7A=
		</data>
		<key>Headers/ATUploader.h</key>
		<data>
		pNl8meNwcaiwBX3dmy/uHEgR+m4=
		</data>
		<key>Headers/AntBehavior.h</key>
		<data>
		g9pPA9hXyz8k0bzkefN1pZR3yVs=
		</data>
		<key>Headers/AntDAU.h</key>
		<data>
		7JlesB4xQl4HPWEpDun4ZSWAMP8=
		</data>
		<key>Headers/AntEvent+Private.h</key>
		<data>
		tq2h6doMcx3rLZ9DMtSrGy4XGwI=
		</data>
		<key>Headers/AntEvent.h</key>
		<data>
		OMaBsw4BJzn1h4hgahmKXE42TNc=
		</data>
		<key>Headers/AntLogCrypt.h</key>
		<data>
		p9xXlrl7QCmKEVww3abubJpSM4Q=
		</data>
		<key>Headers/AntLogInterceptor.h</key>
		<data>
		JFmmBOdZZTfrbIBavWHLuB5QXvA=
		</data>
		<key>Headers/AntLogInterface.h</key>
		<data>
		VnT1BlS5ggqYZWUsJ7tsH8CPT70=
		</data>
		<key>Headers/AntLogLevel.h</key>
		<data>
		nkwkYaVffPwIOem84y/2q7lVTig=
		</data>
		<key>Headers/AntLogPreference.h</key>
		<data>
		a184UESOPb5eMv9g+53IlSqZjT8=
		</data>
		<key>Headers/AntLogSampleCenter.h</key>
		<data>
		RN80xxSS3DcowomYVw5Ak4CV9Aw=
		</data>
		<key>Headers/AntLogSelfMonitor.h</key>
		<data>
		Bw2C/O3qEhwTjwK9bkI9VzWmEao=
		</data>
		<key>Headers/AntLogUtils.h</key>
		<data>
		XK6uO0Wpfn3i6ysASp9C+zcsaxY=
		</data>
		<key>Headers/AntPerformance.h</key>
		<data>
		rBEclJbg1eW8WRY8/1g70fJjB04=
		</data>
		<key>Headers/AntRealtimeLogItem.h</key>
		<data>
		CLjg1DwRSVHK5ujm6Qgm3H55Dq0=
		</data>
		<key>Headers/AntRealtimeLogUploader.h</key>
		<data>
		o9lk8DSqiUhGfpZy8wEo8t0Dj+Y=
		</data>
		<key>Headers/ISampleControl.h</key>
		<data>
		Dq5UDFWh6kkfP0kAp3JrAKrK/uo=
		</data>
		<key>Headers/MPLogCryptDelegate.h</key>
		<data>
		yf3oLOZkQVuldPdtBxbBcYdKCL4=
		</data>
		<key>Headers/MergerExposureLogManage.h</key>
		<data>
		R09KpylZOL78VGoZL244hA5fLMM=
		</data>
		<key>Headers/SPMTrackerInfo.h</key>
		<data>
		+kCmZdVmbUM5zpyoy0b+tSS3rKA=
		</data>
		<key>Headers/SPMTrackerLog.h</key>
		<data>
		59rPTWFTIqlUqap0rV6sEZSESsk=
		</data>
		<key>Info.plist</key>
		<data>
		hxUGRw0F7V0CLkeJgKK/hZS8KNk=
		</data>
		<key>en.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			zmV6UqBSo6r1NOz798vd5O4zTBA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
	</dict>
	<key>files2</key>
	<dict>
		<key>APRemoteLogging.bundle/baseline.json</key>
		<dict>
			<key>hash</key>
			<data>
			mKILCpuOT8RycIKWwxgz5nKUd7Y=
			</data>
			<key>hash2</key>
			<data>
			VImaKLODO8WItMVdcIMF/pSbE94h6zDX5+mjOMqDexs=
			</data>
		</dict>
		<key>Headers/APCrashLogParamHelper.h</key>
		<dict>
			<key>hash</key>
			<data>
			ShDtD+w/CSN4mV2g/BKlI/c5/Hw=
			</data>
			<key>hash2</key>
			<data>
			JWYkofnVCyk7Nt+QVhJrjWLydiSe9qmFWoo5+qlXY70=
			</data>
		</dict>
		<key>Headers/APLogAddions.h</key>
		<dict>
			<key>hash</key>
			<data>
			AGuDWgwTnMF+CWgLpAeLL4oMGdg=
			</data>
			<key>hash2</key>
			<data>
			jb9hD+dI7Ds/sZkZyzZVPNW9zaDzyJmnHlChDzb/emg=
			</data>
		</dict>
		<key>Headers/APLogSpmUtils.h</key>
		<dict>
			<key>hash</key>
			<data>
			SqC4K7+gRrl9E0/RmjQMoCyVGw0=
			</data>
			<key>hash2</key>
			<data>
			JtAk9bWtdlIhGUqRJpk5QI0HFU/DS/oEjV75zSMwTh0=
			</data>
		</dict>
		<key>Headers/APMonitorPointDataDefines.h</key>
		<dict>
			<key>hash</key>
			<data>
			Cdn5cHCVeKK19lV5nzduUy10kEY=
			</data>
			<key>hash2</key>
			<data>
			FyhW7WFUsakR+bptX23Q5KlUdBHhMdsL1HbW+WKBVyY=
			</data>
		</dict>
		<key>Headers/APMonitorPointManager.h</key>
		<dict>
			<key>hash</key>
			<data>
			QcKlCCpdt+CWwEs9zOzSV79FaIY=
			</data>
			<key>hash2</key>
			<data>
			I+LUngou5qFEHI78So+0oP8/KraeRw+mLWIHjbtMYDY=
			</data>
		</dict>
		<key>Headers/APRemoteLogger+AntLog.h</key>
		<dict>
			<key>hash</key>
			<data>
			G2U5R98rZQCETdIpl3OkAVDFQUw=
			</data>
			<key>hash2</key>
			<data>
			s3DWq8K5NOhEhPP120L1P1OFimEI9oQJMHIEiWZBLDQ=
			</data>
		</dict>
		<key>Headers/APRemoteLogger+Internal.h</key>
		<dict>
			<key>hash</key>
			<data>
			7z/q1Wj4/XsSv5MFYDKjiWdqjUE=
			</data>
			<key>hash2</key>
			<data>
			FCyu3TbG6Qc+ZRVFgwGeAc77LS+T5a/qSLqwUly4uFg=
			</data>
		</dict>
		<key>Headers/APRemoteLogger.h</key>
		<dict>
			<key>hash</key>
			<data>
			5M6DUOD1G/zulzTRfmRh+mHO2SY=
			</data>
			<key>hash2</key>
			<data>
			WB0EvS2EjUx7rbRXJ9d8OOeRNW7PWmvKid84i+RfKw0=
			</data>
		</dict>
		<key>Headers/APRemoteLogging.h</key>
		<dict>
			<key>hash</key>
			<data>
			eydCAonxKMlX4ojLoeW0hnuSgks=
			</data>
			<key>hash2</key>
			<data>
			SIpcHJ6vNjyyb9Cm6XyHwaUXF91imzOqs2JPVea5dvk=
			</data>
		</dict>
		<key>Headers/APRemoteMarco.h</key>
		<dict>
			<key>hash</key>
			<data>
			jgtnkQW+embj306KkREh+WYcGh4=
			</data>
			<key>hash2</key>
			<data>
			S1UthPUpDEFiWKb7hvKyZ2ghyvpEVIH2BTy0PZVlKSE=
			</data>
		</dict>
		<key>Headers/ATActionMgr.h</key>
		<dict>
			<key>hash</key>
			<data>
			kx9ZyLDS/atD1qVkgjYuasIkqkQ=
			</data>
			<key>hash2</key>
			<data>
			MO0QwsCWYYQcTiRrfEG6/F8Xrq4jlePIb3EI8B4vF3w=
			</data>
		</dict>
		<key>Headers/ATAppendAction.h</key>
		<dict>
			<key>hash</key>
			<data>
			R/hXio7peVwy3ZEKdFFVmyOf5QY=
			</data>
			<key>hash2</key>
			<data>
			rEAPF22dVzut2EoRihTJTz23QVNCie8KSofZbPF/lhE=
			</data>
		</dict>
		<key>Headers/ATAppender.h</key>
		<dict>
			<key>hash</key>
			<data>
			aMy94ZMlPw0TAj6d9sq65ET9wuk=
			</data>
			<key>hash2</key>
			<data>
			+vH+XHiae6VGzevgIVIIapFkguQVDQxqlvFrEEtsWD4=
			</data>
		</dict>
		<key>Headers/ATAppenderMgr.h</key>
		<dict>
			<key>hash</key>
			<data>
			S5KdTFr1X+zTHJnc6jx5pXXhByg=
			</data>
			<key>hash2</key>
			<data>
			NcV97QWWY2Neb6tp/LgJHRv6lToWq4WFSPqotW0pRXk=
			</data>
		</dict>
		<key>Headers/ATBehaviorLayout.h</key>
		<dict>
			<key>hash</key>
			<data>
			2OY5S0L+eOoDivZJlcsGdAXbAkk=
			</data>
			<key>hash2</key>
			<data>
			2aBdQn5AakULQ30YKsSrij8s9wbjHXlp50o1OTnhIp4=
			</data>
		</dict>
		<key>Headers/ATBehaviorLogger.h</key>
		<dict>
			<key>hash</key>
			<data>
			eL/37w7mzdh85o+BqPGmO8UR20s=
			</data>
			<key>hash2</key>
			<data>
			gNFXzjwvatoy+RTbK1btqP0pQTjQwkNVbstdyJmuhOY=
			</data>
		</dict>
		<key>Headers/ATConfigMgr.h</key>
		<dict>
			<key>hash</key>
			<data>
			xwx/evZCKOk1tQsHB1Ox0z9NBLs=
			</data>
			<key>hash2</key>
			<data>
			4BIXnzz0SG6MY7NCS2ELhReBmL5TRlTaZNcgOwM8Mgc=
			</data>
		</dict>
		<key>Headers/ATContext.h</key>
		<dict>
			<key>hash</key>
			<data>
			7zEOHyfwJ04Shp3AetAT8ax0sWU=
			</data>
			<key>hash2</key>
			<data>
			5/obzwBtyYVwuzHeg+Wr5P4+3nKt1z1zA1Qi5+GEpb0=
			</data>
		</dict>
		<key>Headers/ATCrash.h</key>
		<dict>
			<key>hash</key>
			<data>
			wEdupoW3LLfS0xx6ODV1RyNmhVw=
			</data>
			<key>hash2</key>
			<data>
			ZB5LqogYk0CMkUGzF5j1MoRAgC90+H2KdOnBYV1y080=
			</data>
		</dict>
		<key>Headers/ATCrashLayout.h</key>
		<dict>
			<key>hash</key>
			<data>
			N7aLLSpubBUsxAnRQ3u6SX8+stk=
			</data>
			<key>hash2</key>
			<data>
			wmxa1dsPCJoX6ph9cPtzS+hU0qcjv7PcTdaciQjgUCE=
			</data>
		</dict>
		<key>Headers/ATCrashLogger.h</key>
		<dict>
			<key>hash</key>
			<data>
			szSbmgaojnEqxWaZ4rrrV5ZGukY=
			</data>
			<key>hash2</key>
			<data>
			X3R4MhjfabPWbC0xGLw1Ml/+x0NE70Ip+n+8uTWP2cM=
			</data>
		</dict>
		<key>Headers/ATDataFlow.h</key>
		<dict>
			<key>hash</key>
			<data>
			rEajPF98cWGxY4SxcN/rL2iVSOc=
			</data>
			<key>hash2</key>
			<data>
			rmIQLK8a2mfZkgipAL3dvKXcRbryIY0d5u42caggj5g=
			</data>
		</dict>
		<key>Headers/ATDataFlowLayout.h</key>
		<dict>
			<key>hash</key>
			<data>
			ol5svCz9CQK2hKk3f2ohAcOGq6g=
			</data>
			<key>hash2</key>
			<data>
			aFN5vqs7AWwO4Nfj/+zgRYS/3Zw04pBwQypuv8Ig9Zc=
			</data>
		</dict>
		<key>Headers/ATDataFlowLogger.h</key>
		<dict>
			<key>hash</key>
			<data>
			uV1U7LRh59MgpdmEBQ1ZdY8dvMU=
			</data>
			<key>hash2</key>
			<data>
			3aNX14JLuUp0LaVPTaa47uSxUB8+WL996ZYzt0bizT8=
			</data>
		</dict>
		<key>Headers/ATEvent.h</key>
		<dict>
			<key>hash</key>
			<data>
			8NR/VDuIH1BG6ma5n0qndBRo3ko=
			</data>
			<key>hash2</key>
			<data>
			DDLVuU75OmaBuj5xshWTPej8ew3G6YIzGdy6bvH6T7c=
			</data>
		</dict>
		<key>Headers/ATEventLogger.h</key>
		<dict>
			<key>hash</key>
			<data>
			ZLMnlOptWaMCyG6Lw+s8xRDTfYc=
			</data>
			<key>hash2</key>
			<data>
			PslGZ3Mfr1fJumP/ZgNDY10Eehut4FVC46wOdR+iHpQ=
			</data>
		</dict>
		<key>Headers/ATHTTPUploader.h</key>
		<dict>
			<key>hash</key>
			<data>
			rVwJccDuIyi2c2GFOMnklSXS8/4=
			</data>
			<key>hash2</key>
			<data>
			LqZFtY18Ine/T2lhN+2QZHlf5/zF09gjGLf6HyAu3xs=
			</data>
		</dict>
		<key>Headers/ATLayout.h</key>
		<dict>
			<key>hash</key>
			<data>
			pVGhtsAM2Pi29xhyOmT7o0ua7Mg=
			</data>
			<key>hash2</key>
			<data>
			nDJS+6IZrtxVZ8epPJ51/979/XfDhWc5CTK9Wx1/K48=
			</data>
		</dict>
		<key>Headers/ATLinkLogger.h</key>
		<dict>
			<key>hash</key>
			<data>
			TRD79PVNhJpLv2JvXBz3F2CQkds=
			</data>
			<key>hash2</key>
			<data>
			miTMMpoOB4PeY9XktMs48igTHPhhr5lEjfHPRSmI3Is=
			</data>
		</dict>
		<key>Headers/ATLogger.h</key>
		<dict>
			<key>hash</key>
			<data>
			A5UhKG95+3tRCXK/4TxLfPIZgsM=
			</data>
			<key>hash2</key>
			<data>
			geCsNoBQ63irmQSzJUYmxvcskByOqG46IDrfje9IR7o=
			</data>
		</dict>
		<key>Headers/ATMessage.h</key>
		<dict>
			<key>hash</key>
			<data>
			zRj/3s1e9imokXBn34oWSNGGr6Q=
			</data>
			<key>hash2</key>
			<data>
			gC9s9YOicHJDq+ng/PHi6AxV1LABt+jZBsW0grhrv30=
			</data>
		</dict>
		<key>Headers/ATMigrator.h</key>
		<dict>
			<key>hash</key>
			<data>
			EKdiZqURwDPAwpxJ7MEUIx5zR7Q=
			</data>
			<key>hash2</key>
			<data>
			8Kk11O8DkJra1YkA6qL0Os4aEtX8GwwREF+j6VfsbH0=
			</data>
		</dict>
		<key>Headers/ATMonitor.h</key>
		<dict>
			<key>hash</key>
			<data>
			UKvoRYtGVO5xUwkay40Ka0e4JY0=
			</data>
			<key>hash2</key>
			<data>
			jOnV6JR3dXfJMSr4iU3dfIBTGMIR2I0aj0mnURwwyQI=
			</data>
		</dict>
		<key>Headers/ATMonitorLayout.h</key>
		<dict>
			<key>hash</key>
			<data>
			fa3TezbJ4Slm+1t4SWbcRbacZ6s=
			</data>
			<key>hash2</key>
			<data>
			ryhdGIHdnybVmYIorCuHg3MUvO0mstvwaxDLDYcBjTY=
			</data>
		</dict>
		<key>Headers/ATMonitorLogger.h</key>
		<dict>
			<key>hash</key>
			<data>
			gt/vYeiMO6LGSkm/yUJ7ZnxRaRQ=
			</data>
			<key>hash2</key>
			<data>
			r7Bv/fd85feAwyX+9ndHpaqh8/6uWi7tBRDDpwZ7mtA=
			</data>
		</dict>
		<key>Headers/ATNetEnvUtil.h</key>
		<dict>
			<key>hash</key>
			<data>
			wLvVUjuXOnWTVPxHR+uN9SWZ86k=
			</data>
			<key>hash2</key>
			<data>
			Nk8YABzvJPDltfpOH+Q3h6ElilvKhvw5jsZ+B8RNBnI=
			</data>
		</dict>
		<key>Headers/ATParameters.h</key>
		<dict>
			<key>hash</key>
			<data>
			Wfij7pdVIp4rdZkTxXyzga49ZG0=
			</data>
			<key>hash2</key>
			<data>
			j/azxvpburQq7f2uSScFYh81mmwiup6EQKiLBD3XWc0=
			</data>
		</dict>
		<key>Headers/ATPerformanceLayout.h</key>
		<dict>
			<key>hash</key>
			<data>
			5EnbzKeomEOkNuclNRrbr8O+rGc=
			</data>
			<key>hash2</key>
			<data>
			8WA9K7vI1lzWSA8pyU2l68U2F/0zQt+7xT/wmnYi5yw=
			</data>
		</dict>
		<key>Headers/ATPerformanceLogger.h</key>
		<dict>
			<key>hash</key>
			<data>
			m8EUl69bU9IUzPhNcr2SD6U08j0=
			</data>
			<key>hash2</key>
			<data>
			0kotGtouqUnJ/hxcVDcaDnVk9L022sPMMHabTvld5MA=
			</data>
		</dict>
		<key>Headers/ATUploadAction.h</key>
		<dict>
			<key>hash</key>
			<data>
			POCfVllConRKnaxAOv8jxFcvY7A=
			</data>
			<key>hash2</key>
			<data>
			UDgdoh7VT0/777VeBWufgxu+qqqkvNnOgDQTKK+xBrw=
			</data>
		</dict>
		<key>Headers/ATUploader.h</key>
		<dict>
			<key>hash</key>
			<data>
			pNl8meNwcaiwBX3dmy/uHEgR+m4=
			</data>
			<key>hash2</key>
			<data>
			jP/xWMKVLUyNZOi8v/tyLBjBwuiT4DLdQ3eYbpvxBPo=
			</data>
		</dict>
		<key>Headers/AntBehavior.h</key>
		<dict>
			<key>hash</key>
			<data>
			g9pPA9hXyz8k0bzkefN1pZR3yVs=
			</data>
			<key>hash2</key>
			<data>
			d86691Zs8KFOUYuiLif2jIkzxoDdUAiMBSxDJh5PBKk=
			</data>
		</dict>
		<key>Headers/AntDAU.h</key>
		<dict>
			<key>hash</key>
			<data>
			7JlesB4xQl4HPWEpDun4ZSWAMP8=
			</data>
			<key>hash2</key>
			<data>
			aSXgMFm+Zpn/FQOnIVLOtgcqqygMzHKEK1TEKBUT/54=
			</data>
		</dict>
		<key>Headers/AntEvent+Private.h</key>
		<dict>
			<key>hash</key>
			<data>
			tq2h6doMcx3rLZ9DMtSrGy4XGwI=
			</data>
			<key>hash2</key>
			<data>
			6sdld9Dbqad/6s/F7+TmLmkvFyFMg6zhlZ9Cz6EMZA4=
			</data>
		</dict>
		<key>Headers/AntEvent.h</key>
		<dict>
			<key>hash</key>
			<data>
			OMaBsw4BJzn1h4hgahmKXE42TNc=
			</data>
			<key>hash2</key>
			<data>
			dOGmanMp7Ajg9pLFOLhf3x6TrP1MEsjBzCdkO1XOGlI=
			</data>
		</dict>
		<key>Headers/AntLogCrypt.h</key>
		<dict>
			<key>hash</key>
			<data>
			p9xXlrl7QCmKEVww3abubJpSM4Q=
			</data>
			<key>hash2</key>
			<data>
			6f0GbLiv666Za/Iy09M4YNZAwB/bEvX6gXPLthwwM+g=
			</data>
		</dict>
		<key>Headers/AntLogInterceptor.h</key>
		<dict>
			<key>hash</key>
			<data>
			JFmmBOdZZTfrbIBavWHLuB5QXvA=
			</data>
			<key>hash2</key>
			<data>
			xIGpiqeRQrmuNuGKNgDKEK1MViONv0hvobd+zEJoXRc=
			</data>
		</dict>
		<key>Headers/AntLogInterface.h</key>
		<dict>
			<key>hash</key>
			<data>
			VnT1BlS5ggqYZWUsJ7tsH8CPT70=
			</data>
			<key>hash2</key>
			<data>
			q/5ZWSwpa9hhWT9RXhrwF7thVjhh3c2+TV+//dbD5Dg=
			</data>
		</dict>
		<key>Headers/AntLogLevel.h</key>
		<dict>
			<key>hash</key>
			<data>
			nkwkYaVffPwIOem84y/2q7lVTig=
			</data>
			<key>hash2</key>
			<data>
			4rbrIazjKdm1UJE48WJownKsuaBsXusyXTzRXib4QDw=
			</data>
		</dict>
		<key>Headers/AntLogPreference.h</key>
		<dict>
			<key>hash</key>
			<data>
			a184UESOPb5eMv9g+53IlSqZjT8=
			</data>
			<key>hash2</key>
			<data>
			DtC6N4U0AfsuLhSH6rwpzXX8vSK+21WyLpaou7Wfr40=
			</data>
		</dict>
		<key>Headers/AntLogSampleCenter.h</key>
		<dict>
			<key>hash</key>
			<data>
			RN80xxSS3DcowomYVw5Ak4CV9Aw=
			</data>
			<key>hash2</key>
			<data>
			PIkxeHtmIxiLdROVLzEt1n2+9v2fJ6WiQAetRSn9cOU=
			</data>
		</dict>
		<key>Headers/AntLogSelfMonitor.h</key>
		<dict>
			<key>hash</key>
			<data>
			Bw2C/O3qEhwTjwK9bkI9VzWmEao=
			</data>
			<key>hash2</key>
			<data>
			DZzBgYnYXdEZgxKrYZmr8TI1WXTzQK+sF1yhFlmdDbU=
			</data>
		</dict>
		<key>Headers/AntLogUtils.h</key>
		<dict>
			<key>hash</key>
			<data>
			XK6uO0Wpfn3i6ysASp9C+zcsaxY=
			</data>
			<key>hash2</key>
			<data>
			PRMEyOdm/tS4CEZmei9/o9sJh+zQd344HwRj3YUvXg8=
			</data>
		</dict>
		<key>Headers/AntPerformance.h</key>
		<dict>
			<key>hash</key>
			<data>
			rBEclJbg1eW8WRY8/1g70fJjB04=
			</data>
			<key>hash2</key>
			<data>
			O98zFKyXBZx5yL5jifFHRd1LNQnb2+O88Yc9I2LBNXM=
			</data>
		</dict>
		<key>Headers/AntRealtimeLogItem.h</key>
		<dict>
			<key>hash</key>
			<data>
			CLjg1DwRSVHK5ujm6Qgm3H55Dq0=
			</data>
			<key>hash2</key>
			<data>
			Qdef5rfc94FHlp+b7uriy0RGW24/T0tgAEHKBkYk0KY=
			</data>
		</dict>
		<key>Headers/AntRealtimeLogUploader.h</key>
		<dict>
			<key>hash</key>
			<data>
			o9lk8DSqiUhGfpZy8wEo8t0Dj+Y=
			</data>
			<key>hash2</key>
			<data>
			J6/uZYYrou13m7O0iGO5bg1Rzu70YcyqJwUGd1kNLFM=
			</data>
		</dict>
		<key>Headers/ISampleControl.h</key>
		<dict>
			<key>hash</key>
			<data>
			Dq5UDFWh6kkfP0kAp3JrAKrK/uo=
			</data>
			<key>hash2</key>
			<data>
			Ro14het3aJIju5QucbYUMMOVcpNhgV18fKoFSC1YsJY=
			</data>
		</dict>
		<key>Headers/MPLogCryptDelegate.h</key>
		<dict>
			<key>hash</key>
			<data>
			yf3oLOZkQVuldPdtBxbBcYdKCL4=
			</data>
			<key>hash2</key>
			<data>
			GYXgm29qWCKoAYOA0TnkmBRQ2SDcezuK9fhDOvEnQJM=
			</data>
		</dict>
		<key>Headers/MergerExposureLogManage.h</key>
		<dict>
			<key>hash</key>
			<data>
			R09KpylZOL78VGoZL244hA5fLMM=
			</data>
			<key>hash2</key>
			<data>
			Lbx73kiUZTvyOP1e16Q0KVFDPEF1aPDw4RH4Vc3YYOg=
			</data>
		</dict>
		<key>Headers/SPMTrackerInfo.h</key>
		<dict>
			<key>hash</key>
			<data>
			+kCmZdVmbUM5zpyoy0b+tSS3rKA=
			</data>
			<key>hash2</key>
			<data>
			1bNvjL/o5tzjhCpDC/uBqz+szaFdVnepOCLEScQtoWM=
			</data>
		</dict>
		<key>Headers/SPMTrackerLog.h</key>
		<dict>
			<key>hash</key>
			<data>
			59rPTWFTIqlUqap0rV6sEZSESsk=
			</data>
			<key>hash2</key>
			<data>
			ald6CsON+Wgyyk9nlZGyphw0WMSgkkrwiVBPAs/7ao8=
			</data>
		</dict>
		<key>en.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			zmV6UqBSo6r1NOz798vd5O4zTBA=
			</data>
			<key>hash2</key>
			<data>
			kmHsztpgjvF0JW5f3HdMHm49z1M0CcG8OT1JDQHHE/E=
			</data>
			<key>optional</key>
			<true/>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
