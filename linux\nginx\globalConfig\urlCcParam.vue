<template>
	<view class="container">
		<!-- 头部卡片 -->
		<view class="card header-card">
			<view class="header">
				<text class="header-title">{{ activeTab === 'list' ? 'URL 管理' : '添加 URL' }}</text>
				<view v-if="activeTab === 'list'" class="btn-add" hover-class="btn-hover" @tap="activeTab = 'add'">
					<uv-icon name="plus" size="16" color="#ffffff"></uv-icon>
					<text class="btn-text">添加</text>
				</view>
				<view v-else class="btn-back" hover-class="btn-hover" @tap="activeTab = 'list'">
					<uv-icon name="arrow-left" size="16" color="#ffffff"></uv-icon>
					<text class="btn-text">返回</text>
				</view>
			</view>
			<view v-if="activeTab === 'add'">
				<view class="pt-20">
					<!-- 类型 -->
					<view class="form-item">
						<text class="form-item-label">类型</text>
						<view class="form-item-content">
							<picker
								:value="matchModeIndex"
								:range="matchModes"
								@change="onMatchModeChange"
								class="form-picker"
							>
								<view class="picker-value">
									<text>{{ newItem.matchMode }}</text>
									<uv-icon name="arrow-down" size="14" color="#666"></uv-icon>
								</view>
							</picker>
						</view>
					</view>

					<!-- URL -->
					<view class="form-item">
						<text class="form-item-label">URL</text>
						<view class="form-item-content">
							<input
								v-model="newItem.url"
								type="text"
								class="form-input"
								placeholder="输入URL"
								@blur="validateUrlField"
								:class="{ 'input-error': errors.url }"
							/>
						</view>
						<text v-if="errors.url" class="error-text">{{ errors.url }}</text>
					</view>

					<!-- 参数值 -->
					<view class="form-item">
						<text class="form-item-label">参数值</text>
						<view class="form-item-content">
							<textarea
								v-model="newItem.params"
								class="form-textarea"
								placeholder="默认为空参数表示匹配所有参数，一行一个"
								:class="{ 'input-error': errors.params }"
							></textarea>
						</view>
						<text v-if="errors.params" class="error-text">{{ errors.params }}</text>
					</view>

					<!-- 验证方式 -->
					<view class="form-item">
						<text class="form-item-label">验证方式</text>
						<view class="form-item-content">
							<picker
								:value="verifyModeIndex"
								:range="verifyModes"
								@change="onVerifyModeChange"
								class="form-picker"
							>
								<view class="picker-value">
									<text>{{ newItem.verifyMode }}</text>
									<uv-icon name="arrow-down" size="14" color="#666"></uv-icon>
								</view>
							</picker>
						</view>
					</view>

					<!-- 提示信息 -->
					<view class="form-tips">
						<view class="form-tip-item">
							<text class="dot">•</text>
							<text>优先级：匹配模式 > URL匹配模式 > 配置模式</text>
						</view>
						<view class="form-tip-item">
							<text class="dot">•</text>
							<text>当前模式为当URL开头匹配时，保存URL时的路径</text>
						</view>
						<view class="form-tip-item">
							<text class="dot">•</text>
							<text
								>举例：某URL为注册页面www.bit.cn/regire.html，那么选择URL为：/regire.html
								验证模式任选</text
							>
						</view>
						<view class="form-tip-item">
							<text class="dot">•</text>
							<text>注意：如果网站存在CDN，建议设置为路径验证，其他验证可能会存在CDN缓存的问题</text>
						</view>
					</view>

					<!-- 操作按钮 -->
					<view class="form-submit">
						<button class="submit-btn" hover-class="submit-btn-hover" @tap="addItem">添加</button>
					</view>
				</view>
			</view>
		</view>

		<!-- 主内容区域 -->
		<view class="content-wrapper">
			<!-- List View -->
			<view v-if="activeTab === 'list'" class="url-list-container">
				<!-- 空状态 -->
				<view v-if="urlItems.length === 0" class="empty-state">
					<uv-icon name="info-circle" size="32" color="#909399"></uv-icon>
					<text class="empty-text">当前数据为空，点击右上角添加</text>
				</view>

				<!-- 数据列表 -->
				<view v-else class="url-list">
					<view v-for="item in urlItems" :key="item?.id || 'unknown'" class="url-card">
						<view class="url-card__header">
							<text class="url-card__title" :title="item?.url || ''">{{ item?.url || '未知URL' }}</text>
							<view class="url-card__action" @tap="deleteItem(item?.id)" v-if="item?.id">
								<uv-icon name="trash" size="16" color="#e53935"></uv-icon>
							</view>
						</view>

						<view class="url-card__content">
							<view class="url-card__info-row">
								<view class="url-card__info-item">
									<text class="info-label">参数值</text>
									<text class="info-value">{{ item?.params || '-' }}</text>
								</view>
							</view>

							<view class="url-card__info-row">
								<view class="url-card__info-item">
									<text class="info-label">匹配方式</text>
									<text class="info-value">{{ item?.matchMode || '未知' }}</text>
								</view>
								<view class="url-card__info-item">
									<text class="info-label">验证方式</text>
									<text class="info-value">{{ item?.verifyMode || '未知' }}</text>
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script setup>
	import { onMounted } from 'vue';
	import {
		activeTab,
		urlItems,
		newItem,
		matchModes,
		verifyModes,
		matchModeIndex,
		verifyModeIndex,
		onMatchModeChange,
		onVerifyModeChange,
		addItem,
		deleteItem,
		getUrlCcParamData,
		errors,
		validateUrlField,
	} from './useUrlCcParamController.js';

	// 页面加载时获取数据
	onMounted(() => {
		getUrlCcParamData();
	});
</script>

<style lang="scss" scoped>
	// Variables
	$primary-color: #20a50a;
	$primary-hover: #189008;
	$background-color: #f5f5f5;
	$text-color: var(--text-color-primary);
	$light-text: var(--text-color-secondary);
	$border-color: #eaeaea;
	$danger-color: #e53935;
	$card-background: var(--dialog-bg-color);
	$bg-color: var(--dialog-bg-color);
	$base-spacing: 20rpx;

	.container {
		padding: $base-spacing;
	}

	.card {
		background: $bg-color;
		border-radius: 16rpx;
		box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.12);
		overflow: hidden;
	}

	.header {
		display: flex;
		justify-content: space-between;
		align-items: center;
	}

	.header-title {
		font-size: 32rpx;
		font-weight: 500;
		color: $text-color;
	}

	.btn-add,
	.btn-back {
		display: flex;
		align-items: center;
		padding: 16rpx 40rpx;
		background-color: $primary-color;
		border-radius: 8rpx;
		color: white;
		font-size: 28rpx;

		.btn-text {
			margin-left: 10rpx;
		}
	}

	.btn-hover {
		opacity: 0.9;
		transform: scale(0.98);
	}

	// 内容区域
	.content-wrapper {
		margin-top: $base-spacing;
	}

	// 空状态样式
	.url-list-container {
		position: relative;
	}

	.empty-state {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding: 80rpx 0;
		background-color: $bg-color;
		border-radius: 16rpx;
		box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.12);
		color: $light-text;

		.empty-text {
			font-size: 28rpx;
			margin-top: 20rpx;
		}
	}

	// 列表卡片样式
	.url-list {
		display: flex;
		flex-direction: column;
		gap: $base-spacing;
	}

	.url-card {
		background-color: $card-background;
		border-radius: 12rpx;
		box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.12);
		overflow: hidden;
		border: 2rpx solid #f0f0f0;

		&__header {
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: 20rpx;
			background-color: var(--border-color);
			border-bottom: 2rpx solid #f0f0f0;
		}

		&__title {
			font-size: 28rpx;
			font-weight: 500;
			color: $primary-color;
			word-break: break-all;
		}

		&__action {
			width: 60rpx;
			height: 60rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			background-color: rgba($danger-color, 0.05);
			border-radius: 30rpx;

			&:active {
				background-color: rgba($danger-color, 0.15);
			}
		}

		&__content {
			padding: 20rpx;
		}

		&__info-row {
			display: flex;
			margin-bottom: 16rpx;

			&:last-child {
				margin-bottom: 0;
			}
		}

		&__info-item {
			flex: 1;
			margin-right: 20rpx;

			&:last-child {
				margin-right: 0;
			}
		}
	}

	.info-label {
		font-size: 24rpx;
		color: $light-text;
		display: block;
		margin-bottom: 8rpx;
	}

	.info-value {
		font-size: 28rpx;
		color: $text-color;
		background-color: var(--border-color);
		padding: 12rpx 16rpx;
		border-radius: 6rpx;
		display: block;
		word-break: break-all;
	}

	.form-item {
		margin-bottom: 30rpx;
	}

	.form-item-label {
		display: block;
		font-size: 28rpx;
		color: $text-color;
		margin-bottom: 10rpx;
	}

	.form-item-content {
		width: 100%;
	}

	.form-input,
	.picker-value {
		width: 100%;
		height: 80rpx;
		border: 2rpx solid #ddd;
		border-radius: 6rpx;
		padding: 0 20rpx;
		font-size: 28rpx;
		background-color: #fff;
		box-sizing: border-box;

		&.input-error {
			border-color: #e53935;
		}
	}

	.form-textarea {
		width: 100%;
		height: 160rpx;
		border: 2rpx solid #ddd;
		border-radius: 6rpx;
		padding: 20rpx;
		font-size: 28rpx;
		background-color: #fff;
		box-sizing: border-box;

		&.input-error {
			border-color: #e53935;
		}
	}

	.error-text {
		color: #e53935;
		font-size: 24rpx;
		margin-top: 8rpx;
		display: block;
	}

	.form-picker {
		width: 100%;
	}

	.picker-value {
		display: flex;
		align-items: center;
		justify-content: space-between;
		height: 80rpx;
	}

	.form-tips {
		margin: 40rpx 0;
	}

	.form-tip-item {
		display: flex;
		align-items: flex-start;
		margin-bottom: 16rpx;
		font-size: 26rpx;
		color: $light-text;
		line-height: 1.5;

		.dot {
			margin-right: 10rpx;
		}
	}

	.form-submit {
		margin: 60rpx 0;
	}

	.submit-btn {
		width: 100%;
		height: 90rpx;
		line-height: 90rpx;
		text-align: center;
		background-color: $primary-color;
		color: white;
		font-size: 32rpx;
		border-radius: 6rpx;
	}

	.submit-btn-hover {
		background-color: $primary-hover;
	}

	.pb-20 {
		padding-bottom: 20rpx;
	}
</style>
