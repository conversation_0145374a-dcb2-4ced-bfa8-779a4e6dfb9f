<template>
	<view>
		<!-- 执行周期 -->
		<CycleForm
			:formData="formData"
			@update:formData="updateFormData"
			:showProcessLock="true"
			:isEditMode="isEditMode"
			class="mb-32"
		/>

		<!-- 执行用户 -->
		<view class="form-group">
			<view class="form-label-row">
				<text>执行用户</text>
			</view>
			<button class="region-select-button" @click="showUserPicker">
				<text>{{ getUserLabel() }}</text>
				<uv-icon name="arrow-down" size="14" :color="iconColor"></uv-icon>
			</button>
		</view>

		<!-- 脚本内容 -->
		<view class="form-group">
			<view class="form-label-row">
				<text>* 脚本内容</text>
			</view>
			<view class="textarea-wrapper">
				<uv-textarea
					v-model="scriptValue"
					placeholder="请输入脚本内容"
					height="200"
					:textStyle="{ fontSize: '26rpx', color: 'var(--text-color-primary)', lineHeight: '1.6' }"
					:customStyle="{ backgroundColor: 'var(--dialog-bg-color)', paddingRight: '120rpx' }"
				/>
			</view>
		</view>

		<!-- 温馨提示 -->
		<view class="form-group">
			<view class="form-label-row">
				<text>温馨提示</text>
			</view>
			<view class="help-text">
				<text>为了保证服务器的安全稳定，shell脚本中以下命令不可使用：shutdown, init 0, mkfs, passwd, chpasswd, --stdin, mkfs.ext, mke2fs</text>
			</view>
		</view>

		<!-- 用户选择器 -->
		<uv-picker
			ref="userPicker"
			:columns="[userOptions]"
			keyName="label"
			@confirm="onUserConfirm"
		></uv-picker>
	</view>
</template>

<script setup>
	import { ref, reactive, onMounted, onBeforeUnmount, getCurrentInstance, computed, watch } from 'vue';
	import { getSystemUserListForCrontab } from '@/api/crontab';
	import { getTheme, THEME_TYPE } from '@/hooks/useTheme.js';
	import { truncateText } from '../useController';
	import CycleForm from './CycleForm.vue';

	const { proxy } = getCurrentInstance();

	const props = defineProps({
		formData: {
			type: Object,
			required: true
		},
		isEditMode: {
			type: Boolean,
			default: false
		}
	});

	const emit = defineEmits(['update:formData']);

	// 主题相关
	const currentTheme = ref(getTheme());
	const iconColor = computed(() => {
		return currentTheme.value === THEME_TYPE.DARK ? '#cccccc' : '#666666';
	});

	// 脚本内容的双向绑定计算属性
	const scriptValue = computed({
		get() {
			return props.formData.sBody;
		},
		set(value) {
			emit('update:formData', { ...props.formData, sBody: value });
		}
	});



	// 用户选项
	const userOptions = ref([
		{ label: 'root', value: 'root' }
	]);

	// Picker引用
	const userPicker = ref(null);

	// 显示选择器
	const showUserPicker = () => {
		userPicker.value?.open();
	};

	const getUserLabel = () => {
		const user = props.formData.user || 'root';
		const option = userOptions.value.find(item => item.value === user);
		return option ? truncateText(option.label) : 'root';
	};





	const onUserConfirm = (e) => {
		const selectedValue = e.value[0].value;
		updateField('user', selectedValue);
	};

	// 更新字段
	const updateField = (field, event) => {
		const value = event.detail?.value || event.target?.value || event;
		emit('update:formData', { ...props.formData, [field]: value });
	};

	// 更新表单数据
	const updateFormData = (newData) => {
		emit('update:formData', { ...props.formData, ...newData });
	};

	// 初始化默认值
	const initializeDefaults = () => {
		// 只在非编辑模式下初始化默认值
		if (!props.isEditMode) {
			// 检查是否需要设置默认值
			const needsDefaults = !props.formData.user || !props.formData.hour || !props.formData.minute;
			if (needsDefaults) {
				emit('update:formData', {
					...props.formData,
					// 基础字段
					hour: props.formData.hour || '1',
					minute: props.formData.minute || '30',
					user: props.formData.user || 'root',
					flock: props.formData.flock !== undefined ? props.formData.flock : true,
					// Shell脚本特有字段
					sType: 'toShell',
					// 时间相关字段
					type: props.formData.type || 'day',
					week: props.formData.week || '1',
					where1: props.formData.where1 || '1',
					second: props.formData.second || '5',
					timeSet: props.formData.timeSet || '',
					timeType: props.formData.timeType || 'sday',
					// 其他必要字段
					test: '',
					sName: '',
					backupTo: '',
					save: '',
					urladdress: '',
					save_local: '0',
					notice: '0',
					notice_channel: '',
					datab_name: '',
					tables_name: '',
					keyword: '',
					version: '',
					stop_site: '0'
				});
			}
		}
	};

	// 获取系统用户列表
	const loadSystemUsers = async () => {
		try {
			const res = await getSystemUserListForCrontab({ all_user: true });
			if (res && Array.isArray(res)) {
				userOptions.value = res.map(user => ({
					label: user,
					value: user
				}));
			}
		} catch (error) {
			console.error('加载系统用户失败:', error);
		}

		// 在加载用户列表后初始化默认值（仅非编辑模式）
		if (!props.isEditMode) {
			initializeDefaults();
		}
	};



	// 主题变化监听
	const handleThemeChange = (event) => {
		currentTheme.value = event.theme;
	};

	onMounted(() => {
		// 先初始化默认值（仅非编辑模式）
		if (!props.isEditMode) {
			initializeDefaults();
		}
		// 然后加载系统用户列表
		loadSystemUsers();
		// 监听主题变化
		uni.$on('themeChange', handleThemeChange);
	});

	onBeforeUnmount(() => {
		// 移除主题变化监听
		uni.$off('themeChange', handleThemeChange);
	});
</script>

<style lang="scss" scoped>
	@import './form-styles.scss';



	.textarea-wrapper {
		position: relative;
	}

	.select-script-button {
		position: absolute;
		top: 20rpx;
		right: 20rpx;
		background: var(--primary-color);
		border: none;
		border-radius: 12rpx;
		padding: 12rpx 20rpx;
		font-size: 22rpx;
		font-weight: 500;
		color: #ffffff;
		box-shadow: var(--box-shadow);
		transition: all 0.3s ease;

		&:active {
			transform: scale(0.95);
			box-shadow: var(--box-shadow);
		}
	}

	.help-text {
		background: var(--bg-color-secondary);
		border: 1px solid var(--border-color);
		border-radius: 16rpx;
		padding: 20rpx;

		text {
			font-size: 22rpx;
			color: var(--text-color-secondary);
			line-height: 1.6;
			font-weight: 400;
		}
	}

	// 重写基础样式，使其更精致
	.form-group {
		margin-bottom: 32rpx;
		padding: 24rpx;
		background: var(--bg-color);
		border-radius: 20rpx;
		box-shadow: var(--box-shadow);
		border: 1px solid var(--border-color);

		&:last-child {
			margin-bottom: 0;
		}
	}

	.form-label-row {
		margin-bottom: 16rpx;

		text {
			font-size: 28rpx;
			font-weight: 600;
			color: var(--text-color-primary);
			position: relative;

			&::before {
				content: '';
				position: absolute;
				left: -12rpx;
				top: 50%;
				transform: translateY(-50%);
				width: 6rpx;
				height: 20rpx;
				background: var(--primary-color);
				border-radius: 3rpx;
			}
		}
	}

	.region-select-button {
		background: var(--bg-color);
		border: 1px solid var(--border-color);
		border-radius: 16rpx;
		transition: all 0.3s ease;

		&:active {
			transform: scale(0.98);
			box-shadow: var(--box-shadow);
		}

		text {
			font-size: 26rpx;
			font-weight: 500;
			color: var(--text-color-primary);
		}
	}

	.form-row {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-top: 20rpx;
		padding-top: 20rpx;
		border-top: 1px solid var(--border-color);

		text {
			font-size: 26rpx;
			font-weight: 500;
			color: var(--text-color-primary);
		}
	}


</style>
