import { defineStore } from 'pinia';
import { ref } from 'vue';
import { storeToRefs } from 'pinia';
import { $t } from '@/locale/index';

export const useServerListStore = defineStore('serverList', () => {
	const serverList = ref([]);
	const serverInit = ref([
		{
			name: $t('server.connecting'),
			ip: '',
			rawPath: '',
			uptime: $t('server.day', { days: 0 }),
			active: false,
			status: false,
			// 优化图表初始数据：提供有意义的默认值，确保图表能立即渲染基础框架
			load: {
				usage: 0, // 保持0%显示，但确保数据结构完整
				total: $t('server.smooth'),
			},
			cpu: {
				usage: 0, // 保持0%显示，但确保数据结构完整
				cores: 0,
			},
			memory: {
				usage: 0, // 保持0%显示，但确保数据结构完整
				total: '0 G',
			},
			disk: {
				usage: 0, // 保持0%显示，但确保数据结构完整
				total: '0.00 G',
			},
			network: {
				up: '0.00',
				down: '0.00',
			},
			// 添加图表渲染状态标识，用于区分初始状态和加载状态
			isChartInitialized: true, // 标识图表数据已初始化，可以渲染
		},
	]);

	// 应用排序到服务器列表
	const applySortOrder = () => {
		const sortOrder = uni.getStorageSync('serverSortOrder');

		if (!sortOrder || !Array.isArray(sortOrder) || sortOrder.length === 0) {
			return;
		}

		// 根据保存的排序顺序重新排列服务器列表
		const sortedList = [];
		const remainingServers = [...serverList.value];

		// 按照保存的顺序添加服务器 - 使用 rawPath 匹配，与其他地方保持一致
		sortOrder.forEach((rawPath) => {
			const index = remainingServers.findIndex((server) => server.rawPath === rawPath);
			if (index !== -1) {
				sortedList.push(remainingServers.splice(index, 1)[0]);
			}
		});

		// 添加不在排序列表中的新服务器到首部（确保新服务器始终在列表首部）
		sortedList.unshift(...remainingServers);

		serverList.value = sortedList;
	};

	// 注释：排序功能已简化，现在直接基于 configList 的顺序
	// 不再需要单独的排序配置，因为 configList 就是我们的主要数据源

	// 清空排序配置
	const clearSortOrder = () => {
		uni.removeStorageSync('serverSortOrder');
		console.log('已清空排序配置');
	};

	// 辅助函数，获取响应式状态
	function getReactiveState() {
		const store = useServerListStore();
		return {
			...storeToRefs(store),
		};
	}

	return {
		getReactiveState,
		serverList,
		serverInit,
		applySortOrder,
		clearSortOrder,
	};
});
