<template>
	<view>
		<!-- 文件拆分 - 仅在非本地备份时显示 -->
		<view v-if="showFileSplit" class="split-section form-group">
			<view class="split-header">
				<text class="split-title">文件拆分</text>
			</view>
			<view class="split-container">
				<button class="split-select-button" @click="showSplitTypePicker">
					<text>{{ getSplitTypeLabel() }}</text>
					<uv-icon name="arrow-down" size="14" :color="iconColor"></uv-icon>
				</button>

				<view v-if="formData.split_type !== '0'" class="split-value-container">
					<uv-input
						:key="`split-input-${formData.split_type}-${formData.split_value}`"
						v-model="localSplitValue"
						@blur="validateSplitValue"
						type="number"
						border="surround"
						:customStyle="{ width: '240rpx', fontSize: '28rpx' }"
					>
						<template v-slot:suffix>
							<text class="split-unit">{{ formData.split_type === 'num' ? '个' : 'MB' }}</text>
						</template>
					</uv-input>
				</view>
			</view>

			<view class="split-description">
				<text>*为避免文件过大导致上传失败，可以选择按大小或数量拆分。仅文件超过 5GB 时支持拆分，小文件请忽略</text>
			</view>
		</view>

		<!-- 备份设置 - 仅在非本地备份时显示 -->
		<view v-if="showFileSplit" class="backup-setting-section form-group">
			<view class="backup-setting-container">
				<view class="switch-container">
					<text class="switch-text">同时保留本地备份（和云存储保留份数一致）</text>
					<uv-switch
						:model-value="!!formData.save_local"
						@change="updateSaveLocal"
						:customStyle="{ marginLeft: '24rpx' }"
						activeColor="#20a50a"
					></uv-switch>
				</view>
			</view>
		</view>

		<!-- 文件拆分类型选择器 -->
		<uv-picker
			ref="splitTypePicker"
			:columns="[splitTypeOptions]"
			keyName="label"
			@confirm="onSplitTypeConfirm"
		></uv-picker>
	</view>
</template>

<script setup>
	import { ref, computed, watch, getCurrentInstance, nextTick } from 'vue';
	import { getTheme, THEME_TYPE } from '@/hooks/useTheme.js';
	import { fileSplitForm } from '../useController.js';

	const { proxy } = getCurrentInstance();

	const props = defineProps({
		formData: {
			type: Object,
			required: true
		},
		isEnterprise: {
			type: Boolean,
			default: false
		}
	});

	const emit = defineEmits(['update:formData']);

	// 主题相关
	const currentTheme = ref(getTheme());
	const iconColor = computed(() => {
		return currentTheme.value === THEME_TYPE.DARK ? '#cccccc' : '#666666';
	});

	// 计算是否显示文件拆分功能
	const showFileSplit = computed(() => {
		return !props.isEnterprise &&
			   props.formData.backupTo &&
			   props.formData.backupTo !== 'localhost';
	});

	// 使用本地状态处理输入，避免频繁更新父组件导致软键盘收起
	const localSplitValue = ref(String(props.formData.split_value));

	// 监听 props 变化，同步到本地状态
	watch(() => props.formData.split_value, (newValue) => {
		localSplitValue.value = String(newValue);
	});

	// 文件拆分类型选项
	const splitTypeOptions = ref([
		{ label: '不拆分', value: '0' },
		{ label: '按文件大小拆分', value: 'size' },
		{ label: '按数量拆分', value: 'num' }
	]);

	// Picker ref
	const splitTypePicker = ref(null);

	// 显示文件拆分类型选择器
	const showSplitTypePicker = () => {
		splitTypePicker.value?.open();
	};

	// 获取文件拆分类型标签
	const getSplitTypeLabel = () => {
		const option = splitTypeOptions.value.find(item => item.value === props.formData.split_type);
		return option ? option.label : '不拆分';
	};

	// 确认选择文件拆分类型
	const onSplitTypeConfirm = (e) => {
		const selectedValue = e.value[0].value;

		let updateData = {
			...props.formData,
			split_type: selectedValue
		};

		// 根据选择的类型设置默认值
		if (selectedValue === 'num') {
			updateData.split_value = 5;
			fileSplitForm.value.split_value = 5;
		} else if (selectedValue === 'size') {
			updateData.split_value = 1024;
			fileSplitForm.value.split_value = 1024;
		} else if (selectedValue === '0') {
			// 不拆分时，重置为默认值
			updateData.split_value = 5;
			fileSplitForm.value.split_value = 5;
		}

		// 同步更新 fileSplitForm
		fileSplitForm.value.split_type = selectedValue;

		// 先发送更新事件
		emit('update:formData', updateData);

		// 使用 nextTick 确保 DOM 更新后再强制刷新
		nextTick(() => {
			// 更新本地状态
			localSplitValue.value = String(updateData.split_value);
		});
	};

	// 验证拆分数值
	const validateSplitValue = () => {
		const splitValue = Number(localSplitValue.value);
		if (isNaN(splitValue) || splitValue <= 0) {
			const defaultValue = props.formData.split_type === 'num' ? 5 : 1024;

			// 更新本地状态
			localSplitValue.value = String(defaultValue);

			// 同步更新 fileSplitForm
			fileSplitForm.value.split_value = defaultValue;

			// 更新父组件
			emit('update:formData', {
				...props.formData,
				split_value: defaultValue
			});
		} else {
			// 如果值有效，确保同步更新 fileSplitForm
			fileSplitForm.value.split_value = splitValue;

			// 更新父组件，确保存储的是数值类型
			emit('update:formData', {
				...props.formData,
				split_value: splitValue
			});
		}
	};

	// 更新本地保存设置
	const updateSaveLocal = (value) => {
		const numValue = Number(value);

		// 同步更新 fileSplitForm
		fileSplitForm.value.save_local = numValue;

		emit('update:formData', {
			...props.formData,
			save_local: numValue
		});
	};
</script>

<style lang="scss" scoped>
	@import './form-styles.scss';

	.split-section {
		margin-top: 24rpx;
		padding: 20rpx;
		background: var(--bg-color);
		border-radius: 16rpx;
		border: 1px solid var(--border-color);
	}

	.split-header {
		margin-bottom: 16rpx;
	}

	.split-title {
		font-size: 26rpx;
		font-weight: 600;
		color: var(--text-color-primary);
	}

	.split-container {
		display: flex;
		align-items: center;
		gap: 20rpx;
		flex-wrap: wrap;
	}

	.backup-setting-section {
		margin-top: 16rpx;
		padding: 16rpx 20rpx;
		background: var(--bg-color);
		border-radius: 12rpx;
		border: 1px solid var(--border-color);
	}

	.split-select-button {
		width: 280rpx;
		height: 90rpx !important;
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 0 24rpx;
		background: var(--bg-color);
		border: 1px solid var(--border-color);
		border-radius: 16rpx;
		transition: all 0.3s ease;

		&:active {
			transform: scale(0.98);
			box-shadow: var(--box-shadow);
		}

		text {
			font-size: 26rpx;
			font-weight: 500;
			color: var(--text-color-primary);
		}
	}

	.split-value-container {
		display: flex;
		align-items: center;
	}

	.split-unit {
		font-size: 26rpx;
		font-weight: 600;
		color: var(--text-color-primary);
		padding: 0 8rpx;
	}

	.split-description {
		margin-top: 16rpx;
		padding: 12rpx 16rpx;
		background: var(--bg-color-secondary);
		border-radius: 12rpx;
		border-left: 4rpx solid var(--primary-color);

		text {
			font-size: 22rpx;
			line-height: 1.4;
			color: var(--text-color-secondary);
		}
	}

	.backup-setting-container {
		display: flex;
		align-items: center;
	}

	.switch-container {
		display: flex;
		align-items: center;
		justify-content: space-between;
		width: 100%;
	}

	.switch-text {
		font-size: 26rpx;
		font-weight: 500;
		color: var(--text-color-primary);
		flex: 1;
	}

	// 重写基础样式，使其更精致
	.form-group {
		margin-bottom: 32rpx;
		padding: 24rpx;
		background: var(--bg-color);
		border-radius: 20rpx;
		box-shadow: var(--box-shadow);
		border: 1px solid var(--border-color);

		&:last-child {
			margin-bottom: 0;
		}
	}

	.form-label-row {
		margin-bottom: 16rpx;

		text {
			font-size: 28rpx;
			font-weight: 600;
			color: var(--text-color-primary);
			position: relative;

			&::before {
				content: '';
				position: absolute;
				left: -12rpx;
				top: 50%;
				transform: translateY(-50%);
				width: 6rpx;
				height: 20rpx;
				background: var(--primary-color);
				border-radius: 3rpx;
			}
		}
	}
</style>
