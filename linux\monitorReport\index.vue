<template>
	<page-container title="监控报表" v-model:activeIndex="tabCurrentIndex" :showTabBar="isBuy" :tabData="['概览', '网站', 'IP统计', '蜘蛛统计']" @tabClick="tabChange">
		<view class="content">
			<view class="" v-if="isBuy">
				<swiper
					:style="{ height: swiperHeight != 0 ? swiperHeight + 'px' : 'calc(100vh - 280rpx)' }"
					:skip-hidden-item-layout="true"
					:current="currentIndex - 1"
					:disable-touch="true"
					@change="swiperChange"
				>
					<!-- 概览 -->
					<swiper-item class="swiper-1">
						<overviewView ref="overview" />
					</swiper-item>
					<swiper-item class="swiper-2">
						<siteListComponent ref="siteList" />
					</swiper-item>
					<swiper-item class="swiper-3">
						<ipStatisticsComponent ref="ipStatistics" />
					</swiper-item>
					<swiper-item class="swiper-4">
						<spiderStatisticsComponent ref="spiderStatistics" />
					</swiper-item>
				</swiper>
			</view>

			<!-- 未安装 -->
			<view class="pay-tips" v-else>
				<install />
			</view>
		</view>
	</page-container>
</template>

<script setup>
	import PageContainer from '@/components/PageContainer/index.vue';
	import { onReachBottom, onShow } from '@dcloudio/uni-app';
	import { onMounted, onUnmounted } from 'vue';
	import overviewView from './components/overviewView.vue';
	import siteListComponent from './components/siteList.vue';
	import ipStatisticsComponent from './components/ipStatistics.vue';
	import spiderStatisticsComponent from './components/spiderStatistics.vue';
	import install from './install.vue';
	import {
		isBuy,
		version,
		min_version,
		currentIndex,
		selectDay,
		swiperHeight,
		scrollLeft,
		setInter,
		overview,
		siteList,
		ipStatistics,
		spiderStatistics,
		showNavList,
		selectPreventInfo,
		swiperChange,
		cutType,
		tabChange,
		tabCurrentIndex
	} from './useController';

	// 页面加载
	onMounted(() => {
		uni.showLoading({
			title: '数据加载中...',
			mask: true,
		});
	});

	onShow(() => {
		selectPreventInfo();
	});

	// 页面销毁
	onUnmounted(() => {
		if (setInter.value) {
			clearInterval(setInter.value);
		}
	});

	// 触底事件
	onReachBottom(() => {
		if (siteList.value && isBuy.value) {
			siteList.value.getList();
		}
	});
</script>
