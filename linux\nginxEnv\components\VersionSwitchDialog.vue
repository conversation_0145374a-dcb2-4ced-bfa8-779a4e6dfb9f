<template>
	<CustomDialog
		v-model="visible"
		:isMaskClick="false"
		:zIndex="9999"
		title="版本切换进度"
		:showCancel="false"
		:showConfirm="false"
		contentHeight="400rpx"
	>
		<view class="switch-progress">
			<!-- 进度条 -->
			<view class="progress-bar">
				<view class="progress-fill" :style="{ width: `${progress}%` }"></view>
			</view>
			<text class="progress-text">{{ progress }}%</text>
		</view>

		<view class="log-container">
			<view class="log-header">
				<text class="log-title">切换日志</text>
				<text class="log-status" :class="{ 'status-error': hasError }">
					{{ hasError ? '切换失败' : isCompleted ? '切换完成' : '正在切换' }}
				</text>
			</view>
			<scroll-view class="log-content" scroll-y :scroll-into-view="scrollToId">
				<text
					v-for="(log, index) in logs"
					:key="index"
					:id="`log-${index}`"
					:class="{ 'log-error': log.type === 'error' }"
				>
					{{ log.timestamp }} - {{ log.message }}
				</text>
			</scroll-view>
		</view>

		<!-- 错误时显示关闭按钮 -->
		<view class="actions" v-if="hasError">
			<button class="btn-complete" @click="handleComplete">
				关闭
			</button>
		</view>
	</CustomDialog>
</template>

<script setup>
	import { ref, computed, watch } from 'vue';
	import CustomDialog from '@/components/CustomDialog/index.vue';

	const props = defineProps({
		modelValue: {
			type: Boolean,
			default: false
		},
		logs: {
			type: Array,
			default: () => []
		},
		progress: {
			type: Number,
			default: 0
		},
		hasError: {
			type: Boolean,
			default: false
		},
		isCompleted: {
			type: Boolean,
			default: false
		}
	});

	const emit = defineEmits(['update:modelValue', 'complete']);

	const visible = computed({
		get: () => props.modelValue,
		set: (value) => emit('update:modelValue', value)
	});

	const scrollToId = computed(() => {
		return props.logs.length > 0 ? `log-${props.logs.length - 1}` : '';
	});

	const handleComplete = () => {
		emit('complete');
		visible.value = false;
	};
</script>

<style lang="scss" scoped>
	.switch-progress {
		display: flex;
		align-items: center;
		gap: 16rpx;
		margin-bottom: 32rpx;

		.progress-bar {
			flex: 1;
			height: 16rpx;
			background-color: #f3f4f6;
			border-radius: 8rpx;
			overflow: hidden;

			.progress-fill {
				height: 100%;
				background: linear-gradient(90deg, #20a50a 0%, #16a34a 100%);
				border-radius: 8rpx;
				transition: width 0.3s ease;
			}
		}

		.progress-text {
			font-size: 24rpx;
			font-weight: 600;
			color: #20a50a;
			min-width: 80rpx;
			text-align: right;
		}
	}

	.log-container {
		flex: 1;
		display: flex;
		flex-direction: column;
		height: 400rpx;

		.log-header {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-bottom: 16rpx;
			padding-bottom: 16rpx;
			border-bottom: 2rpx solid #e5e7eb;

			.log-title {
				font-size: 28rpx;
				font-weight: 600;
				color: var(--text-primary-color);
			}

			.log-status {
				font-size: 24rpx;
				font-weight: 500;
				color: #20a50a;

				&.status-error {
					color: #dc2626;
				}
			}
		}

		.log-content {
			flex: 1;
			height: 320rpx;
			background-color: #1f2937;
			border-radius: 12rpx;
			padding: 16rpx;
			font-family: 'Courier New', monospace;

			text {
				display: block;
				font-size: 22rpx;
				color: #d1d5db;
				line-height: 1.5;
				margin-bottom: 8rpx;

				&.log-error {
					color: #fca5a5;
				}

				&:last-child {
					margin-bottom: 0;
				}
			}
		}
	}

	.actions {
		margin-top: 32rpx;
		display: flex;
		justify-content: center;

		.btn-complete {
			width: 200rpx;
			height: 72rpx;
			background: linear-gradient(135deg, #20a50a 0%, #16a34a 100%);
			border: none;
			border-radius: 12rpx;
			color: white;
			font-size: 28rpx;
			font-weight: 600;
			display: flex;
			align-items: center;
			justify-content: center;
			box-shadow: 0 4rpx 12rpx rgba(32, 165, 10, 0.3);
			transition: all 0.3s ease;

			&:active {
				transform: translateY(2rpx);
				box-shadow: 0 2rpx 6rpx rgba(32, 165, 10, 0.3);
			}
		}
	}
</style>
