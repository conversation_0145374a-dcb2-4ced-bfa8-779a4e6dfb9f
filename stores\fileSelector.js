import { ref } from 'vue';

// 全局文件选择器状态
const selectedPaths = ref([]);
const selectorCallback = ref(null);

/**
 * 设置文件选择回调
 * @param {Function} callback 回调函数
 */
export const setFileSelectorCallback = (callback) => {
	selectorCallback.value = callback;
};

/**
 * 设置选择的文件路径
 * @param {Array} paths 文件路径数组
 */
export const setSelectedPaths = (paths) => {
	selectedPaths.value = paths;

	// 如果有回调函数，延迟调用以确保目标页面完全恢复
	if (selectorCallback.value && typeof selectorCallback.value === 'function') {
		const callback = selectorCallback.value;
		// 调用前先清除回调，避免重复调用
		selectorCallback.value = null;

		// 延迟执行回调，确保页面导航完成且 pageContainer 已重新挂载
		setTimeout(() => {
			try {
				callback(paths);
			} catch (error) {
				console.error('回调函数调用失败:', error);
			}
		}, 300); // 300ms 延迟，确保页面完全恢复
	}
};

/**
 * 获取选择的文件路径
 * @returns {Array} 文件路径数组
 */
export const getSelectedPaths = () => {
	return selectedPaths.value;
};

/**
 * 清除选择的文件路径
 */
export const clearSelectedPaths = () => {
	selectedPaths.value = [];
	// 注意：不清除回调函数，因为它可能在文件选择过程中需要使用
};

/**
 * 打开文件选择器
 * @param {string} mode 选择模式 'all', 'file', 'folder'
 * @param {boolean} multiple 是否多选
 * @param {Function} callback 选择完成后的回调函数
 */
export const openFileSelector = (mode = 'folder', multiple = false, callback = null) => {
	// 清除之前的选择（但保留回调函数）
	selectedPaths.value = [];

	// 设置回调函数
	if (callback) {
		setFileSelectorCallback(callback);
	}

	// 构建URL
	const getTitle = (mode, multiple) => {
		const modeText = mode === 'file' ? '文件' : mode === 'folder' ? '文件夹' : '文件/文件夹';
		const multipleText = multiple ? '多选' : '单选';
		return `选择${modeText}（${multipleText}）`;
	};

	const url = `/pages/file-selector/index?mode=${mode}&multiple=${multiple}&title=${encodeURIComponent(
		getTitle(mode, multiple),
	)}`;

	uni.navigateTo({
		url: url,
	});
};
