<template>
	<page-container ref="pageContainer" :is-back="true" title="SSH设置">
		<view class="admin-panel pb-30">
			<view class="container">
				<!-- SSH状态卡片 -->
				<view class="card status-card">
					<view class="card__content">
						<view class="setting-item">
							<view class="setting-item__header">
								<view class="">
									<text class="setting-item__label text-primary">SSH开关</text>
								</view>
								<uv-switch
									:model-value="sshEnabled"
									@change="toggleSSH"
									activeColor="var(--primary-color)"
									inactiveColor="#d1d5db"
									size="20"
									:loading="sshSwitchLoading"
								></uv-switch>
							</view>
						</view>

						<view v-if="sshEnabled" class="stats-container p-20 mt-10">
							<view class="flex justify-between items-center mb-12">
								<text class="status-item__label text-primary">SSH连接情况:</text>
								<view class="refresh-button p-8" @tap="refreshStats">
									<text v-if="statsLoading">刷新中...</text>
									<text v-else>刷新</text>
								</view>
							</view>
							<view class="flex flex-wrap justify-between gap-12">
								<view class="stats-item flex items-center flex-1 success">
									<text class="stats-text">连接成功: {{ stats.success }}</text>
									<text class="stats-subtext">(今日新增: {{ stats.todaySuccess }})</text>
								</view>
								<view class="stats-item flex items-center flex-1 failed">
									<text class="stats-text">连接失败: {{ stats.failed }}</text>
									<text class="stats-subtext">(今日新增: {{ stats.todayFailed }})</text>
								</view>
							</view>
						</view>
					</view>
				</view>
				<!-- 密码登录卡片 -->
				<view class="card settings-card">
					<view class="setting-item">
						<view class="setting-item__header">
							<view class="">
								<text class="setting-item__label text-primary">SSH密码登录</text>
							</view>
							<uv-switch
								v-model="passwordLoginEnabled"
								@change="togglePasswordLogin"
								activeColor="var(--primary-color)"
								inactiveColor="#d1d5db"
								size="20"
								:loading="passwordLoginLoading"
							></uv-switch>
						</view>
					</view>
				</view>

				<!-- 密钥登录卡片 -->
				<view class="card settings-card">
					<view class="setting-item">
						<view class="setting-item__header">
							<view class="">
								<text class="setting-item__label text-primary">SSH密钥登录</text>
							</view>
							<uv-switch
								v-model="keyLoginEnabled"
								@change="toggleKeyLogin"
								activeColor="var(--primary-color)"
								inactiveColor="#d1d5db"
								size="20"
								:loading="keyLoginLoading"
							></uv-switch>
						</view>
						<view class="setting-item__content">
							<text class="setting-item__hint">推荐使用密钥登录，关闭密码，安全性更高</text>
						</view>
					</view>
				</view>

				<!-- SSH端口设置卡片 -->
				<view class="card settings-card">
					<view class="setting-item">
						<view class="setting-item__header">
							<view class="">
								<text class="setting-item__label text-primary">SSH端口</text>
							</view>
							<uv-button
								type="success"
								text="保存"
								size="small"
								:loading="portLoading"
								:disabled="portLoading"
								@click="handleSavePort"
								customStyle="height: 60rpx;"
							></uv-button>
						</view>
						<view class="setting-item__content mt-10">
							<uv-input
								v-model="sshPort"
								type="number"
								border="surround"
								placeholder="请输入端口号"
								:disabled="portLoading"
								clearable
							></uv-input>
							<text class="setting-item__hint">当前SSH协议所使用的的端口，默认为22</text>
						</view>
					</view>
				</view>

				<!-- root登录设置卡片 -->
				<view class="card settings-card">
					<view class="setting-item">
						<view class="setting-item__header">
							<view class="">
								<text class="setting-item__label text-primary">root登录设置</text>
							</view>
						</view>
						<view class="setting-item__content">
							<view class="picker-trigger mt-10 mb-16" @click="picker.open()">
								<text class="picker-text">{{ rootLoginModes[rootLoginModeIndex]?.value }}</text>
								<text class="picker-icon">▼</text>
							</view>
							<uv-picker
								ref="picker"
								:columns="[rootLoginModes]"
								@confirm="confirmPicker"
								keyName="value"
								confirmColor="var(--primary-color)"
								:defaultIndex="[rootLoginModeIndex]"
								:loading="rootLoginModeLoading"
								title="选择登录模式"
								confirmText="确定"
								cancelText="取消"
							></uv-picker>
						</view>
					</view>
				</view>

				<!-- root密码设置卡片 -->
				<view class="card settings-card">
					<view class="setting-item">
						<view class="setting-item__header">
							<view class="">
								<text class="setting-item__label text-primary">root密码</text>
							</view>
							<view class="btn-group">
								<uv-button
									type="info"
									text="生成"
									size="small"
									plain
									@click="generatePassword"
									customStyle="height: 60rpx;"
									class="mr-10"
									:disabled="rootPasswordLoading"
								></uv-button>
								<uv-button
									type="success"
									text="重置"
									size="small"
									:loading="rootPasswordLoading"
									:disabled="rootPasswordLoading || rootPassword === ''"
									@click="setPasswordDialog = true"
									customStyle="height: 60rpx;"
								></uv-button>
							</view>
						</view>
						<view class="setting-item__content mt-10">
							<uv-input
								v-model="rootPassword"
								border="surround"
								placeholder="请输入新密码"
								:disabled="rootPasswordLoading"
								@blur="handlePasswordBlur"
								clearable
							></uv-input>
							<text class="setting-item__hint"
								>建议使用复杂度高的密码，修改后请及时保存，关闭页面会清空密码框</text
							>
						</view>
					</view>
				</view>

				<!-- root密钥 -->
				<view class="card settings-card">
					<view class="setting-item">
						<view class="text-#ef4444 mb-10 text-24" v-if="!keyLoginEnabled">请先开启SSH密钥登录，再查看密钥</view>
						<view class="setting-item__header">
							<view class="">
								<text class="setting-item__label text-primary">root密钥</text>
							</view>
							<view class="btn-group">
								<uv-button
									type="info"
									text="重新生成"
									size="small"
									plain
									@click="generateKey"
									customStyle="height: 60rpx;"
									class="mr-10"
									:disabled="!keyLoginEnabled"
								></uv-button>
								<uv-button
									type="success"
									text="复制"
									size="small"
									:disabled="!keyLoginEnabled"
									@click="copyKey"
									customStyle="height: 60rpx;"
								></uv-button>
							</view>
						</view>
						<view class="setting-item__content mt-10">
							<uv-textarea :value="rootKey" :maxlength="-1" :disabled="!keyLoginEnabled" border="surround" placeholder="root密钥"></uv-textarea>
						</view>
					</view>
				</view>
			</view>
		</view>
		<custom-dialog v-model="setPasswordDialog" title="提示" @confirm="handleSetPassword" contentHeight="200rpx">
			<view class="text-secondary flex justify-center items-center h-full"
				>重置root密码后，之前的密码将失效，是否继续操作？</view
			>
		</custom-dialog>
		<custom-dialog v-model="setSSHDialog" title="提示" @confirm="handleSetSSH" contentHeight="200rpx">
			<view class="text-secondary flex justify-center items-center h-full">{{
				sshEnabled ? '停用SSH服务后您将无法使用终端工具连接服务器，继续吗？' : '是否启动SSH服务？'
			}}</view>
		</custom-dialog>
	</page-container>
</template>

<script setup>
	import PageContainer from '@/components/PageContainer/index.vue';
	import CustomDialog from '@/components/CustomDialog/index.vue';
	import { onMounted, onUnmounted } from 'vue';
	import {
		sshEnabled,
		passwordLoginEnabled,
		keyLoginEnabled,
		sshPort,
		rootPassword,
		rootKey,
		isLoading,
		saveSuccess,
		rootLoginModes,
		rootLoginModeIndex,
		picker,
		stats,
		toggleSSH,
		togglePasswordLogin,
		toggleKeyLogin,
		confirmPicker,
		handlePasswordBlur,
		handleSave,
		refreshStats,
		generatePassword,
		copyKey,
		handleSSHInfo,
		pageContainer,
		handleSSHBasicConfig,
		setPasswordDialog,
		handleSetPassword,
		handleSavePort,
		setSSHDialog,
		handleSetSSH,
		generateKey,
		getKey,
		// 独立loading状态
		sshSwitchLoading,
		passwordLoginLoading,
		keyLoginLoading,
		portLoading,
		rootLoginModeLoading,
		rootPasswordLoading,
		statsLoading,
	} from './useController.js';

	onMounted(async () => {
		uni.showLoading({
			title: '加载中...',
			mask: true,
		});
		await handleSSHInfo();
		refreshStats();
		await handleSSHBasicConfig();
		if (keyLoginEnabled.value) {
			await getKey();
		}
		uni.hideLoading();
	});

	onUnmounted(() => {
		rootPassword.value = '';
		rootKey.value = '';
	});
</script>

<style lang="scss">
	// 变量定义
	$primary-color: var(--primary-color);
	$primary-hover-color: #16a34a;
	$danger-color: #ef4444;
	$success-color: var(--primary-color);
	$text-color: var(--text-color-primary);
	$text-light-color: var(--text-color-secondary);
	$border-color: #e5e7eb;
	$card-bg-color: var(--dialog-bg-color);
	$input-border-color: #d1d5db;
	$input-focus-border-color: var(--primary-color);
	$divider-color: #e5e7eb;

	$border-radius: 12rpx;
	$box-shadow: 0 3rpx 15rpx rgba(0, 0, 0, 0.08);

	// 混合器
	@mixin flex($direction: row, $justify: flex-start, $align: stretch) {
		display: flex;
		flex-direction: $direction;
		justify-content: $justify;
		align-items: $align;
	}

	@mixin transition($property: all, $duration: 0.2s, $timing: ease-in-out) {
		transition: $property $duration $timing;
	}

	.container {
		max-width: 750rpx;
		margin: 30rpx auto;
		padding: 0 30rpx;
	}

	.card {
		background-color: $card-bg-color;
		border-radius: $border-radius;
		box-shadow: $box-shadow;
		overflow: hidden;
		margin-bottom: 24rpx;
		border: 1rpx solid rgba(226, 232, 240, 0.8);

		&__title {
			font-size: 32rpx;
			font-weight: 500;
		}
	}

	.status-card {
		margin-bottom: 24rpx;
	}

	.stats-container {
		background-color: var(--border-color);
		border: 1rpx solid rgba(226, 232, 240, 0.6);
		border-radius: 14rpx;
		animation: expandIn 0.3s ease;
		box-shadow: inset 0 1rpx 3rpx rgba(0, 0, 0, 0.01);
	}

	.stats-item {
		padding: 10rpx 14rpx;
		background-color: var(--dialog-bg-color);
		border-radius: 10rpx;
		border: 1rpx solid rgba(0, 0, 0, 0.03);
		box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.02);
		min-width: calc(50% - 16rpx);
	}

	.refresh-button {
		border-radius: 50%;
		transition: all 0.2s ease;
		font-size: 24rpx;
		color: #64748b;
	}

	.refresh-button:active {
		background-color: rgba(0, 0, 0, 0.05);
		transform: scale(0.95);
	}

	.setting-item {
		&:hover {
			background-color: rgba(0, 0, 0, 0.01);
		}

		&__header {
			@include flex(row, space-between, center);
		}

		&__title {
			@include flex(row, flex-start, center);
		}

		&__label {
			font-size: 28rpx;
			font-weight: 500;
		}

		&__content {
			position: relative;
		}

		&__hint {
			font-size: 24rpx;
			color: $text-light-color;
			margin-top: 8rpx;
		}
	}

	.stats-text {
		font-size: 24rpx;
		margin: 0 6rpx;
		font-weight: 500;
	}

	.stats-subtext {
		font-size: 20rpx;
		opacity: 0.75;
	}

	.success {
		color: var(--primary-color);
	}

	.failed {
		color: #ef4444;
	}

	.btn-group {
		@include flex(row, flex-end, center);
	}

	.status-item {
		@include flex(row, space-between, center);
		padding: 8rpx 0;

		&__label {
			font-size: 28rpx;
			font-weight: 500;
		}

		&__value {
			font-size: 28rpx;
		}
	}

	.status-badge {
		background-color: $success-color;
		color: white;
		padding: 8rpx 16rpx;
		border-radius: 9999rpx;
		font-size: 24rpx;
		font-weight: 500;
	}

	/* 选择器样式 */
	.picker-trigger {
		height: 72rpx;
		border: 1rpx solid #e2e8f0;
		border-radius: 10rpx;
		padding: 0 20rpx;
		display: flex;
		align-items: center;
		justify-content: space-between;
		background-color: #ffffff;
		box-shadow: inset 0 1rpx 3rpx rgba(0, 0, 0, 0.02);
		transition: all 0.3s ease;
	}

	.picker-trigger:active {
		border-color: rgba(16, 185, 129, 0.5);
		box-shadow: 0 0 0 2rpx rgba(16, 185, 129, 0.1);
	}

	.picker-text {
		font-size: 26rpx;
		color: #334155;
	}

	.picker-icon {
		font-size: 18rpx;
		color: #64748b;
	}

	/* 动画 */
	@keyframes expandIn {
		from {
			opacity: 0;
			max-height: 0;
			transform: scale(0.98);
		}
		to {
			opacity: 1;
			max-height: 500rpx;
			transform: scale(1);
		}
	}
</style>
