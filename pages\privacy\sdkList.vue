<template>
  <page-container title="第三方SDK列表">
    <view class="container">
      <uni-table border stripe emptyText="暂无更多数据" class="table-view">
        <!-- 表头行 -->
        <uni-tr>
          <uni-th width="120" align="center">SDK名称</uni-th>
          <uni-th align="center">包名信息</uni-th>
          <uni-th align="center">使用目的</uni-th>
          <uni-th align="center">使用的权限</uni-th>
          <uni-th align="center">涉及个人信息</uni-th>
          <uni-th align="center">隐私权政策链接</uni-th>
        </uni-tr>
        <!-- 表格数据行 -->
        <uni-tr>
          <uni-td>微信开放平台</uni-td>
          <uni-td>com.tencent.mm</uni-td>
          <uni-td>登录、分享、支付（请根据具体使用目的填写）</uni-td>
          <uni-td>{{
            `android.permission.ACCESS_NETWORK_STATE
						android.permission.ACCESS_WIFI_STATE`
          }}</uni-td>
          <uni-td>存储的个人文件、网络信息</uni-td>
          <uni-td class="table-color" align="center">
            <text @click="searchResult(resultList[0])">微信隐私协议</text>
          </uni-td>
        </uni-tr>
        <uni-tr>
          <uni-td>新浪开放平台</uni-td>
          <uni-td>com.sina.weibo</uni-td>
          <uni-td>登录、分享（请根据具体使用目的填写）</uni-td>
          <uni-td>{{
            `android.permission.ACCESS_NETWORK_STATE
						android.permission.ACCESS_WIFI_STATE`
          }}</uni-td>
          <uni-td>存储的个人文件、网络信息</uni-td>
          <uni-td class="table-color" align="center">
            <text @click="searchResult(resultList[1])">新浪隐私协议</text>
          </uni-td>
        </uni-tr>
        <uni-tr>
          <uni-td>QQ开放平台</uni-td>
          <uni-td>com.tencent.open</uni-td>
          <uni-td>登录、分享（请根据具体使用目的填写）</uni-td>
          <uni-td>{{
            `android.permission.MODIFY_AUDIO_SETTINGS
						android.permission.ACCESS_NETWORK_STATE
						android.permission.ACCESS_WIFI_STATE`
          }}</uni-td>
          <uni-td>存储的个人文件、读取手机状态和身份、网络信息</uni-td>
          <uni-td class="table-color" align="center">
            <text @click="searchResult(resultList[2])">qq隐私协议</text>
          </uni-td>
        </uni-tr>
        <uni-tr>
          <uni-td>支付宝开放平台</uni-td>
          <uni-td>com.alipay</uni-td>
          <uni-td>登录、分享（请根据具体使用目的填写）</uni-td>
          <uni-td>{{
            `android.permission.ACCESS_NETWORK_STATE
						android.permission.ACCESS_WIFI_STATE`
          }}</uni-td>
          <uni-td>网络信息</uni-td>
          <uni-td class="table-color" align="center">
            <text @click="searchResult(resultList[3])">支付宝隐私协议</text>
          </uni-td>
        </uni-tr>
        <uni-tr>
          <uni-td>个验一键登录</uni-td>
          <uni-td>com.g.elogin、com.g.gysdk</uni-td>
          <uni-td>登录（请根据具体使用目的填写）</uni-td>
          <uni-td>{{
            `android.permission.READ_PHONE_STATE
						android.permission.READ_EXTERNAL_STORAGE
						android.permission.WRITE_EXTERNAL_STORAGE
						android.permission.ACCESS_NETWORK_STATE
						android.permission.ACCESS_WIFI_STATE
						android.permission.CHANGE_NETWORK_STATE`
          }}</uni-td>
          <uni-td>存储的个人文件、读取手机状态和身份、网络信息</uni-td>
          <uni-td class="table-color" align="center">
            <text @click="searchResult(resultList[4])">https://docs.getui.com/privacy/</text>
          </uni-td>
        </uni-tr>
      </uni-table>
    </view>
  </page-container>
</template>

<script setup>
  import { ref } from 'vue';
  import PageContainer from '@/components/PageContainer/index.vue';

  const resultList = ref([
    'https://weixin.qq.com/cgi-bin/readtemplate?lang=zh_CN&t=weixin_agreement&s=privacy',
    'https://weibo.com/signup/v5/privacy?spm=a1zaa.8161610.0.0.4f8776217Wu8R1',
    'https://docs.qq.com/doc/p/8ca74fba95bca4a30eeee1044050b510300d5382?pub=1',
    'https://render.alipay.com/p/yuyan/180020010001196791/preview.html?agreementId=AG00000132',
    'https://docs.getui.com/privacy/',
  ]);

  const searchResult = (str) => {
    // #ifdef APP-PLUS
    plus.runtime.openURL(str);
    // #endif

    // #ifndef APP-PLUS
    window.open(str, '_blank');
    // #endif
  };
</script>

<style lang="scss" scoped>
  .container {
    min-height: 100vmax;
    width: 100%;
    margin-top: -10rpx;
  }
  .table-view {
    // transform: rotate(90deg);
    .table-color {
      color: #20a53a;
    }
  }
</style>
