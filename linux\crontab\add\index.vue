<template>
	<page-container ref="pageContainer" :title="isEditMode ? '编辑计划任务' : '添加计划任务'" :isBack="true">
		<view class="container">
			<view class="card">
				<view class="card-content">
					<!-- 任务类型 -->
					<view class="form-group">
						<view class="form-label-row">
							<text>任务类型</text>
						</view>
						<button class="region-select-button" @click="showTaskTypePicker" :disabled="disableTaskType" :style="disableTaskType ? 'background: var(--bg-color-disabled)' : ''">
							<text>{{ taskTypeLabel || '请选择任务类型' }}</text>
							<uv-icon name="arrow-down" size="14" color="#666666"></uv-icon>
						</button>
					</view>

					<!-- 任务名称 -->
					<view class="form-group">
						<view class="form-label-row">
							<text>任务名称</text>
						</view>
						<view class="input-wrapper">
							<uv-input
								v-model="formData.name"
								placeholder="请输入计划任务名称"
								:disabled="disableName"
								disabledColor="var(--bg-color-disabled)"
								border="surround"
								:customStyle="{ fontSize: '28rpx' }"
							/>
						</view>
					</view>

					<!-- 动态表单项组件 -->
					<DynamicFormItems
						:formData="formData"
						:isEditMode="isEditMode"
						@update:formData="updateFormData"
						@url-input="onUrlInput"
					/>


				</view>
			</view>
		</view>

		<!-- Picker组件 -->
		<uv-picker
			ref="taskTypePicker"
			:columns="[taskTypeOptions]"
			keyName="label"
			@confirm="onTaskTypeConfirm"
		></uv-picker>



		<!-- 底部按钮 -->
		<view class="bottom-actions">
			<uv-button text="取消" type="info" :plain="true" @click="handleCancel" :customStyle="{ width: '250rpx', height: '88rpx', backgroundColor: '#efefef' }" />
			<uv-button :text="isEditMode ? '保存' : '确定'" type="success" @click="handleSubmit" :loading="submitting" :customStyle="{ width: '250rpx', height: '88rpx' }" />
		</view>
	</page-container>
</template>

<script setup>
	import { onMounted } from 'vue';
	import { onLoad } from '@dcloudio/uni-app';
	import PageContainer from '@/components/PageContainer/index.vue';
	import DynamicFormItems from './components/DynamicFormItems.vue';
	import { useAddCrontab } from './useController';

	// 引入控制器逻辑
	const {
		pageContainer,
		formData,
		taskTypePicker,
		taskTypeOptions,
		disableName,
		disableTaskType,
		submitting,
		taskTypeLabel,
		showTaskTypePicker,
		onTaskTypeConfirm,
		onUrlInput,
		updateFormData,
		handleSubmit,
		handleCancel,
		// 编辑相关
		isEditMode,
		initEditData,
	} = useAddCrontab();

	// 页面挂载时初始化编辑数据
	onLoad((options) => {
		initEditData(options);
	});
</script>

<style lang="scss" scoped>
	// 主题色变量
	$primary-color: #20a50a;
	$primary-light: lighten($primary-color, 45%);
	$text-color: var(--text-color-primary);
	$text-color-disabled: var(--text-color-disabled);
	$border-color: #ddd;
	$bg-color: var(--dialog-bg-color);
	$bg-color-disabled: var(--bg-color-disabled);
	$muted-color: #666;
	$light-bg: #f5f5f5;

	.container {
		padding-bottom: 200rpx; /* 为底部按钮留出空间 */
		background-color: var(--bg-color);
	}

	.card {
		background: $bg-color;
		border-radius: 20rpx;
		box-shadow: 0 2rpx 16rpx rgba(0, 0, 0, 0.1);
		overflow: hidden;
	}

	.form-group {
		margin-bottom: 30rpx;

		&:last-child {
			margin-bottom: 0;
		}
	}

	.form-label-row {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 20rpx;

		text {
			font-size: 28rpx;
			font-weight: 500;
			color: $text-color;
		}
	}

	.form-row {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 20rpx;

		text {
			flex: 1;
			font-size: 28rpx;
			color: $text-color;
		}
	}

	.input-wrapper {
		width: 100%;
	}

	.input-wrapper-small {
		width: 240rpx;
	}

	.textarea-wrapper {
		width: 100%;
	}

	.region-select-button {
		width: 100%;
		height: 80rpx;
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 0 30rpx;
		background-color: $bg-color;
		border: 1px solid $border-color;
		border-radius: 12rpx;

		&:disabled {
			background-color: $bg-color-disabled;
			opacity: 0.6;

			text {
				color: $text-color-disabled;
			}
		}

		text {
			font-size: 28rpx;
			color: $text-color;
		}
	}

	.accordion {
		border: 1px solid $border-color;
		border-radius: 12rpx;
		margin-bottom: 30rpx;
		overflow: hidden;
	}

	.accordion-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 30rpx;
		background-color: $light-bg;

		text {
			font-size: 28rpx;
			font-weight: 500;
		}
	}

	.accordion-content {
		padding: 30rpx;
		border-top: 1px solid $border-color;
	}

	.time-input-group {
		display: flex;
		align-items: center;
		width: 240rpx;
	}

	.time-input {
		width: 80rpx;
		height: 80rpx;
		padding: 0 15rpx;
		border: 1px solid $border-color;
		border-radius: 12rpx;
		font-size: 28rpx;
		color: $text-color;
		background-color: $bg-color;
		text-align: center;
	}

	.time-separator {
		font-size: 32rpx;
		font-weight: bold;
		color: $text-color;
		margin: 0 10rpx;
	}

	.info-section {
		margin-top: 40rpx;

		text {
			display: block;
			font-size: 24rpx;
			color: $muted-color;
			line-height: 1.6;
			margin-bottom: 16rpx;

			&:last-child {
				margin-bottom: 0;
			}
		}
	}

	.bottom-actions {
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		padding: 30rpx;
		background-color: var(--bg-color);
		border-top: 1rpx solid #e5e5e5;
		display: flex;
		justify-content: space-around;
		z-index: 100;
		box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);
	}


</style>
