<template>
    <view class="empty-container">
        <image class="empty-image" :src="logoImg" mode="aspectFit"></image>
        <text class="empty-desc text-center">{{ $t('blank.blankTitle') }}</text>
        <bt-button class="add-button" plain :text="$t('blank.addServer')" @click="handleAddServer"></bt-button>
        <view class="text-bt-primary text-26" style="margin-top: 100rpx" @click="handleViewNovice">查看新手教程</view>
    </view>
</template>

<script setup>
import { computed } from 'vue';
import BtButton from '@/components/BtButton/index.vue';
import { $t } from '@/locale/index';

const emit = defineEmits(['scan']);
const panelType = import.meta.env.VITE_PANEL;
const logoImg = computed(() => {
    return panelType === 'btPanel' ? '/static/login/logo.png' : '/static/login/aapanel-logo.png'
})

const handleAddServer = () => {
    emit('scan');
}

const handleViewNovice = () => {
    uni.navigateTo({
        url: '/pages/novice/novice?type=server',
        animationType: 'zoom-fade-out',
    });
}
</script>

<style scoped>
.empty-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    box-sizing: border-box;
    margin-top: 10vh;
}

.empty-image {
    width: 100%;
    height: 30vh;
    margin-bottom: 24rpx;
}

.empty-desc {
    font-size: 32rpx;
    font-weight: 600;
    color: var(--text-color-tertiary);
    margin-bottom: 20rpx;
}

.add-button {
    margin-top: 10vh;
    width: 320rpx;
    height: 76rpx;
    line-height: 76rpx;
    text-align: center;
}
</style>
