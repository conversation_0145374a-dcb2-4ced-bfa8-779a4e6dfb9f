<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>AutoConfig.json</key>
		<data>
		VvBzzttsRVymavAQGWGKdc7qq7k=
		</data>
		<key>Headers/APApplicationInfo.h</key>
		<data>
		O5zdaATcD5bXEV0Ow8Ws7h6Ay68=
		</data>
		<key>Headers/APBase64.h</key>
		<data>
		+mmiq5AWDMAlqVD1WhmbpJbxN6U=
		</data>
		<key>Headers/APEmoji.h</key>
		<data>
		C21fAzIholpK/CbFUFK0n4JGgaI=
		</data>
		<key>Headers/APFastMethodSwizzling.h</key>
		<data>
		xw0MbGL4paNVpALTALB2hC+b8hk=
		</data>
		<key>Headers/APLanguage+MPaaS.h</key>
		<data>
		Em/mdQ/4HJ10i38p3ABHiTod8Y0=
		</data>
		<key>Headers/APLanguage.h</key>
		<data>
		z8/MQ6cXKilrbFXA8lrVBdlhYCA=
		</data>
		<key>Headers/APLanguageBundleLoader.h</key>
		<data>
		FOonSKLp2+tLbwxI2oLtKHPPmtg=
		</data>
		<key>Headers/APMD5.h</key>
		<data>
		QiPVr6fJlStNmPat3zFPi7qOFdg=
		</data>
		<key>Headers/APMPaaS.h</key>
		<data>
		fNrKwOdhVp+x53ccRJn1u231v/o=
		</data>
		<key>Headers/APMobileFoundation.h</key>
		<data>
		iL/kMmrTQ3JP7ve75IMcoYfBI60=
		</data>
		<key>Headers/APRemoteLogging+MPaaS.h</key>
		<data>
		pbcXs7YJAUqUjtHLykXXlyQq7wY=
		</data>
		<key>Headers/APSSKeychain.h</key>
		<data>
		t7yAHxOez9VRhp3Gg8d5EBu0YCc=
		</data>
		<key>Headers/APSearchManager.h</key>
		<data>
		ix+HOeZtAzyZTqaUKskXfLbfba0=
		</data>
		<key>Headers/APSearchPosition.h</key>
		<data>
		6iveneZ9CE2RhgppCSQRLY84Lu0=
		</data>
		<key>Headers/APShareKit+MPaaS.h</key>
		<data>
		8TAuewEXonSctlc3sRCQ5WKJvjI=
		</data>
		<key>Headers/APThemeManager+MPaaS.h</key>
		<data>
		gJw91COdb6SsCWJUfG9h+7rVssI=
		</data>
		<key>Headers/APThreadPoolManager.h</key>
		<data>
		v4CVQtgngrylKuOjT1ftPswfBXk=
		</data>
		<key>Headers/APThreadTaskMonitor.h</key>
		<data>
		tAVh8hP6aXux9BgvT6hg+9/FJH8=
		</data>
		<key>Headers/AUBarButtonItem+MPaaS.h</key>
		<data>
		YZ3bq92WN506IwrK7Hz/kTSR7Xc=
		</data>
		<key>Headers/DFCrashReport+MPaaS.h</key>
		<data>
		pmetV71sdDLGYmX9eNH0RetX200=
		</data>
		<key>Headers/DTDeviceInfo.h</key>
		<data>
		564g4OSQTQ1hVWUJZUNwLX1o25Q=
		</data>
		<key>Headers/DTJsonHelper.h</key>
		<data>
		2ny9qukc1LyhIM9/0n8J7VpHwc0=
		</data>
		<key>Headers/DTMobileFramework+MPaaS.h</key>
		<data>
		Y4CqwZNU09gZvBJbu4c4gZN1VZk=
		</data>
		<key>Headers/DTNumber.h</key>
		<data>
		mR/3pyzdTe5ojq2TB+0KUFmKMWY=
		</data>
		<key>Headers/DynamicRelease+MPaaS.h</key>
		<data>
		uYd6ScjH5nNIYDpUbrLNqTgtm6g=
		</data>
		<key>Headers/EBCManager+MPaaS.h</key>
		<data>
		WORfYN1f34bPeLjB7qEZouguTtw=
		</data>
		<key>Headers/GTMNSString+HTML.h</key>
		<data>
		h4hpU21k57qTtUhsCdKNZFKvA8w=
		</data>
		<key>Headers/MPAppState.h</key>
		<data>
		4EMudmpRf7eZ5C+mndLMPtkVxDY=
		</data>
		<key>Headers/MPAppStateMaintainer.h</key>
		<data>
		85Upk/pzkImYVYLFPCiI+lMmuPg=
		</data>
		<key>Headers/MPCryptKit.h</key>
		<data>
		7ZSh8SgX+Y6sxO6Ub8lvOEvCMz8=
		</data>
		<key>Headers/MPDynamicLoader.h</key>
		<data>
		qSFBAV1EUwq3YZobl3zq46v665Q=
		</data>
		<key>Headers/MPFunctionMaintainer.h</key>
		<data>
		WEi+qIa7QreBNssZkIr2ZXM3Ots=
		</data>
		<key>Headers/MPJSONKit.h</key>
		<data>
		CsZYTOtaZiR/19Z2rBSy5ApHmt8=
		</data>
		<key>Headers/MPLiteSettingService+MPaaS.h</key>
		<data>
		0KKRe44Wyb+27TwBx9dHAwF8kns=
		</data>
		<key>Headers/MPLiteSettingService.h</key>
		<data>
		RBFIWh3JfHHB7DpVc7zHpKgLEn0=
		</data>
		<key>Headers/MPThreadManager.h</key>
		<data>
		d8IbV/0Fkbf2Tfic5CapSvd5R7Y=
		</data>
		<key>Headers/MPUnification.h</key>
		<data>
		u1AbqakHP7/AUeDt23M8BrX9aWc=
		</data>
		<key>Headers/MPZipKit.h</key>
		<data>
		WKLsjYsC7VNBXEEWOOrk9BiK3w8=
		</data>
		<key>Headers/MPaaS+Decoupling.h</key>
		<data>
		YInakdeNKH2IDvOI5tmBcV6JAdU=
		</data>
		<key>Headers/MPaaS+ImportAPLanguage.h</key>
		<data>
		wguGzFMNF+NDvFtojkwA2jGP0ds=
		</data>
		<key>Headers/MPaaS+ImportAPRemoteLogging.h</key>
		<data>
		pYFBZaPwI0XIkGZsEC1EWnhY1v4=
		</data>
		<key>Headers/MPaaS+ImportAPShareKit.h</key>
		<data>
		GJ5LkaD0qe+JVXxtPq3XrOrJsu4=
		</data>
		<key>Headers/MPaaS+ImportAPThemeManager.h</key>
		<data>
		fIBujAPCWohhCJxNc7eQewZ8izk=
		</data>
		<key>Headers/MPaaS+ImportAUBarButtonItem.h</key>
		<data>
		XkIvs0CDmNMAxrng159g9ugHMgo=
		</data>
		<key>Headers/MPaaS+ImportDFCrashReport.h</key>
		<data>
		uumY+mz0B3t0SF8uYlq92MD8gsE=
		</data>
		<key>Headers/MPaaS+ImportDynamicRelease.h</key>
		<data>
		VzI25yM0jm03Wu+tlTdsJ9my5Ws=
		</data>
		<key>Headers/MPaaS+ImportMPLiteSettingService.h</key>
		<data>
		DQgsWRofFntX0sVcYym0m0EAXYo=
		</data>
		<key>Headers/MPaaS+MonitorStartUpTime.h</key>
		<data>
		7ssU/ltE9BIogP+6+YHTMoKKzj0=
		</data>
		<key>Headers/MPaaSConfigInfo.h</key>
		<data>
		fcPrmifkW3bAXtYvU3RAHStxPu0=
		</data>
		<key>Headers/MPaaSDVInfo.h</key>
		<data>
		RvYYrwCjhQ+R9cpvK3NL+PWbsEE=
		</data>
		<key>Headers/MPaaSInterface.h</key>
		<data>
		76E6DBpsCd8uvXVb0+aaOaAP1F8=
		</data>
		<key>Headers/MTNetModel.h</key>
		<data>
		XAhNKTVH9f0FaEmSdjF0Nvoakqo=
		</data>
		<key>Headers/MTNetStatistic.h</key>
		<data>
		uOdZ7oMCAHwTrIldD6o78+7JqyY=
		</data>
		<key>Headers/MTNetStatisticProxy.h</key>
		<data>
		TdqUbgCUSaJ8nFcdc+0E0LdAiTc=
		</data>
		<key>Headers/MobileFoundation.h</key>
		<data>
		cSEYiCveA8rXr1LzP1p7kXyRe40=
		</data>
		<key>Headers/NSArray+DTExtensions.h</key>
		<data>
		6DQRDfGJlIKSZcx6TryhwwemE9g=
		</data>
		<key>Headers/NSDate+DTJsonString.h</key>
		<data>
		8s6FAMTUYfpYcmuk660xTF6fK9s=
		</data>
		<key>Headers/NSDictionary+DTExtensions.h</key>
		<data>
		w3N2jxBb4xGzdhMSTOcbGd5yb28=
		</data>
		<key>Headers/NSString+DTRegularExpressionValidator.h</key>
		<data>
		iNel47xVZQMZlrB8ELvw7WmI4Bs=
		</data>
		<key>Headers/NSStringURLUtils.h</key>
		<data>
		l6R6402V2gHgrZUtngij6mvKWt4=
		</data>
		<key>Headers/NSStringUtils.h</key>
		<data>
		ZVT2f/OIXZ8bOruEwJe9bLKuBDM=
		</data>
		<key>Headers/NSURL+DTExtensions.h</key>
		<data>
		IelUBWERWyhaIK7RjuckvblVrgg=
		</data>
		<key>Headers/NSURL+URLWithFixedString.h</key>
		<data>
		QSXn8mE7CMmcu5lGf9o9/dZG57o=
		</data>
		<key>Headers/UIDevice+mPaas.h</key>
		<data>
		Kfroujg6TvV0i7dzDORmyXkHu8U=
		</data>
		<key>Headers/UIImage+Color.h</key>
		<data>
		wtPdIcChaSdp14W+d7p4r9Re/CE=
		</data>
		<key>Headers/UIView+Helper.h</key>
		<data>
		So9J9RhNTn1/OxDDZZUEPrXJe94=
		</data>
		<key>Headers/UIView+SnapShot.h</key>
		<data>
		c36MP1Ev4QbFma/BcED7KBdf5Z4=
		</data>
		<key>Headers/mPaas.h</key>
		<data>
		WazJO4zOTop9/CkwbNWk/QXZPY8=
		</data>
		<key>Info.plist</key>
		<data>
		0AyNpDet3k+mK/vIyntb+j+i/eU=
		</data>
		<key>pinyin</key>
		<data>
		nxGTGQIYv7WblFD6Ztn+y22AqZs=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>AutoConfig.json</key>
		<dict>
			<key>hash</key>
			<data>
			VvBzzttsRVymavAQGWGKdc7qq7k=
			</data>
			<key>hash2</key>
			<data>
			DJSVdshSNN90ugIr9nxJ47kHD9QPRpihoYtlZnNB3Ys=
			</data>
		</dict>
		<key>Headers/APApplicationInfo.h</key>
		<dict>
			<key>hash</key>
			<data>
			O5zdaATcD5bXEV0Ow8Ws7h6Ay68=
			</data>
			<key>hash2</key>
			<data>
			KqdXi3RI0JEL65aMxkm3LGcVtvQauX4hKlUuvxq0JqI=
			</data>
		</dict>
		<key>Headers/APBase64.h</key>
		<dict>
			<key>hash</key>
			<data>
			+mmiq5AWDMAlqVD1WhmbpJbxN6U=
			</data>
			<key>hash2</key>
			<data>
			BXSpN3uxXNbHl6Gs9CNYag4aUypJmm1kx2+FEAOmttk=
			</data>
		</dict>
		<key>Headers/APEmoji.h</key>
		<dict>
			<key>hash</key>
			<data>
			C21fAzIholpK/CbFUFK0n4JGgaI=
			</data>
			<key>hash2</key>
			<data>
			EWpszINVS78tvVR8Ua1HQ7Ed+uQcHjacAEaVO3MffYQ=
			</data>
		</dict>
		<key>Headers/APFastMethodSwizzling.h</key>
		<dict>
			<key>hash</key>
			<data>
			xw0MbGL4paNVpALTALB2hC+b8hk=
			</data>
			<key>hash2</key>
			<data>
			C7vn4JE9k/SdxD3M8jU/QMS58znhzqMwV1bjg1qQGB4=
			</data>
		</dict>
		<key>Headers/APLanguage+MPaaS.h</key>
		<dict>
			<key>hash</key>
			<data>
			Em/mdQ/4HJ10i38p3ABHiTod8Y0=
			</data>
			<key>hash2</key>
			<data>
			ICPLRNdHWRpJMz2iYsgLKzqhQhsEE3VU0UcxaUngVbg=
			</data>
		</dict>
		<key>Headers/APLanguage.h</key>
		<dict>
			<key>hash</key>
			<data>
			z8/MQ6cXKilrbFXA8lrVBdlhYCA=
			</data>
			<key>hash2</key>
			<data>
			JR6ahyZ21pQUg9wCEAwoNCOrfJcFMaThsCThZcuIezA=
			</data>
		</dict>
		<key>Headers/APLanguageBundleLoader.h</key>
		<dict>
			<key>hash</key>
			<data>
			FOonSKLp2+tLbwxI2oLtKHPPmtg=
			</data>
			<key>hash2</key>
			<data>
			YBJfYQ+qtbxB5NZ1QcD1eUkHVrc9SBBybOk/XF2vT8I=
			</data>
		</dict>
		<key>Headers/APMD5.h</key>
		<dict>
			<key>hash</key>
			<data>
			QiPVr6fJlStNmPat3zFPi7qOFdg=
			</data>
			<key>hash2</key>
			<data>
			MKuwPfjzD9eiqo2e1pKBoPytPqHBg3xGSP+TAQCQYX8=
			</data>
		</dict>
		<key>Headers/APMPaaS.h</key>
		<dict>
			<key>hash</key>
			<data>
			fNrKwOdhVp+x53ccRJn1u231v/o=
			</data>
			<key>hash2</key>
			<data>
			pVLsNOwsF9lzEVinGfs/ornviMBYiV2GlzjTDODp63M=
			</data>
		</dict>
		<key>Headers/APMobileFoundation.h</key>
		<dict>
			<key>hash</key>
			<data>
			iL/kMmrTQ3JP7ve75IMcoYfBI60=
			</data>
			<key>hash2</key>
			<data>
			iUNlLzpliS37ym/yWVajZ1WAuJv4NlPYAkBeU33UEdU=
			</data>
		</dict>
		<key>Headers/APRemoteLogging+MPaaS.h</key>
		<dict>
			<key>hash</key>
			<data>
			pbcXs7YJAUqUjtHLykXXlyQq7wY=
			</data>
			<key>hash2</key>
			<data>
			ydK4v4IWVPJQcYWYfX88DNPaH08R+DtTVpPy2td0uzs=
			</data>
		</dict>
		<key>Headers/APSSKeychain.h</key>
		<dict>
			<key>hash</key>
			<data>
			t7yAHxOez9VRhp3Gg8d5EBu0YCc=
			</data>
			<key>hash2</key>
			<data>
			AiT6cU3gcJzCGWK6HlXJkX3ugEteUF+Sg38yjRRRh4Q=
			</data>
		</dict>
		<key>Headers/APSearchManager.h</key>
		<dict>
			<key>hash</key>
			<data>
			ix+HOeZtAzyZTqaUKskXfLbfba0=
			</data>
			<key>hash2</key>
			<data>
			gMc6eNFFkhMHXM+l6+fn4n10s9lTjGmxybCll8xWZl0=
			</data>
		</dict>
		<key>Headers/APSearchPosition.h</key>
		<dict>
			<key>hash</key>
			<data>
			6iveneZ9CE2RhgppCSQRLY84Lu0=
			</data>
			<key>hash2</key>
			<data>
			LlsfrGGy/nHeOaKSkmaQ2V5tEKLC2CQ/puvL0RDs81w=
			</data>
		</dict>
		<key>Headers/APShareKit+MPaaS.h</key>
		<dict>
			<key>hash</key>
			<data>
			8TAuewEXonSctlc3sRCQ5WKJvjI=
			</data>
			<key>hash2</key>
			<data>
			LIhgXUSy9udeo6vjT2lq1bcXOyyKwAu4IBuZFydDE+Y=
			</data>
		</dict>
		<key>Headers/APThemeManager+MPaaS.h</key>
		<dict>
			<key>hash</key>
			<data>
			gJw91COdb6SsCWJUfG9h+7rVssI=
			</data>
			<key>hash2</key>
			<data>
			65pjxG8E+0qPYlArhJO6rXr7/DTHEwEKvItlPgutFlU=
			</data>
		</dict>
		<key>Headers/APThreadPoolManager.h</key>
		<dict>
			<key>hash</key>
			<data>
			v4CVQtgngrylKuOjT1ftPswfBXk=
			</data>
			<key>hash2</key>
			<data>
			f2QU+Pz8Zkg/q4vm+xbIM4hDaRl4QAyn6NewWgyGMls=
			</data>
		</dict>
		<key>Headers/APThreadTaskMonitor.h</key>
		<dict>
			<key>hash</key>
			<data>
			tAVh8hP6aXux9BgvT6hg+9/FJH8=
			</data>
			<key>hash2</key>
			<data>
			hoagWSDAKQNWzhvqLae3/D/169LKd6cqL5BMmwRvKaQ=
			</data>
		</dict>
		<key>Headers/AUBarButtonItem+MPaaS.h</key>
		<dict>
			<key>hash</key>
			<data>
			YZ3bq92WN506IwrK7Hz/kTSR7Xc=
			</data>
			<key>hash2</key>
			<data>
			2Gf1f9V+PdFChBYLRk7/qqz8H+FGQvmS/Q4zmvRcUZ0=
			</data>
		</dict>
		<key>Headers/DFCrashReport+MPaaS.h</key>
		<dict>
			<key>hash</key>
			<data>
			pmetV71sdDLGYmX9eNH0RetX200=
			</data>
			<key>hash2</key>
			<data>
			t81Mqafrwy2twa7GDUCj+Cj7ivHYQN3ZW/v5EmfIpMI=
			</data>
		</dict>
		<key>Headers/DTDeviceInfo.h</key>
		<dict>
			<key>hash</key>
			<data>
			564g4OSQTQ1hVWUJZUNwLX1o25Q=
			</data>
			<key>hash2</key>
			<data>
			RHxs9xLt9VozQn5jzwQyoDGylN1a5NyY6wdV+/2a5js=
			</data>
		</dict>
		<key>Headers/DTJsonHelper.h</key>
		<dict>
			<key>hash</key>
			<data>
			2ny9qukc1LyhIM9/0n8J7VpHwc0=
			</data>
			<key>hash2</key>
			<data>
			EXM7B/THLk++/vTf3T9qNN1DVtGNZ7mm/xJe1CJBSoo=
			</data>
		</dict>
		<key>Headers/DTMobileFramework+MPaaS.h</key>
		<dict>
			<key>hash</key>
			<data>
			Y4CqwZNU09gZvBJbu4c4gZN1VZk=
			</data>
			<key>hash2</key>
			<data>
			+GIjqp5rH5uyPhUcqxHiKLD9+4n4mBGRPo7OftCQvLY=
			</data>
		</dict>
		<key>Headers/DTNumber.h</key>
		<dict>
			<key>hash</key>
			<data>
			mR/3pyzdTe5ojq2TB+0KUFmKMWY=
			</data>
			<key>hash2</key>
			<data>
			CAswE5eNw5PyKri+iovWF7WbMZqHitH7GgQBPaJxQTI=
			</data>
		</dict>
		<key>Headers/DynamicRelease+MPaaS.h</key>
		<dict>
			<key>hash</key>
			<data>
			uYd6ScjH5nNIYDpUbrLNqTgtm6g=
			</data>
			<key>hash2</key>
			<data>
			w+dgZoICSk6vUTp6V5D0VZ2RfKloEcbgZNRlaHXOngI=
			</data>
		</dict>
		<key>Headers/EBCManager+MPaaS.h</key>
		<dict>
			<key>hash</key>
			<data>
			WORfYN1f34bPeLjB7qEZouguTtw=
			</data>
			<key>hash2</key>
			<data>
			OR+djPj9937htH6wVP1SfE5uLgC+KlgGJZb1OkiNZCE=
			</data>
		</dict>
		<key>Headers/GTMNSString+HTML.h</key>
		<dict>
			<key>hash</key>
			<data>
			h4hpU21k57qTtUhsCdKNZFKvA8w=
			</data>
			<key>hash2</key>
			<data>
			Mg5btQjc5eLV0+v643zE03TaNRdC3kKR/T3X5MgWd18=
			</data>
		</dict>
		<key>Headers/MPAppState.h</key>
		<dict>
			<key>hash</key>
			<data>
			4EMudmpRf7eZ5C+mndLMPtkVxDY=
			</data>
			<key>hash2</key>
			<data>
			oL2J7K4IHC50isqiaFuwzZgrnh66gGBVu3HATWsSneY=
			</data>
		</dict>
		<key>Headers/MPAppStateMaintainer.h</key>
		<dict>
			<key>hash</key>
			<data>
			85Upk/pzkImYVYLFPCiI+lMmuPg=
			</data>
			<key>hash2</key>
			<data>
			OYh4F2nYKYVr7pT4+PcfMS4p2g44T/iz5qbSfI5ebXI=
			</data>
		</dict>
		<key>Headers/MPCryptKit.h</key>
		<dict>
			<key>hash</key>
			<data>
			7ZSh8SgX+Y6sxO6Ub8lvOEvCMz8=
			</data>
			<key>hash2</key>
			<data>
			1bdrlcFkw+bN/Mj+rOtvqmaNDlwnKeN0rLHjkDAxjBY=
			</data>
		</dict>
		<key>Headers/MPDynamicLoader.h</key>
		<dict>
			<key>hash</key>
			<data>
			qSFBAV1EUwq3YZobl3zq46v665Q=
			</data>
			<key>hash2</key>
			<data>
			+puvRCCVI7eTc5lv5eN1Rt666r5e2znl9H95YWtBGug=
			</data>
		</dict>
		<key>Headers/MPFunctionMaintainer.h</key>
		<dict>
			<key>hash</key>
			<data>
			WEi+qIa7QreBNssZkIr2ZXM3Ots=
			</data>
			<key>hash2</key>
			<data>
			ZkU3Q9hAqzk1iXE5ShzOhbtlb1c/uIEtU8Bhd/FkcR8=
			</data>
		</dict>
		<key>Headers/MPJSONKit.h</key>
		<dict>
			<key>hash</key>
			<data>
			CsZYTOtaZiR/19Z2rBSy5ApHmt8=
			</data>
			<key>hash2</key>
			<data>
			V6bbZCo8CTi520ru8A5Mo4D/f2nXRCyWQCgrh4tGS3c=
			</data>
		</dict>
		<key>Headers/MPLiteSettingService+MPaaS.h</key>
		<dict>
			<key>hash</key>
			<data>
			0KKRe44Wyb+27TwBx9dHAwF8kns=
			</data>
			<key>hash2</key>
			<data>
			JXXBkEa1uaC8fhbsb7gEwqaj2JsFwylW5lG8BXsyMYM=
			</data>
		</dict>
		<key>Headers/MPLiteSettingService.h</key>
		<dict>
			<key>hash</key>
			<data>
			RBFIWh3JfHHB7DpVc7zHpKgLEn0=
			</data>
			<key>hash2</key>
			<data>
			kpbPWyqHUnoUBITUFHs/Vk29S4QA1FdqJ93vo4XydI8=
			</data>
		</dict>
		<key>Headers/MPThreadManager.h</key>
		<dict>
			<key>hash</key>
			<data>
			d8IbV/0Fkbf2Tfic5CapSvd5R7Y=
			</data>
			<key>hash2</key>
			<data>
			GFGZMShZfsx0C7j8WuVH4aJxAow7uPZ6hihP4zB3Vlc=
			</data>
		</dict>
		<key>Headers/MPUnification.h</key>
		<dict>
			<key>hash</key>
			<data>
			u1AbqakHP7/AUeDt23M8BrX9aWc=
			</data>
			<key>hash2</key>
			<data>
			VtI3JnGpXg6zmnh9Rnaie/qIXMZT4M2mqyLeFS8hN34=
			</data>
		</dict>
		<key>Headers/MPZipKit.h</key>
		<dict>
			<key>hash</key>
			<data>
			WKLsjYsC7VNBXEEWOOrk9BiK3w8=
			</data>
			<key>hash2</key>
			<data>
			76PeAPtu8B9rCSmZZqQqEjzmORAKxvOeKy8k6yiKBd8=
			</data>
		</dict>
		<key>Headers/MPaaS+Decoupling.h</key>
		<dict>
			<key>hash</key>
			<data>
			YInakdeNKH2IDvOI5tmBcV6JAdU=
			</data>
			<key>hash2</key>
			<data>
			oN5GdcmV6g/I7duVVdLLpr8EK2F7IxhD7YO275Vd87M=
			</data>
		</dict>
		<key>Headers/MPaaS+ImportAPLanguage.h</key>
		<dict>
			<key>hash</key>
			<data>
			wguGzFMNF+NDvFtojkwA2jGP0ds=
			</data>
			<key>hash2</key>
			<data>
			w5a5dp13IR311Zl9uDs5c2Oj0Utx9zAlURm7tcmgAwU=
			</data>
		</dict>
		<key>Headers/MPaaS+ImportAPRemoteLogging.h</key>
		<dict>
			<key>hash</key>
			<data>
			pYFBZaPwI0XIkGZsEC1EWnhY1v4=
			</data>
			<key>hash2</key>
			<data>
			z+DBfzfAxsIRsCe/sTGSep20OQTptErS0zrKeuPnhXE=
			</data>
		</dict>
		<key>Headers/MPaaS+ImportAPShareKit.h</key>
		<dict>
			<key>hash</key>
			<data>
			GJ5LkaD0qe+JVXxtPq3XrOrJsu4=
			</data>
			<key>hash2</key>
			<data>
			C6ds50DsezPutQ3xHgjpQxWlS4yj3leFMJvQFq30wiY=
			</data>
		</dict>
		<key>Headers/MPaaS+ImportAPThemeManager.h</key>
		<dict>
			<key>hash</key>
			<data>
			fIBujAPCWohhCJxNc7eQewZ8izk=
			</data>
			<key>hash2</key>
			<data>
			txRNkTiJdN/G5GoIXTe4pA5iu/omF0AUkJto6NCXf2M=
			</data>
		</dict>
		<key>Headers/MPaaS+ImportAUBarButtonItem.h</key>
		<dict>
			<key>hash</key>
			<data>
			XkIvs0CDmNMAxrng159g9ugHMgo=
			</data>
			<key>hash2</key>
			<data>
			juFIpSGDdepTD0sWcYdhmp3zQCjVMpNnmfc+NsrNqIw=
			</data>
		</dict>
		<key>Headers/MPaaS+ImportDFCrashReport.h</key>
		<dict>
			<key>hash</key>
			<data>
			uumY+mz0B3t0SF8uYlq92MD8gsE=
			</data>
			<key>hash2</key>
			<data>
			y4BtMP8HD50e+eaydnRh2IQsNb1orLo4Jy1DPgelnLM=
			</data>
		</dict>
		<key>Headers/MPaaS+ImportDynamicRelease.h</key>
		<dict>
			<key>hash</key>
			<data>
			VzI25yM0jm03Wu+tlTdsJ9my5Ws=
			</data>
			<key>hash2</key>
			<data>
			c6GpeIqN625xtpxhicbYOYL+cWNp/Sfe6ab6H3ZEDsc=
			</data>
		</dict>
		<key>Headers/MPaaS+ImportMPLiteSettingService.h</key>
		<dict>
			<key>hash</key>
			<data>
			DQgsWRofFntX0sVcYym0m0EAXYo=
			</data>
			<key>hash2</key>
			<data>
			7BlzQ0A7hAPY7DczoI438n42OsekB1V9SuMal3TbgYY=
			</data>
		</dict>
		<key>Headers/MPaaS+MonitorStartUpTime.h</key>
		<dict>
			<key>hash</key>
			<data>
			7ssU/ltE9BIogP+6+YHTMoKKzj0=
			</data>
			<key>hash2</key>
			<data>
			9GB/TgKm23TRuLGUYMWLaxKZ3UX3rieXgpJOvlwnxlM=
			</data>
		</dict>
		<key>Headers/MPaaSConfigInfo.h</key>
		<dict>
			<key>hash</key>
			<data>
			fcPrmifkW3bAXtYvU3RAHStxPu0=
			</data>
			<key>hash2</key>
			<data>
			MAMr3WKqHKADT/ucyeYH15xJocMYqaeWeIhPMJJHJVc=
			</data>
		</dict>
		<key>Headers/MPaaSDVInfo.h</key>
		<dict>
			<key>hash</key>
			<data>
			RvYYrwCjhQ+R9cpvK3NL+PWbsEE=
			</data>
			<key>hash2</key>
			<data>
			U9fLjgValxEeTc2lS95Fyn/UieQjeB3os3KdKbWGkn0=
			</data>
		</dict>
		<key>Headers/MPaaSInterface.h</key>
		<dict>
			<key>hash</key>
			<data>
			76E6DBpsCd8uvXVb0+aaOaAP1F8=
			</data>
			<key>hash2</key>
			<data>
			hjONeaqj0x9Phklf5FU4Sh9xaOOsuOcGMwpvz+Jf5t0=
			</data>
		</dict>
		<key>Headers/MTNetModel.h</key>
		<dict>
			<key>hash</key>
			<data>
			XAhNKTVH9f0FaEmSdjF0Nvoakqo=
			</data>
			<key>hash2</key>
			<data>
			eld5zyv6JAjQaSKJREe9YyTGPznvlIT8fHzFf4o2Pts=
			</data>
		</dict>
		<key>Headers/MTNetStatistic.h</key>
		<dict>
			<key>hash</key>
			<data>
			uOdZ7oMCAHwTrIldD6o78+7JqyY=
			</data>
			<key>hash2</key>
			<data>
			2K04Q5WGd4dSwzaEcCdEH4UpqZpQncfEoVUZhk3fH/0=
			</data>
		</dict>
		<key>Headers/MTNetStatisticProxy.h</key>
		<dict>
			<key>hash</key>
			<data>
			TdqUbgCUSaJ8nFcdc+0E0LdAiTc=
			</data>
			<key>hash2</key>
			<data>
			LuhCAcMVUH+uaN3V0z2s+Gym9LSWfn+cgCT2NNPpJYw=
			</data>
		</dict>
		<key>Headers/MobileFoundation.h</key>
		<dict>
			<key>hash</key>
			<data>
			cSEYiCveA8rXr1LzP1p7kXyRe40=
			</data>
			<key>hash2</key>
			<data>
			PXb+aNzwY942flOD3JIiVN3tksjjTTYqv2uX8fT8GBw=
			</data>
		</dict>
		<key>Headers/NSArray+DTExtensions.h</key>
		<dict>
			<key>hash</key>
			<data>
			6DQRDfGJlIKSZcx6TryhwwemE9g=
			</data>
			<key>hash2</key>
			<data>
			m9KPZhB19c5HA2w8O7MQ9ljwoQ4eyHfRKXlky3LZMp8=
			</data>
		</dict>
		<key>Headers/NSDate+DTJsonString.h</key>
		<dict>
			<key>hash</key>
			<data>
			8s6FAMTUYfpYcmuk660xTF6fK9s=
			</data>
			<key>hash2</key>
			<data>
			N+gUz9FohGXmhACR2py+7uAoJyafQZPqNDf13I3ZBRI=
			</data>
		</dict>
		<key>Headers/NSDictionary+DTExtensions.h</key>
		<dict>
			<key>hash</key>
			<data>
			w3N2jxBb4xGzdhMSTOcbGd5yb28=
			</data>
			<key>hash2</key>
			<data>
			cEoYTeekzlOwkVNfTyswDIhE0mN/M6ACmuz7WfB6Uro=
			</data>
		</dict>
		<key>Headers/NSString+DTRegularExpressionValidator.h</key>
		<dict>
			<key>hash</key>
			<data>
			iNel47xVZQMZlrB8ELvw7WmI4Bs=
			</data>
			<key>hash2</key>
			<data>
			PuMYjLjzW8i2nay1rC6J8l3fjeHffpxoNRfQ6bQ6/bI=
			</data>
		</dict>
		<key>Headers/NSStringURLUtils.h</key>
		<dict>
			<key>hash</key>
			<data>
			l6R6402V2gHgrZUtngij6mvKWt4=
			</data>
			<key>hash2</key>
			<data>
			4sgK64z+G4Vlvnli2RZN754VDGA0KHRNVLzT25skVC4=
			</data>
		</dict>
		<key>Headers/NSStringUtils.h</key>
		<dict>
			<key>hash</key>
			<data>
			ZVT2f/OIXZ8bOruEwJe9bLKuBDM=
			</data>
			<key>hash2</key>
			<data>
			voV4O8TvptX6ixUCzLzYGmbIx1wHfaED+dzjh/ebXg4=
			</data>
		</dict>
		<key>Headers/NSURL+DTExtensions.h</key>
		<dict>
			<key>hash</key>
			<data>
			IelUBWERWyhaIK7RjuckvblVrgg=
			</data>
			<key>hash2</key>
			<data>
			U6c9Ma7LPHvflwleKXlatApRbruzfKH5gOB24wXyr9E=
			</data>
		</dict>
		<key>Headers/NSURL+URLWithFixedString.h</key>
		<dict>
			<key>hash</key>
			<data>
			QSXn8mE7CMmcu5lGf9o9/dZG57o=
			</data>
			<key>hash2</key>
			<data>
			HG0yAd5fMMn+qIZ6pUjfZoeCZPoucH61VOLz/2boyos=
			</data>
		</dict>
		<key>Headers/UIDevice+mPaas.h</key>
		<dict>
			<key>hash</key>
			<data>
			Kfroujg6TvV0i7dzDORmyXkHu8U=
			</data>
			<key>hash2</key>
			<data>
			3fXf2JEufpVIIXQ4DA/uls+ylros52xirM3KeioTWrQ=
			</data>
		</dict>
		<key>Headers/UIImage+Color.h</key>
		<dict>
			<key>hash</key>
			<data>
			wtPdIcChaSdp14W+d7p4r9Re/CE=
			</data>
			<key>hash2</key>
			<data>
			ts57I0OyAK2iIgC9vfRQR0F7I1U+4FkhVwyWQdqlzE0=
			</data>
		</dict>
		<key>Headers/UIView+Helper.h</key>
		<dict>
			<key>hash</key>
			<data>
			So9J9RhNTn1/OxDDZZUEPrXJe94=
			</data>
			<key>hash2</key>
			<data>
			AHDliRhI0Hg/6rb1/LQblqim2QU4iYMGQqFDhUAnwyg=
			</data>
		</dict>
		<key>Headers/UIView+SnapShot.h</key>
		<dict>
			<key>hash</key>
			<data>
			c36MP1Ev4QbFma/BcED7KBdf5Z4=
			</data>
			<key>hash2</key>
			<data>
			z/hzIxZ9XIz4k26ND8d1nQEc1TnvtOaqKE/aQLqtuTk=
			</data>
		</dict>
		<key>Headers/mPaas.h</key>
		<dict>
			<key>hash</key>
			<data>
			WazJO4zOTop9/CkwbNWk/QXZPY8=
			</data>
			<key>hash2</key>
			<data>
			Aqrk0hEImWq7dW3z9MqA0ilqtcOEvS+XEkI0UJ5efvw=
			</data>
		</dict>
		<key>pinyin</key>
		<dict>
			<key>hash</key>
			<data>
			nxGTGQIYv7WblFD6Ztn+y22AqZs=
			</data>
			<key>hash2</key>
			<data>
			CwKsbKwqGEf7rmDTp9JCDnaed4aQnhWYPGW9Yi/h2+E=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
