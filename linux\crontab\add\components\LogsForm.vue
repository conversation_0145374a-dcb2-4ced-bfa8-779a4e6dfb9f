<template>
	<view>
		<!-- 存储路径 -->
		<view class="form-group">
			<view class="form-label-row">
				<text>存储路径</text>
			</view>
			<view class="input-wrapper">
				<uv-input
					:value="formData.sName"
					@input="updateField('sName', $event)"
					placeholder="请输入日志存储路径"
					border="surround"
					:customStyle="{ backgroundColor: 'var(--dialog-bg-color)', fontSize: '28rpx' }"
				/>
			</view>
		</view>

		<!-- 切割日志网站 -->
		<view class="form-group">
			<view class="form-label-row">
				<text>切割日志网站</text>
			</view>
			<button class="region-select-button" @click="showSitePicker" :disabled="isEditMode">
				<text>{{ getSiteLabel() || '请选择网站' }}</text>
				<uv-icon name="arrow-down" size="14" color="#666666"></uv-icon>
			</button>
		</view>

		<!-- 保留份数 -->
		<view class="form-group">
			<view class="form-label-row">
				<text>保留份数</text>
			</view>
			<view class="input-wrapper">
				<uv-input
					:value="formData.save"
					@input="updateField('save', $event)"
					placeholder="请输入保留份数"
					type="number"
					border="surround"
					:customStyle="{ backgroundColor: 'var(--dialog-bg-color)', fontSize: '28rpx' }"
				/>
			</view>
		</view>

		<!-- 进程锁 -->
		<view class="form-group">
			<view class="form-row">
				<text>进程锁</text>
				<uv-switch :value="formData.flock" @change="updateField('flock', $event)" activeColor="#20a53a"></uv-switch>
			</view>
		</view>

		<!-- Picker组件 -->
		<uv-picker
			ref="sitePicker"
			:columns="[siteOptions]"
			keyName="label"
			@confirm="onSiteConfirm"
		></uv-picker>
	</view>
</template>

<script setup>
	import { ref, onMounted, getCurrentInstance } from 'vue';
	import { getCrontabDataList } from '@/api/crontab';
	import { truncateText } from '../useController';

	const { proxy } = getCurrentInstance();

	const props = defineProps({
		formData: {
			type: Object,
			required: true
		},
		isEditMode: {
			type: Boolean,
			default: false
		}
	});

	const emit = defineEmits(['update:formData']);

	// 数据选项
	const siteOptions = ref([{ label: '所有[所有网站]', value: 'ALL' }]);

	// Picker引用
	const sitePicker = ref(null);

	// 更新字段
	const updateField = (field, event) => {
		const value = event.detail?.value || event.target?.value || event;
		emit('update:formData', { [field]: value });
	};

	// 显示选择器
	const showSitePicker = () => {
		proxy.$refs.sitePicker?.open();
	};

	// 获取显示标签
	const getSiteLabel = () => {
		const option = siteOptions.value.find(item => item.value === props.formData.urladdress);
		return option ? truncateText(option.label) : '';
	};

	// 确认选择
	const onSiteConfirm = (e) => {
		const selectedValue = e.value[0].value;
		emit('update:formData', {
			urladdress: selectedValue,
			name: `切割日志[ ${selectedValue === 'ALL' ? '所有网站' : selectedValue} ]`
		});
	};

	// 获取数据
	const loadData = async () => {
		try {
			const res = await getCrontabDataList({ type: 'sites' });
			
			// 设置网站选项
			siteOptions.value = [{ label: '所有[所有网站]', value: 'ALL' }].concat(
				res.data.map(item => ({
					label: `${item.name}[${item.ps}]`,
					value: item.name
				}))
			);

			// 初始化默认值
			if (!props.formData.sName) {
				emit('update:formData', { 
					sName: '/www/wwwlogs',
					urladdress: 'ALL',
					save: '180',
					name: '切割日志[ 所有网站 ]'
				});
			}
		} catch (error) {
			console.error('加载数据失败:', error);
		}
	};

	onMounted(() => {
		loadData();
	});
</script>

<style lang="scss" scoped>
	@import './form-styles.scss';

	.form-row {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-top: 20rpx;

		text {
			font-size: 28rpx;
			font-weight: 500;
			color: var(--text-color-primary);
		}
	}
</style>
