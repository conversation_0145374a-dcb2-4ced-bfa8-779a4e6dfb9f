/**
 * 数据类型校验工具函数
 */

/**
 * 检查值是否为数组
 * @param {*} value - 要检查的值
 * @returns {boolean} - 是否为数组
 */
export const isArray = (value) => {
  return Array.isArray(value);
};

/**
 * 检查值是否为对象
 * @param {*} value - 要检查的值
 * @returns {boolean} - 是否为对象
 */
export const isObject = (value) => {
  return value !== null && typeof value === 'object' && !Array.isArray(value);
};

/**
 * 检查值是否为空对象
 * @param {*} value - 要检查的值
 * @returns {boolean} - 是否为空对象
 */
export const isEmptyObject = (value) => {
  return isObject(value) && Object.keys(value).length === 0;
};

/**
 * 检查值是否为字符串
 * @param {*} value - 要检查的值
 * @returns {boolean} - 是否为字符串
 */
export const isString = (value) => {
  return typeof value === 'string';
};

/**
 * 检查值是否为空字符串
 * @param {*} value - 要检查的值
 * @returns {boolean} - 是否为空字符串
 */
export const isEmptyString = (value) => {
  return isString(value) && value.trim().length === 0;
};

/**
 * 检查值是否为数字
 * @param {*} value - 要检查的值
 * @returns {boolean} - 是否为数字
 */
export const isNumber = (value) => {
  return typeof value === 'number' && !isNaN(value);
};

/**
 * 检查值是否为整数
 * @param {*} value - 要检查的值
 * @returns {boolean} - 是否为整数
 */
export const isInteger = (value) => {
  return Number.isInteger(value);
};

/**
 * 检查值是否为布尔值
 * @param {*} value - 要检查的值
 * @returns {boolean} - 是否为布尔值
 */
export const isBoolean = (value) => {
  return typeof value === 'boolean';
};

/**
 * 检查值是否为函数
 * @param {*} value - 要检查的值
 * @returns {boolean} - 是否为函数
 */
export const isFunction = (value) => {
  return typeof value === 'function';
};

/**
 * 检查值是否为 null
 * @param {*} value - 要检查的值
 * @returns {boolean} - 是否为 null
 */
export const isNull = (value) => {
  return value === null;
};

/**
 * 检查值是否为 undefined
 * @param {*} value - 要检查的值
 * @returns {boolean} - 是否为 undefined
 */
export const isUndefined = (value) => {
  return value === undefined;
};

/**
 * 检查值是否为空（null、undefined、空字符串、空数组、空对象）
 * @param {*} value - 要检查的值
 * @returns {boolean} - 是否为空
 */
export const isEmpty = (value) => {
  if (isNull(value) || isUndefined(value)) return true;
  if (isString(value)) return isEmptyString(value);
  if (isArray(value)) return value.length === 0;
  if (isObject(value)) return isEmptyObject(value);
  return false;
};

/**
 * 检查值是否为有效值（非空、非undefined、非null）
 * @param {*} value - 要检查的值
 * @returns {boolean} - 是否为有效值
 */
export const isValid = (value) => {
  return !isNull(value) && !isUndefined(value);
};

/**
 * 检查对象是否包含指定的键
 * @param {Object} obj - 要检查的对象
 * @param {string} key - 要检查的键
 * @returns {boolean} - 是否包含指定的键
 */
export const hasKey = (obj, key) => {
  return isObject(obj) && Object.prototype.hasOwnProperty.call(obj, key);
};

/**
 * 检查对象是否包含指定的键，且该键的值满足条件
 * @param {Object} obj - 要检查的对象
 * @param {string} key - 要检查的键
 * @param {*} value - 要匹配的值（可选）
 * @param {Function} predicate - 值的判断函数（可选）
 * @returns {boolean} - 是否满足条件
 */
export const hasKeyValue = (obj, key, value, predicate) => {
  if (!hasKey(obj, key)) return false;

  if (predicate && typeof predicate === 'function') {
    return predicate(obj[key]);
  }

  if (arguments.length === 3) {
    return obj[key] === value;
  }

  return true;
};
