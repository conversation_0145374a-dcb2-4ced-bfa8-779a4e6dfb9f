# BtButton 按钮组件

BtButton是基于uv-ui的uv-button二次封装的组件，简化了配置，预设了更符合实际使用场景的默认值。

## 引入方式

```js
// 页面内引入
import BtButton from '@/components/BtButton/index.vue'
```

## 基础用法

```vue
<template>
  <!-- 基础用法 -->
  <BtButton text="确定按钮" @click="clickHandler" />

  <!-- 不同类型的按钮 -->
  <BtButton text="主要按钮" type="primary" />
  <BtButton text="信息按钮" type="info" />
  <BtButton text="警告按钮" type="warning" />
  <BtButton text="危险按钮" type="error" />
  <BtButton text="成功按钮" type="success" />

  <!-- 不同尺寸的按钮 -->
  <BtButton text="大号按钮" size="large" />
  <BtButton text="普通按钮" size="normal" />
  <BtButton text="小型按钮" size="small" />
  <BtButton text="迷你按钮" size="mini" />

  <!-- 镂空按钮 -->
  <BtButton text="镂空按钮" :plain="true" />

  <!-- 禁用状态 -->
  <BtButton text="禁用按钮" :disabled="true" />

  <!-- 加载状态 -->
  <BtButton text="加载中" :loading="true" />

  <!-- 带图标的按钮 -->
  <BtButton text="图标按钮" icon="map" />

  <!-- 圆形按钮 -->
  <BtButton text="圆形按钮" shape="circle" />

  <!-- 自定义颜色 -->
  <BtButton text="渐变色按钮" color="linear-gradient(to right, rgb(66, 83, 216), rgb(213, 51, 186))" />

  <!-- 使用插槽自定义内容 -->
  <BtButton type="primary">
    自定义内容
  </BtButton>

  <!-- 使用后缀插槽 -->
  <BtButton text="查看更多">
    <template #suffix>
      <uv-icon name="arrow-right"></uv-icon>
    </template>
  </BtButton>
</template>

<script setup>
const clickHandler = () => {
  console.log('按钮被点击');
}
</script>
```

## API

### Props

| 参数 | 说明 | 类型 | 默认值 |
| --- | --- | --- | --- |
| text | 按钮文字 | String, Number | '' |
| type | 按钮类型 | String | 'primary' |
| size | 按钮尺寸 | String | 'normal' |
| shape | 按钮形状 | String | 'square' |
| plain | 是否镂空 | Boolean | false |
| hairline | 是否细边框 | Boolean | false |
| disabled | 是否禁用 | Boolean | false |
| loading | 是否加载中 | Boolean | false |
| icon | 图标名称 | String | '' |
| color | 按钮颜色，支持渐变色 | String | '' |
| customStyle | 自定义按钮样式 | Object, String | {} |
| customTextStyle | 自定义文字样式 | Object, String | {} |

### 按钮类型

| 类型值 | 说明 |
| --- | --- |
| primary | 主要按钮 |
| info | 信息按钮 |
| warning | 警告按钮 |
| error | 危险按钮 |
| success | 成功按钮 |

### 按钮尺寸

| 尺寸值 | 说明 |
| --- | --- |
| large | 大号按钮 |
| normal | 普通按钮 |
| small | 小号按钮 |
| mini | 迷你按钮 |

### 事件

| 事件名 | 说明 | 回调参数 |
| --- | --- | --- |
| click | 点击按钮时触发 | event: Event |

### 插槽

| 插槽名 | 说明 |
| --- | --- |
| default | 按钮内容插槽 |
| suffix | 后缀内容插槽 |
``` 