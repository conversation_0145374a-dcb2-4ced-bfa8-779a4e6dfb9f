import { defineStore } from 'pinia';
import { ref } from 'vue';
import { storeToRefs } from 'pinia';

export const useConfigStore = defineStore('config', () => {
  const configList = ref(uni.getStorageSync('configList') || []);
  const accountList = ref(uni.getStorageSync('accountList') || []);
  const currentServerInfo = ref(null);
  const phoneBrand = ref('');
  const phoneModel = ref('');
  const unlockType = ref('');
  const panelVersion = ref('');
  const harmonySslVerification = ref(uni.getStorageSync('harmonySslVerification') || false);

  // 辅助函数，获取响应式状态
  function getReactiveState() {
    const store = useConfigStore();
    return {
      ...storeToRefs(store),
    };
  }
  return { configList, getReactiveState, phoneBrand, phoneModel, accountList, currentServerInfo, unlockType, panelVersion, harmonySslVerification };
});
