import { computed, ref, watch, markRaw } from 'vue';
import ThemeSwitch from '@/components/ThemeSwitch/index.vue';
import SslVerification from '@/pages/index/setting/sslVerification/index.vue';
import { $t } from '@/locale/index.js';
import { useConfigStore } from '@/store/modules/config';

const theme = ref(uni.getStorageSync('app_theme'));
export const appUnlock = ref(uni.getStorageSync('appUnlock'));
export const unlockModel = ref(false);
export const pageContainer = ref(null);

// 处理主题变更事件
const handleThemeChange = (newTheme) => {
	theme.value = newTheme;
	// 这里可以添加额外的主题变更处理逻辑
	cellList.value[0].title =
		$t('setting.theme') + ': ' + (newTheme === 'dark' ? $t('setting.dark') : $t('setting.light'));
	uni.setTabBarStyle({
		backgroundColor: newTheme === 'dark' ? '#2c2c2c' : '#ededed',
	});
};

// Cell 配置列表 - 按照分组组织
export const settingGroups = computed(() => [
	{
		title: $t('setting.general'),
		items: [
			{
				icon: 'color-filled',
				iconType: 'uni',
				title: $t('setting.theme') + ': ' + (theme.value === 'dark' ? $t('setting.dark') : $t('setting.light')),
				slotComponent: {
					component: markRaw(ThemeSwitch),
					props: {},
					events: {
						'theme-changed': handleThemeChange,
					},
				},
				isLink: true,
				arrow: true,
			},
			// {
			//     icon: 'settings',
			//     iconType: 'uni',
			//     title: '自定义底部导航栏',
			//     isLink: true,
			//     arrow: true,
			//     onClick: () => {
			//         // 自定义导航栏设置页面
			//         uni.navigateTo({
			//             url: '/pages/index/setting/navbar/index'
			//         })
			//     }
			// }
		],
	},
	{
		title: $t('setting.security'),
		items: [
			{
				icon: 'locked',
				iconType: 'uni',
				title: $t('setting.graphicUnlock'),
				value: appUnlock.value === 'open' ? $t('setting.enabled') : $t('setting.disabled'),
				isLink: true,
				arrow: true,
				onClick: () => {
					// 图形解锁设置页面
					handleLock();
				},
			},
			{
				icon: 'locked-filled',
				iconType: 'uni',
				title: $t('setting.modifyGraphicPassword'),
				isLink: true,
				arrow: true,
				onClick: () => {
					// 图形解锁设置页面
					handleAppUnlock();
				},
			},
			// #ifdef APP-HARMONY
			{
				icon: 'locked',
				iconType: 'uni',
				title: '跳过网络SSL验证',
				slotComponent: {
					component: markRaw(SslVerification),
					props: {},
					events: {},
				},
			},
			// #endif
		],
	},
	{
		title: $t('setting.about'),
		items: [
			{
				icon: 'info-filled',
				iconType: 'uni',
				title: $t('setting.aboutBtApp'),
				isLink: true,
				arrow: true,
				onClick: () => {
					// 关于页面
					uni.navigateTo({
						url: '/pages/index/setting/about/index',
						animationType: 'zoom-fade-out',
					});
				},
			},
			{
				icon: 'chat-filled',
				iconType: 'uni',
				title: '',
				isUniLink: true,
				href: 'https://qm.qq.com/q/UuY9tIuwgI',
				linkText: $t('setting.feedback'),
				isLink: true,
				arrow: true,
			},
		],
	},
]);

export const handleLock = () => {
	if (appUnlock.value !== 'open') {
		uni.setStorageSync('appUnlock', 'open');
		uni.setStorageSync('appLogin', []);
		uni.navigateTo({
			url: '/pages/blank/index',
			animationType: 'zoom-fade-out',
		});
	} else {
		unlockModel.value = true;
	}
};

export const confirmCloseUnlock = (close) => {
	const { unlockType } = useConfigStore().getReactiveState();
	unlockType.value = '';
	appUnlock.value = 'close';
	uni.setStorageSync('appUnlock', 'close');
	uni.setStorageSync('appLogin', []);
	pageContainer.value.notify.success($t('setting.closeSuccess'));
	close && close();
};

const handleAppUnlock = () => {
	let checkLock = appUnlock.value === 'open' ? true : false;
	if (checkLock && uni.getStorageSync('appLogin').length > 0) {
		const { unlockType } = useConfigStore().getReactiveState();
		unlockType.value = 'reset';
		uni.navigateTo({
			url: '/pages/blank/index',
			animationType: 'zoom-fade-out',
		});
	} else {
		pageContainer.value.notify.error($t('setting.enableGraphicUnlockFirst'));
	}
};

// 为了保持与现有代码兼容，同时提供扁平结构的列表
export const cellList = computed(() => {
	const flatList = [];
	settingGroups.value.forEach((group) => {
		group.items.forEach((item) => {
			flatList.push(item);
		});
	});
	return flatList;
});
