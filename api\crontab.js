import axios from '@/api/request';

const DEFAULT_API_TYPE = import.meta.env.VITE_PANEL_OWNER || 'domestic';

/**
 * @description 获取计划任务列表
 * @param {Object} data - 请求参数
 * @param {number|string} data.type_id - 任务类型ID (可选)
 * @param {string} data.search - 搜索关键字 (可选)
 * @param {string} data.order_param - 排序参数 (可选)
 * @param {number} data.count - 每页数量 (可选)
 * @param {number} data.p - 页码 (可选)
 * @returns {Promise<Object>} 返回计划任务列表
 */
export const getCrontabList = (data) => {
	const url = DEFAULT_API_TYPE === 'domestic' ? '/crontab?action=GetCrontab' : '/v2/crontab?action=GetCrontab';
	return axios(url, data, 'POST');
};

/**
 * @description 获取计划任务类型列表
 * @returns {Promise<Object>} 返回任务类型列表
 */
export const getCrontabTypes = () => {
	const url = DEFAULT_API_TYPE === 'domestic' ? '/crontab?action=GetType' : '/v2/crontab?action=GetType';
	return axios(url);
};

/**
 * @description 添加计划任务
 * @param {Object} data - 任务数据
 * @returns {Promise<Object>} 返回添加结果
 */
export const addCrontab = (data) => {
	const url = DEFAULT_API_TYPE === 'domestic' ? '/crontab?action=AddCrontab' : '/v2/crontab?action=AddCrontab';
	return axios(url, data, 'POST');
};

/**
 * @description 修改计划任务
 * @param {Object} data - 任务数据
 * @returns {Promise<Object>} 返回修改结果
 */
export const modifyCrontab = (data) => {
	const url = DEFAULT_API_TYPE === 'domestic' ? '/crontab?action=ModifyCrontab' : '/v2/crontab?action=ModifyCrontab';
	return axios(url, data, 'POST');
};

/**
 * @description 保存计划任务 (modify_crond)
 * @param {Object} data - 任务数据，参数和添加一致
 * @returns {Promise<Object>} 返回保存结果
 */
export const modifyCrond = (data) => {
	const url = DEFAULT_API_TYPE === 'domestic' ? '/crontab?action=modify_crond' : '/v2/crontab?action=modify_crond';
	return axios(url, data, 'POST');
};

/**
 * @description 删除计划任务
 * @param {Object} data - 删除参数
 * @param {number} data.id - 任务ID
 * @returns {Promise<Object>} 返回删除结果
 */
export const deleteCrontab = (data) => {
	const url = DEFAULT_API_TYPE === 'domestic' ? '/crontab?action=DelCrontab' : '/v2/crontab?action=DelCrontab';
	return axios(url, data, 'POST');
};

/**
 * @description 启用/禁用计划任务
 * @param {Object} data - 状态参数
 * @param {number} data.id - 任务ID
 * @param {string} data.if_stop - 是否停止 ('True': 停止, 'False': 启动)
 * @returns {Promise<Object>} 返回状态切换结果
 */
export const setCrontabStatus = (data) => {
	const url =
		DEFAULT_API_TYPE === 'domestic' ? '/crontab?action=set_cron_status' : '/v2/crontab?action=set_cron_status';
	return axios(url, data, 'POST');
};

/**
 * @description 获取计划任务日志路径
 * @param {Object} data - 日志参数
 * @param {number} data.id - 任务ID
 * @returns {Promise<Object>} 返回日志路径和大小信息，格式：{log_path: string, size: string}
 */
export const getCrontabLogPath = (data) => {
	const url = DEFAULT_API_TYPE === 'domestic' ? '/crontab?action=get_log_path' : '/v2/crontab?action=get_log_path';
	return axios(url, data, 'POST');
};

/**
 * @description 获取计划任务日志
 * @param {Object} data - 日志参数
 * @param {number} data.id - 任务ID
 * @param {number} data.start_timestamp - 开始时间戳 (可选)
 * @param {number} data.end_timestamp - 结束时间戳 (可选)
 * @returns {Promise<Object>} 返回任务日志，格式：{msg: string, status: boolean}
 */
export const getCrontabLog = (data) => {
	const url = DEFAULT_API_TYPE === 'domestic' ? '/crontab?action=GetLogs' : '/v2/crontab?action=GetLogs';
	return axios(url, data, 'POST');
};

/**
 * @description 执行计划任务
 * @param {Object} data - 执行参数
 * @param {number} data.id - 任务ID
 * @returns {Promise<Object>} 返回执行结果
 */
export const startTask = (data) => {
	const url = DEFAULT_API_TYPE === 'domestic' ? '/crontab?action=StartTask' : '/v2/crontab?action=StartTask';
	return axios(url, data, 'POST');
};

/**
 * @description 获取计划任务数据列表
 * @param {Object} data - 请求参数
 * @returns {Promise<Object>} 返回数据列表
 */
export const getCrontabDataList = (data) => {
	const url = DEFAULT_API_TYPE === 'domestic' ? '/crontab?action=GetDataList' : '/v2/crontab?action=GetDataList';
	return axios(url, data, 'POST');
};

/**
 * @description 获取数据库列表
 * @param {Object} data - 请求参数
 * @param {string} data.db_type - 数据库类型 (mysql, mongodb, redis, pgsql)
 * @returns {Promise<Object>} 返回数据库列表，数据结构：[{name: "888", ps: "888", table_list: [{tb_name: "所有", value: "", cron_id: null}, {tb_name: "123", value: "123", cron_id: null}]}]
 */
export const getDatabases = (data) => {
	const url = DEFAULT_API_TYPE === 'domestic' ? '/crontab?action=GetDatabases' : '/v2/crontab?action=GetDatabases';
	return axios(url, data, 'POST');
};

/**
 * @description 获取执行用户列表
 * @param {Object} data - 请求参数
 * @param {boolean} data.all_user - 是否获取所有用户，默认为true
 * @returns {Promise<Array<string>>} 返回用户名数组，如：["root", "www", "bin", "daemon", "adm", "lp", "sync", "shutdown", "halt", "mail", "operator", "games"]
 */
export const getSystemUserListForCrontab = (data) => {
	const url =
		DEFAULT_API_TYPE === 'domestic'
			? '/crontab?action=get_system_user_list'
			: '/v2/crontab?action=get_system_user_list';
	return axios(url, data, 'POST');
};

/**
 * @description 添加MySQL增量备份计划任务
 * @param {Object} data - 任务数据
 * @returns {Promise<Object>} 返回添加结果
 */
export const addMysqlIncrementCrontab = (data) => {
	const url =
		DEFAULT_API_TYPE === 'domestic'
			? '/crontab?action=AddMysqlIncrementCrontab'
			: '/v2/crontab?action=AddMysqlIncrementCrontab';
	return axios(url, data, 'POST');
};

/**
 * @description 修改MySQL增量备份计划任务
 * @param {Object} data - 任务数据
 * @returns {Promise<Object>} 返回修改结果
 */
export const modifyMysqlIncrementCrontab = (data) => {
	const url =
		DEFAULT_API_TYPE === 'domestic'
			? '/crontab?action=ModifyMysqlIncrementCrontab'
			: '/v2/crontab?action=ModifyMysqlIncrementCrontab';
	return axios(url, data, 'POST');
};

/**
 * @description 清空计划任务日志
 * @param {Object} data - 清空参数
 * @param {number} data.id - 任务ID
 * @returns {Promise<Object>} 返回清空结果，格式：{msg: "任务日志已清空!", status: true}
 */
export const clearCrontabLog = (data) => {
	const url = DEFAULT_API_TYPE === 'domestic' ? '/crontab?action=DelLogs' : '/v2/crontab?action=DelLogs';
	return axios(url, data, 'POST');
};
