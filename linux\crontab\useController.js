import { ref, nextTick, computed } from 'vue';
import { getCrontabList as getCrontabListApi, setCrontabStatus, startTask, deleteCrontab } from '@/api/crontab';
import { triggerVibrate } from '@/utils/common';
import { throttle } from '@/uni_modules/uni-easyinput/components/uni-easyinput/common.js';

export const pageContainer = ref(null);

// 分页相关
export const crontabPaging = ref(null);

// 当前选中的分类 (0: 运行中, 1: 已停止)
export const currentTab = ref(0);

// 上下文菜单相关
export const showContextMenu = ref(false);
export const activeCrontab = ref(null);
export const activeIndex = ref(-1);
export const menuPosition = ref({
	top: '0px',
	left: '0px',
	class: '',
});
export const clonePosition = ref({
	top: '0px',
	left: '0px',
	width: '0px',
	height: '0px',
});

// 可编辑的任务类型
const editableTaskTypes = ['toShell', 'site', 'database'];

// 检查当前任务是否可编辑
export const isCurrentTaskEditable = computed(() => {
	if (!activeCrontab.value) return false;
	return editableTaskTypes.includes(activeCrontab.value.sType);
});

// 触摸相关
export const touchStartTime = ref(0);
export const touchStartPos = ref({ x: 0, y: 0 });
export const isTouchMoved = ref(false);
export const longPressTimer = ref(null);
export const LONG_PRESS_THRESHOLD = 600; // 长按阈值，单位毫秒
export const MOVE_THRESHOLD = 10; // 移动阈值，单位像素

// 临时菜单状态
export const showTempMenu = ref(false);

// 菜单高度管理
export const actualMenuHeight = ref(140);

// 确认对话框状态
export const showStartDialog = ref(false);
export const showStopDialog = ref(false);
export const showDeleteDialog = ref(false);

/**
 * 获取计划任务列表
 */
export const getCrontabList = async (page, pageSize) => {
	try {
		// type_id: -2 运行中的数据, -3 停止的数据
		const type_id = currentTab.value === 0 ? -2 : -3;

		const res = await getCrontabListApi({
			p: page,
			count: pageSize,
			type_id: type_id,
		});

		return res.data || [];
	} catch (error) {
		console.error('获取计划任务列表失败:', error);
		return [];
	}
};

/**
 * 点击计划任务项
 */
const crontabClickCore = (crontab) => {
	// 可以在这里添加点击处理逻辑，比如跳转到详情页
	console.log('点击计划任务:', crontab);
};

export const handleCrontabClick = throttle(crontabClickCore, 2000, 1);

/**
 * 触摸开始
 */
export const handleTouchStart = (event) => {
	// 清除可能存在的定时器
	if (longPressTimer.value) {
		clearTimeout(longPressTimer.value);
	}

	// 记录触摸开始时间和位置
	touchStartTime.value = Date.now();
	touchStartPos.value = {
		x: event.touches[0].clientX,
		y: event.touches[0].clientY,
	};
	isTouchMoved.value = false;

	// 设置长按定时器
	longPressTimer.value = setTimeout(() => {
		if (!isTouchMoved.value) {
			const index = event.currentTarget.dataset.index;
			const crontabData = JSON.parse(event.currentTarget.dataset.crontab);
			showFloatingMenu(crontabData, event, index);
		}
	}, LONG_PRESS_THRESHOLD);
};

/**
 * 触摸移动
 */
export const handleTouchMove = (event) => {
	const currentPos = {
		x: event.touches[0].clientX,
		y: event.touches[0].clientY,
	};

	const deltaX = Math.abs(currentPos.x - touchStartPos.value.x);
	const deltaY = Math.abs(currentPos.y - touchStartPos.value.y);

	if (deltaX > MOVE_THRESHOLD || deltaY > MOVE_THRESHOLD) {
		isTouchMoved.value = true;
		if (longPressTimer.value) {
			clearTimeout(longPressTimer.value);
			longPressTimer.value = null;
		}
	}
};

/**
 * 触摸结束
 */
export const handleTouchEnd = () => {
	if (longPressTimer.value) {
		clearTimeout(longPressTimer.value);
		longPressTimer.value = null;
	}
};

/**
 * 触摸取消
 */
export const handleTouchCancel = () => {
	if (longPressTimer.value) {
		clearTimeout(longPressTimer.value);
		longPressTimer.value = null;
	}
};

/**
 * 显示悬浮菜单 - 两阶段定位
 */
export const showFloatingMenu = (crontab, event, index) => {
	// 触感反馈
	triggerVibrate();

	activeCrontab.value = crontab;
	activeIndex.value = index;

	// 获取系统信息，用于检测是否会超出屏幕
	const systemInfo = uni.getSystemInfoSync();
	const screenHeight = systemInfo.windowHeight;
	const screenWidth = systemInfo.windowWidth;

	// 获取被长按元素相对于页面的位置
	uni.createSelectorQuery()
		.selectAll('.crontab-item-container')
		.boundingClientRect((rects) => {
			if (!rects || !rects[index]) return;

			const rect = rects[index];

			// 设置克隆项位置
			clonePosition.value = {
				top: `${rect.top}px`,
				left: `${rect.left}px`,
				width: `${rect.width}px`,
				height: `${rect.height}px`,
			};

			// 预估参数
			const tabbarHeight = 60; // 底部导航栏高度
			const headerHeight = 50; // 顶部标题栏高度
			const menuWidth = 340; // 菜单宽度
			const edgeBuffer = 5; // 边缘安全距离 - 减小以使菜单更贴近克隆项

			// 计算菜单位置
			let menuTop,
				menuLeft,
				menuClass = '';

			// 水平定位 - 居中显示，但保持在屏幕内
			menuLeft = rect.left + rect.width / 2;
			// 防止菜单超出屏幕左侧
			if (menuLeft - menuWidth / 2 < edgeBuffer) {
				menuLeft = menuWidth / 2 + edgeBuffer;
			}
			// 防止菜单超出屏幕右侧
			if (menuLeft + menuWidth / 2 > screenWidth - edgeBuffer) {
				menuLeft = screenWidth - menuWidth / 2 - edgeBuffer;
			}

			// 垂直定位 - 计算上下空间
			const spaceAbove = rect.top - headerHeight;
			const spaceBelow = screenHeight - rect.bottom - tabbarHeight;

			// 优先考虑下方显示，如果下方空间不足，再考虑上方
			if (spaceBelow >= actualMenuHeight.value + edgeBuffer) {
				// 下方有足够空间
				menuTop = rect.bottom + edgeBuffer;
				menuClass = 'menu-bottom';
			} else if (spaceAbove >= actualMenuHeight.value + edgeBuffer) {
				// 上方有足够空间 - 菜单底部紧贴克隆项顶部
				menuTop = rect.top - actualMenuHeight.value - edgeBuffer;
				menuClass = 'menu-top menu-position-bottom'; // 添加菜单位置标记
			} else {
				// 两边都没有理想空间，选择空间较大的一边
				if (spaceBelow >= spaceAbove) {
					// 使用下方剩余空间
					menuTop = rect.bottom + edgeBuffer;
					menuClass = 'menu-bottom';
				} else {
					// 使用上方剩余空间 - 菜单底部紧贴克隆项顶部
					menuTop = rect.top - actualMenuHeight.value - edgeBuffer;
					menuClass = 'menu-top menu-position-bottom';
				}
			}

			// 设置菜单初始位置和样式
			menuPosition.value = {
				top: `${menuTop}px`,
				left: `${menuLeft}px`,
				class: menuClass,
			};

			// 显示菜单
			showContextMenu.value = true;

			// 第二阶段：菜单渲染完成后进行精确定位
			nextTick(() => {
				uni.createSelectorQuery()
					.select('.context-menu')
					.boundingClientRect((menuRect) => {
						if (menuRect) {
							// 更新实际菜单高度
							actualMenuHeight.value = menuRect.height;

							// 如果实际菜单宽度与预估不同，调整水平居中
							const actualMenuWidth = menuRect.width;
							if (Math.abs(actualMenuWidth - menuWidth) > 10) {
								// 重新计算水平位置
								let adjustedLeft = rect.left + rect.width / 2;
								if (adjustedLeft - actualMenuWidth / 2 < edgeBuffer) {
									adjustedLeft = actualMenuWidth / 2 + edgeBuffer;
								}
								if (adjustedLeft + actualMenuWidth / 2 > screenWidth - edgeBuffer) {
									adjustedLeft = screenWidth - actualMenuWidth / 2 - edgeBuffer;
								}
								menuPosition.value.left = `${adjustedLeft}px`;
							}
						}
					})
					.exec();
			});
		})
		.exec();
};

/**
 * 隐藏上下文菜单
 */
export const hideContextMenu = () => {
	showContextMenu.value = false;
	activeCrontab.value = null;
	activeIndex.value = -1;
};

/**
 * 测量菜单高度
 */
export const measureMenuHeight = () => {
	// 显示临时测量菜单
	showTempMenu.value = true;

	// 等待临时菜单渲染完成
	nextTick(() => {
		uni.createSelectorQuery()
			.select('.temp-measure-menu')
			.boundingClientRect((rect) => {
				if (rect && rect.height > 0) {
					actualMenuHeight.value = rect.height;
				}
				// 隐藏临时菜单
				showTempMenu.value = false;
			})
			.exec();
	});
};

/**
 * 编辑计划任务
 */
const editCrontabCore = () => {
	const crontab = activeCrontab.value;

	if (!crontab) {
		pageContainer.value.notify.error('任务信息获取失败');
		return;
	}

	// 检查任务类型是否可编辑
	if (!editableTaskTypes.includes(crontab.sType)) {
		hideContextMenu();
		pageContainer.value.notify.error('该类型任务不支持编辑');
		return;
	}

	hideContextMenu();

	// 跳转到编辑页面，传递任务数据
	uni.navigateTo({
		url: `/linux/crontab/add/index?mode=edit&id=${crontab.id}&data=${encodeURIComponent(JSON.stringify(crontab))}`,
		animationType: 'zoom-fade-out',
	});
};

export const handleEditCrontab = throttle(editCrontabCore, 2000, 1);

/**
 * 查看日志
 */
const viewLogCore = () => {
	const crontab = activeCrontab.value;
	if (!crontab) {
		pageContainer.value.notify.error('任务信息获取失败');
		return;
	}

	hideContextMenu();

	// 跳转到日志页面，传递任务ID和相关参数
	uni.navigateTo({
		url: `/linux/crontab/log/index?taskId=${crontab.id}&taskName=${encodeURIComponent(
			crontab.rname || crontab.name || '',
		)}`,
		animationType: 'zoom-fade-out',
	});
};

export const handleViewLog = throttle(viewLogCore, 2000, 1);

/**
 * 删除计划任务
 */
const deleteCrontabCore = () => {
	showDeleteDialog.value = true;
};

export const handleDeleteCrontab = throttle(deleteCrontabCore, 2000, 1);

/**
 * 显示启动确认对话框
 */
const startConfirmCore = () => {
	showStartDialog.value = true;
};

export const showStartConfirm = throttle(startConfirmCore, 2000, 1);

/**
 * 显示停止确认对话框
 */
const stopConfirmCore = () => {
	showStopDialog.value = true;
};

export const showStopConfirm = throttle(stopConfirmCore, 2000, 1);

/**
 * 确认启动计划任务
 */
export const confirmStartCrontab = async (close) => {
	close && close();
	await handleStartCrontab(activeCrontab.value);
};

/**
 * 确认停止计划任务
 */
export const confirmStopCrontab = async (close) => {
	close && close();
	await handleStopCrontab(activeCrontab.value);
};

/**
 * 确认删除计划任务
 */
export const confirmDeleteCrontab = async (close) => {
	close && close();

	// 保存当前任务信息，因为 hideContextMenu 会清空 activeCrontab
	const crontab = activeCrontab.value;
	if (!crontab?.id) {
		pageContainer.value.notify.error('任务信息获取失败');
		return;
	}

	hideContextMenu();
	uni.showLoading({
		title: '删除中...',
	});

	try {
		const res = await deleteCrontab({
			id: Number.parseInt(crontab.id),
		});

		if (res.status) {
			pageContainer.value.notify.success(res.msg || '删除成功');
			crontabPaging.value.reload();
		} else {
			pageContainer.value.notify.error(res.msg || '删除失败');
		}
	} catch (error) {
		console.error(error);
		pageContainer.value.notify.error('删除失败');
	} finally {
		uni.hideLoading();
	}
};

/**
 * 启动计划任务
 */
export const handleStartCrontab = async (crontab) => {
	uni.showLoading({
		title: '启动中...',
	});
	try {
		const res = await setCrontabStatus({
			id: Number.parseInt(crontab.id),
			if_stop: 'False',
		});
		if (res.status) {
			pageContainer.value.notify.success(res.msg);
			hideContextMenu();
			crontabPaging.value.reload();
		} else {
			pageContainer.value.notify.error(res.msg);
		}
	} catch (error) {
		console.error(error);
		pageContainer.value.notify.error('启动失败');
	} finally {
		uni.hideLoading();
	}
};

/**
 * 停止计划任务
 */
export const handleStopCrontab = async (crontab) => {
	uni.showLoading({
		title: '停止中...',
	});
	try {
		const res = await setCrontabStatus({
			id: Number.parseInt(crontab.id),
			if_stop: 'True',
		});
		if (res.status) {
			pageContainer.value.notify.success(res.msg);
			hideContextMenu();
			crontabPaging.value.reload();
		} else {
			pageContainer.value.notify.error(res.msg);
		}
	} catch (error) {
		console.error(error);
		pageContainer.value.notify.error('停止失败');
	} finally {
		uni.hideLoading();
	}
};

/**
 * 执行计划任务
 */
const executeCrontabCore = async () => {
	// 保存当前任务信息，因为 hideContextMenu 会清空 activeCrontab
	const crontabId = activeCrontab.value?.id;
	if (!crontabId) {
		pageContainer.value.notify.error('任务信息获取失败');
		return;
	}

	hideContextMenu();
	uni.showLoading({
		title: '执行中...',
	});
	try {
		const res = await startTask({
			id: Number.parseInt(crontabId),
		});
		if (res.status) {
			pageContainer.value.notify.success(res.msg || '任务执行成功');
			crontabPaging.value.reload();
		} else {
			pageContainer.value.notify.error(res.msg || '任务执行失败');
		}
	} catch (error) {
		console.error(error);
		pageContainer.value.notify.error('执行失败');
	} finally {
		uni.hideLoading();
	}
};

export const handleExecuteCrontab = throttle(executeCrontabCore, 2000, 1);

/**
 * 处理备份路径点击事件
 */
const backupPathClickCore = (crontab) => {
	// 获取备份路径
	const backTable = ['site', 'database', 'path'];
	if (!backTable.includes(crontab.sType) || !crontab.db_backup_path) {
		return;
	}

	const backupPath = crontab.db_backup_path;
	if (backupPath === '--' || !backupPath) {
		return;
	}

	// 跳转到文件管理页面，传递备份路径参数
	uni.navigateTo({
		url: `/linux/files/index?path=${encodeURIComponent(backupPath)}`,
		animationType: 'zoom-fade-out',
	});
};

export const handleBackupPathClick = throttle(backupPathClickCore, 2000, 1);

/**
 * 添加计划任务
 */
const addCrontabCore = () => {
	uni.navigateTo({
		url: '/linux/crontab/add/index',
		animationType: 'zoom-fade-out',
	});
};

export const handleAddCrontab = throttle(addCrontabCore, 2000, 1);
