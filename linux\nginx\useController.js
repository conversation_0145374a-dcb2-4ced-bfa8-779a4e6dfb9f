import { ref, reactive } from 'vue';
import { getPluginInfo } from '@/api/config';
import { getNginxFirewallInfo as getNginxFirewallInfoApi, setNginxFirewall } from '@/api/nginx';

// 防火墙开关状态
export const isFirewallOn = ref(true);
export const pageContainer = ref(null);
export const firewallLoading = ref(false);
// 防火墙安装状态
export const isFirewallInstalled = ref(false);


// 分类展开状态
export const openCategories = reactive({
    overview: true,
    blockTypes: false,
    management: true,
    settings: true,
});

// 分类定义
export const categories = [
    {
        id: 'overview',
        title: '安全概览',
        icon: 'icon-site-monitor',
        iconClass: 'icon-primary',
        useIconfont: true,
    },
    {
        id: 'blockTypes',
        title: '拦截类型',
        icon: 'icon-safety',
        iconClass: 'icon-primary',
        useIconfont: true,
    },
    {
        id: 'management',
        title: '管理功能',
        icon: 'list',
        iconClass: 'icon-primary',
        useIconfont: false,
    },
    // {
    //     id: 'settings',
    //     title: '设置选项',
    //     icon: 'icon-setting',
    //     iconClass: 'icon-primary',
    //     useIconfont: true,
    // },
];

// 安全指标数据
export const securityMetrics = ref([
    {
        icon: 'icon-insecurity',
        iconClass: 'icon-primary',
        title: '风险拦截',
        count: 0,
        unit: '次',
        colorClass: 'card-normal',
        useIconfont: true,
    },
    {
        icon: 'scan',
        iconClass: 'icon-primary',
        title: '安全保护',
        count: 0,
        unit: '天',
        colorClass: 'card-normal',
        useIconfont: false,
    },
]);

// 拦截类型数据
export const blockTypes = ref([]);

// 管理功能
export const managementFeatures = [
    { icon: 'icon-control', iconClass: 'icon-primary', title: '全局配置', id: 'globalConfig', useIconfont: true },
    { icon: 'icon-site', iconClass: 'icon-primary', title: '站点配置', id: 'siteConfig', useIconfont: true },
    { icon: 'file-text', iconClass: 'icon-primary', title: '日志查看', id: 'logView', useIconfont: false },
    // { icon: 'icon-ip', iconClass: 'icon-primary', title: 'IP黑名单', id: 'ipBlacklist', useIconfont: true },
    // { icon: 'map-fill', iconClass: 'icon-primary', title: '地区限制', id: 'areaLimit', useIconfont: false },
    // { icon: 'clock-fill', iconClass: 'icon-primary', title: '封锁历史', id: 'blockHistory', useIconfont: false },
];

// 设置选项
export const settingsOptions = [
    { icon: 'icon-setting', iconClass: 'icon-primary', title: '基本设置', useIconfont: true },
    { icon: 'grid-fill', iconClass: 'icon-primary', title: '监控设置', useIconfont: false },
    { icon: 'warning-fill', iconClass: 'icon-primary', title: '告警设置', useIconfont: false },
    { icon: 'lock-fill', iconClass: 'icon-primary', title: '安全设置', useIconfont: false },
];

// 切换分类展开/折叠
export const toggleCategory = (categoryId) => {
    openCategories[categoryId] = !openCategories[categoryId];
};

// 防火墙开关切换事件
export const switchChange = async (value) => {
    try {
        firewallLoading.value = true
        const res = await setNginxFirewall()
        if (res.status) {
            isFirewallOn.value = value
        }
        res.status ? pageContainer.value.notify.success(res.msg) : pageContainer.value.notify.error(res.msg)
    } catch (error) {
        console.error('防火墙状态切换失败:', error);
    } finally {
        firewallLoading.value = false
    }
};

// 刷新数据
export const refreshData = () => {
    console.log('刷新数据');
    // 这里可以添加刷新数据的逻辑
};

// 显示拦截类型详情
export const showBlockTypeDetails = (blockType) => {
    console.log('查看拦截类型详情:', blockType.title);
    // 这里可以添加导航到详情页的逻辑
};

// 导航到功能页面
export const navigateToFeature = (feature) => {
    switch (feature.id) {
        case 'globalConfig':
            uni.navigateTo({
                url: '/linux/nginx/globalConfig/index',
                animationType: 'zoom-fade-out',
            });
            break;
        case 'siteConfig':
            uni.navigateTo({
                url: '/linux/nginx/siteConfig/index',
                animationType: 'zoom-fade-out',
            });
            break;
        case 'logView':
            uni.navigateTo({
                url: '/linux/nginx/logView/index',
                animationType: 'zoom-fade-out',
            });
            break;
    }
};

// 导航到设置页面
export const navigateToSetting = (setting) => {
    // 这里可以添加导航逻辑
};

// 转换图标名称为uv-icon支持的名称
export const getIconName = (iconName) => {
    // 根据传入的图标名映射到uv-icon组件支持的图标名
    const iconMap = {
        shield: 'shield-fill',
        'shield-alert': 'warning-fill',
        'shield-check': 'checkmark-circle-fill',
        chart: 'grid-fill',
        list: 'list',
        settings: 'setting-fill',
        database: 'server-fill',
        code: 'edit-pen-fill',
        server: 'server-man',
        globe: 'map-fill',
        terminal: 'grid',
        'file-code': 'file-text-fill',
        'file-text': 'file-text',
        map: 'map',
        users: 'account-fill',
        clock: 'clock-fill',
        'bar-chart': 'grid-fill',
        'alert-triangle': 'warning-fill',
        lock: 'lock-fill',
        scan: 'scan',
    };

    return iconMap[iconName] || 'info-circle-fill'; // 默认返回info图标
};

// 根据样式类获取颜色值 (统一使用#666666作为主色调)
export const getColorByClass = (className) => {
    // 统一使用#666666灰色，保持界面颜色单调
    return '#666666';
};

// 查询nginx防火墙是否安装
export const isNginxFirewallInstalled = async () => {
    try {
        const res = await getPluginInfo({ sName: 'btwaf' });
        // 先检查 endtime，如果是 -1 就直接返回 false
        if (res.endtime == -1) {
            isFirewallInstalled.value = false;
            return;
        }
        // 然后检查其他条件
        if (!res.status || (res.setup != undefined && !res.setup)) {
            isFirewallInstalled.value = false;
            return;
        }
        isFirewallInstalled.value = true;
    } catch (error) {
        console.error('查询nginx防火墙是否安装失败:', error);
        isFirewallInstalled.value = false;
    }
};

// 获取nginx防火墙信息
export const getNginxFirewallInfo = async () => {
    try {
        const res = await getNginxFirewallInfoApi();
        isFirewallOn.value = res.open
        securityMetrics.value[0].count = res.total.total
        securityMetrics.value[1].count = res.safe_day
        blockTypes.value = res.total.rules
    } catch (error) {
        console.error('获取nginx防火墙信息失败:', error);
    }
}
