(()=>{"use strict";var t={d:(e,n)=>{for(var c in n)t.o(n,c)&&!t.o(e,c)&&Object.defineProperty(e,c,{enumerable:!0,get:n[c]})},o:(t,e)=>Object.prototype.hasOwnProperty.call(t,e),r:t=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})}},e={};t.r(e),t.d(e,{border:()=>N,default:()=>mt,flex:()=>a,media:()=>R,pseudo:()=>C,sketch:()=>W,unocss:()=>I});var n=function(){return n=Object.assign||function(t){for(var e,n=1,c=arguments.length;n<c;n++)for(var a in e=arguments[n])Object.prototype.hasOwnProperty.call(e,a)&&(t[a]=e[a]);return t},n.apply(this,arguments)},c={ai:"align-items",jc:"justify-content",fd:"flex-direction"};function a(){return function(t){var e=Object.keys(t.theme.breakpoints),a=function(n){return n.slice(Number(t.prefix.includes(n[0]))).slice(Number(e.includes(n[0])||e.includes(n[1])))},o="center|start|end|baseline|stretch",r="((".concat(t.prefix.join("|"),")-((").concat(e.join("|"),")-)?)?flex(-(col|row))?-");return{name:"unocss:presets:flex",rules:[["^".concat(r,"(").concat(o,")$"),function(t){var e,o=a(t),r=o[0],i=o[1],l=o[2];i=["col","row"].includes(i)?l:i,i=["start","end"].includes(i)?"".concat(r,"-").concat(i):i;var s={};return(t.includes("col")||t.includes("row"))&&(s[c.fd]=t.includes("col")?"column":"row"),n(n({},s),((e={display:"flex"})[c.ai]=i,e[c.jc]=i,e))}],["^".concat(r,"(").concat("normal|self-start|self-end","|").concat(o,")-(between|around|evenly)$"),function(t){var e,o=a(t).reverse(),r=o[0],i=o[1],l={};return(t.includes("col")||t.includes("row"))&&(l[c.fd]=t.includes("col")?"column":"row"),n(n({},l),((e={display:"flex"})[c.jc]="space-".concat(r),e[c.ai]=t.includes("start")||t.includes("end")?"".concat(t.includes("self")?"self":"flex","-").concat(i):i,e))}],["^".concat(r,"((normal|self-start|self-end)-(center|baseline|stretch)|self-start|self-end)$"),function(t){var e,o=a(t).reverse(),r=o[0],i=o[1],l={};return(t.includes("col")||t.includes("row"))&&(l[c.fd]=t.includes("col")?"column":"row"),n(n({},l),((e={display:"flex"})[c.jc]=["start","end"].includes(r)?"flex-".concat(r):r,e[c.ai]=(t.includes("start")||t.includes("end"))&&t.includes("self")?"self-".concat(["start","end"].includes(r)?r:i):i,e))}]]}}}var o=function(t,e){return new RegExp(e).test(t)},r=function(t){return/^-?(\.?[0-9])+$/.test(t)};const i={number:r,value:function(t,e){return void 0===e&&(e="rpx"),r(t)&&0!=+t?"".concat(t).concat(e):t},className:function(t,e){var n;void 0===e&&(e=!0);var c=null===(n=t.replace(/<!--[\s\S]*?-->/g,"").match(/class=("[\s\S]+?"|'[\s\S]+?')/g))||void 0===n?void 0:n.map((function(t){return t=t.toString().replace(/\s+/g," ").replace(/\?/g," ").replace(/class=/g,"").replace(/['"\[\]\{\}=]/g,"").trim(),e?t.replace(/:/g," "):t}));return c=e?null==c?void 0:c.map((function(t){return t.replace(/:?/g,"").split(/\x20/).map((function(t){return t.toString().trim()}))})).flat().filter(Boolean):null==c?void 0:c.map((function(t){return t.toString().trim()})).filter(Boolean),Array.from(new Set(c))},test:o,toRgb:function(t){var e=t.toString().trim().toLowerCase().replace(/#/g,"");if(!o(e,/^#?[0-9A-Fa-f]+$/)||![3,5,6].includes(e.length))return t;if(3===e.length){for(var n="",c=0;c<e.length;c++)n+=e.slice(c,c+1).concat(e.slice(c,c+1));e=n}var a=[];for(c=0;c<6;c+=2)a.push(parseInt("0x".concat(e.slice(c,c+2))));return a.join(",")},rgbToHex:function(t){return t.startsWith("#")||!o(t,/^[0-9]{1,3},[0-9]{1,3},[0-9]{1,3}$/)?t:"#".concat(t.split(",").map((function(t){return(+t).toString(16).padStart(2,"0")})).join(""))},isRgb:function(t){return o(t,/^[0-9]{1,3},[0-9]{1,3},[0-9]{1,3}$/)}},l={underline:"-"};function s(t,e){var n=t,c=e.sort((function(t,e){return e.length-t.length})).map((function(t){var e=t.split(l.underline),c=e.findIndex((function(t){return t.includes("%")})),a=e.findIndex((function(t){return t.includes("!")})),o=e.findIndex((function(t){return t.includes("#")})),r=1;if(e.length>1&&~c){e.splice(c,0,"full");var i=e[++c].replace("%","");e.splice(c,1,i),r++,n=n.replace(new RegExp(t,"g"),e.join(l.underline))}if(e.length>1&&~a){var s=e.join(l.underline),u=e[a+r-1].replace("!","");e.splice(a+r-1,1,u),e.splice(a+r,0,"important"),r++,n=n.replace(new RegExp(s,"g"),e.join(l.underline))}if(e.length>1&&~o){s=e.join(l.underline);var d=e[o].replace("#","");e.splice(o,1,d),!t.startsWith("color")&&!t.startsWith("bg")&&e.push("color"),r++,n=n.replace(new RegExp(s,"g"),e.join(l.underline))}return e.join(l.underline)})).filter(Boolean);return{code:n,classNames:c}}var u={w:"width",h:"height",m:"margin",b:"border",p:"padding",z:"z-index",op:"opacity",zi:"z-index",of:"overflow",ws:"white-space",lh:"line-height",display:"display",list:"list-style",rd:"border-radius",space:"white-space",table:"table-layout",bg:"background-color",round:"border-radius",letter:"letter-spacing",justify:"justify-content"},d={text:"color",size:"font-size",wrap:"font-wrap",align:"text-align",indent:"text-indent",anchor:"text-anchor",last:"text-align-last",justify:"text-justify",emphasis:"text-emphasis",overflow:"text-overflow",rendering:"text-rendering",transform:"text-transform",decoration:"text-decoration",orientation:"text-orientation",combine:"text-combine-upright",offset:"text-underline-offset",skip:"text-decoration-skip-ink",position:"text-underline-position"},f={style:"font-style",weight:"font-weight",stretch:"font-stretch",synthesis:"font-synthesis"},p={t:"top",l:"left",r:"right",b:"bottom"};const h={full:function(t,e){return"full"===t?e?"".concat(e,"%"):"100%":t},globl_value:["inherit","initial","unset","revert","layer"],return_value:function(t,e,n){var c,a,o=(n||{}).end,r=void 0===o?"":o;return["x","y"].includes(e[1])?((c={})["".concat(u[e[0]],"-").concat("x"===e[1]?p.l:p.t).concat(r)]=t,c["".concat(u[e[0]],"-").concat("x"===e[1]?p.r:p.b).concat(r)]=t,c):((a={})["".concat(1===e.length?u[e]:"".concat(u[e[0]],"-").concat(p[e[1]])).concat(r)]=t,a)}};function m(t){var e=Object.keys(t);return{color:function(e){var n=function(t){for(var e=[],n=t.length-1;n>=0;n--){var c=t.slice(0,n+1).join("-");e.unshift(c)}return e}(e).sort((function(t,e){return+t-+e?1:-1})),c=n.findIndex((function(e){return t[e]})),a=~c?n[c]:e[0],o=e[e.length-1];o=i.number(o)?o:"";var r=function(t){var e=+o/100>1?"1":+o/100<0?"0":+o/100;return o?"rgba(".concat(t,",").concat("color"===o?1:e,")"):"rgb(".concat(t,")")};switch(!0){case!t[a]&&i.test(a,/^([a-zA-Z0-9]{4}|[a-zA-Z0-9]{8})$/):return"#".concat(a);case t[a]&&i.isRgb(t[a]):return r(t[a]);case t[a]:return t[a];default:var l=i.toRgb(a);return i.isRgb(l)?r(l):t[l]||l}},colorString:e.join("|"),colorOpacityString:e.filter((function(e){return i.isRgb(t[e])})).join("|")}}const v=["rpx","px","em","rem","vh","vw","pt","pc","in","mm","cm","svh","lvh","dvh","vmin","vmax","vi","vb","svmin","dvmin","svmax","dvmax","svi","dvi","svb","dvb"];var b="([0-9]{1,7})",g="".concat(b,"(-").concat(b,")?"),y="".concat(b,"(-").concat(b,")?(").concat(v.join("|"),")?"),w="".concat(b,"(.").concat(b,")?"),x="".concat(w,"(").concat(v.join("|"),")?"),k="full(-".concat(w,")?"),_="(-(0|100|[0-9]{0,2}))?";const j={number:b,opacity:_,decimal:w,number_unit:"".concat(b,"(").concat(v.join("|"),")?"),number_hyphen:g,number_hyphen_unit:y,number_decimal_unit:x,number_decimal_unit_full:"(".concat(x,"|").concat(k,")"),color:"(([a-z0-9A-Z]{3}|[a-z0-9A-Z]{6})".concat(_,"|([a-z0-9A-Z]{4}|[a-z0-9A-Z]{8}))")};var A=function(){return A=Object.assign||function(t){for(var e,n=1,c=arguments.length;n<c;n++)for(var a in e=arguments[n])Object.prototype.hasOwnProperty.call(e,a)&&(t[a]=e[a]);return t},A.apply(this,arguments)},E=function(t,e,n){if(n||2===arguments.length)for(var c,a=0,o=e.length;a<o;a++)!c&&a in e||(c||(c=Array.prototype.slice.call(e,0,a)),c[a]=e[a]);return t.concat(c||Array.prototype.slice.call(e))};function S(t){if("string"!=typeof t)throw new TypeError("options.prefix must be a string");if(!/[a-z]/.test(t[0]))throw new TypeError("options.prefix must start with a letter")}var O=function(t,e,n){if(n||2===arguments.length)for(var c,a=0,o=e.length;a<o;a++)!c&&a in e||(c||(c=Array.prototype.slice.call(e,0,a)),c[a]=e[a]);return t.concat(c||Array.prototype.slice.call(e))};function z(t,e,n){var c=[],a=e.sort((function(t,e){return e.length-t.length})).reduce((function(e,a){var o=a,r=a.split(l.underline),s="";a.includes("important")&&(s=r.splice(r.indexOf("important"),1)[0],a=r.join(l.underline));var v=function(t,e){var n,c=t.split(l.underline).map((function(t){return t.toString().trim()})).filter(Boolean),a=c[0],o=c[1];e.prefix.forEach(S);var r,s=function(t,e){var n=m(t),c=n.color,a=n.colorString,o=n.colorOpacityString,r=h.globl_value.join("|"),s=function(t,n,c){return void 0===c&&(c=""),i.test(t,"^".concat(c,"((").concat(e,")-)?").concat(n,"$"))},v=function(t,e,n,c){var a;void 0===c&&(c=h.globl_value);var o=Array.isArray(c)?c:h.globl_value.slice(c.start||0,c.end||h.globl_value.length);if(s(t,"".concat(e,"-(").concat(o.join("|"),")"))){var r=e.split(l.underline).reverse()[0];return(a={})[u[r]||r]=n===h.globl_value[4]?"".concat(h.globl_value[3],"-").concat(n):n,a}},b=function(t){return t.slice(Number(e.includes(t[0])))},g=function(t){for(var e,n,c=[],a=1;a<arguments.length;a++)c[a-1]=arguments[a];var o=b(c),r=o[0],l=o[1],d=o[2];switch(!0){case s(t,"".concat(r,"-(min|max|fit)")):return(e={})[u[r]]="".concat(l,"-content"),e;case s(t,"".concat(r,"-(auto|").concat(j.number_decimal_unit_full,")")):return(n={})[u[r]]=i.value(h.full(l,d)),n;default:return v(t,r,l,{end:3})}},y=function(t){for(var e,n=[],c=1;c<arguments.length;c++)n[c-1]=arguments[c];var a=b(n),o=a[0],r=a[1];return!0===s(t,"(block|flex|none|grid|hidden|contents|list-item|flow-root|table(-row)?|inline(-(block|flex|grid))?)")?((e={})[u.display]=r?"".concat(o,"-").concat(r):"hidden"===o?"none":o,e):v(t,o,r)};return{a:g,b:function(t){for(var e,n,c,a=[],o=1;o<arguments.length;o++)a[o-1]=arguments[o];var r=b(a),i=r[0],d=r[1];if(["w","h"].includes(d))switch(!0){case s(t,"(min|max)-(w|h)"):return(e={})["".concat(i,"-").concat(u[d])]="".concat(i,"-content"),e;case s(t,"min-(w|h)-min"):case s(t,"max-(w|h)-max"):return;default:var f=b(a).slice(1),p=f.join(l.underline),h=g.apply(void 0,E([p],f,!1)),m=v(p,f[0],f[1],{start:3}),y="".concat(i,"-").concat(u[d]);return h?((n={})[y]=h[u[d]],n):m?((c={})[y]=m[u[d]],c):void 0}},c:function(t){for(var e=[],n=1;n<arguments.length;n++)e[n-1]=arguments[n];var c=b(e),a=c[0],o=c[1],r=c[2],l="m"===a[0],d=l?"-?":"";switch(o=t.startsWith("-")?"-".concat(o):o,r=t.startsWith("-")?"-".concat(r):r,!0){case l&&s(t,"".concat(a,"-auto")):case s(t,"".concat(a,"-(").concat(j.number_decimal_unit_full,")"),d):return h.return_value(i.value(h.full(o,r)),a);default:var f=v(t.toString().replace(a,a[0]),a[0],o,{end:l?3:0});return f?h.return_value(f[u[a[0]]],a):void 0}},aj:function(t){for(var e,n,r,l=[],d=1;d<arguments.length;d++)l[d-1]=arguments[d];var f=b(l),p=f[0],m=f[1],g=f[2],y=f[3];switch(!0){case s(t,"b-(collapse|separate)"):return(e={})["".concat(u.b,"-collapse")]=m,e;case i.test(t,"b-spacing-(x|y)-".concat(j.number_decimal_unit)):return(n={})["".concat(u.b,"-spacing")]="".concat(i.value("x"===g?y:" 0")," ").concat(i.value("y"===g?y:" 0")),n;case s(t,"^b-spacing-".concat(j.number_decimal_unit)):return(r={})["".concat(u.b,"-spacing")]=i.value(g),r;case s(t,"".concat(p,"(-(thin|medium|thick|").concat(j.number_decimal_unit,"))?")):return h.return_value(i.value(m||"1"),p,{end:"-width"});case s(t,"".concat(p,"-(").concat(a,")")):case s(t,"".concat(p,"-(").concat(o,")").concat(j.opacity)):case s(t,"".concat(p,"-").concat(j.color,"-color")):return h.return_value(c(f.slice(1)),p,{end:f.includes("color")?"-color":""});case s(t,"".concat(p,"-(none|hidden|dotted|dashed|solid|double|groove|ridge|inset|outset)")):return h.return_value(m,p,{end:"-style"});default:var w=v(t.toString().replace(p,p[0]),p[0],m,{end:3});return w?h.return_value(w[u[p[0]]],p):void 0}},d:function(t){for(var e,n=[],c=1;c<arguments.length;c++)n[c-1]=arguments[c];var a=b(n),o=a[0],r=a[1],i=a[2];return!0===s(t,"".concat(o,"-(normal|").concat(j.number_decimal_unit_full,")"))?((e={})[u[o]]=h.full(r,i),e):v(t,o,r,{end:3})},e:function(t){for(var e,n,c,a,o,l,d,f=[],m=1;m<arguments.length;m++)f[m-1]=arguments[m];var g=b(f),y=g[0],w=g[1],x=g[2],k=g[3];switch(!0){case s(t,"".concat(y,"-").concat(j.number_decimal_unit_full)):return(e={})[u[y]]=i.value(h.full(w,x)),e;case s(t,"".concat(y,"-(t|b)-").concat(j.number_decimal_unit_full)):return x=i.value(h.full(x,k)),(n={})["".concat(u.b,"-").concat(p[w],"-").concat(p.l,"-radius")]=x,n["".concat(u.b,"-").concat(p[w],"-").concat(p.r,"-radius")]=x,n;case s(t,"".concat(y,"-(l|r)-").concat(j.number_decimal_unit_full)):return x=i.value(h.full(x,k)),(c={})["".concat(u.b,"-").concat(p.t,"-").concat(p[w],"-radius")]=x,c["".concat(u.b,"-").concat(p.b,"-").concat(p[w],"-radius")]=x,c;case s(t,"".concat(y,"-(tl|tr|bl|br)-").concat(j.number_decimal_unit_full)):return(a={})["".concat(u.b,"-").concat(p[w[0]],"-").concat(p[w[1]],"-radius")]=x=i.value(h.full(x,k)),a;case s(t,"".concat(y,"-(t|b)-(").concat(r,")")):y="".concat(y,"-").concat(w);var _=v(t,y,x);return(o={})["".concat(u.b,"-").concat(p[w],"-").concat(p.l,"-radius")]=_[y],o["".concat(u.b,"-").concat(p[w],"-").concat(p.r,"-radius")]=_[y],o;case s(t,"".concat(y,"-(l|r)-(").concat(r,")")):return y="".concat(y,"-").concat(w),_=v(t,y,x),(l={})["".concat(u.b,"-").concat(p.t,"-").concat(p[w],"-radius")]=_[y],l["".concat(u.b,"-").concat(p.b,"-").concat(p[w],"-radius")]=_[y],l;case s(t,"".concat(y,"-(tl|tr|bl|br)-(").concat(r,")")):return y="".concat(y,"-").concat(w),_=v(t,y,x),(d={})["".concat(u.b,"-").concat(p[w[0]],"-").concat(p[w[1]],"-radius")]=_[y],d;default:return v(t,y,w)}},f:function(t){for(var e,n,c=[],a=1;a<arguments.length;a++)c[a-1]=arguments[a];var o=b(c),r=o[0],i=o[1];switch(!0){case s(t,"".concat(r,"-inherit")):return(e={})[u[r]]=i,e;case s(t,"".concat(r,"-").concat(j.decimal)):var l=+i/100;return(n={})[u[r]]=l>1?"1":l<0?"0":"".concat(l),n;default:return v(t,r,i)}},g:function(t){for(var e,n,c=[],a=1;a<arguments.length;a++)c[a-1]=arguments[a];var o=b(c),r=o[0],i=o[1];return!0===s(t,"".concat(r,"-(auto|").concat(j.number,")"),"-?")?((e={})[u[r]]=(null===(n=null==t?void 0:t.startsWith)||void 0===n?void 0:n.call(t,"-"))?"-".concat(i):i,e):v(t,r,i,{end:3})},h:function(t){for(var e,n=[],c=1;c<arguments.length;c++)n[c-1]=arguments[c];var a=b(n),o=a[0],i=a[1];switch(!0){case s(t,"static"):case!t.includes("static")&&s(t,"".concat(o,"(-").concat(j.number,")?"),"-?"):var l={position:o};return i&&(l[u.z]=(null===(e=null==t?void 0:t.startsWith)||void 0===e?void 0:e.call(t,"-"))?"-".concat(i):i),l;case s(t,"position-(".concat(r,")")):return v(t,o,i)}},i:function(t){for(var e,n=[],r=1;r<arguments.length;r++)n[r-1]=arguments[r];var i=b(n),l=i[0],d=i[1];switch(i[2],!0){case s(t,"".concat(l,"-(").concat(a,")")):case s(t,"".concat(l,"-(").concat(o,")").concat(j.opacity)):case s(t,"".concat(l,"-").concat(j.color)):return(e={})[u[l]||l]=c(i.slice(1)),e;default:return v(t,l,d)}},j:function(t){for(var e,n,l,u,f,p,m,g,y,w,x,k,_,A=[],E=1;E<arguments.length;E++)A[E-1]=arguments[E];var S=b(A),O=S[0],z=S[1],R=S[2],N=S[3];switch(!0){case s(t,"".concat(O,"-").concat(j.number_decimal_unit_full)):case s(t,"".concat(O,"-(math|larger|large|smaller|medium|x{0,2}-(small|large)|xxx-large)")):return(e={})[d.size]=i.value(R?"".concat(z,"-").concat(R):h.full(z,R)),e;case s(t,"".concat(O,"-(").concat(a,")")):case s(t,"".concat(O,"-(").concat(o,")").concat(j.opacity)):case s(t,"".concat(O,"-").concat(j.color,"-color")):return(n={})[d.text]=c(S.slice(1)),n;case s(t,"".concat(O,"-(start|end|left|right|center|justify|all|match)")):return z="all"===z?"justify-".concat(z):"match"===z?"".concat(z,"-parent"):z,(l={})[d.align]=z,l;case s(t,"".concat(O,"-(decoration|emphasis)-(").concat(a,")")):case s(t,"".concat(O,"-(decoration|emphasis)-(").concat(o,")").concat(j.opacity)):case s(t,"".concat(O,"-(decoration|emphasis)-").concat(j.color,"-color")):return(u={})["".concat(d[z],"-color")]=c(S.slice(2)),u;case s(t,"".concat(O,"-decoration-(none|hidden|underline|overline|line|blink)")):return R="line"===R?"".concat(R,"-through"):R,(f={})["".concat(d[z],"-line")]="hidden"===R?"none":R,f;case s(t,"".concat(O,"-skip-(none|hidden|auto|all)")):return{"text-decoration-skip-ink":"hidden"===R?"none":R};case s(t,"".concat(O,"-decoration-(solid|double|dotted|dashed|wavy)")):return(p={})["".concat(d[z],"-style")]=R,p;case s(t,"".concat(O,"-decoration-(auto|from|").concat(j.number_decimal_unit_full,")")):return(m={})["".concat(d[z],"-thickness")]=i.value("from"===R?"".concat(R,"-font"):h.full(R,N)),m;case s(t,"".concat(O,"-position-((o|u)(r|l)|(l|r)(o|u))")):case s(t,"".concat(O,"-emphasis-((o|u)(r|l)|(l|r)(o|u))")):var I={o:"over",u:"under",l:"left",r:"right"};return R="".concat(I[R[0]]," ").concat(I[R[1]]),(g={})["position"===z?d[z]:"".concat(d[z],"-position")]=R,g;case s(t,"".concat(O,"-emphasis-(none|hidden|filled|open|dot|circle|triangle)")):return(y={})["".concat(d[z],"-style")]="hidden"===R?"none":R,y;case s(t,"".concat(O,"-indent-").concat(j.number_decimal_unit_full)):case s(t,"".concat(O,"-offset-(auto|").concat(j.number_decimal_unit_full,")")):return(w={})[d[z]]=i.value(h.full(R,N)),w;case s(t,"".concat(O,"-(clip|ellipsis)")):case s(t,"".concat(O,"-anchor-(start|end|middle)")):case s(t,"".concat(O,"-combine-(none|hidden|all)")):case s(t,"".concat(O,"-(wrap|nowrap|balance|pretty|stable)?")):case s(t,"".concat(O,"-orientation-(mixed|upright|sideways|right|use)")):case s(t,"".concat(O,"-last-(auto|start|end|left|right|center|justify)")):case s(t,"".concat(O,"-justify-(none|hidden|auto|word|character|distribute)")):case s(t,"".concat(O,"-transform-(none|hidden|capitalize|uppercase|lowercase|width|size)")):case s(t,"".concat(O,"-rendering-(auto|speed|legibility|geometric)$")):switch(R){case"hidden":R="none";break;case"width":R="full-".concat(R);break;case"word":case"character":R="inter-".concat(R);break;case"right":R="".concat(R,"-right");break;case"size":R="full-".concat(R,"-kana");break;case"use":R="".concat(R,"-glyph-orientation");break;case"speed":case"legibility":R="optimize".concat(R.toString().replace(R[0],R.charAt(0).toUpperCase()));break;case"geometric":R="".concat(R,"Precision")}return(x={})[d[z]||d[["clip","ellipsis"].includes(z)?"overflow":"wrap"]]=R||z,x;case s(t,"".concat(O,"-emphasis-(color|position|style)-(").concat(r,")")):case s(t,"".concat(O,"-decoration-(color|line|style|thickness)-(").concat(r,")")):return O="".concat(O,"-").concat(z,"-").concat(R),v(t,O,N);case s(t,"".concat(O,"-(size|align|last|anchor|combine|decoration|skip|emphasis|indent|justify|orientation|overflow|rendering|offset|transform|position|wrap)-(").concat(r,")")):var $=v(t,"".concat(O,"-").concat(z),R);return $?((k={})[d[z]]=$[z],k):void 0;default:var C=v(t,O,z);return C?((_={})[d.text]=C[O],_):void 0}},k:function(t){for(var e,n,c,a,o,i,l,u=[],d=1;d<arguments.length;d++)u[d-1]=arguments[d];var p=b(u),m=p[0],g=p[1],y=p[2];switch(!0){case s(t,"".concat(m,"-(italic|oblique)")):return(e={})[f.style]=g,e;case s(t,"".concat(m,"-full(-").concat(j.decimal,")?")):return(n={})[f.stretch]=h.full(g,y),n;case s(t,"".concat(m,"-((semi-)?(condensed|expanded)|(ultra|extra)-(condensed|expanded))")):return(c={})[f.stretch]=y?"".concat(g,"-").concat(y):g,c;case s(t,"".concat(m,"-(style|stretch)-normal")):return(a={})[f[g]]=y,a;case s(t,"".concat(m,"-(none|hidden|weight|style|position)")):return(o={})[f.synthesis]="hidden"===g?"none":g,o;case s(t,"".concat(m,"-(bold|lighter|bolder|normal|").concat(j.decimal,")")):return(i={})[f.weight]=g,i;case s(t,"".concat(m,"-(style|stretch|synthesis|weight)-(").concat(r,")")):var w=v(t,"".concat(m,"-").concat(g),y);return w?((l={})[f[g]]=w[g],l):void 0}},l:function(t){for(var e,n,c,a=[],o=1;o<arguments.length;o++)a[o-1]=arguments[o];var i=b(a),l=i[0],d=i[1],f=i[2];switch(!0){case s(t,"".concat(l,"-(normal|nowrap|pre|wrap|line|break)")):switch(d){case"wrap":case"line":d="pre-".concat(d);break;case"break":d="".concat(d,"-spaces")}return(e={})[u[l]]=d,e;case s(t,"".concat(l,"-collapse(-(preserve|breaks|spaces|break))?")):switch(f){case"breaks":case"spaces":f="preserve-".concat(f);break;case"break":f="".concat(f,"-spaces")}return(n={})["".concat(u[l],"-").concat(d)]=f||d,n;case s(t,"".concat(l,"-collapse-(").concat(r,")$")):var p=v(t,"".concat(l,"-").concat(d),f);return p?((c={})["".concat(u[l],"-").concat(d)]=p[d],c):void 0;default:return v(t,l,d)}},m:function(t){for(var e=[],n=1;n<arguments.length;n++)e[n-1]=arguments[n];var c=b(e),a=c[0],o=c[1],l=c[2];switch(!0){case s(t,"".concat(a,"(-(normal|break|keep|auto))?")):switch(o){case"keep":case"break":o="".concat(o,"-all");break;case"auto":o="".concat(o,"-phrase")}return{"word-break":o||a};case s(t,"^".concat(a,"-").concat(j.number_decimal_unit,"$")):return{"word-spacing":i.value(o)};case s(t,"".concat(a,"-spacing-(normal|").concat(r,")$")):return(u=v(t,"".concat(a,"-").concat(o),l))?{"word-spacing":u[o]}:void 0;default:var u;return(u=v(t,a,o))?{"word-break":u[a]}:void 0}},n:y,o:function(t){for(var e,n,c,a,o,i=[],l=1;l<arguments.length;l++)i[l-1]=arguments[l];var u=b(i),d=u[0],f=u[1],p=u[2],m=u[3],g="".concat(d,"-").concat(f);switch(!0){case s(t,"".concat(d)):return y(t,d);case s(t,"".concat(d,"-(none|hidden|auto|min|").concat(j.number_decimal_unit_full,")")):return(e={})[d]="hidden"===f?"none":"min"===f?"".concat(f,"-content"):h.full(f,p),e;case s(t,"".concat(d,"-(row|col)(-reverse)?")):return f="col"===f?"column":f,(n={})["".concat(d,"-direction")]=p?"".concat(f,"-").concat(p):f,n;case s(t,"".concat(d,"-(grow|shrink)-").concat(j.decimal)):case s(t,"".concat(d,"-flow-(nowrap|(row|col|wrap)(-reverse)?)")):case s(t,"".concat(d,"-basis-(auto|content|min|max|fit|").concat(j.number_decimal_unit_full,")")):switch(p){case"min":case"max":case"fit":p="".concat(p,"-content");break;case"col":p="column"}return(c={})[g]=m?"".concat(p,"-").concat(m):h.full(p,m),c;case s(t,"".concat(d,"-(nowrap|wrap|reverse)")):return(a={})["".concat(d,"-wrap")]="reverse"===f?"wrap-".concat(f):f,a;case s(t,"".concat(d,"-(basis|direction|flow|shrink|grow|wrap)-(").concat(r,")")):d="".concat(d,"-").concat(f);var w=v(t,d,p);return w?((o={})[g]=w[f],o):void 0;default:return v(t,d,f)}},p:function(t){for(var e,n,c,a,o,l,u=[],d=1;d<arguments.length;d++)u[d-1]=arguments[d];var f=b(u),p=f[0],h=f[1],m=f[2],g=f[3];switch(!0){case s(t,"".concat(p)):return y(t,p);case s(t,"".concat(p,"-(none|hidden)")):return(e={})[p]="hidden"===h?"none":h,e;case s(t,"".concat(p,"-area-(auto|some|").concat(j.number,")")):return"some"===m&&(m="".concat(m,"-").concat(p,"-").concat(h)),(n={})["".concat(p,"-").concat(h)]=i.number(m)?"".concat(m," / ").concat(m," / ").concat(m," / ").concat(m):m,n;case s(t,"".concat(p,"-auto-(cols|rows)-").concat(j.decimal,"fr")):case s(t,"".concat(p,"-auto-(cols|rows)(-(min|max|").concat(r,"|").concat(j.number_decimal_unit_full,"))?")):return m="cols"===m?"columns":m,g="layer"===g?"revert-".concat(g):g,g=["min","max"].includes(g)?"".concat(g,"-content"):g,(c={})["".concat(p,"-").concat(h,"-").concat(m)]=g?i.value(g):h,c;case s(t,"".concat(p,"-auto-flow-(row|column|dense|").concat(r,")")):return(a={})["".concat(p,"-").concat(h,"-").concat(m)]="layer"===g?"revert-".concat(g):g,a;case s(t,"".concat(p,"-(col|row)(-(start|end))?-(auto|somegridarea|").concat(j.number,"|").concat(r,")")):return h="col"===h?"column":h,g=g||m,(o={})["".concat(p,"-").concat(h).concat(["start","end"].includes(m)?"-".concat(m):"")]=i.number(g)?"".concat(g," / ").concat(g):g,o;case s(t,"".concat(p,"-(cols|rows)-(none|hidden|subgrid|masonry|").concat(j.number,"|").concat(r,")")):return h="cols"===h?"columns":h,m="hidden"===m?"none":m,(l={})["".concat(p,"-template-").concat(h)]=i.number(m)?"repeat(".concat(m,", minmax(0, 1fr))"):m,l;case s(t,"".concat(p,"-area-(").concat(r,")")):return p="".concat(p,"-").concat(h),v(t,p,m);default:return v(t,p,h)}},q:function(t){for(var e,n,c,a=[],o=1;o<arguments.length;o++)a[o-1]=arguments[o];var i=b(a),l=i[0],d=i[1],f=i[2];switch(!0){case s(t,"".concat(l,"-item")):return y(t,l,d);case s(t,"".concat(l,"-(square|inside|none|hidden)")):return(e={})[u[l]]="hidden"===d?"none":d,e;case s(t,"".concat(l,"-image-(none|hidden)")):case s(t,"".concat(l,"-position-(inside|outside)")):case s(t,"".concat(l,"-type-(disc|circle|square|decimal|georgian|trad|kannada|custom|none|hidden)")):switch(f){case"hidden":f="none";break;case"trad":f="".concat(f,"-chinese-informal");break;case"custom":f="".concat(f,"-counter-style")}return(n={})["".concat(u[l],"-").concat(d)]=f,n;case s(t,"".concat(l,"-(image|type|position)-(").concat(r,")")):var p=v(t,"".concat(l,"-").concat(d),f);return p?((c={})["".concat(u[l],"-").concat(d)]=p[d],c):void 0;default:return v(t,l,d)}},r:function(t){for(var e,n=[],c=1;c<arguments.length;c++)n[c-1]=arguments[c];var a=b(n),o=a[0],r=a[1];switch(!0){case s(t,"".concat(o,"(-row)?")):return y(t,o,r);case s(t,"".concat(o,"-(auto|fixed)")):return(e={})[u[o]]=r,e;default:return v(t,o,r)}},s:function(t){for(var e,n,c=[],a=1;a<arguments.length;a++)c[a-1]=arguments[a];var o=b(c),i=o[0],l=o[1],u=o[2],d=o[3];switch(!0){case s(t,"".concat(i,"-(center|(flex-)?(start|end)|left|right|normal|between|around|evenly|stretch)")):switch(l){case"around":case"evenly":case"between":l="space-".concat(l)}return(e={})["".concat(i,"-content")]=u?"".concat(l,"-").concat(u):l,e;case s(t,"".concat(i,"-self-auto")):case s(t,"".concat(i,"-(items|self)-(normal|stretch|center|start|end|((flex|self)-)?(start|end)|left|right|baseline|anchor)")):return u="anchor"===u?"".concat(u,"-center"):u,(n={})["".concat(i,"-").concat(l)]=d?"".concat(u,"-").concat(d):u,n;case s(t,"".concat(i,"-(items|self)-(").concat(r,")")):return i="".concat(i,"-").concat(l),v(t,i,u);default:return v(t,i,l)}},t:function(t){for(var e,n,c=[],a=1;a<arguments.length;a++)c[a-1]=arguments[a];var o=b(c),r=o[0],i=o[1],l=o[2];switch(!0){case s(t,"items-(normal|stretch|center|start|end|((flex|self)-)?(start|end)|anchor|baseline)"):case s(t,"self-(auto|normal|center|start|end|((flex|self)-)?(start|end)|anchor|baseline|stretch)"):return i="anchor"===i?"".concat(i,"-center"):i,(e={})["align-".concat(r)]=l?"".concat(i,"-").concat(l):i,e;default:var u=v(t,r,i);return u?((n={})["align-".concat(r)]=u[r],n):void 0}},u:function(t){for(var e,n,c,a=[],o=1;o<arguments.length;o++)a[o-1]=arguments[o];var r=b(a),i=r[0],l=r[1],u=r[2];switch(!0){case s(t,"".concat(i,"-(none|hidden|(no-)?(open|close)|empty)")):switch(l){case"hidden":l="none";break;case"no":l="".concat(l,"-").concat(u);break;case"empty":l='""'}return(e={})[i]=l,e;case s(t,"".concat(i,"-(normal|center|(flex-)?(start|end)|baseline|between|around|evenly|stretch)")):switch(l){case"around":case"evenly":case"between":l="space-".concat(l)}return(n={})["align-".concat(i)]=u?"".concat(l,"-").concat(u):l,n;default:var d=v(t,i,l);return d?((c={})["align-".concat(i)]=d[i],c):void 0}},v:function(t){for(var e,n,c=[],a=1;a<arguments.length;a++)c[a-1]=arguments[a];var o=b(c),r=o[0],l=o[1],u=o[2];return!0===s(t,"".concat(r,"-(auto|").concat(j.number_decimal_unit_full,")"),"-?")?(l=h.full(l,u),(e={})[r]=i.value("".concat((null===(n=null==t?void 0:t.startsWith)||void 0===n?void 0:n.call(t,"-"))?t[0]:"").concat(l)),e):v(t,r,l)},w:function(t){for(var e,n,c,a,o,l=[],u=1;u<arguments.length;u++)l[u-1]=arguments[u];var d=b(l),f=d[0],m=d[1],g=d[2],y=d[3],w={"--un-skew-x":0,"--un-skew-y":0,"--un-scale-x":1,"--un-scale-y":1,"--un-scale-z":1,"--un-rotate":0,"--un-rotate-x":0,"--un-rotate-y":0,"--un-rotate-z":0,"--un-translate-x":0,"--un-translate-y":0,"--un-translate-z":0,transform:"translateX(var(--un-translate-x)) translateY(var(--un-translate-y)) translateZ(var(--un-translate-z)) rotate(var(--un-rotate)) rotateX(var(--un-rotate-x)) rotateY(var(--un-rotate-y)) rotateZ(var(--un-rotate-z)) skewX(var(--un-skew-x)) skewY(var(--un-skew-y)) scaleX(var(--un-scale-x)) scaleY(var(--un-scale-y)) scaleZ(var(--un-scale-z))"},x=(null===(o=null==t?void 0:t.startsWith)||void 0===o?void 0:o.call(t,"-"))?"-":"";switch(!0){case s(t,"transform-(none|hidden)"):return(e={})[f]="hidden"===m?"none":m,e;case s(t,"skew-([xy]-".concat(j.decimal,"(deg|turn|rad)?|").concat(j.decimal,"(deg|turn|rad)?)")):case s(t,"rotate-([xyz]-".concat(j.decimal,"(deg|turn|rad)?|").concat(j.decimal,"(deg|turn|rad)?)")):var k={};switch(m){case"x":case"y":case"z":k["--un-".concat(f,"-").concat(m)]=i.number(g)&&"0"!==g?"".concat(x).concat(g,"deg"):g;break;default:var _=i.number(m)&&"0"!==m?"".concat(x).concat(m,"deg"):m;"skew"===f?(k["--un-".concat(f,"-x")]=_,k["--un-".concat(f,"-y")]=_):k["--un-".concat(f)]=_}return A(A({},w),k);case s(t,"translate-([xyz]-".concat(j.number_decimal_unit_full,"|").concat(j.number_decimal_unit_full,")"),"-?"):switch(k={},m=h.full(m,g),g=h.full(g,y),m){case"x":case"y":case"z":k["--un-translate-".concat(m)]=i.value("".concat(x).concat(g));break;default:k["--un-translate-x"]=i.value("".concat(x).concat(m)),k["--un-translate-y"]=i.value("".concat(x).concat(m))}return A(A({},w),k);case s(t,"scale-([xyz]-".concat(j.decimal,"|").concat(j.decimal,")"),"-?"):switch(k={},m){case"x":case"y":case"z":k["--un-scale-".concat(m)]="".concat(x).concat(g);break;default:k["--un-scale-x"]="".concat(x).concat(m),k["--un-scale-y"]="".concat(x).concat(m)}return A(A({},w),k);case s(t,"transform-(".concat(r,")")):return v(t,f,m);case s(t,"transform-(content|border|fill|stroke|view)"):return(n={})["".concat(f,"-box")]="".concat(m,"-box"),n;case s(t,"transform-(flat|3d)"):return(c={})["".concat(f,"-style")]="3d"===m?"preserve-".concat(m):m,c;case s(t,"transform-(c|t|r|b|l)(c|t|r|b|l)?"):var E=function(t){return p[m[t]]||"center"};return(a={})["".concat(f,"-origin")]="".concat(E(0)).concat(p[m[1]]||"c"===m[1]?" ".concat(E(1)):""),a;case s(t,"transform-(origin|style|box)-(".concat(r,")")):return f="".concat(f,"-").concat(m),v(t,f,g)}},x:function(t){for(var e,n,c=[],a=1;a<arguments.length;a++)c[a-1]=arguments[a];var o=b(c),l=o[0],u=o[1],d=o[2];switch(!0){case s(t,"".concat(l,"-(none|hidden|").concat(j.number_decimal_unit,")$")):return(e={})[l]="hidden"===u?"none":i.value(u),e;case s(t,"".concat(l,"-[xy]([xy])?")):return(n={})["".concat(l,"-origin")]="".concat(u[0],"-position").concat(u[1]?" ".concat(u[1],"-position"):""),n;case s(t,"".concat(l,"-origin-(").concat(r,")")):return l="".concat(l,"-").concat(u),v(t,l,d);default:return v(t,l,u)}},y:function(t){for(var e,n,c,a,o,r,l=[],d=1;d<arguments.length;d++)l[d-1]=arguments[d];var f=b(l),p=f[0],m=f[1],g=f[2];switch(!0){case s(t,"".concat(p,"-(visible|hidden|clip|scroll|auto)")):return(e={})[u[p]||p]=m,e;case s(t,"".concat(p,"-anchor-(auto|none|hidden)")):return(n={})["".concat(u[p]||p,"-").concat(m)]="hidden"===g?"none":g,n;case s(t,"".concat(p,"-(block|inline|x|y)-(visible|hidden|clip|scroll|auto)")):return(c={})["".concat(u[p]||p,"-").concat(m)]=g,c;case s(t,"".concat(p,"-").concat(j.number_decimal_unit)):return(a={})["".concat(u[p]||p,"-clip-margin")]=i.value(m),a;case s(t,"".concat(p,"-(normal|break|anywhere)")):return(o={})["".concat(u[p]||p,"-wrap")]="break"===m?"".concat(m,"-word"):m,o;case s(t,"".concat(p,"-(anchor|block|clip|inline|wrap|x|y)-(").concat(h.globl_value.join("|"),")")):var y=v(t,"".concat(p,"-").concat(m),g);return y?((r={})["".concat(u[p]||p,"-").concat(m).concat("clip"===m?"-margin":"")]=y[m],r):void 0;default:return v(t,p,m)}},z:function(t){for(var e,n,c,a=[],o=1;o<arguments.length;o++)a[o-1]=arguments[o];var l=b(a),u=l[0],d=l[1],f=l[2],p=l[3];switch(!0){case s(t,"".concat(u,"-").concat(j.number_decimal_unit_full)):return(e={})[u]=i.value(h.full(d,f)),e;case s(t,"".concat(u,"-row-").concat(j.number_decimal_unit_full)):case s(t,"".concat(u,"-col-(").concat(j.number_decimal_unit_full,"|normal)")):return d="col"===d?"column":d,f="full"===f?p?"".concat(p,"%"):"100%":f,(n={})["".concat(d,"-").concat(u)]=i.value(f),n;case s(t,"".concat(u,"-(row|col)-(").concat(r,")$")):var m=v(t,"".concat(u,"-").concat(d),f);return f=d,d="col"===d?"column":d,m?((c={})["".concat(d,"-").concat(u)]=m[f],c):void 0;default:return v(t,u,d)}},aa:function(t){for(var e,n,l,u,d,f,p=[],h=1;h<arguments.length;h++)p[h-1]=arguments[h];var m=b(p),g=m[0],y=m[1],w=m[2],x=m[3];switch(!0){case s(t,"".concat(g,"-(none|hidden|all)")):return{"column-span":"hidden"===y?"none":y};case s(t,"".concat(g,"-(auto|").concat(j.number_decimal_unit,")")):return{"column-width":i.value(y)};case s(t,"".concat(g,"-fill-(auto|balance)")):case s(t,"".concat(g,"-count-(auto|").concat(j.number,")")):return(e={})["column-".concat(y)]=w,e;case s(t,"".concat(g,"-rule-(").concat(a,")")):case s(t,"".concat(g,"-rule-(").concat(o,")").concat(j.opacity)):case s(t,"".concat(g,"-rule-").concat(j.color,"-color")):return(n={})["column-".concat(y,"-color")]=c(m.slice(2)),n;case s(t,"".concat(g,"-rule-(none|hidden|dotted|dashed|solid|double|groove|ridge|inset|outset)")):return(l={})["column-".concat(y,"-style")]=w,l;case s(t,"".concat(g,"-rule-(thin|medium|thick|").concat(j.number_decimal_unit,")")):return(u={})["column-".concat(y,"-width")]=i.value(w),u;case s(t,"".concat(g,"-rule-(color|style|width)-(").concat(r,")")):var k=v(t,"".concat(g,"-").concat(y,"-").concat(w),x);return k?((d={})["column-".concat(y,"-").concat(w)]=k[w],d):void 0;case s(t,"".concat(g,"-(count|fill|rule|span|width)-(").concat(r,")")):var _=v(t,"".concat(g,"-").concat(y),w);return _?((f={})["column-".concat(y)]=_[y],f):void 0}},ab:function(t){for(var e,n,c=[],a=1;a<arguments.length;a++)c[a-1]=arguments[a];var o=b(c),r=o[0],i=o[1];if(!0===s(t,"".concat(r,"-(contain|cover|fill|none|scale|hidden)")))return(e={})["object-".concat(r)]="scale"===i?"".concat(i,"-down"):"hidden"===i?"none":i,e;var l=v(t,r,i);return l?((n={})["object-".concat(r)]=l[r],n):void 0},ac:function(t){for(var e,n,c,a,o,l,u=[],d=1;d<arguments.length;d++)u[d-1]=arguments[d];var f=b(u),p=f[0],h=f[1],m=f[2],g=f[3];switch(!0){case s(t,"".concat(p,"(-").concat(j.decimal,"(m?s)?)?")):return{"transition-timing-function":"cubic-bezier(.4,0,.2,1)","transition-duration":h?i.number(h)?"".concat(h,"ms"):h:"150ms","transition-property":"color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter"};case s(t,"".concat(p,"-(none|hidden)")):return(e={})[p]="hidden"===h?"none":h,e;case s(t,"".concat(p,"-(all|margin|padding|((min|max)-)?(width|height)|opacity|color|bg|border|transform|size|shadow|colors)(-").concat(j.decimal,")?")):switch(h){case"bg":h="background-color";break;case"size":h="font-".concat(h);break;case"shadow":h="box-".concat(h);break;case"colors":h="color, background-color, border-color, outline-color, text-decoration-color, fill, stroke";break;case"min":case"max":h="".concat(h,"-").concat(m),m=g}return{"transition-property":h,"transition-timing-function":"cubic-bezier(.4,0,.2,1)","transition-duration":m?i.number(m)?"".concat(m,"ms"):m:"150ms"};case s(t,"".concat(p,"-(normal|allow)")):return(n={})["".concat(p,"-behavior")]="allow"===h?"".concat(h,"-discrete"):h,n;case s(t,"".concat(p,"-(delay|duration)-").concat(j.decimal,"(m?s)?")):return(c={})["".concat(p,"-").concat(h)]=i.number(m)?"".concat(m,"ms"):m,c;case s(t,"".concat(p,"-property-(none|hidden|all|margin|padding|((min|max)-)?(width|height)|opacity|color|bg|border|transform|size|shadow|colors)")):switch(m){case"bg":m="background-color";break;case"size":m="font-".concat(m);break;case"shadow":m="box-".concat(m);break;case"colors":m="color, background-color, border-color, outline-color, text-decoration-color, fill, stroke";break;case"min":case"max":m="".concat(m,"-").concat(g)}return(a={})["".concat(p,"-property")]=m,a;case s(t,"".concat(p,"-(ease(-(in(-out)?|out|))?|linear|start|end)")):return h=["start","end"].includes(h)?"step-".concat(h):h,(o={})["".concat(p,"-timing-function")]=m?"".concat(h,"-").concat(m).concat(g?"-".concat(g):""):h,o;case s(t,"".concat(p,"-(behavior|delay|duration|property|timing)-(").concat(r,")")):p="".concat(p,"-").concat(h);var y=v(t,p,m);return y?((l={})["".concat(p).concat("timing"===h?"-function":"")]=y[h],l):void 0;default:return v(t,p,h)}},ad:function(t){for(var e,n,c,a=[],o=1;o<arguments.length;o++)a[o-1]=arguments[o];var i=b(a),l=i[0],u=i[1],d=i[2];switch(!0){case s(t,"".concat(l,"-(auto|loose|normal|strict|anywhere)")):return(e={})["".concat(l,"-break")]=u,e;case s(t,"".concat(l,"-clamp-(none|hidden|").concat(j.number,")")):d="hidden"===d?"none":d;var f=((n={overflow:"hidden",display:"-webkit-box"})["".concat(l,"-").concat(u)]=d,n["-webkit-box-orient"]="vertical",n["-webkit-".concat(l,"-").concat(u)]=d,n);return"none"===d&&(delete f.display,delete f.overflow,delete f["-webkit-box-orient"]),f;case s(t,"^".concat(l,"-clamp-(").concat(r,")")):return l="".concat(l,"-").concat(u),v(t,l,d);default:var p=v(t,l,u);return p?((c={})["".concat(l,"-break")]=p[l],c):void 0}},ae:function(t){for(var e,n,c,a,o=[],i=1;i<arguments.length;i++)o[i-1]=arguments[i];var l=b(o),u=l[0],d=l[1],f=l[2];switch(!0){case s(t,"".concat(u,"-(slice|clone)")):return(e={})["".concat(u,"-decoration-break")]=d,e;case s(t,"".concat(u,"-(none|hidden)")):return(n={})["".concat(u,"-shadow")]="hidden"===d?"none":d,n;case s(t,"".concat(u,"-(border|content)")):return(c={})["".concat(u,"-sizing")]="".concat(d,"-box"),c;case s(t,"".concat(u,"-(break|shadow|sizing)-(").concat(r,")")):var p="";switch(d){case"break":p="".concat(u,"-decoration-break");break;case"sizing":case"shadow":p="".concat(u,"-sizing")}u="".concat(u,"-").concat(d);var h=v(t,u,f);return h?((a={})[p]=h[u],a):void 0}},af:function(t){for(var e,n=[],c=1;c<arguments.length;c++)n[c-1]=arguments[c];var a=b(n),o=a[0],i=a[1],l=a[2],u=a[3];switch(!0){case s(t,"".concat(o,"-inside-(auto|avoid(-(page|column|region))?)")):case s(t,"".concat(o,"-(after|before)-(auto|avoid(-(page|column|region))?|always|all|page|left|right|recto|verso|column|region)")):return(e={})["".concat(o,"-").concat(l)]=u?"".concat(l,"-").concat(u):l,e;case s(t,"".concat(o,"-(after|before|inside)-(").concat(r,")")):return o="".concat(o,"-").concat(i),v(t,o,l)}},ag:function(t){for(var e,n=[],c=1;c<arguments.length;c++)n[c-1]=arguments[c];var a=b(n),o=a[0],r=a[1],l=a[2];switch(!0){case s(t,"".concat(o,"-").concat(j.number_decimal_unit_full),"-?"):case s(t,"".concat(o,"-(baseline|sub|super|(text-)?(top|bottom)|middle)")):return l=l?"".concat(r,"-").concat(l):h.full(r,l),{"vertical-align":i.value("".concat((null===(e=null==t?void 0:t.startsWith)||void 0===e?void 0:e.call(t,"-"))?"-":"").concat(l))};default:var u=v(t,o,r);return u?{"vertical-align":u[o]}:void 0}},ah:function(t){for(var e,n,c=[],a=1;a<arguments.length;a++)c[a-1]=arguments[a];var o=b(c),r=o[0],l=o[1],u=o[2];switch(!0){case s(t,"".concat(r,"-").concat(j.number_decimal_unit_full)):case s(t,"".concat(r,"-(math|larger|large|smaller|medium|x{0,2}-(small|large)|xxx-large)")):return u=u?"".concat(l,"-").concat(u):h.full(l,u),(e={})[d.size]=i.value(l),e;default:var f=v(t,r,l);return f?((n={})[d.size]=f[r],n):void 0}},ai:function(t){for(var e,n=[],c=1;c<arguments.length;c++)n[c-1]=arguments[c];var a=b(n),o=a[0],r=a[1];return!0===s(t,"".concat(o,"-(normal|").concat(j.number_decimal_unit,")"))?((e={})[u[o]]=i.value(r),e):v(t,o,r,{end:3})}}}(e.theme.colors,e.prefix.join("|")),v={w:(r=s).a,h:r.a,min:r.b,max:r.b,p:r.c,px:r.c,py:r.c,pt:r.c,pr:r.c,pb:r.c,pl:r.c,m:r.c,mx:r.c,my:r.c,mt:r.c,mr:r.c,mb:r.c,ml:r.c,b:r.aj,bx:r.aj,by:r.aj,bt:r.aj,br:r.aj,bb:r.aj,bl:r.aj,fixed:r.h,sticky:r.h,static:r.h,absolute:r.h,relative:r.h,position:r.h,flex:r.o,list:r.q,table:r.r,flow:r.n,none:r.n,block:r.n,inline:r.n,hidden:r.n,display:r.n,contents:r.n,self:r.t,items:r.t,content:r.u,top:r.v,left:r.v,right:r.v,bottom:r.v,skew:r.w,scale:r.w,rotate:r.w,transform:r.w,translate:r.w,rd:r.e,round:r.e,z:r.g,zi:r.g,op:r.f,opacity:r.f,ws:r.l,space:r.l,of:r.y,overflow:r.y,v:r.ag,va:r.ag,lh:r.d,fs:r.ah,bg:r.i,grid:r.p,text:r.j,box:r.ae,gap:r.z,fit:r.ab,col:r.aa,color:r.i,line:r.ad,word:r.m,font:r.k,break:r.af,letter:r.ai,justify:r.s,transition:r.ac,perspective:r.x};return null===(n=v[e.prefix.includes(a)?o:a])||void 0===n?void 0:n.call.apply(n,O([v,t],c,!1))}(a,n);if(v){if(s){for(var b in v)v[b]="".concat(v[b]).concat(s?" !".concat(s):"");a="".concat(a,"-").concat(s)}a.includes(".")&&(a=a.toString().replace(/\./g,l.underline)),t=t.replace(new RegExp(o,"g"),a),e.push({name:a,value:v})}else c.push(o);return e}),[]);return{code:t,styles:a,notMatchClassNames:c}}function R(){return function(t){return{name:"unocss:presets:media",transform:function(e){var n=e,c=function(e,c){var a=s(n,e),o=z(a.code,a.classNames,t);o.styles=o.styles.map((function(t){return t.name="".concat(c,"-").concat(t.name),t})),t.callback(o.styles,o.notMatchClassNames),n=o.code},a=Object.keys(t.theme.breakpoints);t.prefix.forEach((function(t){if(a.includes(t))throw new Error('prefix "'.concat(t,'" is already used by breakpoints'))})),i.className(e).forEach((function(e){var n,o=e.split(l.underline).filter(Boolean),r=o[0],i=o[1];o.length>1&&a.includes(r)?c([o.slice(1).join(l.underline)],r):o.length>2&&(null===(n=t.prefix)||void 0===n?void 0:n.includes(r))&&a.includes(i)&&c([o.slice(2).join(l.underline)],"".concat(r,"-").concat(i))}))}}}}function N(t){var e=void 0===t?{}:t,n=e.width,c=void 0===n?1:n,a=e.style,o=void 0===a?"solid":a,r=e.color,l=void 0===r?"currentColor":r;return function(t){var e=m(t.theme.colors),n=e.color,a=e.colorString,r=e.colorOpacityString,s="".concat(a,"|(").concat(r,")").concat(j.opacity),u=Object.keys(t.theme.breakpoints),d=function(e){var n;return e.slice(Number(t.prefix.includes(e[0]))).slice(Number(null!==(n=u.includes(e[0]))&&void 0!==n?n:u.includes(e[1])))},f="((".concat(t.prefix.join("|"),")-((").concat(u.join("|"),")-)?)?");return{name:"unocss:presets:border",rules:[["^".concat(f,"border-").concat(j.number_unit,"(-([a-z0-9A-Z]+").concat(j.opacity,"(-color)?|").concat(s,"))?(-important)?$"),function(t,e){var c,a,r=d(t),s=t.includes("important")?" !important":"";s&&r.splice(r.length-1,1);var u=r[0],f=r[1],p=r[2];return r[3],p=i.toRgb(p?null!==(a=e[p])&&void 0!==a?a:"#".concat(p):l),p=n(r.slice(2)),(c={})["".concat(u,"-style")]=o,c["".concat(u,"-color")]="".concat(p).concat(s),c["".concat(u,"-width")]="".concat(i.value(f)).concat(s),c}],["^".concat(f,"border-(top|bottom|left|right)-").concat(j.number_unit,"(-([a-z0-9A-Z]+").concat(j.opacity,"(-color)?|").concat(s,"))?(-important)?$"),function(t,e){var c,a,r=d(t),s=t.includes("important")?" !important":"";s&&r.splice(r.length-1,1);var u=r[0],f=r[1],p=r[2],h=r[3];return r[4],h=i.toRgb(h?null!==(a=e[h])&&void 0!==a?a:"#".concat(h):l),h=n(r.slice(3)),(c={})["".concat(u,"-").concat(f,"-style")]=o,c["".concat(u,"-").concat(f,"-color")]="".concat(h).concat(s),c["".concat(u,"-").concat(f,"-width")]="".concat(i.value(p)).concat(s),c}],["^".concat(f,"border-(x|y)-").concat(j.number_unit,"(-([a-z0-9A-Z]+").concat(j.opacity,"(-color)?|").concat(s,"))?(-important)?$"),function(t,e){var c,a,r=d(t),s=t.includes("important")?" !important":"";s&&r.splice(r.length-1,1);var u=r[0],f=r[1],p=r[2],h=r[3],m=(r[4],"x"===f?"left":"top"),v="x"===f?"right":"bottom";return h=i.toRgb(h?null!==(a=e[h])&&void 0!==a?a:"#".concat(h):l),h=n(r.slice(3)),(c={})["".concat(u,"-").concat(m,"-style")]=o,c["".concat(u,"-").concat(v,"-style")]=o,c["".concat(u,"-").concat(m,"-color")]="".concat(h).concat(s),c["".concat(u,"-").concat(v,"-color")]="".concat(h).concat(s),c["".concat(u,"-").concat(m,"-width")]="".concat(i.value(p)).concat(s),c["".concat(u,"-").concat(v,"-width")]="".concat(i.value(p)).concat(s),c}],["^".concat(f,"border-([a-z0-9A-Z]+").concat(j.opacity,"(-color)?|").concat(s,")(-important)?$"),function(t,e){var a,r,s=d(t),u=t.includes("important")?" !important":"";u&&s.splice(s.length-1,1);var f=s[0],p=s[1];return s[2],p=i.toRgb(p?null!==(r=e[p])&&void 0!==r?r:"#".concat(p):l),p=n(s.slice(1)),(a={})["".concat(f,"-style")]=o,a["".concat(f,"-color")]="".concat(p).concat(u),a["".concat(f,"-width")]="".concat(i.value(c.toString())).concat(u),a}],["^".concat(f,"border-(top|bottom|left|right)-([a-z0-9A-Z]+").concat(j.opacity,"(-color)?|").concat(s,")(-important)?$"),function(t,e){var a,r,s=d(t),u=t.includes("important")?" !important":"";u&&s.splice(s.length-1,1);var f=s[0],p=s[1],h=s[2];return s[3],h=i.toRgb(h?null!==(r=e[h])&&void 0!==r?r:"#".concat(h):l),h=n(s.slice(2)),(a={})["".concat(f,"-").concat(p,"-style")]=o,a["".concat(f,"-").concat(p,"-color")]="".concat(h).concat(u),a["".concat(f,"-").concat(p,"-width")]="".concat(i.value(c.toString())).concat(u),a}],["^".concat(f,"border-(x|y)-([a-z0-9A-Z]+").concat(j.opacity,"(-color)?|").concat(s,")(-important)?$"),function(t,e){var a,r,s=d(t),u=t.includes("important")?" !important":"";u&&s.splice(s.length-1,1);var f=s[0],p=s[1],h=s[2],m=(s[3],"x"===p?"left":"top"),v="x"===p?"right":"bottom";return h=i.toRgb(h?null!==(r=e[h])&&void 0!==r?r:"#".concat(h):l),h=n(s.slice(2)),(a={})["".concat(f,"-").concat(m,"-style")]=o,a["".concat(f,"-").concat(v,"-style")]=o,a["".concat(f,"-").concat(m,"-color")]="".concat(h).concat(u),a["".concat(f,"-").concat(v,"-color")]="".concat(h).concat(u),a["".concat(f,"-").concat(m,"-width")]="".concat(i.value(c.toString())).concat(u),a["".concat(f,"-").concat(v,"-width")]="".concat(i.value(c.toString())).concat(u),a}]]}}}function I(){return function(t){return{name:"unocss:presets:default",transform:function(e){var n=s(e,i.className(e).map((function(t){return t.replace(/(.*\(|,|\))/g,"").split(/\x20/)})).flat().filter((function(t){return!/[\(\):]/.test(t)}))),c=z(n.code,n.classNames,t);return t.callback(c.styles,c.notMatchClassNames),c.code}}}}var $=["hover","after","before","placeholder","selection","active","checked","disabled","empty","enabled","focus","visible","within","required"];function C(){return function(t){return{name:"unocss:presets:pseudo",transform:function(e){var n=e;return i.className(e,!1).forEach((function(e){e.includes(":")&&Array.from(new Set(e.match(new RegExp("(".concat($.join("|"),"):(\\(((-?#?[a-z0-9]+(.[0-9]+)?([! %]+)?)+)?\\)|[^ (]+)"),"g")))).sort((function(t,e){return e.length-t.length})).forEach((function(e){var c=new Set([]);if(e.includes("(")){var a=e.split(":")[0],o="";Array.from(new Set(e.match(new RegExp("(-?#?[a-z0-9]+(.[0-9]+)?([!%]+)?)+","g")))).forEach((function(t){a!==t&&(t="".concat(a,"-").concat(t),c.add(t),o+="".concat(t," "))})),n=n.replace(new RegExp(e.replace(")","\\)").replace("(","\\("),"g"),o.trim())}else{var r=e.replace(":",l.underline);n=n.replace(new RegExp(e,"g"),r),c.add(r)}Array.from(c).sort((function(t,e){return e.length-t.length})).forEach((function(e){var c=e.split(l.underline),a=c[0],o=c.slice(1),r=s(n,[o.join(l.underline)]),i=z(r.code,r.classNames,t);n=i.code,i.styles=i.styles.map((function(t){var e="";switch(a){case"hover":case"focus":case"empty":case"active":case"checked":case"enabled":case"required":case"disabled":e=":".concat(a);break;case"after":case"before":case"selection":case"placeholder":e="::".concat(a);break;case"within":case"visible":e=":focus-".concat(a)}return t.name="".concat(a,"-").concat(t.name).concat(e),t})),t.callback(i.styles,i.notMatchClassNames)}))}))})),n}}}}var T=function(t,e,n){if(n||2===arguments.length)for(var c,a=0,o=e.length;a<o;a++)!c&&a in e||(c||(c=Array.prototype.slice.call(e,0,a)),c[a]=e[a]);return t.concat(c||Array.prototype.slice.call(e))},P=v.join("|"),Z=["m(argin)?\\(((-?[0-9]+(".concat(P,"|%)?|auto)(!|important)?( +)?){1,4}\\)"),"(p(adding)?|rd|round)\\(([0-9]+(".concat(P,"|%)?(!|important)?( +)?){1,4}\\)"),"b(t|r|b|l)?(order(-(top|right|bottom|left))?)?\\([0-9]+(".concat(P,")?(!|important)?( +)?(none|hidden|dotted|dashed|solid|double|groove|ridge|inset|outset)?( +)?(#?[a-zA-Z0-9]+)?\\)")],q={m:"margin",p:"padding",b:"border",bt:"border-top",bl:"border-left",br:"border-right",bb:"border-bottom"};function W(){return function(t){var e=Object.keys(t.theme.breakpoints),n=function(n,c){return T(T(T([],t.prefix,!0),e,!0),t.prefix.map((function(t){return e.map((function(e){return"".concat(t,"-").concat(e)}))})).flat(),!0).map((function(t){return"".concat(t,"-").concat(n)})).find((function(t){return t===c}))||n};return{name:"unocss:presets:sketch",transform:function(c){var a=t.prefix.join("|"),o=c;return i.className(c,!1).forEach((function(c){Z.forEach((function(r){Array.from(new Set(c.match(new RegExp("(((".concat(a,")-)?((").concat(e.join("|"),")-)?)?").concat(r),"g")))).forEach((function(e){var c=e.replace(/\)/,"").split(/\(/),a=c[0],r=c[1].split(/\x20+/).filter(Boolean).map((function(t){return t.replace(/(!|important)/g," !important")})),s={},u=a.split(l.underline),d=u[u.length-1];switch(d=q[d]||d,a){case n("m",a):case n("p",a):case n("margin",a):case n("padding",a):switch(r.length){case 1:s["".concat(d,"-top")]=s["".concat(d,"-bottom")]=i.value(r[0]);break;case 2:s["".concat(d,"-top")]=s["".concat(d,"-bottom")]=i.value(r[0]),s["".concat(d,"-left")]=s["".concat(d,"-right")]=i.value(r[1]);break;case 3:s["".concat(d,"-top")]=i.value(r[0]),s["".concat(d,"-left")]=s["".concat(d,"-right")]=i.value(r[1]),s["".concat(d,"-bottom")]=i.value(r[2]);break;case 4:s["".concat(d,"-top")]=i.value(r[0]),s["".concat(d,"-right")]=i.value(r[1]),s["".concat(d,"-bottom")]=i.value(r[2]),s["".concat(d,"-left")]=i.value(r[3])}break;case n("rd",a):case n("round",a):switch(r.length){case 1:s["border-top-left-radius"]=s["border-bottom-right-radius"]=i.value(r[0]);break;case 2:s["border-top-left-radius"]=s["border-bottom-right-radius"]=i.value(r[0]),s["border-top-right-radius"]=s["border-bottom-left-radius"]=i.value(r[1]);break;case 3:s["border-top-left-radius"]=i.value(r[0]),s["border-top-right-radius"]=s["border-bottom-left-radius"]=i.value(r[1]),s["border-bottom-right-radius"]=i.value(r[2]);break;case 4:s["border-top-left-radius"]=i.value(r[0]),s["border-top-right-radius"]=i.value(r[1]),s["border-bottom-right-radius"]=i.value(r[2]),s["border-bottom-left-radius"]=i.value(r[3])}break;case n("b",a):case n("bt",a):case n("br",a):case n("bb",a):case n("bl",a):case n("border",a):case n("border-top",a):case n("border-left",a):case n("border-right",a):case n("border-bottom",a):switch(r.length){case 1:s["".concat(d,"-width")]=i.value(r[0]);break;case 2:s["".concat(d,"-width")]=i.value(r[0]),s["".concat(d,"-style")]=i.value(r[1]);break;case 3:s["".concat(d,"-width")]=i.value(r[0]),s["".concat(d,"-style")]=i.value(r[1]),s["".concat(d,"-color")]=r[2].startsWith("#")?r[2]:t.theme.colors[r[2]]||r[2]}}var f=r.reduce((function(t,e){return"".concat(t,"-").concat(e.replace(/[#!]/,"").replace("important","").trim())}),"");f="".concat(a).concat(f.endsWith(l.underline)?f.slice(0,-1):f),o=o.replace(new RegExp(e.replace(")","\\)").replace("(","\\("),"g"),f),t.callback({name:f,value:s},Object.keys(s).length?"":e)}))}))})),o}}}}const M=require("node:fs");function B(){var t="".concat(__dirname,"/index.scss"),e=function(){M.writeFileSync(t,"",{encoding:"utf-8"})},n=function(){try{return M.readFileSync(t,{encoding:"utf-8"})}catch(t){e()}};return{list:function(){var t,e;return new Set(null===(e=null===(t=n())||void 0===t?void 0:t.match(/\.(-?[\d\w]+)+/g))||void 0===e?void 0:e.map((function(t){return t.slice(1)})))},value:n,write:function(e){M.writeFileSync(t,"".concat(n()).concat(e,"\n"),{encoding:"utf-8"})},clear:e,cover:function(e){M.writeFileSync(t,"".concat(e,"\n"),{encoding:"utf-8"})}}}function F(t,e){if(void 0===e&&(e=""),"string"!=typeof t&&"object"!=typeof t||"object"==typeof t&&Array.isArray(t))throw new Error("options".concat(e,".colors must be a string or object"))}var D=function(){return D=Object.assign||function(t){for(var e,n=1,c=arguments.length;n<c;n++)for(var a in e=arguments[n])Object.prototype.hasOwnProperty.call(e,a)&&(t[a]=e[a]);return t},D.apply(this,arguments)},V={aliceBlue:"240,248,255",antiquewhite:"250,235,215",aqua:"0,255,255",aquamarine:"127,255,212",azure:"240,255,255",beige:"245,245,220",bisque:"255,228,196",black:"0,0,0",blanchedalmond:"255,235,205",blue:"0,0,255",blueviolet:"138,43,226",brown:"165,42,42",burlywood:"222,184,135",cadetblue:"95,158,160",chartreuse:"127,255,0",chocolate:"210,105,30",coral:"255,127,80",cornflowerblue:"100,149,237",cornsilk:"255,248,220",crimson:"220,20,60",cyan:"0,255,255",darkblue:"0,0,139",darkcyan:"0,139,139",darkgoldenrod:"184,134,11",darkgray:"169,169,169",darkgreen:"0,100,0",darkkhaki:"189,183,107",darkmagenta:"139,0,139",darkolivegreen:"85,107,47",darkorange:"255,140,0",darkorchid:"153,50,204",darkred:"139,0,0",darksalmon:"233,150,122",darkseagreen:"143,188,143",darkslateblue:"72,61,139",darkslategray:"47,79,79",darkturquoise:"0,206,209",darkviolet:"148,0,211",deeppink:"255,20,147",deepskyblue:"0,191,255",dimgray:"105,105,105",dodgerblue:"30,144,255",firebrick:"178,34,34",floralwhite:"255,250,240",forestgreen:"34,139,34",fuchsia:"255,0,255",gainsboro:"220,220,220",ghostwhite:"248,248,255",gold:"255,215,0",goldenrod:"218,165,32",gray:"128,128,128",green:"0,128,0",greenyellow:"173,255,47",honeydew:"240,255,240",hotpink:"255,105,180",indianred:"205,92,92",indigo:"75,0,130",ivory:"255,255,240",khaki:"240,230,140",lavender:"230,230,250",lavenderblush:"255,240,245",lawngreen:"124,252,0",lemonchiffon:"255,250,205",lightblue:"173,216,230",lightcoral:"240,128,128",lightcyan:"224,255,255",lightgoldenrodyellow:"250,250,210",lightgray:"211,211,211",lightgreen:"144,238,144",lightpink:"255,182,193",lightsalmon:"255,160,122",lightseagreen:"32,178,170",lightskyblue:"135,206,250",lightslategray:"119,136,153",lightsteelblue:"176,196,222",lightyellow:"255,255,224",lime:"0,255,0",limegreen:"50,205,50",linen:"250,240,230",magenta:"255,0,255",maroon:"128,0,0",mediumaquamarine:"102,205,170",mediumblue:"0,0,205",mediumorchid:"186,85,211",mediumpurple:"147,112,219",mediumseagreen:"60,179,113",mediumslateblue:"123,104,238",mediumspringgreen:"0,250,154",mediumturquoise:"72,209,204",mediumvioletred:"199,21,133",midnightblue:"25,25,112",mintcream:"245,255,250",mistyrose:"255,228,225",moccasin:"255,228,181",navajowhite:"255,222,173",navy:"0,0,128",oldlace:"253,245,230",olive:"128,128,0",olivedrab:"107,142,35",orange:"255,165,0",orangered:"255,69,0",orchid:"218,112,214",palegoldenrod:"238,232,170",palegreen:"152,251,152",paleturquoise:"175,238,238",palevioletred:"219,112,147",papayawhip:"255,239,213",peachpuff:"255,218,185",peru:"205,133,63",pink:"255,192,203",plum:"221,160,221",powderblue:"176,224,230",purple:"128,0,128",red:"255,0,0",rosybrown:"188,143,143",royalblue:"65,105,225",saddlebrown:"139,69,19",salmon:"250,128,114",sandybrown:"244,164,96",seagreen:"46,139,87",seashell:"255,245,238",sienna:"160,82,45",silver:"192,192,192",skyblue:"135,206,235",slateblue:"106,90,205",slategray:"112,128,144",snow:"255,250,250",springgreen:"0,255,127",steelblue:"70,130,180",tan:"210,180,140",teal:"0,128,128",thistle:"216,191,216",tomato:"255,99,71",turquoise:"64,224,208",violet:"238,130,238",wheat:"245,222,179",white:"255,255,255",whitesmoke:"245,245,245",yellow:"255,255,0",yellowgreen:"154,205,50",transparent:"transparent",current:"currentColor"},L="".concat(process.env.VITE_ROOT_DIR,"/uni.scss"),X=function(t){return t.toString().replace(/[A-Z]/g,(function(t,e){return"".concat(e?"-":"").concat(t.toLowerCase())}))},Y=function(t,e,n){if(void 0===e&&(e=""),void 0===n&&(n=0),n>1)throw new Error("Colors Nesting levels cannot exceed two");var c={};for(var a in t)if(F(a),Object.prototype.hasOwnProperty.call(t,a)){var o=t[a],r="default"===a.toLocaleLowerCase()?"":X(a),i=e?"".concat(e).concat(r?"-".concat(r):""):r;"object"==typeof o?Object.assign(c,Y(o,i,n+1)):c[i]=o}return c},H=function(){return H=Object.assign||function(t){for(var e,n=1,c=arguments.length;n<c;n++)for(var a in e=arguments[n])Object.prototype.hasOwnProperty.call(e,a)&&(t[a]=e[a]);return t},H.apply(this,arguments)},G=function(t,e,n,c){return new(n||(n=Promise))((function(a,o){function r(t){try{l(c.next(t))}catch(t){o(t)}}function i(t){try{l(c.throw(t))}catch(t){o(t)}}function l(t){var e;t.done?a(t.value):(e=t.value,e instanceof n?e:new n((function(t){t(e)}))).then(r,i)}l((c=c.apply(t,e||[])).next())}))},U=function(t,e){var n,c,a,o={label:0,sent:function(){if(1&a[0])throw a[1];return a[1]},trys:[],ops:[]},r=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return r.next=i(0),r.throw=i(1),r.return=i(2),"function"==typeof Symbol&&(r[Symbol.iterator]=function(){return this}),r;function i(i){return function(l){return function(i){if(n)throw new TypeError("Generator is already executing.");for(;r&&(r=0,i[0]&&(o=0)),o;)try{if(n=1,c&&(a=2&i[0]?c.return:i[0]?c.throw||((a=c.return)&&a.call(c),0):c.next)&&!(a=a.call(c,i[1])).done)return a;switch(c=0,a&&(i=[2&i[0],a.value]),i[0]){case 0:case 1:a=i;break;case 4:return o.label++,{value:i[1],done:!1};case 5:o.label++,c=i[1],i=[0];continue;case 7:i=o.ops.pop(),o.trys.pop();continue;default:if(!((a=(a=o.trys).length>0&&a[a.length-1])||6!==i[0]&&2!==i[0])){o=0;continue}if(3===i[0]&&(!a||i[1]>a[0]&&i[1]<a[3])){o.label=i[1];break}if(6===i[0]&&o.label<a[1]){o.label=a[1],a=i;break}if(a&&o.label<a[2]){o.label=a[2],o.ops.push(i);break}a[2]&&o.ops.pop(),o.trys.pop();continue}i=e.call(t,o)}catch(t){i=[6,t],c=0}finally{n=a=0}if(5&i[0])throw i[1];return{value:i[0]?i[1]:void 0,done:!0}}([i,l])}}};function J(t,e,n,c,a){return G(this,void 0,void 0,(function(){var o,r,i,l,s,u,d,f,p;return U(this,(function(h){switch(h.label){case 0:o={callback:c,allClassNames:a,theme:t.theme||{},rules:t.rules||[],prefix:t.prefix||[],exclude:t.exclude||[],shortcuts:t.shortcuts||[]},r=e,i=new Set,l=function(e){var c,a,l,s,u,h,m,v,b;return U(this,(function(g){switch(g.label){case 0:return"function"!=typeof(c=t.presets[e])?[3,2]:[4,c(o)];case 1:return l=g.sent(),[3,3];case 2:l=c,g.label=3;case 3:if(function(t,e){var n,c;if("string"!=typeof t.name)throw new Error("options.preset[".concat(e,"].name must be a string"));if(!t.name.startsWith("unocss:presets:"))throw new Error("options.preset[".concat(e,"].name must start with 'unocss:presets:'"));if("unocss:presets:"===t.name)throw new Error("options.preset[".concat(e,"].name must not be 'unocss:presets:'"));if(t.name.endsWith(":"))throw new Error("options.preset[".concat(e,"].name must not end with '-'"));if(t.transform&&"function"!=typeof t.transform)throw new Error("options.preset[".concat(e,"].transform must be a function"));if((null===(n=t.theme)||void 0===n?void 0:n.colors)&&("object"!=typeof t.theme.colors||"object"==typeof t.theme.colors&&Array.isArray(t.theme.colors)))throw new Error("options.preset[".concat(e,"].theme.colors must be an object"));if((null===(c=t.theme)||void 0===c?void 0:c.breakpoints)&&("object"!=typeof t.theme.breakpoints||"object"==typeof t.theme.breakpoints&&Array.isArray(t.theme.breakpoints)))throw new Error("options.preset[".concat(e,"].theme.breakpoints must be an object"));if(t.prefix&&!Array.isArray(t.prefix))throw new Error("options.preset[".concat(e,"].prefix must be a string or an array"));if(t.exclude&&!Array.isArray(t.exclude))throw new Error("options.preset[".concat(e,"].exclude must be a string or an array"));if(t.rules&&!Array.isArray(t.rules))throw new Error("options.preset[".concat(e,"].rules must be an array"));if(t.shortcuts&&!Array.isArray(t.shortcuts))throw new Error("options.preset[".concat(e,"].shortcuts must be an object"));var a=Object.keys(t).filter((function(t){return!["name","transform","prefix","exclude","rules","shortcuts"].includes(t)}));if(a.length)throw new Error("options.preset[".concat(e,"].").concat(a.join(", ")," is not supported"))}(a=l,e),i.has(a.name))throw new Error("Duplicate preset name: ".concat(a.name));return s=n.slice(process.env.VITE_ROOT_DIR.length+1),a.exclude&&(h=o.exclude).push.apply(h,a.exclude),(null===(d=Array.from(new Set(a.exclude)))||void 0===d?void 0:d.find((function(t){return s.includes(t)})))?[2,{value:H(H({},o),{code:r})}]:(i.add(a.name),a.rules&&(m=o.rules).push.apply(m,a.rules),a.prefix&&(v=o.prefix).push.apply(v,a.prefix),a.shortcuts&&(b=o.shortcuts).push.apply(b,a.shortcuts),(null===(f=a.theme)||void 0===f?void 0:f.colors)&&(o.theme.colors=H(H({},o.theme.colors),a.theme.colors)),(null===(p=a.theme)||void 0===p?void 0:p.breakpoints)&&(o.theme.breakpoints=H(H({},o.theme.breakpoints||{}),a.theme.breakpoints)),a.transform?[4,a.transform(r,n)]:[3,5]);case 4:u=g.sent(),r=u||r,g.label=5;case 5:return[2]}}))},s=0,h.label=1;case 1:return s<t.presets.length?[5,l(s)]:[3,4];case 2:if("object"==typeof(u=h.sent()))return[2,u.value];h.label=3;case 3:return s++,[3,1];case 4:return[2,H(H({},o),{code:r})]}}))}))}var K=B(),Q=function(t){return t.replace(/\n+/g,"\n").replace(/\t+/g,"\t")};function tt(t,e,n,c){void 0===c&&(c=!0);var a="",o=K.value(),r="@media(min-width:",s=[],u=Object.keys(e.theme.breakpoints);return t.sort((function(t,e){return t.name<e.name?-1:1})).forEach((function(t){if("object"==typeof t.value&&Object.keys(t.value).length){var d=t.name.split(l.underline),f=d[0],p=d[1],h=e.prefix.includes(f),m=u.includes(h?p:f),v=h?m?2:1:m?1:0,b="".concat(d.slice(v,v+1).join(l.underline),"-"),g=new RegExp("\\.-?".concat(v?b:"","(").concat(function(t){for(var e="",n=t;(null==n?void 0:n.length)>0;)e.length>0&&(e+="|"),e+=n,n=n.slice(0,-1);return e}(d[v+Number(!!v)]),")((-?[a-z0-9A-Z]+)+)?")),y=void 0,w=e.theme.breakpoints[h?p:f],x="".concat(r).concat(i.value(w,"px"),")"),k=Object.keys(t.value).reduce((function(e,c){return"".concat(e).concat(c,":").concat(n(t.value[c]),";")}),"").slice(0,-1),_=".".concat(t.name,"{").concat(k,"}"),j=!0,A=!1,E=function(t,n,c){var a;if(y=null===(a=null==t?void 0:t.match(g))||void 0===a?void 0:a.index,m)if(t.includes(x))A=!0,y=t.indexOf(x)+x.length+2;else{j=!1,A=!0,_="".concat(x,"{\n\t").concat(_,"\n}");for(var o=!0,l=t?u.map((function(t){return e.theme.breakpoints[t]})).filter((function(t){return t>w})):[],s=0;s<l.length;s++){var d=t.indexOf("".concat(r).concat(l[s]));if(~d){y=d,o=!1;break}}o&&(y=void 0)}else t.includes(r)&&(j=!1,A=!0,y=t.indexOf(r));i.number("".concat(y))?n():c()};c?E(o,(function(){o=Q("".concat(o.slice(0,y)).concat(A&&j?"\t":"").concat(_,"\n").concat(o.slice(y)))}),(function(){o+=Q("\n".concat(_))})):a.includes(_)||E(a,(function(){a=Q("".concat(a.slice(0,y)).concat(A&&j?"\t":"").concat(_,"\n").concat(a.slice(y)))}),(function(){a+=Q(_)})),g.lastIndex=0}else s.push(t.name)})),c&&K.cover(o),{notMatchClassNames:s,css:a}}var et=function(){return et=Object.assign||function(t){for(var e,n=1,c=arguments.length;n<c;n++)for(var a in e=arguments[n])Object.prototype.hasOwnProperty.call(e,a)&&(t[a]=e[a]);return t},et.apply(this,arguments)},nt=function(t,e,n){if(n||2===arguments.length)for(var c,a=0,o=e.length;a<o;a++)!c&&a in e||(c||(c=Array.prototype.slice.call(e,0,a)),c[a]=e[a]);return t.concat(c||Array.prototype.slice.call(e))};var ct=function(){return ct=Object.assign||function(t){for(var e,n=1,c=arguments.length;n<c;n++)for(var a in e=arguments[n])Object.prototype.hasOwnProperty.call(e,a)&&(t[a]=e[a]);return t},ct.apply(this,arguments)},at=function(t,e,n){if(n||2===arguments.length)for(var c,a=0,o=e.length;a<o;a++)!c&&a in e||(c||(c=Array.prototype.slice.call(e,0,a)),c[a]=e[a]);return t.concat(c||Array.prototype.slice.call(e))},ot=["uno","xx"],rt="".concat(process.env.VITE_ROOT_DIR,"/log.json"),it=function(t){var e="{}";try{e=M.readFileSync(rt,{encoding:"utf-8"})}catch(t){e="{}"}var n=JSON.stringify(t,null,"\t");"{}"!==n&&e!==n&&M.writeFileSync(rt,n,{flag:"w"})},lt=function(){M.existsSync(rt)&&M.unlinkSync(rt)},st=function(t,e,n,c){return new(n||(n=Promise))((function(a,o){function r(t){try{l(c.next(t))}catch(t){o(t)}}function i(t){try{l(c.throw(t))}catch(t){o(t)}}function l(t){var e;t.done?a(t.value):(e=t.value,e instanceof n?e:new n((function(t){t(e)}))).then(r,i)}l((c=c.apply(t,e||[])).next())}))},ut=function(t,e){var n,c,a,o={label:0,sent:function(){if(1&a[0])throw a[1];return a[1]},trys:[],ops:[]},r=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return r.next=i(0),r.throw=i(1),r.return=i(2),"function"==typeof Symbol&&(r[Symbol.iterator]=function(){return this}),r;function i(i){return function(l){return function(i){if(n)throw new TypeError("Generator is already executing.");for(;r&&(r=0,i[0]&&(o=0)),o;)try{if(n=1,c&&(a=2&i[0]?c.return:i[0]?c.throw||((a=c.return)&&a.call(c),0):c.next)&&!(a=a.call(c,i[1])).done)return a;switch(c=0,a&&(i=[2&i[0],a.value]),i[0]){case 0:case 1:a=i;break;case 4:return o.label++,{value:i[1],done:!1};case 5:o.label++,c=i[1],i=[0];continue;case 7:i=o.ops.pop(),o.trys.pop();continue;default:if(!((a=(a=o.trys).length>0&&a[a.length-1])||6!==i[0]&&2!==i[0])){o=0;continue}if(3===i[0]&&(!a||i[1]>a[0]&&i[1]<a[3])){o.label=i[1];break}if(6===i[0]&&o.label<a[1]){o.label=a[1],a=i;break}if(a&&o.label<a[2]){o.label=a[2],o.ops.push(i);break}a[2]&&o.ops.pop(),o.trys.pop();continue}i=e.call(t,o)}catch(t){i=[6,t],c=0}finally{n=a=0}if(5&i[0])throw i[1];return{value:i[0]?i[1]:void 0,done:!0}}([i,l])}}},dt=function(t,e,n){if(n||2===arguments.length)for(var c,a=0,o=e.length;a<o;a++)!c&&a in e||(c||(c=Array.prototype.slice.call(e,0,a)),c[a]=e[a]);return t.concat(c||Array.prototype.slice.call(e))},ft=B(),pt={},ht=new Set(ft.list());function mt(){return st(this,arguments,void 0,(function(t){var e,wr=new Set(),n=this;return void 0===t&&(t={}),ut(this,(function(c){return t.presets=t.presets||[I()],e=function(t){var e,n,c,log = t.log;t.prefix=t.prefix?Array.isArray(t.prefix)?t.prefix:[t.prefix]:ot,t.prefix=Array.from(new Set(at(at([],ot,!0),t.prefix,!0))),t.exclude=t.exclude?Array.isArray(t.exclude)?t.exclude:[t.exclude]:["node_modules","uni_modules"],t.mode=t.mode||process.env.VITE_USER_NODE_ENV||"development",t.log="function"==typeof t.log?t.log:function(){return!!log},t.rules=Array.isArray(t.rules)?t.rules:t.rules?[t.rules]:[],t.shortcuts=Array.isArray(t.shortcuts)?t.shortcuts:t.shortcuts?[t.shortcuts]:[];var a=t.unit||v[0];return t.theme={generator:!!(null===(e=t.theme)||void 0===e?void 0:e.generator),colors:ct({},(null===(n=t.theme)||void 0===n?void 0:n.colors)||{}),breakpoints:ct({sm:640,md:768,lg:1024,xl:1280,xl2:1536},(null===(c=t.theme)||void 0===c?void 0:c.breakpoints)||{})},t.unit=function(t){switch(!0){case"function"==typeof a:return a(t);case v.includes(a):return t.toString().replace("rpx",a);default:return t}},t}(t),function(t){if(t.presets&&!Array.isArray(t.presets))throw new Error("options.presets must be an array");if(t.prefix&&"string"!=typeof t.prefix&&!Array.isArray(t.prefix))throw new Error("options.prefix must be a string or an array");if(t.exclude&&"string"!=typeof t.exclude&&!Array.isArray(t.exclude))throw new Error("options.exclude must be a string or an array");if(t.theme&&("object"!=typeof t.theme||"object"==typeof t.theme&&Array.isArray(t.theme)))throw new Error("options.colors must be an object");if(!t.mode&&!["development","production"].includes(t.mode))throw new Error("options.mode must be a string and must be one of 'development' or 'production'");if(t.autoImport&&"boolean"!=typeof t.autoImport)throw new Error("options.autoImport must be a boolean");if(t.log&&"boolean"!=typeof t.log&&"function"!=typeof t.log)throw new Error("options.log must be a boolean or a function");if(t.unit&&"function"!=typeof t.unit&&!v.includes(t.unit))throw new Error("options.unit must be a function or a string and must be one of ".concat(v));if(t.rules&&!Array.isArray(t.rules))throw new Error("options.rules must be an array");if(t.shortcuts&&"object"!=typeof t.shortcuts)throw new Error("options.shortcuts must be an object");var e=Object.keys(t).filter((function(t){return!["presets","prefix","exclude","theme","mode","autoImport","log","unit","rules","shortcuts"].includes(t)}));if(e.length)throw new Error("options.".concat(e.join(", ")," is not supported"))}(e),"development"===e.mode&&(ft.clear(),ht.clear()),[2,{enforce:"pre",name:"vite:vue:unocss",transform:function(t,c){return st(n,void 0,void 0,(function(){var n,a,o,r,u,d,f,p,h;return ut(this,(function(m){switch(m.label){case 0:return/(.(u|n)?vue(\?type=page)?)$/.test(c)?(n=c.slice(process.env.VITE_ROOT_DIR.length+1),/^(App.(n|u)?vue)$/.test(n)&&e.autoImport?(function(t,e){var n="".concat(__dirname,"/index.scss").slice(process.env.VITE_ROOT_DIR.replace(/\//g,"\\").length).replace(/\\/g,"/");if(!t.includes(n)){var c='\n\t@import ".'.concat(n,'";');if(t.includes('lang="scss"')){var a=t.indexOf('lang="scss"'),o=t.slice(a).indexOf(">")+1+a;t="".concat(t.slice(0,o)).concat(c).concat(t.slice(o))}else t="".concat(t,'\n\n<style lang="scss">').concat(c,"\n</style>");M.writeFileSync(e,t,{encoding:"utf-8"})}}(t,c),[2]):(e.theme.colors=function(t){var e={};if(t.theme.generator&&M.existsSync(L)){var n=M.readFileSync(L,{encoding:"utf-8"}).match(/\$(-?[a-zA-z0-9])+((\x20)+)?:((\x20)+)?\#?([a-zA-Z0-9\(\)\/(\x20)+)?%,.]+)/g);(null==n?void 0:n.length)&&(e=Object.fromEntries(n.map((function(t){var e=t.split(":"),n=e[0],c=e[1];return n=n.startsWith("$")?n.slice(1):n,[X(n),c]}))))}var c=D(D({},e),Y(t.theme.colors||{}));for(var a in c)(c[a].toString().trim().startsWith("#")||c[a].toString().trim().startsWith("rgb"))&&(V[a]=i.toRgb(c[a]));return V}(e),a={styles:[],currentPageStyles:[],notMatchClassNameSet:new Set},o=function(t,e,n){void 0===e&&(e=[]),void 0===n&&(n=[]),t=Array.isArray(t)?t:[t],(e=Array.isArray(e)?e:[e]).forEach((function(t){a.notMatchClassNameSet.add(t)})),t.forEach((function(t){if(ht.has(t.name)){var e=a.styles.findIndex((function(e){return t.name===e.name}));~e&&(a.styles[e].value=t.value)}else a.styles.push(t),ht.add(t.name);a.currentPageStyles.length&&!a.currentPageStyles.find((function(e){return t.name!==e.name}))||a.currentPageStyles.push(t)})),(n=Array.isArray(n)?n:[n]).forEach((function(t){a.notMatchClassNameSet.delete(t)}))},[4,J(e,t,c,o,(function(){return dt(dt([],Array.from(ft.list()),!0),Array.from(ht),!0)}))])):[3,2];case 1:return r=m.sent(),Array.from(new Set(r.exclude)).find((function(t){return n.includes(t)}))?[2]:(function(t,e,n,c){var a,o=Object.keys(t.theme.colors).reduce((function(e,n){return e[n]=i.rgbToHex(t.theme.colors[n]),e}),{}),r=function(n,a){var o=z("",s("",Array.from(a)).classNames,t),r=[];o.styles.forEach((function(t){c.includes(t.name)&&r.push(t.name)}));var i={name:n,value:o.styles.reduce((function(t,e){return et(et({},t),e.value)}),{})};e(i,o.notMatchClassNames,r)},u=nt(nt([],n,!0),c,!0);null===(a=t.shortcuts)||void 0===a||a.forEach((function(t){if(function(t,e){if(void 0===e&&(e=""),"object"!=typeof t)throw new Error("options".concat(e,".shortcuts must be an object"));if(Array.isArray(t)){if(2!==t.length)throw new Error("options".concat(e,".shortcuts must be an array with 2 elements"));if("string"!=typeof t[0]&&("object"!=typeof t[0]||!t[0].test))throw new Error("options".concat(e,".shortcuts[0] must be a string or a RegExp"));if("function"!=typeof t[1])throw new Error("options".concat(e,".shortcuts[1] must be a function"))}else{if(Object.keys(t).filter((function(t){return"string"!=typeof t})).length)throw new Error("options".concat(e,".shortcuts as object keys must be strings"));if(Object.values(t).filter((function(t){return"string"!=typeof t})).length)throw new Error("options".concat(e,".shortcuts as object values must be strings"))}}(t),Array.isArray(t)){var e=t[0],n=t[1];u.forEach((function(t){if(new RegExp(e,"g").test(t)){var c=n(t.split(l.underline),o),a=new Set(c.split(" ").filter(Boolean));r(t,Array.from(a))}}))}else for(var c in t){var a=Array.from(new Set(t[c].split(" ").filter(Boolean)));u.includes(c)&&r(c,a)}}))}(r,o,a.styles.map((function(t){return t.name})),Array.from(a.notMatchClassNameSet)),function(t,e,n,c){var a,o=Object.keys(t.theme.colors).reduce((function(e,n){return e[n]=i.rgbToHex(t.theme.colors[n]),e}),{}),r=function(t,n,c,a){if(new RegExp(n,"g").test(t)){var r=c(t.split(l.underline),o);e({name:t,value:r},[],a?t:[])}};null===(a=t.rules)||void 0===a||a.forEach((function(t){var e=t[0],a=t[1];n.forEach((function(t){r(t,e,a)})),c.forEach((function(t){r(t,e,a,!0)}))}))}(r,o,a.styles.map((function(t){return t.name})),Array.from(a.notMatchClassNameSet)),u=tt(a.styles,r,e.unit,!wr.has(n)),wr.add(n),(h=a.notMatchClassNameSet).add.apply(h,u.notMatchClassNames),d=Array.from(a.notMatchClassNameSet),"development"===e.mode&&e.log(n)&&d.length&&((f=d.filter(Boolean)).length?pt[n]=f:delete pt[n],it(pt)),!e.log(n)&&lt(),p="\n<style>\n".concat(tt(a.currentPageStyles,r,e.unit,!1).css,"\n</style>"),[2,"".concat(r.code).concat("development"===e.mode?p:"")]);case 2:return[2]}}))}))}}]}))}))}module.exports=e})();
