//
//  TBScanMacros.h
//  TBScanSDK
//
//  Created by <PERSON> on 15/7/29.
//  Copyright (c) 2015年 Taobao.com. All rights reserved.
//

#import <TBDecodeSDK/tbItf.h>

#if defined(__cplusplus)
#define TBScanSDK_EXPORT extern "C"
#else
#define TBScanSDK_EXPORT extern
#endif

enum {
    ScanType_Null = -1,
    ScanType_GoodsBarcode = EAN13|EAN8|UPCA|UPCE|EAN14, //商品条码
    ScanType_FASTMAIL = CODE39|CODE128, //快递单条码
    ScanType_PrivateBarCode_ITF = ITF, //水电煤条码
    ScanType_Barcode = ScanType_GoodsBarcode|ScanType_FASTMAIL|ScanType_PrivateBarCode_ITF, //商品条码+快递单条码+水电煤条码
    ScanType_QRCode = QRCODE, //二维码
    ScanType_DATAMATRIX = DATAMATRIX, // dm二维码
    ScanType_PDF417 = PDF417, // PDF417码(较慢)
    ScanType_LOTTERY = ScanType_DATAMATRIX|ScanType_PDF417, /// 彩票码
    ScanType_GEN3 = GEN3, //视觉码-三代码
    ScanType_ARCode = APCODE, //AR个性码
    ScanType_NarrowCode = NARROW_CODE, //自研窄条码
    ScanType_HMCode = HM_CODE,         //自动态异构码条码
    ScanType_All_Code = ScanType_Barcode|ScanType_QRCode|ScanType_DATAMATRIX|ScanType_GEN3, //商品条码+快递单条码+水电煤条码+二维码+彩票dm二维码  。老版本的类型，逐渐废弃，不推荐使用
    ScanType_Default_Code = ScanType_Barcode|ScanType_QRCode|ScanType_GEN3, /// 各种类型条码加二维码，推荐使用
    ScanType_ALIPAY_Code = ScanType_Default_Code|ScanType_LOTTERY|ScanType_ARCode|ScanType_HMCode, // 支付宝主入口支持的类型
    ScanType_Net_Logo = HYSDK_NET_LOGO,//海报识别
    ScanType_CLOTHES = HYSDK_RESERVED1,//扫衣服
    ScanType_OCR = HYSDK_RESERVED2,//OCR
    ScanType_OtherReserved3 = HYSDK_RESERVED3,//预留
    ScanType_OtherReserved4 = HYSDK_RESERVED4,//预留
    ScanType_OtherReserved5 = HYSDK_RESERVED5,//预留
    ScanType_OtherReserved6 = HYSDK_RESERVED6,//预留
    ScanType_OtherReserved7 = HYSDK_RESERVED7,//预留
};
typedef long ScanType;// 扫描类型

//// 扫描到结果后会停止扫描等待接入方处理，如果处理方式是跳出扫码界面则返回时可以继续扫，如果没有跳出扫码界面、处理完成后想要继续扫码，就可以发这个通知
extern NSString* const TBScanContinueNotification;
// 扫码摄像头start通知
extern NSString* const TBScanCameraDidStartNotification;
// 扫码摄像头stop通知
extern NSString* const TBScanCameraDidStopNotification;
// 扫码摄像头模糊通知
extern NSString* const TBScanContinueBlurNotification;
// 扫码摄像头模糊通知，获取内容的Key，内容为时长
extern NSString* const TBScanContinueBlurTimeNotificationKey;

