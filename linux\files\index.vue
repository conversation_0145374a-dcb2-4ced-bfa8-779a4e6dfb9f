<template>
	<page-container ref="pageContainer" :is-back="true">
		<template #title>
			<view class="custom-title-container">
				<view class="title-main text-secondary">{{ `${currentServerInfo?.name} (${currentDisk})` }}</view>
				<view class="title-subtitle">
					<uv-icon name="info-circle" size="14" color="#999"></uv-icon>
					<text class="subtitle-text">长按列表项进行更多操作</text>
				</view>
			</view>
		</template>
		<!-- <template #nav-left>
			<view class="flex items-center text-26 flex-nowrap text-secondary min-w-100 max-w-280">
				<text class="path-text">{{ navPath }}</text>
			</view>
		</template> -->
		<template #nav-right>
			<view class="relative">
				<uni-icons
					class="mr-20"
					color="var(--text-color-secondary)"
					type="more-filled"
					size="24"
					@tap="toggleNavMenu"
				></uni-icons>

				<!-- 导航栏下拉菜单蒙层 -->
				<view
					class="fixed top-0 left-0 right-0 bottom-0 z-39"
					v-if="showNavMenu"
					@tap="hideNavMenu"
					@touchmove.prevent
				></view>

				<!-- 导航栏下拉菜单 -->
				<view class="nav-dropdown-menu" v-if="showNavMenu" @tap.stop>
					<view class="menu-item" @tap="showDiskSelectorDialog">
						<uni-icons type="settings" size="16" color="var(--primary-color)"></uni-icons>
						<text class="menu-text">切换盘符</text>
					</view>
					<view class="menu-divider"></view>
					<view class="menu-item" @tap="openCreateFile('folder')">
						<uni-icons
							fontFamily="iconfont"
							class="icon-folder"
							size="16"
							color="var(--primary-color)"
						></uni-icons>
						<text class="menu-text">{{ $t('files.newFolder') }}</text>
					</view>
					<view class="menu-divider"></view>
					<view class="menu-item" @tap="openCreateFile('file')">
						<uni-icons fontFamily="iconfont" class="icon-file" size="16" color="var(--primary-color)"></uni-icons>
						<text class="menu-text">{{ $t('files.newFile') }}</text>
					</view>
					<view class="menu-divider"></view>
					<view class="menu-item" @tap="clickUpload">
						<uni-icons type="image" size="16" color="var(--primary-color)"></uni-icons>
						<text class="menu-text">上传手机相册</text>
					</view>
					<view class="menu-divider"></view>
					<view class="menu-item" @tap="clickFileUpload">
						<uni-icons type="paperplane" size="16" color="var(--primary-color)"></uni-icons>
						<text class="menu-text">上传手机文件</text>
					</view>
				</view>
			</view>
		</template>
		<view class="p-20">
			<FileHeadTab />

			<!-- 模糊遮罩 -->
			<view class="blur-overlay" v-if="showContextMenu" @click="hideContextMenu"></view>

			<!-- 克隆项容器 - 放在外层，使用fixed定位 -->
			<view class="fixed-clone-container" v-if="showContextMenu">
				<view class="item-clone-wrapper" :style="clonePosition" v-if="activeFile">
					<view class="file-item-clone bg-primary flex items-center">
						<view class="flex justify-center items-center w-80 h-80">
							<uni-icons
								fontFamily="iconfont"
								:class="`icon-${getFileIconType(activeFile.ext, activeFile.icon)}`"
								size="24"
								:color="getFileIconColor(activeFile.ext, activeFile.icon)"
							></uni-icons>
						</view>
						<view class="flex-1 ml-20">
							<view class="flex items-start">
								<view class="text-primary text-26 text-ellipsis ws-nowrap overflow-hidden max-w-400 min-w-50">
									{{ activeFile.fileName + activeFile.isLink }}
								</view>
							</view>
							<view class="text-tertiary text-22" v-if="activeFile.ps">{{ activeFile.ps }}</view>
						</view>
							<view class="flex flex-col items-flex-end pr-16">
								<view class="text-tertiary text-22 mb-8">{{ activeFile.time }}</view>
								<view class="text-tertiary text-22" v-if="activeFile.ext !== 'folder'">{{
									getByteUnit(activeFile.size)
								}}</view>
								<view class="flex flex-row items-center" v-show="activeFile.ext === 'folder'">
									<text class="text-tertiary lh-18rpx text-22">查看</text>
									<uni-icons
										type="right"
										color="var(--text-color-tertiary)"
										size="13"
										class="ml-2"
									></uni-icons>
								</view>
							</view>
					</view>
				</view>
			</view>

			<!-- 悬浮上下文菜单 -->
			<view class="context-menu" v-if="showContextMenu" :style="menuPosition" :class="menuPosition.class">
				<view class="menu-item" @click="showRemarkDialog = true">
					<uni-icons type="calendar" size="16" color="var(--text-color-primary)"></uni-icons>
					<text class="menu-text">{{ $t('files.modifyRemark') }}</text>
				</view>
				<view class="menu-divider"></view>
				<view class="menu-item" @click="openRenameDialog">
					<uni-icons type="compose" size="16" color="var(--text-color-primary)"></uni-icons>
					<text class="menu-text">{{ $t('common.rename') }}</text>
				</view>
				<view class="menu-divider"></view>
				<view class="menu-item relative" v-if="currentDownloadUrl && activeFile?.ext !== 'folder'">
					<uni-link
						:href="currentDownloadUrl"
						:text="activeFile?.fileName || '下载'"
						color="var(--text-color-primary)"
						:showUnderLine="false"
						copyTips="下载链接已复制，请在浏览器中粘贴下载"
						class="download-link"
					>
						<view class="download-content">
							<uni-icons type="download" size="16" color="var(--text-color-primary)"></uni-icons>
							<text class="menu-text">下载</text>
						</view>
					</uni-link>
					<view class="menu-divider absolute left-0 bottom-0 right-0"></view>
				</view>
				<view class="menu-item menu-delete" @click="showDeleteDialog = true">
					<uni-icons type="trash" size="16" color="#FF3B30"></uni-icons>
					<text class="menu-text menu-text-delete">{{ $t('common.delete') }}</text>
				</view>
			</view>

			<!-- 添加一个隐藏的临时菜单用于测量高度 -->
			<view
				class="temp-measure-menu context-menu"
				v-if="showTempMenu"
				style="position: absolute; opacity: 0; pointer-events: none; top: -9999px"
			>
				<view class="menu-item">
					<uni-icons type="paperclip" size="16" color="#007AFF"></uni-icons>
					<text class="menu-text">{{ $t('files.modifyRemark') }}</text>
				</view>
				<view class="menu-divider"></view>
				<view class="menu-item">
					<uni-icons type="compose" size="16" color="#007AFF"></uni-icons>
					<text class="menu-text">{{ $t('common.rename') }}</text>
				</view>
				<view class="menu-divider"></view>
				<view class="menu-item">
					<uni-icons type="download" size="16" color="#007AFF"></uni-icons>
					<text class="menu-text">下载</text>
				</view>
				<view class="menu-divider"></view>
				<view class="menu-item menu-delete">
					<uni-icons type="trash" size="16" color="#FF3B30"></uni-icons>
					<text class="menu-text menu-text-delete">{{ $t('common.delete') }}</text>
				</view>
			</view>

			<z-paging
				ref="paging"
				class="mt-260"
				:default-page-size="100"
				use-virtual-list
				:force-close-inner-list="true"
				:auto-hide-loading-after-first-loaded="false"
				:auto-show-system-loading="true"
				@virtualListChange="virtualListChange"
				@query="queryList"
				@refresherStatusChange="reload"
				:refresher-complete-delay="200"
			>
				<view class="px-30" :id="`zp-id-${item.zp_index}`" :key="item.zp_index" v-for="item in filesList">
					<view
						class="file-item-container"
						@touchstart="handleTouchStart($event)"
						@touchmove="handleTouchMove($event)"
						@touchend="handleTouchEnd($event)"
						@touchcancel="handleTouchCancel($event)"
						:data-index="item.zp_index"
						:data-file="JSON.stringify(item)"
					>
						<view
							class="py-16 flex items-center file-item"
							:class="{ hidden: showContextMenu && activeIndex === item.zp_index }"
						>
							<view class="flex justify-center items-center w-80 h-80">
								<uni-icons
									fontFamily="iconfont"
									:class="`icon-${getFileIconType(item.ext, item.icon)}`"
									size="24"
									:color="getFileIconColor(item.ext, item.icon)"
								></uni-icons>
							</view>
							<view class="flex-1 ml-20">
								<view class="flex items-start">
									<view class="text-primary text-26 text-ellipsis ws-nowrap overflow-hidden max-w-400 min-w-50">
										{{ item.fileName + item.isLink }}
									</view>
								</view>
								<view class="text-tertiary text-22" v-if="item.ps">{{ item.ps }}</view>
							</view>
							<view class="flex flex-col items-flex-end pr-16">
								<view class="text-tertiary text-22 mb-8">{{ item.time }}</view>
								<view class="text-tertiary text-22" v-if="item.ext !== 'folder'">{{
									getByteUnit(item.size)
								}}</view>
								<view class="flex flex-row items-center" v-show="item.ext === 'folder'">
									<text class="text-tertiary lh-18rpx text-22">查看</text>
									<uni-icons
										type="right"
										color="var(--text-color-tertiary)"
										size="13"
										class="ml-2"
									></uni-icons>
								</view>
							</view>
						</view>
					</view>
					<!-- <view class="file-divider h-2"></view> -->
				</view>
			</z-paging>
		</view>
		<uni-fab
			:pattern="{ buttonColor: '#20a50a' }"
			horizontal="right"
			vertical="bottom"
			direction="vertical"
			:popMenu="false"
			@click="clickActionSheet"
		>
		</uni-fab>
		<uv-action-sheet
			ref="actionSheet"
			:actions="uploadActionList"
			cancelText="取消"
			safeAreaInsetBottom
			@select="handleUploadActionSheet"
		>
		</uv-action-sheet>
		<CustomDialog
			contentHeight="200rpx"
			v-model="showDeleteDialog"
			:title="$t('common.tip')"
			:confirmText="$t('common.delete')"
			:confirmStyle="{
				backgroundColor: '#FF3B30',
			}"
			@confirm="confirmDeleteFile"
		>
			<view class="text-secondary flex justify-center items-center h-full">
				{{ $t('files.confirmDelete', { name: activeFile?.fileName }) }}
			</view>
		</CustomDialog>
		<CustomDialog
			contentHeight="100rpx"
			v-model="showRenameDialog"
			:title="$t('common.rename')"
			:confirmText="$t('common.rename')"
			@confirm="renameFile"
			@cancel="renameFileName = ''"
		>
			<view class="text-secondary flex justify-center items-center h-full">
				<uv-input v-model="renameFileName" :placeholder="activeFile?.fileName" />
			</view>
		</CustomDialog>
		<CustomDialog
			contentHeight="100rpx"
			v-model="showRemarkDialog"
			:title="$t('files.modifyRemark')"
			:confirmText="$t('common.modify')"
			@confirm="confirmRemark"
			@cancel="renameRemark = ''"
		>
			<view class="text-secondary flex justify-center items-center h-full">
				<uv-input v-model="renameRemark" :placeholder="activeFile?.ps || $t('files.enterRemark')" />
			</view>
		</CustomDialog>
		<CustomDialog
			contentHeight="100rpx"
			v-model="showCreateDialog"
			:title="$t(createFileType === 'folder' ? 'files.newFolder' : 'files.newFile')"
			:confirmText="$t('files.create')"
			@confirm="confirmCreate"
			@cancel="createFileName = ''"
		>
			<view class="text-secondary flex justify-center items-center h-full">
				<uv-input
					v-model="createFileName"
					:placeholder="$t(createFileType === 'folder' ? 'files.enterFolderName' : 'files.enterFileName')"
				/>
			</view>
		</CustomDialog>

		<!-- 磁盘选择器弹窗 -->
		<CustomDialog
			contentHeight="300rpx"
			v-model="showDiskSelector"
			title="选择盘符"
			:showConfirm="false"
			:cancelText="$t('common.cancel')"
		>
			<view class="disk-selector-content">
				<view
					class="disk-item"
					v-for="disk in diskList"
					:key="disk.path"
					@tap="switchDisk(disk.path)"
					:class="{ active: disk.path === currentDisk }"
				>
					<view class="disk-info">
						<view class="disk-path">{{ disk.path }}</view>
						<view class="disk-details">
							<text class="disk-size">{{ disk.size[0] }}</text>
							<text class="disk-usage">已用 {{ disk.size[1] }} / 可用 {{ disk.size[2] }}</text>
						</view>
						<view class="disk-type">{{ disk.type }} - {{ disk.filesystem }}</view>
					</view>
					<view class="disk-usage-bar">
						<view class="usage-bar-bg">
							<view class="usage-bar-fill" :style="{ width: disk.size[3] }"></view>
						</view>
						<text class="usage-percent">{{ disk.size[3] }}</text>
					</view>
				</view>
			</view>
		</CustomDialog>

		<!-- 上传手机相册弹窗 -->
		<CustomDialog
			v-model="showUploadPopup"
			title="上传手机相册"
			contentHeight="200rpx"
			confirmText="上传"
			cancelText="取消"
			@confirm="confirmUpload"
			@cancel="cancelUpload"
			:maskClick="false"
		>
			<view class="upload-dialog-content">
				<view class="form-item">
					<view class="form-label">选择保存位置：</view>
					<view class="form-value" @click="chooseSaveLocation">
						<text class="location-text">{{ saveLocation || '请选择保存位置' }}</text>
						<uni-icons type="right" size="16" color="#999"></uni-icons>
					</view>
				</view>
				<view class="form-divider"></view>

				<!-- 上传图片提示 -->
				<view class="upload-tips" v-if="uploadList.length > 0"> 将要上传的文件如下(可点击预览)： </view>

				<!-- 选择图片按钮区域 -->
				<view class="form-item">
					<view class="upload-btn-group">
						<view
							class="w-full upload-btn primary"
							@click="openUpload(false)"
							v-if="uploadList.length === 0"
						>
							选择图片
						</view>
						<view
							class="w-full upload-btn secondary"
							@click="openUpload(true)"
							v-if="uploadList.length > 0"
						>
							继续上传
						</view>
					</view>
				</view>

				<!-- 图片管理按钮 -->
				<view class="form-item" v-if="uploadList.length > 0">
					<view class="upload-btn-group mt-10">
						<view class="w-full upload-btn danger" @click="clearAllUploadImages"> 清空所有 </view>
						<view class="upload-count-info"> 已选择 {{ uploadList.length }}/9 张图片 </view>
					</view>
				</view>

				<!-- 图片显示 -->
				<scroll-view :scroll-y="true" class="upload-scroll" v-if="uploadList.length > 0">
					<view class="scroll-img">
						<view class="upload-image-container" v-for="(item, index) in uploadList" :key="index">
							<image
								:src="item"
								mode="aspectFill"
								@click="previewUpload(index)"
								class="upload-image"
							></image>
							<view class="image-remove-btn" @click="removeUploadImage(index)">
								<uni-icons type="close" size="12" color="#fff"></uni-icons>
							</view>
						</view>
					</view>
				</scroll-view>
			</view>
		</CustomDialog>

		<!-- 上传手机文件弹窗 -->
		<CustomDialog
			v-model="showUploadFilePopup"
			title="上传手机文件"
			contentHeight="200rpx"
			confirmText="上传"
			cancelText="取消"
			@confirm="confirmFileUpload"
			@cancel="cancelFileUpload"
			:maskClick="false"
		>
			<view class="upload-dialog-content">
				<view class="form-item">
					<view class="form-label">选择保存位置：</view>
					<view class="form-value" @click="chooseSaveLocation">
						<text class="location-text">{{ saveLocation || '请选择保存位置' }}</text>
						<uni-icons type="right" size="16" color="#999"></uni-icons>
					</view>
				</view>
				<view class="form-divider"></view>

				<!-- 文件选择提示 -->
				<view class="upload-tips" v-if="uploadFileList.length > 0">
					已选择 {{ uploadFileList.length }} 个文件：
				</view>

				<!-- 选择文件按钮 -->
				<view class="form-item">
					<view class="upload-btn-group">
						<view class="w-full upload-btn primary" @click="uploadMedia" v-if="uploadFileList.length === 0">
							选择文件
						</view>
						<view class="w-full upload-btn secondary" @click="uploadMedia" v-if="uploadFileList.length > 0">
							继续上传
						</view>
						<view
							class="w-full upload-btn danger ml-10"
							@click="clearAllUploadFiles"
							v-if="uploadFileList.length > 0"
						>
							清空所有
						</view>
					</view>
				</view>

				<!-- 文件列表显示 -->
				<view class="upload-tips" v-if="uploadFileList.length > 0">
					<view v-for="file in uploadFileList" :key="file.id" style="margin-bottom: 10rpx">
						{{ file.name }} ({{ getByteUnit(file.size) }})
						<text
							@click="removeUploadFile(file.id)"
							style="color: #f56c6c; margin-left: 20rpx; cursor: pointer"
							>删除</text
						>
					</view>
				</view>
			</view>
		</CustomDialog>

		<!-- 上传进度弹窗 - 增强版本 -->
		<CustomDialog
			v-model="showProgressPopup"
			title="上传中..."
			contentHeight="200rpx"
			:showConfirm="false"
			:showCancel="false"
			:isMaskClick="false"
		>
			<view class="progress-dialog-content">
				<!-- 总体进度 -->
				<view class="progress-section">
					<view class="progress-text">
						已上传{{ uploadCount }} / 共{{ activeCount }}个文件 ({{ percentage }}%)
					</view>
					<view class="progress-container">
						<progress :percent="percentage" :show-info="true" activeColor="#20A53A"></progress>
					</view>
				</view>
			</view>
		</CustomDialog>
	</page-container>
</template>

<script setup>
	import { ref, watch, onMounted, computed } from 'vue';
	import { onLoad, onUnload, onBackPress } from '@dcloudio/uni-app';
	import PageContainer from '@/components/PageContainer/index.vue';
	import CustomDialog from '@/components/CustomDialog/index.vue';
	import FileHeadTab from './fileHeadTab.vue';
	import { useConfigStore } from '@/store/modules/config';
	import { $t } from '@/locale/index.js';
	import {
		getFilesList,
		currentDisk,
		paging,
		getFileIconType,
		getFileIconColor,
		currentPath,
		pageContainer,
		showContextMenu,
		activeFile,
		activeIndex,
		menuPosition,
		clonePosition,
		showTempMenu,
		measureMenuHeight,
		handleTouchStart,
		handleTouchMove,
		handleTouchEnd,
		handleTouchCancel,
		hideContextMenu,
		renameFile,
		confirmDeleteFile,
		pathList,
		cutDirPath,
		parsePath,
		showDeleteDialog,
		showRenameDialog,
		renameFileName,
		openRenameDialog,
		showRemarkDialog,
		renameRemark,
		confirmRemark,
		// 导航菜单相关
		showNavMenu,
		toggleNavMenu,
		hideNavMenu,
		createFileType,
		createFileName,
		showCreateDialog,
		openCreateFile,
		confirmCreate,
		actionSheet,
		uploadActionList,
		handleUploadActionSheet,
		clickActionSheet,
		// 切换盘符相关
		showDiskSelector,
		diskList,
		showDiskSelectorDialog,
		switchDisk,
		// 下载功能
		downloadFileToLocal,
		getDownloadUrl,
		// 文件上传功能
		showUploadPopup,
		showUploadFilePopup,
		showProgressPopup,
		saveLocation,
		uploadList,
		uploadCount,
		activeCount,
		percentage,
		uploadFileInfo,
		uploadFileList,
		clickUpload,
		openUpload,
		previewUpload,
		cancelUpload,
		confirmUpload,
		uploadMedia,
		clickFileUpload,
		confirmFileUpload,
		cancelFileUpload,
		chooseSaveLocation,
		// 新增的文件管理功能
		removeUploadImage,
		clearAllUploadImages,
		addFileToUploadList,
		removeUploadFile,
		clearAllUploadFiles,
	} from './useController';
	import { getByteUnit } from '@/utils/common';

	const { currentServerInfo } = useConfigStore().getReactiveState();

	const filesList = ref([]);

	const virtualListChange = (vList) => {
		filesList.value = vList;
	};

	const queryList = async (page, pageSize) => {
		try {
			const res = await getFilesList(page, pageSize);
			paging.value.complete(res);
			paging.value.updateVirtualListRender();
		} catch (error) {
			paging.value.complete([]);
		}
	};

	const navPath = computed(() => {
		const maxLength = 8; // 设置最大字符数限制
		const path =
			pathList.value[pathList.value.length - 1].name === $t('files.rootDirectory')
				? '/'
				: pathList.value[pathList.value.length - 1].name;

		// 如果路径长度超过限制，截取并添加省略号
		if (path.length > maxLength) {
			return path.substring(0, maxLength) + '...';
		}

		return path;
	});

	// 获取当前文件的下载链接
	const currentDownloadUrl = ref('');

	const updateDownloadUrl = async () => {
		if (activeFile.value && activeFile.value.ext !== 'folder') {
			try {
				const url = await getDownloadUrl(activeFile.value);
				currentDownloadUrl.value = url || '';
			} catch (error) {
				currentDownloadUrl.value = '';
			}
		} else {
			currentDownloadUrl.value = '';
		}
	};

	// 监听activeFile变化，更新下载链接
	watch(activeFile, updateDownloadUrl, { immediate: true });

	const reload = (reloadType) => {
		if (reloadType === 'complete') {
			pageContainer.value.notify.success('刷新成功');
		}
	};

	// 页面加载时处理路径参数
	onLoad((options) => {
		if (options.path) {
			// 设置初始路径
			currentPath.value = decodeURIComponent(options.path);
		}
	});

	// 在页面挂载时获取菜单高度
	onMounted(() => {
		measureMenuHeight();
	});

	// 监听菜单显示状态，防止页面滚动
	watch(showContextMenu, (val) => {
		if (val) {
			// 菜单显示时，禁用页面滚动
			uni.pageScrollTo({
				scrollTop: 0,
				duration: 0,
			});

			// 锁定列表滚动
			if (paging.value) {
				paging.value.lockScroll && paging.value.lockScroll(true);
			}

			// 关闭导航下拉菜单
			showNavMenu.value = false;
		} else {
			// 恢复列表滚动
			if (paging.value) {
				paging.value.lockScroll && paging.value.lockScroll(false);
			}
		}
	});

	onBackPress(() => {
		// 如果导航菜单正在显示，先关闭菜单
		if (showNavMenu.value) {
			showNavMenu.value = false;
			return true;
		}

		// 如果长按菜单正在显示，先关闭菜单
		if (showContextMenu.value) {
			hideContextMenu();
			return true;
		}

		if (pathList.value.length === 1) {
			return false;
		}
		cutDirPath(pathList.value[pathList.value.length - 2].path);
		return true;
	});

	onUnload(() => {
		currentPath.value = '/';
		currentDisk.value = '/';
		showDeleteDialog.value = false;
		showRenameDialog.value = false;
		showRemarkDialog.value = false;
		showCreateDialog.value = false;
		showDiskSelector.value = false;
	});
</script>

<style lang="scss" scoped>
	.file-divider {
		background-color: var(--text-color-light);
		opacity: 0.8;
	}

	.file-item-container {
		border-bottom: 1rpx solid var(--text-color-light);
		position: relative;
		overflow: visible;
	}

	.file-item {
		position: relative;
		z-index: 1;

		&:active {
			background-color: rgba(var(--bg-color-secondary-rgb), 0.4);
			transition: background-color 0.1s ease;
		}
	}

	.hidden {
		visibility: hidden;
	}

	/* 模糊遮罩 */
	.blur-overlay {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: rgba(255, 255, 255, 0.3);
		backdrop-filter: blur(3px);
		-webkit-backdrop-filter: blur(3px);
		z-index: 15;
		pointer-events: auto;
		touch-action: none; /* 禁止所有触摸操作 */
		user-select: none; /* 禁止选择 */
	}

	/* 固定克隆项容器 */
	.fixed-clone-container {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		z-index: 25;
		pointer-events: none;
	}

	/* 克隆项样式 */
	.item-clone-wrapper {
		position: absolute;
		pointer-events: none; /* 阻止触摸事件 */
	}

	.file-item-clone {
		width: 100%;
		height: 100%;
		border-radius: 12rpx;
		transform: scale(1.02);
		box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.2);
		pointer-events: none; /* 确保克隆项不响应触摸事件 */
	}

	/* 上下文菜单 */
	.context-menu {
		position: fixed;
		z-index: 30;
		background: var(--bg-color);
		border-radius: 14rpx;
		box-shadow: var(--box-shadow);
		padding: 10rpx 0;
		transform: translate(-50%, 0);
		min-width: 340rpx;
		backdrop-filter: blur(20px);
		-webkit-backdrop-filter: blur(20px);

		&.menu-top {
			animation: fadeInTop 0.2s ease;
			transform: translate(-50%, 0);
		}

		&.menu-bottom {
			animation: fadeInBottom 0.2s ease;
			transform: translate(-50%, 0);
		}

		/* 菜单位于上方但紧贴克隆项顶部 */
		&.menu-position-bottom {
			transform: translate(-50%, 0);
		}
	}

	.menu-item {
		display: flex;
		align-items: center;
		padding: 24rpx 30rpx;

		&:active {
			background-color: rgba(0, 0, 0, 0.05);
		}
	}

	.menu-text {
		margin-left: 20rpx;
		font-size: 28rpx;
		color: var(--text-color-primary);
		font-weight: 400;
	}

	.menu-delete {
		opacity: 0.9;
	}

	.menu-text-delete {
		color: #ff3b30;
	}

	.menu-divider {
		height: 1rpx;
		background-color: rgba(0, 0, 0, 0.1);
		margin: 0 10rpx;
	}

	@keyframes fadeInTop {
		from {
			opacity: 0;
			transform: translate(-50%, 10px);
		}
		to {
			opacity: 1;
			transform: translate(-50%, 0);
		}
	}

	@keyframes fadeInBottom {
		from {
			opacity: 0;
			transform: translate(-50%, -10px);
		}
		to {
			opacity: 1;
			transform: translate(-50%, 0);
		}
	}

	/* 菜单位于顶部时的自定义动画 */
	.menu-top.menu-position-bottom {
		animation: fadeInTopPosition 0.2s ease;
	}

	@keyframes fadeInTopPosition {
		from {
			opacity: 0;
			transform: translate(-50%, 10px);
		}
		to {
			opacity: 1;
			transform: translate(-50%, 0);
		}
	}

	.path-text {
		white-space: nowrap;
		word-break: keep-all;
	}

	/* 导航栏下拉菜单 */
	.nav-dropdown-menu {
		position: absolute;
		top: 60rpx;
		right: 0;
		z-index: 40;
		background: rgba(249, 249, 249, 0.94);
		border-radius: 14rpx;
		box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.25);
		padding: 10rpx 0;
		min-width: 300rpx;
		backdrop-filter: blur(20px);
		-webkit-backdrop-filter: blur(20px);
		animation: fadeInDown 0.2s ease;
		transform-origin: top right;

		.menu-item {
			display: flex;
			align-items: center;
			padding: 24rpx 30rpx;

			&:active {
				background-color: rgba(0, 0, 0, 0.05);
			}
		}

		.menu-text {
			margin-left: 20rpx;
			font-size: 26rpx;
			color: #666;
			font-weight: 400;
		}

		.menu-divider {
			height: 1rpx;
			background-color: rgba(0, 0, 0, 0.1);
			margin: 0 10rpx;
		}
	}

	/* 修改下拉菜单动画，只在垂直方向有效 */
	@keyframes fadeInDown {
		from {
			opacity: 0;
			transform: translateY(-10rpx);
		}
		to {
			opacity: 1;
			transform: translateY(0);
		}
	}

	/* 磁盘选择器样式 */
	.disk-selector-content {
		padding: 20rpx 0;
	}

	.disk-item {
		padding: 30rpx 40rpx;
		border-bottom: 1rpx solid var(--text-color-light);
		transition: background-color 0.2s ease;

		&:last-child {
			border-bottom: none;
		}

		&:active {
			background-color: rgba(var(--bg-color-secondary-rgb), 0.4);
		}

		&.active {
			background-color: rgba(32, 165, 10, 0.1);
			border-left: 6rpx solid #20a50a;
		}
	}

	.disk-info {
		margin-bottom: 20rpx;
	}

	.disk-path {
		font-size: 32rpx;
		font-weight: 600;
		color: var(--text-color-primary);
		margin-bottom: 10rpx;
	}

	.disk-details {
		display: flex;
		align-items: center;
		margin-bottom: 8rpx;
	}

	.disk-size {
		font-size: 28rpx;
		color: var(--text-color-primary);
		margin-right: 20rpx;
	}

	.disk-usage {
		font-size: 24rpx;
		color: var(--text-color-secondary);
	}

	.disk-type {
		font-size: 24rpx;
		color: var(--text-color-tertiary);
	}

	.disk-usage-bar {
		display: flex;
		align-items: center;
	}

	.usage-bar-bg {
		flex: 1;
		height: 12rpx;
		background-color: var(--text-color-light);
		border-radius: 6rpx;
		overflow: hidden;
		margin-right: 20rpx;
	}

	.usage-bar-fill {
		height: 100%;
		background: linear-gradient(90deg, #20a50a 0%, #67c23a 50%, #e6a23c 80%, #f56c6c 100%);
		border-radius: 6rpx;
		transition: width 0.3s ease;
	}

	.usage-percent {
		font-size: 24rpx;
		color: var(--text-color-secondary);
		min-width: 80rpx;
		text-align: right;
	}

	/* uni-link 下载样式 */
	.download-link {
		width: 100%;
		display: block;
	}

	.download-content {
		display: flex;
		align-items: center;
		width: 100%;
	}

	/* 文件上传相关样式 - 适配 CustomDialog */
	.upload-dialog-content {
		width: 100%;
		padding: 0;
	}

	.progress-dialog-content {
		width: 100%;
		padding: 20rpx 0;
		text-align: center;
	}

	.progress-text {
		font-size: 28rpx;
		color: var(--text-color-secondary);
		margin-bottom: 30rpx;
		display: block;
		line-height: 1.5;
	}

	.current-file {
		font-size: 24rpx;
		color: var(--text-color-tertiary);
		display: block;
		margin-top: 10rpx;
		word-break: break-all;
	}

	.form-label {
		font-size: 28rpx;
		color: var(--text-color-primary);
		width: 200rpx;
		flex-shrink: 0;
	}

	.form-value {
		flex: 1;
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 20rpx;
		background: #f8f9fa;
		border-radius: 10rpx;
		cursor: pointer;
	}

	.location-text {
		font-size: 28rpx;
		color: var(--text-color-primary);
		flex: 1;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
	}

	.form-divider {
		height: 2rpx;
		background: #f0f0f0;
		margin: 20rpx 0;
	}

	.upload-btn {
		width: 100%;
		height: 80rpx;
		line-height: 80rpx;
		background: #20a50a;
		color: white;
		text-align: center;
		border-radius: 10rpx;
		font-size: 28rpx;
		cursor: pointer;
	}

	.upload-btn:active {
		background: #1a8a08;
	}

	.upload-btn.primary {
		background: #20a50a;
	}

	.upload-btn.primary:active {
		background: #1a8a08;
	}

	.upload-btn.secondary {
		background: #409eff;
	}

	.upload-btn.secondary:active {
		background: #337ecc;
	}

	.upload-btn.danger {
		background: #f56c6c;
	}

	.upload-btn.danger:active {
		background: #dd5a5a;
	}

	.upload-btn-group {
		display: flex;
		align-items: center;
	}

	.upload-count-info {
		font-size: 24rpx;
		color: #666;
		white-space: nowrap;
	}

	.upload-tips {
		font-size: 26rpx;
		color: #666;
		margin: 30rpx 0 20rpx 0;
	}

	.file-name {
		font-weight: 600;
		font-size: 28rpx;
		color: var(--text-color-primary);
	}

	.upload-scroll {
		width: 100%;
		max-height: 400rpx;
		margin-top: 20rpx;
	}

	.scroll-img {
		display: flex;
		flex-wrap: wrap;
		justify-content: space-between;
		padding: 0 10rpx;
	}

	.upload-image {
		width: 47%;
		height: 220rpx;
		margin-bottom: 20rpx;
		border-radius: 10rpx;
		cursor: pointer;
	}

	.upload-image-container {
		position: relative;
		width: 47%;
		margin-bottom: 20rpx;
	}

	.upload-image-container .upload-image {
		width: 100%;
		height: 200rpx;
		border-radius: 10rpx;
		border: 2rpx solid #e0e0e0;
	}

	.progress-container {
		margin: 20rpx 0;
		padding: 0 20rpx;
	}

	.image-item-wrapper {
		position: relative;
		width: 47%;
		margin-bottom: 20rpx;
	}

	.image-item-wrapper .upload-image {
		width: 100%;
		height: 220rpx;
		border-radius: 10rpx;
		cursor: pointer;
	}

	.image-remove-btn {
		position: absolute;
		top: 8rpx;
		right: 8rpx;
		width: 32rpx;
		height: 32rpx;
		background: rgba(0, 0, 0, 0.6);
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
		z-index: 10;
	}

	.image-remove-btn:active {
		background: rgba(0, 0, 0, 0.8);
	}

	/* 增强的上传进度弹窗样式 */
	.progress-dialog-content {
		padding: 20rpx;
		display: flex;
		flex-direction: column;
		height: 100%;
	}

	.progress-section {
		margin-bottom: 20rpx;
	}

	.progress-text {
		font-size: 28rpx;
		color: var(--text-color-primary);
		margin-bottom: 16rpx;
		text-align: center;
		font-weight: 500;
	}

	.progress-container {
		margin-bottom: 10rpx;
	}

	.current-file-section {
		margin-bottom: 20rpx;
		padding: 16rpx;
		background: #f8f9fa;
		border-radius: 8rpx;
		border: 1px solid #e9ecef;
	}

	.section-title {
		font-size: 24rpx;
		color: #666;
		margin-bottom: 8rpx;
		font-weight: 500;
	}

	.current-file-name {
		font-size: 26rpx;
		color: var(--text-color-primary);
		word-break: break-all;
		line-height: 1.4;
	}

	.file-list-section {
		flex: 1;
		min-height: 0;
	}

	.file-info {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 8rpx;
	}

	.file-name {
		font-size: 24rpx;
		color: var(--text-color-primary);
		font-weight: 500;
		flex: 1;
		min-width: 0;
		word-break: break-all;
		margin-right: 12rpx;
	}

	.file-status {
		display: flex;
		align-items: center;
		gap: 8rpx;
		font-size: 22rpx;
	}

	.status-text {
		font-weight: 500;
	}

	.status-text.waiting {
		color: #909399;
	}

	.status-text.uploading {
		color: #409eff;
	}

	.status-text.completed {
		color: #67c23a;
	}

	.status-text.failed {
		color: #f56c6c;
	}

	.progress-text {
		color: #409eff;
		font-weight: 500;
	}
</style>
