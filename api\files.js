import axios from '@/api/request';

const DEFAULT_API_TYPE = import.meta.env.VITE_PANEL_OWNER || 'domestic';

// 获取文件列表
export const getFileList = (data) => {
	const url = DEFAULT_API_TYPE === 'domestic' ? '/files?action=GetDirNew' : '/files?action=GetDir';
	return axios(url, data);
};

/**
 * @description 获取文件内容
 * @returns { Promise }
 */
export const getFileBody = (data) => {
	const url = '/files?action=GetFileBody';
	return axios(url, data);
};

/**
 * @description 保存文件内容
 * @returns { Promise }
 */
export const saveFileBody = (data) => {
	const url = '/files?action=SaveFileBody';
	return axios(url, data);
};

/**
 * @description 删除文件
 * @returns { Promise }
 */
export const deleteFile = (data) => {
	const url = `/files?action=${data.type === 'folder' ? 'DeleteDir' : 'DeleteFile'}`;
	return axios(url, { path: data.path });
};

/**
 * @description 文件重命名
 * @param {String} sfile 旧名称路径
 * @param {String} dfile 新名称路径
 * @returns { Promise }
 */
export const setFileName = (data) => {
	const url = '/files?action=MvFile';
	return axios(url, data);
};

/**
 * @description 设置备注
 * @returns { Promise }
 */
export const setFilePs = (data) => {
	const url = '/files?action=set_file_ps';
	return axios(url, data);
};

/**
 * @description 新建文件/文件夹
 * @param {String} path 新建文件/文件夹路径
 * @returns { Promise }
 */
export const createNewFile = (data) => {
	const url = `/files?action=${data.type === 'folder' ? 'CreateDir' : 'CreateFile'}`;
	return axios(url, data);
};

/**
 * @description 获取磁盘信息
 * @returns { Promise }
 */
export const getDiskInfo = () => {
	const url = '/system?action=GetDiskInfo';
	return axios(url, {}, 'POST');
};

/**
 * @description 获取文件下载链接
 * @param {String} path 文件路径
 * @returns { Promise }
 */
export const getFileDownloadUrl = (data) => {
	const url = '/files?action=DownloadFile';
	return axios(url, data);
};

/**
 * @description 上传文件
 * @param {Object} data 上传参数
 * @param {String} data.f_path 保存路径
 * @param {String} data.f_name 文件名
 * @param {Number} data.f_size 文件大小
 * @param {Number} data.f_start 起始位置（分片上传的字节偏移量）
 * @param {String} data.blob 文件blob数据
 * @returns { Promise }
 */
export const uploadFile = (data) => {
	const url = '/files?action=upload';
	return axios(url, data);
};

/**
 * @description 批量检查文件是否存在
 * @param {Object} data 检查参数
 * @param {String} data.files 文件路径列表，用换行符分隔
 * @returns { Promise } 返回文件存在状态
 */
export const uploadFilesExists = (data) => {
	const url = '/files?action=upload_files_exists';
	return axios(url, data);
};

/**
 * @description 获取系统版本信息
 * @returns { Promise }
 */
export const getSystemTotal = () => {
	const url = '/system?action=GetSystemTotal';
	return axios(url, {}, 'POST');
};
