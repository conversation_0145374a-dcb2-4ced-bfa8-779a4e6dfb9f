# 表单Hooks组件使用文档

## useForm

`useForm` 是一个用于快速生成表单的自定义钩子，它能根据配置自动生成表单组件，并提供表单数据和验证方法。

### 基本用法

```javascript
import { ref } from 'vue';
import useForm from '../hooks/useForm';

// 表单配置
const formConfig = {
  // 标签配置
  labelOptions: {
    position: 'left', // 标签位置：left, top
    width: 100 // 标签宽度，单位rpx
  },
  // 表单项配置
  formItems: [
    {
      type: 'input', // 组件类型
      label: '用户名', // 标签文本
      prop: 'username', // 字段名
      placeholder: '请输入用户名', // 占位文本
      borderBottom: true, // 是否显示底部边框
      rules: { // 验证规则
        required: true,
        message: '请输入用户名',
        trigger: ['blur']
      }
    },
    // 更多表单项...
  ],
  // 初始值
  initialValues: {
    username: '',
    // 更多字段...
  }
};

// 使用表单钩子
const {
  FormComponent, // 表单组件
  formModel,     // 表单数据
  rules,         // 表单规则
  formRef,       // 表单实例引用
  validate,      // 验证方法
  resetForm,     // 重置方法
  setFormValues, // 设置表单值方法
  getFormValues  // 获取表单值方法
} = useForm(formConfig);
```

### 在模板中使用

```html
<template>
  <view>
    <!-- 渲染表单组件 -->
    <FormComponent />
    
    <!-- 操作按钮 -->
    <view class="button-group">
      <uv-button type="primary" @click="submitForm">提交</uv-button>
      <uv-button type="info" @click="resetForm">重置</uv-button>
    </view>
  </view>
</template>

<script setup>
import { ref } from 'vue';
import useForm from '../hooks/useForm';

// 表单配置
const formConfig = {
  labelOptions: {
    position: 'left',
    width: 100
  },
  formItems: [
    // 表单项配置...
  ],
  initialValues: {
    // 初始值...
  }
};

const {
  FormComponent,
  formModel,
  formRef,
  validate,
  resetForm
} = useForm(formConfig);

// 提交表单
const submitForm = async () => {
  try {
    const result = await validate();
    console.log('表单数据：', result);
    // 处理表单提交...
  } catch (error) {
    console.error('表单验证失败：', error);
  }
};
</script>
```

### 支持的表单组件类型

- `input`: 输入框
- `textarea`: 文本域
- `radio`: 分段器（使用 UvSubsection 组件）
- `checkbox`: 复选框
- `select`: 选择器
- `date`: 日期选择器
- `custom`: 自定义组件（需提供render函数）

### 表单项配置说明

每个表单项支持以下配置：

| 属性 | 类型 | 说明 |
| --- | --- | --- |
| type | String | 组件类型 |
| label | String | 标签文本 |
| prop | String | 字段名（必须） |
| placeholder | String | 占位文本 |
| borderBottom | Boolean | 是否显示底部边框 |
| props | Object | 传递给组件的其他属性 |
| rules | Object | 验证规则 |
| options | Array | 选项列表（适用于radio/checkbox/select） |
| render | Function | 自定义渲染函数（适用于custom类型） |

### 组件使用示例

#### 1. 输入框（input）
```javascript
{
  type: 'input',
  label: '用户名',
  prop: 'username',
  placeholder: '请输入用户名',
  borderBottom: true,
  props: {
    type: 'text',           // 输入框类型
    maxlength: 20,          // 最大长度
    clearable: true,        // 是否可清空
    disabled: false,        // 是否禁用
    readonly: false         // 是否只读
  },
  rules: {
    required: true,
    message: '请输入用户名',
    trigger: ['blur']
  }
}
```

#### 2. 文本域（textarea）
```javascript
{
  type: 'textarea',
  label: '备注',
  prop: 'remark',
  placeholder: '请输入备注信息',
  props: {
    maxlength: 200,         // 最大长度
    height: 100,            // 高度
    autoHeight: true,       // 是否自动增高
    disabled: false         // 是否禁用
  }
}
```

#### 3. 分段器（radio）
```javascript
{
  type: 'radio',
  label: '性别',
  prop: 'gender',
  options: [
    { label: '男', value: 'male' },
    { label: '女', value: 'female' }
  ],
  props: {
    activeColor: '#ffffff',        // 激活时的文字颜色
    inactiveColor: '#909399',      // 未激活时的文字颜色
    customItemStyle: {             // 自定义样式
      backgroundColor: '#20a50a'   // 背景颜色
    }
  }
}
```

#### 4. 复选框（checkbox）
```javascript
{
  type: 'checkbox',
  label: '兴趣爱好',
  prop: 'hobbies',
  options: [
    { label: '阅读', value: 'reading' },
    { label: '运动', value: 'sports' },
    { label: '音乐', value: 'music' }
  ],
  props: {
    shape: 'circle',       // 形状：circle/square
    disabled: false,       // 是否禁用
    max: 3                // 最大选择数量
  }
}
```

#### 5. 选择器（select）
```javascript
{
  type: 'select',
  label: '城市',
  prop: 'city',
  options: [
    { label: '北京', value: 'beijing' },
    { label: '上海', value: 'shanghai' },
    { label: '广州', value: 'guangzhou' }
  ],
  props: {
    disabled: false,       // 是否禁用
    placeholder: '请选择城市',
    cancelText: '取消',
    confirmText: '确定'
  }
}
```

#### 6. 日期选择器（date）
```javascript
{
  type: 'date',
  label: '生日',
  prop: 'birthday',
  props: {
    disabled: false,       // 是否禁用
    placeholder: '请选择日期',
    format: 'YYYY-MM-DD',  // 日期格式
    minDate: '1900-01-01', // 最小日期
    maxDate: '2100-12-31'  // 最大日期
  }
}
```

#### 7. 自定义组件（custom）
```javascript
{
  type: 'custom',
  label: '自定义',
  prop: 'custom',
  render: (model, item) => {
    return h('view', {}, [
      h('text', {}, '自定义内容'),
      h('uv-input', {
        modelValue: model[item.prop],
        'onUpdate:modelValue': (val) => model[item.prop] = val
      })
    ]);
  }
}
```

### 完整示例

```javascript
const formConfig = {
  labelOptions: {
    position: 'left',
    width: 100
  },
  formItems: [
    {
      type: 'input',
      label: '用户名',
      prop: 'username',
      placeholder: '请输入用户名',
      borderBottom: true,
      rules: {
        required: true,
        message: '请输入用户名',
        trigger: ['blur']
      }
    },
    {
      type: 'textarea',
      label: '备注',
      prop: 'remark',
      placeholder: '请输入备注信息',
      props: {
        maxlength: 200,
        height: 100,
        autoHeight: true
      }
    },
    {
      type: 'radio',
      label: '性别',
      prop: 'gender',
      options: [
        { label: '男', value: 'male' },
        { label: '女', value: 'female' }
      ],
      rules: {
        required: true,
        message: '请选择性别',
        trigger: ['change']
      }
    },
    {
      type: 'checkbox',
      label: '兴趣爱好',
      prop: 'hobbies',
      options: [
        { label: '阅读', value: 'reading' },
        { label: '运动', value: 'sports' },
        { label: '音乐', value: 'music' }
      ],
      props: {
        shape: 'circle',
        max: 3
      }
    },
    {
      type: 'select',
      label: '城市',
      prop: 'city',
      options: [
        { label: '北京', value: 'beijing' },
        { label: '上海', value: 'shanghai' },
        { label: '广州', value: 'guangzhou' }
      ],
      rules: {
        required: true,
        message: '请选择城市',
        trigger: ['change']
      }
    },
    {
      type: 'date',
      label: '生日',
      prop: 'birthday',
      props: {
        format: 'YYYY-MM-DD',
        minDate: '1900-01-01',
        maxDate: '2100-12-31'
      }
    }
  ],
  initialValues: {
    username: '',
    remark: '',
    gender: '',
    hobbies: [],
    city: '',
    birthday: ''
  }
};
```

### 返回值说明

| 属性 | 类型 | 说明 |
| --- | --- | --- |
| FormComponent | Component | 表单组件 |
| formModel | Ref<Object> | 表单数据 |
| rules | Ref<Object> | 表单规则 |
| formRef | Ref | 表单实例引用 |
| validate | Function | 表单验证方法，返回Promise |
| resetForm | Function | 重置表单方法 |
| setFormValues | Function | 设置表单值方法 |
| getFormValues | Function | 获取表单值方法 |

## 示例页面

可以查看 `pages/form-example/index.vue` 了解完整的使用示例。 