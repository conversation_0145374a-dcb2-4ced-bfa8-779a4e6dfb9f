import { ref, onMounted } from 'vue';
import { $t } from '@/locale/index';

// App更新相关功能
export const useAppUpdate = () => {
    // App更新相关变量
    const phoneType = ref('android'); // 手机类型，可能是 'android' 或 'ios'
    const newVersion = ref(false); // 是否有新版本
    const updateVersionCode = ref(''); // 最新版本号
    const versionName = ref($t('appUpdate.gettingVersion')); // 当前版本号
    const versionNumber = ref(0); // 当前版本的数字表示
    const packagePath = ref(''); // 更新包路径
    const versionMsg = ref(''); // 版本更新信息
    const versionTip = ref(''); // 更新提示信息
    const updateType = ref(''); // 更新类型：'whole'(整包更新) 或 'hot'(热更新)
    const is_beta = ref(1); // 是否为测试版
    const showSumSize = ref(''); // 文件总大小
    const showDownloadSize = ref(''); // 当前已下载大小
    const percentage = ref(0); // 下载进度百分比

    // 控制弹窗显示状态
    const showPackagePopup = ref(false);
    const showProgressPopup = ref(false);
    const showHotUpdatePopup = ref(false);

    // 字节转换为可读大小
    const bytesToSize = (bytes) => {
        if (bytes === 0) return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return (bytes / Math.pow(k, i)).toFixed(2) + ' ' + sizes[i];
    };

    // 导航栏添加红点提示
    const setRedPoint = () => {
        let pages = getCurrentPages(),
            currentPage = pages[pages.length - 1],
            webviewObject = currentPage.$getAppWebview(),
            title = webviewObject.getStyle().titleNView;

        if (title && title.buttons && title.buttons[0]) {
            title.buttons[0].redDot = true;
            webviewObject.setStyle({
                titleNView: title,
            });
        }
    };

    // 监听下载任务状态
    const onStateChanged = (download, status) => {
        showSumSize.value = bytesToSize(download.totalSize);
        showDownloadSize.value = bytesToSize(download.downloadedSize);
        percentage.value = Math.round((download.downloadedSize / download.totalSize) * 100);
    };

    // 检查是否需要更新
    const updateVersion = () => {
        versionTip.value = '';

        // 获取当前应用版本信息
        plus.runtime.getProperty(plus.runtime.appid, (inf) => {
            // 用户当前版本名称
            versionName.value = inf.version;

            // 用户当前版本号
            versionNumber.value = parseInt(inf.version.split('.').join(''));

            // 请求服务器获取最新版本信息
            uni.request({
                url: 'https://www.bt.cn/api/panel/app_version',
                data: {
                    is_beta: is_beta.value,
                    platform: phoneType.value,
                    version: versionNumber.value,
                    panel_type: import.meta.env.VITE_PANEL,
                },
                sslVerify: false,
                method: 'POST',
                success(res) {
                    if (!res.data.status) {
                        updateVersionCode.value = inf.version;
                        return;
                    }

                    versionMsg.value = res.data.msg;

                    // 解析特殊提示
                    if (res.data.msg.indexOf('=tips') != -1) {
                        versionTip.value = res.data.msg.substr(res.data.msg.indexOf('=tips') + 5, res.data.msg.length);
                        versionMsg.value = res.data.msg.substr(0, res.data.msg.indexOf('=tips'));
                    }

                    // 最新版本的名称
                    updateVersionCode.value = res.data.version;
                    // 更新类型
                    updateType.value = res.data.update_type;

                    // 最新版本版本号
                    let systemVersion = parseInt(String(res.data.major) + String(res.data.minor) + String(res.data.patch));

                    if (systemVersion > versionNumber.value) {
                        // 有更新
                        newVersion.value = true;

                        // 设置红点通知
                        setRedPoint();

                        // 要更新的包的url
                        packagePath.value = res.data.package_path;

                        // 整包更新
                        if (updateType.value == 'whole') {
                            // 打开整包更新弹窗
                            showPackagePopup.value = true;
                        } else {
                            // 热更新
                            // 检查上次显示更新提示的日期
                            let lastDate = uni.getStorageSync('updateTime'), // 上次弹框的日期
                                nowDate = new Date().toLocaleDateString(); // 当前日期

                            if (lastDate !== nowDate) {
                                uni.setStorageSync('updateTime', new Date().toLocaleDateString());
                                // 打开热更新弹窗
                                showHotUpdatePopup.value = true;
                            }
                        }
                    }
                },
                fail(err) {
                    pageContainer.value.notify.error($t('appUpdate.checkUpdateFailed') + JSON.stringify(err));
                },
            });
        });
    };

    // 取消更新
    const cancelVersion = () => {
        showPackagePopup.value = false;
        showHotUpdatePopup.value = false;
    };

    // 下载并安装更新（整包更新）
    const downloadVersion = () => {
        if (phoneType.value == 'android') {
            showPackagePopup.value = false;
            showProgressPopup.value = true;

            let downloadTask = plus.downloader.createDownload(packagePath.value, {}, (download, status) => {
                if (status == 200) {
                    showProgressPopup.value = false;
                    plus.runtime.install(plus.io.convertLocalFileSystemURL(download.filename));
                }
            });

            downloadTask.addEventListener('statechanged', onStateChanged, false);
            downloadTask.start();
        } else {
            // iOS跳转到AppStore
            plus.runtime.openURL('https://itunes.apple.com/cn/app/id1513713028?mt=8');
        }
    };

    // 确认热更新
    const updateAffirm = () => {
        showHotUpdatePopup.value = false;
        showProgressPopup.value = true;

        let downloadTask = plus.downloader.createDownload(packagePath.value, {}, (download, status) => {
            if (status == 200) {
                plus.runtime.install(
                    download.filename,
                    {
                        force: false,
                    },
                    () => {
                        showProgressPopup.value = false;
                        pageContainer.value.notify.success($t('appUpdate.updateCompleted'));
                        setTimeout(() => {
                            plus.runtime.restart();
                        }, 1800);
                    },
                    (e) => {
                        pageContainer.value.notify.error($t('appUpdate.installFailed') + JSON.stringify(e));
                    },
                );
            }
        });

        downloadTask.addEventListener('statechanged', onStateChanged, false);
        downloadTask.start();
    };

    // 初始化
    const initAppUpdate = () => {
        // 获取设备类型
        phoneType.value = uni.getSystemInfoSync().platform;
        // #ifdef APP-PLUS
        // 检查更新
        setTimeout(() => {
            updateVersion();
        }, 1000);
        // #endif
    };

    onMounted(() => {
        initAppUpdate();
    });

    return {
        // 变量
        phoneType,
        newVersion,
        updateVersionCode,
        versionName,
        versionNumber,
        packagePath,
        versionMsg,
        versionTip,
        updateType,
        is_beta,
        showSumSize,
        showDownloadSize,
        percentage,
        showPackagePopup,
        showProgressPopup,
        showHotUpdatePopup,
        // 方法
        bytesToSize,
        setRedPoint,
        updateVersion,
        cancelVersion,
        downloadVersion,
        updateAffirm,
    };
};

export const useAgreement = () => {
    const agreementModel = ref(false);
    const closeAgreement = () => {
        agreementModel.value = false;
    };
    const confirmAgreement = () => {
        uni.setStorageSync('firstApp', false);
        agreementModel.value = false;
    };
    const openPage = (type) => {
        uni.navigateTo({
            url: "/pages/privacy/privacy?type=" + type
        })
    };
    return {
        agreementModel,
        closeAgreement,
        confirmAgreement,
        openPage,
    };
};
