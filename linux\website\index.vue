<template>
  <page-container
    ref="pageContainer"
    :showTabBar="isInstallWebServer"
    :tabData="tabData"
    @tabClick="onTabClick"
  >
    <template #title>
			<view class="custom-title-container">
				<view class="title-main text-secondary">{{ $t('website.management')}}</view>
				<view class="title-subtitle">
					<uv-icon name="info-circle" size="14" color="#999"></uv-icon>
					<text class="subtitle-text">长按列表项进行更多操作</text>
				</view>
			</view>
		</template>
    <!-- 模糊遮罩 -->
    <view class="blur-overlay" v-if="showContextMenu" @click="hideContextMenu"></view>

    <!-- 悬浮上下文菜单 -->
    <view class="context-menu" v-if="showContextMenu" :style="menuPosition" :class="menuPosition.class">
      <!-- 启动/暂停选项 -->
      <view class="menu-item" v-if="activeWebsite?.status == 0" @click="showStartConfirm">
        <uv-icon name="play-circle-fill" size="16" color="#20a53a"></uv-icon>
        <text class="menu-text">启动网站</text>
      </view>
      <view class="menu-item" v-if="activeWebsite?.status != 0" @click="showStopConfirm">
        <uv-icon name="pause-circle-fill" size="16" color="#FF3B30"></uv-icon>
        <text class="menu-text">暂停网站</text>
      </view>
      <view class="menu-divider"></view>
      <view class="menu-item" @click="handleEditType('ps')">
        <uni-icons type="compose" size="16" color="#007AFF"></uni-icons>
        <text class="menu-text">{{ $t('website.editRemark') }}</text>
      </view>
      <view class="menu-divider"></view>
      <view class="menu-item" @click="handleEditType('name')">
        <uni-icons type="compose" size="16" color="#007AFF"></uni-icons>
        <text class="menu-text">{{ $t('website.editName') }}</text>
      </view>
      <view class="menu-divider"></view>
      <view class="menu-item menu-delete" @click="showDeleteDialog = true">
        <uni-icons type="trash" size="16" color="#FF3B30"></uni-icons>
        <text class="menu-text menu-text-delete">{{ $t('website.delete') }}</text>
      </view>
    </view>

    <!-- 添加一个隐藏的临时菜单用于测量高度 -->
    <view
      class="temp-measure-menu context-menu"
      v-if="showTempMenu"
      style="position: absolute; opacity: 0; pointer-events: none; top: -9999px"
    >
      <view class="menu-item">
        <uv-icon name="play-circle-fill" size="16" color="#20a53a"></uv-icon>
        <text class="menu-text">启动网站</text>
      </view>
      <view class="menu-divider"></view>
      <view class="menu-item">
        <uni-icons type="paperclip" size="16" color="#007AFF"></uni-icons>
        <text class="menu-text">{{ $t('website.editRemark') }}</text>
      </view>
      <view class="menu-divider"></view>
      <view class="menu-item">
        <uni-icons type="compose" size="16" color="#007AFF"></uni-icons>
        <text class="menu-text">{{ $t('common.rename') }}</text>
      </view>
      <view class="menu-divider"></view>
      <view class="menu-item menu-delete">
        <uni-icons type="trash" size="16" color="#FF3B30"></uni-icons>
        <text class="menu-text menu-text-delete">{{ $t('website.delete') }}</text>
      </view>
    </view>

    <!-- 克隆项容器 - 放在外层，使用fixed定位 -->
    <view class="fixed-clone-container" v-if="showContextMenu">
      <view class="item-clone-wrapper" :style="clonePosition" v-if="activeWebsite">
        <view
          class="website-item-container bg-primary px-30 rd-14 website-item h-170 flex justify-between items-center"
        >
          <view class="flex flex-col items-flex-start py-16">
            <view class="max-w-300" :class="activeWebsite.status == 0 ? 'text-#f44336' : 'text-bt-primary'">
              <text class="text-32 font-bold block overflow-hidden text-ellipsis ws-nowrap mb-8">{{
                activeWebsite.rname
              }}</text>
            </view>
            <view class="max-w-300 text-secondary text-24">
              <text class="block overflow-hidden text-ellipsis ws-nowrap mb-10 text-tertiary">{{ activeWebsite.ps }}</text>
            </view>
            <view class="flex items-center text-24 rd-12rpx pl-10 pr-12 py-2" :style="{ backgroundColor: getSslInfo(activeWebsite.ssl).bg }">
							<uni-icons
								class="mr-4"
								:type="getSslInfo(activeWebsite.ssl).icon"
								:color="getSslInfo(activeWebsite.ssl).color"
								size="16"
							/>
							<text class="text-24 lh-38rpx h-36rpx" :style="{ color: getSslInfo(activeWebsite.ssl).color }">
								{{ getSslInfo(activeWebsite.ssl).label }}
							</text>
						</view>
          </view>
          <view class="flex items-center justify-center">
						<text class="text-24 text-tertiary">查看详情</text>
            <view class="flex w-30 h-30">
              <uni-icons type="right" color="var(--text-color-tertiary)" size="16" />
            </view>
          </view>
        </view>
      </view>
    </view>

    <install v-if="!isInstallWebServer" />
    <z-paging
      v-else
      ref="websitePaging"
      class="mt-260"
      :default-page-size="50"
      use-virtual-list
      :force-close-inner-list="true"
      :auto-hide-loading-after-first-loaded="false"
      :auto-show-system-loading="true"
      @virtualListChange="virtualListChange"
      @query="queryList"
      @refresherStatusChange="reload"
      :refresher-complete-delay="200"
    >
      <view
        class="px-26rpx mt-21rpx"
        v-for="item in websiteList"
        :id="`zp-id-${item.zp_index}`"
        :key="item.zp_index"
        @click="handleDomain(item)"
      >
        <view
          class="website-item-container bg-primary px-30 rd-14 website-item h-170 flex justify-between items-center"
          @touchstart="handleTouchStart($event)"
          @touchmove="handleTouchMove($event)"
          @touchend="handleTouchEnd($event)"
          @touchcancel="handleTouchCancel($event)"
          :data-index="item.zp_index"
          :data-website="JSON.stringify(item)"
        >
          <view class="flex flex-col items-flex-start py-16">
            <view class="max-w-300" :class="item.status == 0 ? 'text-#f44336' : 'text-bt-primary'">
              <text class="text-32 font-bold block overflow-hidden text-ellipsis ws-nowrap mb-8">{{ item.rname }}</text>
            </view>
            <view class="max-w-300 text-secondary text-24">
              <text class="block overflow-hidden text-ellipsis ws-nowrap mb-10 text-tertiary">{{ item.ps }}</text>
            </view>
            <view class="flex items-center text-24 rd-12rpx pl-10 pr-12 py-2" :style="{ backgroundColor: getSslInfo(item.ssl).bg }">
							<uni-icons
								class="mr-4"
								:type="getSslInfo(item.ssl).icon"
								:color="getSslInfo(item.ssl).color"
								size="16"
							/>
							<text class="text-24 lh-38rpx h-36rpx" :style="{ color: getSslInfo(item.ssl).color }">
								{{ getSslInfo(item.ssl).label }}
							</text>
            </view>
          </view>
          <view class="flex items-center justify-center">
						<text class="text-24 text-tertiary">查看详情</text>
            <view class="flex w-30 h-30">
              <uni-icons type="right" color="var(--text-color-tertiary)" size="16" />
            </view>
          </view>
        </view>
      </view>
    </z-paging>
    <CustomDialog
      contentHeight="100rpx"
      v-model="showEditDialog"
      :title="editType === 'name' ? $t('website.editName') : $t('website.editRemark')"
      :confirmText="$t('website.modify')"
      @confirm="confirmEdit"
      @cancel="renameEdit = ''"
    >
      <view class="text-secondary flex justify-center items-center h-full">
        <uv-input v-model="renameEdit" :placeholder="editType === 'name' ? activeWebsite?.rname : activeWebsite?.ps" />
      </view>
    </CustomDialog>
    <CustomDialog
      contentHeight="200rpx"
      v-model="showDeleteDialog"
      :title="$t('website.warning')"
      :confirmText="$t('website.delete')"
      :confirmStyle="{
        backgroundColor: '#FF3B30',
      }"
      @confirm="confirmDeleteFile"
    >
      <view class="text-secondary flex justify-center items-center h-full">
        {{ $t('website.confirmDelete', { name: activeWebsite?.rname }) }}
      </view>
    </CustomDialog>

    <!-- 启动确认对话框 -->
    <CustomDialog
      contentHeight="200rpx"
      v-model="showStartDialog"
      title="启动确认"
      confirmText="确认启动"
      :confirmStyle="{
        backgroundColor: '#20a53a',
      }"
      @confirm="confirmStartSite"
    >
      <view class="text-secondary flex justify-center items-center h-full">
        启动站点【{{ activeWebsite?.rname }}】后，用户可以正常访问网站内容，是否继续操作？
      </view>
    </CustomDialog>

    <!-- 暂停确认对话框 -->
    <CustomDialog
      contentHeight="200rpx"
      v-model="showStopDialog"
      title="暂停确认"
      confirmText="确认暂停"
      :confirmStyle="{
        backgroundColor: '#FF3B30',
      }"
      @confirm="confirmStopSite"
    >
      <view class="text-secondary flex justify-center items-center h-full">
        停用站点【{{ activeWebsite?.rname }}】后，用户访问会显示当前网站停用后的提示页，是否继续操作？
      </view>
    </CustomDialog>
    <!-- <uni-fab :pattern="{ buttonColor: '#20a50a' }" horizontal="right" vertical="bottom" :popMenu="false"> </uni-fab> -->
  </page-container>
</template>

<script setup>
  import { onShow, onBackPress, onUnload } from '@dcloudio/uni-app';
  import PageContainer from '@/components/PageContainer/index.vue';
  import CustomDialog from '@/components/CustomDialog/index.vue';
  import install from './install.vue';
  import { ref, watch, onMounted } from 'vue';
  import { $t } from '@/locale/index.js';
  import {
    websitePaging,
    showContextMenu,
    currentSiteType,
    getSiteList,
    isInstallWebServer,
    getWebServerStatus,
    handleDomain,
    showTempMenu,
    menuPosition,
    handleTouchStart,
    handleTouchMove,
    handleTouchEnd,
    handleTouchCancel,
    clonePosition,
    activeWebsite,
    hideContextMenu,
    measureMenuHeight,
    pageContainer,
    showEditDialog,
    renameEdit,
    handleEditType,
    editType,
    confirmEdit,
    showDeleteDialog,
    confirmDeleteFile,
    handleStartSite,
    handleStopSite,
    showStartDialog,
    showStopDialog,
    showStartConfirm,
    showStopConfirm,
    confirmStartSite,
    confirmStopSite,
  } from './useController';

  const tabData = [$t('website.running'), $t('website.stopped')];

  const websiteList = ref();

  const virtualListChange = (vList) => {
    websiteList.value = vList;
  };

  const queryList = async (page, pageSize) => {
    try {
      const res = await getSiteList(page, pageSize);
      websitePaging.value.complete(res);
      websitePaging.value.updateVirtualListRender();
    } catch (error) {
      websitePaging.value.complete([]);
      console.error(error);
    }
  };

  const reload = (reloadType) => {
    if (reloadType === 'complete') {
      pageContainer.value.notify.success($t('website.refreshSuccess'));
    }
  };

  const onTabClick = (index) => {
    currentSiteType.value = index;
    websitePaging.value.reload();
  };

  onShow(() => {
    getWebServerStatus();
    if (websitePaging.value && isInstallWebServer.value) {
      websitePaging.value.reload();
    }
  });

  onBackPress(() => {
    // 如果长按菜单正在显示，先关闭菜单
    if (showContextMenu.value) {
      hideContextMenu();
      return true;
    }
    return false;
  });

  onUnload(() => {
    currentSiteType.value = 0;
    showEditDialog.value = false;
    showDeleteDialog.value = false;
    showStartDialog.value = false;
    showStopDialog.value = false;
  });

  // 在页面挂载时获取菜单高度
  onMounted(() => {
    measureMenuHeight();
  });

  // 监听菜单显示状态，防止页面滚动
  watch(showContextMenu, (val) => {
    if (val) {
      // 菜单显示时，禁用页面滚动
      uni.pageScrollTo({
        scrollTop: 0,
        duration: 0,
      });

      // 锁定列表滚动
      if (websitePaging.value) {
        websitePaging.value.lockScroll && websitePaging.value.lockScroll(true);
      }
    } else {
      // 恢复列表滚动
      if (websitePaging.value) {
        websitePaging.value.lockScroll && websitePaging.value.lockScroll(false);
      }
    }
  });

function getSslInfo(ssl) {
  if (ssl === -1) {
    return {
      icon: 'cloud-download',
      color: '#EF6B6B',
      bg: '#FFEBEB',
      label: $t('website.status.notDeployed')
    }
  } else if (ssl?.endtime < 0) {
    return {
      icon: 'calendar',
      color: '#f44336',
      bg: '#FFEBEB',
      label: $t('website.status.certExpired')
    }
  } else {
    return {
      icon: 'calendar',
      color: '#20a53a',
      bg: '#E0FFDF',
      label: $t('website.status.sslExpireDays', { days: ssl.endtime })
    }
  }
}
</script>

<style lang="scss" scoped>
  .website-item {
    border: 1rpx solid rgba(255, 255, 255, 0.3);
    box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.15);
    transition: all 0.2s ease;
  }

  .website-item:active {
    background-color: rgba(var(--bg-color-secondary-rgb), 0.4);
    transform: scale(0.98);
  }

  /* 模糊遮罩 */
  .blur-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(3px);
    -webkit-backdrop-filter: blur(3px);
    z-index: 15;
    pointer-events: auto;
    touch-action: none; /* 禁止所有触摸操作 */
    user-select: none; /* 禁止选择 */
  }

  /* 上下文菜单 */
  .context-menu {
    position: fixed;
    z-index: 30;
    background: #fff;
    border-radius: 14rpx;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.25);
    padding: 10rpx 0;
    transform: translate(-50%, 0);
    min-width: 340rpx;
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);

    &.menu-top {
      animation: fadeInTop 0.2s ease;
      transform: translate(-50%, 0);
    }

    &.menu-bottom {
      animation: fadeInBottom 0.2s ease;
      transform: translate(-50%, 0);
    }

    /* 菜单位于上方但紧贴克隆项顶部 */
    &.menu-position-bottom {
      transform: translate(-50%, 0);
    }
  }

  .menu-item {
    display: flex;
    align-items: center;
    padding: 24rpx 30rpx;

    &:active {
      background-color: rgba(0, 0, 0, 0.05);
    }
  }

  .menu-text {
    margin-left: 16rpx;
    font-size: 28rpx;
    color: #333;
  }

  .menu-delete {
    opacity: 0.9;
  }

  .menu-text-delete {
    color: #ff3b30;
  }

  .menu-divider {
    height: 1rpx;
    background-color: rgba(0, 0, 0, 0.1);
    margin: 0 10rpx;
  }

  /* 固定克隆项容器 */
  .fixed-clone-container {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 25;
    pointer-events: none;
  }

  /* 克隆项样式 */
  .item-clone-wrapper {
    position: absolute;
    pointer-events: none; /* 阻止触摸事件 */
  }
</style>
