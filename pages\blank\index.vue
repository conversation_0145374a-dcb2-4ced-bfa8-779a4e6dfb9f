<template>
  <PageContainer ref="pageContainer" :title="$t('blank.lock')" :is-back="false" :has-tab-bar="false">
    <view class="lock-box bg-primary">
      <view class="lock flex flex-col" v-if="contentFlag">
        <view class="uni-text bt-title" v-if="type == 'login'">{{ $t('blank.blankTitle') }}</view>

        <view class="uni-text text-secondary" :style="{ color: text.indexOf($t('blank.patternCompleted')) != -1 ? '#20a50a' : '' }">{{ text }}</view>
        <view class="lock-content">
          <mpvue-gesture-lock
            :containerWidth="590"
            :cycleRadius="70"
            @end="onEnd"
            :password="password"
          ></mpvue-gesture-lock>
        </view>

        <view class="lock-btn flex flex-row" v-if="type == 'set'">
          <view class="again bg-secondary text-primary" @click="again">{{ $t('blank.notSet') }}</view>
          <view class="confirm bg-secondary" :style="{ color: lockPwd.length != 0 ? '#20a50a' : '#999' }" @click="openPopup"
            >{{ $t('common.confirm') }}</view
          >
        </view>

        <view class="lock-btn flex flex-row" v-if="type == 'new'">
          <view class="again bg-secondary text-primary" @click="handleNavigate">{{ $t('blank.back') }}</view>
          <view class="confirm bg-secondary" :style="{ color: lockPwd.length != 0 ? '#20a50a' : '#999' }" @click="openPopup"
            >{{ $t('common.confirm') }}</view
          >
        </view>

        <view class="forget-btn flex flex-row" v-if="type == 'reset'">
          <view class="again" @click="notChange">{{ $t('blank.notChange') }}</view>
        </view>

        <view class="forget-btn flex flex-row" v-if="type == 'login'">
          <view @click="forgetModel = true">{{ $t('blank.forget') }}</view>
        </view>
      </view>
      <EmptyServerList v-else @scan="startScan" />
    </view>
    <CustomDialog
      v-model="lockModel"
      contentHeight="120rpx"
      :title="$t('common.confirm')"
      @close="lockModel = false"
      @confirm="lockConfirm"
    >
      <view class="h-full text-secondary flex items-center justify-center">
        <text>{{ $t('blank.confirmApp') }}</text>
      </view>
    </CustomDialog>
    <CustomDialog
      v-model="forgetModel"
      contentHeight="160rpx"
      :title="$t('blank.reset')"
      @close="forgetModel = false"
      @confirm="forgetConfirm"
      :confirmStyle="{
        backgroundColor: '#FF3B30',
      }"
    >
      <view class="h-full text-secondary flex items-center justify-center">
        <text>{{ $t('blank.forgetConfirm') }}</text>
      </view>
    </CustomDialog>
  </PageContainer>
</template>

<script setup>
  import PageContainer from '@/components/PageContainer/index.vue';
  import mpvueGestureLock from '@/components/Unlock/index.vue';
  import CustomDialog from '@/components/CustomDialog/index.vue';
  import EmptyServerList from '@/pages/index/serverList/emptyServerList.vue';
  import { ref } from 'vue';
  import { onLoad } from '@dcloudio/uni-app';
  import { useConfigStore } from '@/store/modules/config';
  import { appUnlock as appUnlockData } from '@/pages/index/setting/useMethods';
  import { $t } from '@/locale/index.js';
  import useScan from '@/hooks/useScan';
  import { handleCloudBindSuccess, handleBindSuccess } from '@/pages/index/serverList/useController';

  const { unlockType, configList } = useConfigStore().getReactiveState();

  const tap = ref('not');
  const password = ref([]);
  const text = ref($t('blank.drawPattern'));
  const lockPwd = ref([]);
  const type = ref(''); // set:未设置手势密码，login:已设置密码，reset:修改密码，new:新设置密码
  const contentFlag = ref(false);
  const lockModel = ref(false);
  const pageContainer = ref(null);
  const forgetModel = ref(false);
  const pages = getCurrentPages();

  // 扫码功能
  const { isScanning, startScan } = useScan({
    onScanError(error) {
      pageContainer.value.notify.error(error.message || $t('scan.scanFail'));
    },
    onScanSuccess(res) {
      // 扫码成功处理
    },
    async onBindSuccess(bindInfo) {
      try {
        if (bindInfo.type === 'cloud') {
          // 处理云控绑定成功
          const { cloudCode } = bindInfo;
          handleCloudBindSuccess(cloudCode);
        } else {
          // 处理普通绑定成功
          await handleBindSuccess(bindInfo);
          pageContainer.value.notify.success($t('scan.bindSuccess'));
        }

        // 等待一下让用户看到成功提示，然后跳转到主页
        setTimeout(() => {
          uni.reLaunch({
            url: '/pages/index/serverList/index',
            animationType: 'zoom-fade-out',
          });
        }, 1000);
      } catch (error) {
        console.error('绑定处理失败:', error);
        // 即使处理失败也跳转，因为绑定本身已经成功了
        setTimeout(() => {
          uni.reLaunch({
            url: '/pages/index/serverList/index',
            animationType: 'zoom-fade-out',
          });
        }, 1000);
      }
    },
    onBindError(error) {
      pageContainer.value.notify.error(error.message || $t('scan.bindFail'));
    },
    onLoginSuccess(loginInfo) {
      pageContainer.value.notify.success('登录成功');
    },
    onLoginError(error) {
      pageContainer.value.notify.error(error.message || $t('scan.loginFail'));
    }
  });

  const handleNavigate = () => {
    if (pages.length <= 1) {
      // 当前已经是第一页，直接调用reLaunch
      uni.reLaunch({
        url: '/pages/index/serverList/index',
        animationType: 'zoom-fade-out',
      });
    } else {
      // 不是第一页，使用navigateBack
      uni.navigateBack();
    }
  };

  const handleUnlock = (appUnlock) => {
    if (unlockType.value == 'reset') {
      text.value = $t('blank.resetPwd');
      lockPwd.value = uni.getStorageSync('appLogin');
      type.value = 'reset';
    } else if (appUnlock == 'open' && uni.getStorageSync('appLogin').length != 0) {
      text.value = $t('blank.lockPwd');
      lockPwd.value = uni.getStorageSync('appLogin');
      type.value = 'login';
    } else {
      type.value = 'set';
    }
  };

  const forgetConfirm = (close) => {
    uni.showLoading({
      title: $t('blank.resetApp'),
    });
    uni.setStorage({
      key: 'configList',
      data: [],
    });
    configList.value = [];
    uni.setStorage({
      key: 'appUnlock',
      data: 'close',
    });
    uni.setStorage({
      key: 'appLogin',
      data: [],
    });
    close && close();
    appUnlockData.value = 'close';

    const delay = (ms) => new Promise((resolve) => setTimeout(resolve, ms));

    delay(1000)
      .then(() => {
        pageContainer.value.notify.success($t('blank.resetSuccess'));
        return delay(1300); // 2300 - 1000 = 1300，保持总延迟时间不变
      })
      .then(() => {
        uni.reLaunch({
          url: '/pages/index/serverList/index',
          animationType: 'zoom-fade-out',
        });
      });
  };

  const openPopup = () => {
    if (lockPwd.value.length <= 0) return;
    lockModel.value = true;
  };

  const lockConfirm = (close) => {
    close && close();
    appUnlockData.value = 'open';
    unlockType.value = '';
    pageContainer.value.notify.success($t('blank.saveSuccess'));
    uni.setStorage({
      key: 'appLogin',
      data: lockPwd.value,
    });
    uni.setStorage({
      key: 'appUnlock',
      data: 'open',
    });
    setTimeout(() => {
      handleNavigate()
    }, 500);
  };

  const again = () => {
    uni.setStorage({
      key: 'appLogin',
      data: [],
    });
    uni.setStorage({
      key: 'appUnlock',
      data: 'close',
    });
    appUnlockData.value = 'close';
    unlockType.value = '';
    handleNavigate()
  };

  const notChange = () => {
    handleNavigate()
  };

  const onEnd = (data) => {
    // 登录
    if (type.value == 'login') {
      let flag = true;

      if (data.length != lockPwd.value.length) {
        flag = false;
      } else {
        for (let i = 0; i < lockPwd.value.length; i++) {
          if (lockPwd.value[i] != data[i]) {
            flag = false;
          }
        }
      }

      if (flag) {
        text.value = $t('blank.welcome');
        uni.reLaunch({
          animationType: 'zoom-fade-out',
          url: '/pages/index/serverList/index?name=login',
        });
      } else {
        text.value = $t('blank.patternError');
      }
    } else if (type.value == 'set' || type.value == 'new') {
      // 设置图案密码

      lockPwd.value = [];

      if (data.length < 4) {
        text.value = $t('blank.patternTooShort');
        password.value = [];
        lockPwd.value = [];
        return;
      }

      if (password.value.length) {
        if (password.value.join('') === data.join('')) {
          text.value = $t('blank.patternCompleted');
          lockPwd.value = password.value;
          password.value = [];
        } else {
          text.value = $t('blank.patternMismatch');
          password.value = [];
          lockPwd.value = [];
        }
      } else {
        text.value = $t('blank.drawAgain');
        password.value = data;
      }
    } else if (type.value == 'reset') {
      // 重置密码

      let flag = true;

      if (data.length != lockPwd.value.length) {
        flag = false;
      } else {
        for (let i = 0; i < lockPwd.value.length; i++) {
          if (lockPwd.value[i] != data[i]) {
            flag = false;
          }
        }
      }

      if (flag) {
        type.value = 'new';
        lockPwd.value = [];
        password.value = [];
        text.value = $t('blank.drawNewPattern');
      } else {
        text.value = $t('blank.originalPatternError');
      }
    }
  };

  onLoad(() => {
    let appUnlock = uni.getStorageSync('appUnlock');
    // 如果是首次打开app（appUnlock不存在或为空）或者appUnlock不等于'open'，直接跳转到index页面
    if (!appUnlock || appUnlock !== 'open') {
      uni.reLaunch({
        animationType: 'zoom-fade-out',
        url: '/pages/index/serverList/index',
      });

      return;
    }
    contentFlag.value = true;
    handleUnlock(appUnlock);
  });
</script>

<style>
  page {
    background-color: #fff;
  }

  .lock-box {
    height: 100vh;
    width: 100%;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    position: fixed;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    z-index: 1;
    touch-action: none; /* 禁止浏览器默认触摸行为 */
  }

  .lock {
    height: 100%;
    align-items: center;
    justify-content: center;
  }

  .bt-title {
    font-size: 38rpx !important;
    color: #333 !important;
    position: fixed;
    top: 0;
  }

  .uni-text {
    font-size: 28rpx;
    padding-bottom: 50rpx;
  }

  .lock-content {
    width: 100%;
  }

  .lock-btn {
    width: 100%;
    margin-top: 20%;
    align-items: center;
    justify-content: space-around;
  }

  .lock-btn view {
    width: 35%;
    font-size: 30rpx;
    text-align: center;
    height: 90rpx;
    line-height: 90rpx;
    border-radius: 40rpx;
  }

  .forget-btn {
    width: 90%;
  }

  .forget-btn view {
    width: 100%;
    text-align: right;
    padding-top: 40rpx;
    font-size: 28rpx;
    color: #666;
    text-decoration: underline;
  }
</style>
