<template>
	<view class="container">
		<!-- 头部卡片 -->
		<view class="card header-card">
			<view class="header">
				<text class="header-title">{{ activeTab === 'list' ? 'URL 列表' : '添加URL CC防御' }}</text>
				<view v-if="activeTab === 'list'" class="btn-add" hover-class="btn-hover" @tap="activeTab = 'add'">
					<uv-icon name="plus" size="16" color="#ffffff"></uv-icon>
					<text class="btn-text">添加</text>
				</view>
				<view v-else class="btn-back" hover-class="btn-hover" @tap="activeTab = 'list'">
					<uv-icon name="arrow-left" size="16" color="#ffffff"></uv-icon>
					<text class="btn-text">返回</text>
				</view>
			</view>
			<view v-if="activeTab === 'add'">
				<view class="pt-20">
					<!-- URL表单 -->
					<view class="form-item">
						<text class="form-item-label">URL</text>
						<view class="form-item-content">
							<input
								v-model="newUrl"
								type="text"
								class="form-input"
								placeholder="/index.php"
								@blur="validateUrlField"
								:class="{ 'input-error': errors.url }"
							/>
						</view>
						<text v-if="errors.url" class="error-text">{{ errors.url }}</text>
					</view>

					<!-- 访问次数 -->
					<view class="form-item">
						<text class="form-item-label">访问次数</text>
						<view class="form-item-content">
							<view class="input-with-unit">
								<input
									v-model="newFrequency"
									type="number"
									class="form-input"
									placeholder="请输入访问次数"
									@blur="validateFrequencyField"
									:class="{ 'input-error': errors.frequency }"
								/>
								<text class="unit">次</text>
							</view>
						</view>
						<text v-if="errors.frequency" class="error-text">{{ errors.frequency }}</text>
					</view>

					<!-- 时间限制 -->
					<view class="form-item">
						<text class="form-item-label">时间限制</text>
						<view class="form-item-content">
							<view class="input-with-unit">
								<input
									v-model="newCycle"
									type="number"
									class="form-input"
									placeholder="请输入时间限制"
									@blur="validateCycleField"
									:class="{ 'input-error': errors.cycle }"
								/>
								<text class="unit">秒</text>
							</view>
						</view>
						<text v-if="errors.cycle" class="error-text">{{ errors.cycle }}</text>
					</view>

					<!-- 提示信息 -->
					<view class="form-tips">
						<view class="form-tip-item">
							<text class="dot">•</text>
							<text>当前CC防护为单个URI访问CC防护，建议启用后测试访问不影响正常功能</text>
						</view>
					</view>

					<!-- 操作按钮 -->
					<view class="form-submit">
						<button class="submit-btn" hover-class="submit-btn-hover" @tap="addUrl">添加</button>
					</view>
				</view>
			</view>
		</view>

		<!-- 主内容区域 -->
		<view class="content-wrapper">
			<!-- List View -->
			<view v-if="activeTab === 'list'" class="url-list-container">
				<view v-if="urls.length === 0" class="empty-state">
					<uv-icon name="info-circle" size="32" color="#909399"></uv-icon>
					<text class="empty-text">当前数据为空，点击右上角添加</text>
				</view>

				<view v-else class="url-list">
					<view v-for="item in urls" :key="item.id" class="url-card">
						<view class="url-card__header">
							<text class="url-card__title">{{ item.url }}</text>
							<view class="url-card__action" @tap="deleteUrl(item.url)">
								<uv-icon name="trash" size="16" color="#e53935"></uv-icon>
							</view>
						</view>

						<view class="url-card__content">
							<view class="url-card__info-row">
								<view class="url-card__info-item">
									<text class="info-label">访问次数</text>
									<text class="info-value">{{ item.frequency }}次</text>
								</view>
								<view class="url-card__info-item">
									<text class="info-label">时间限制</text>
									<text class="info-value">{{ item.cycle }}秒</text>
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script setup>
	import { ref, onMounted } from 'vue';
	import { getNginxFirewallGlobalConfig, addNginxFirewallCcUriFrequency, deleteNginxFirewallCcUriFrequency } from '@/api/nginx.js';
	import { formPageContainer } from './useController.js';

	const activeTab = ref('list');
	const newUrl = ref('');
	const newFrequency = ref('30');
	const newCycle = ref('60');

	const urls = ref([]);

	// 表单验证错误状态
	const errors = ref({
		url: '',
		frequency: '',
		cycle: ''
	});

	/**
	 * 验证URL字段
	 */
	const validateUrlField = () => {
		const value = newUrl.value.trim();

		// 清除之前的错误
		errors.value.url = '';

		// 检查是否为空
		if (!value) {
			errors.value.url = 'URL不能为空';
			return false;
		}

		// 检查URL格式（简单验证，必须以/开头）
		if (!value.startsWith('/')) {
			errors.value.url = 'URL必须以 / 开头';
			return false;
		}

		return true;
	};

	/**
	 * 验证访问次数字段
	 */
	const validateFrequencyField = () => {
		const value = newFrequency.value;

		// 清除之前的错误
		errors.value.frequency = '';

		// 检查是否为空
		if (!value && value !== 0) {
			errors.value.frequency = '访问次数不能为空';
			return false;
		}

		// 检查是否为数字
		if (isNaN(value)) {
			errors.value.frequency = '访问次数必须是数字';
			return false;
		}

		// 检查是否为整数
		if (!Number.isInteger(Number(value))) {
			errors.value.frequency = '访问次数必须是整数，不能包含小数点';
			return false;
		}

		// 检查是否为正数
		if (Number(value) <= 0) {
			errors.value.frequency = '访问次数必须大于0';
			return false;
		}

		return true;
	};

	/**
	 * 验证时间限制字段
	 */
	const validateCycleField = () => {
		const value = newCycle.value;

		// 清除之前的错误
		errors.value.cycle = '';

		// 检查是否为空
		if (!value && value !== 0) {
			errors.value.cycle = '时间限制不能为空';
			return false;
		}

		// 检查是否为数字
		if (isNaN(value)) {
			errors.value.cycle = '时间限制必须是数字';
			return false;
		}

		// 检查是否为整数
		if (!Number.isInteger(Number(value))) {
			errors.value.cycle = '时间限制必须是整数，不能包含小数点';
			return false;
		}

		// 检查是否为正数
		if (Number(value) <= 0) {
			errors.value.cycle = '时间限制必须大于0';
			return false;
		}

		return true;
	};

	/**
	 * 验证所有表单字段
	 */
	const validateAllFields = () => {
		const urlValid = validateUrlField();
		const frequencyValid = validateFrequencyField();
		const cycleValid = validateCycleField();

		return urlValid && frequencyValid && cycleValid;
	};

	const addUrl = async () => {
		// 验证表单
		if (!validateAllFields()) {
			return;
		}

		try {
			const res = await addNginxFirewallCcUriFrequency({
				url: newUrl.value,
				frequency: parseInt(newFrequency.value) || 30,
				cycle: parseInt(newCycle.value) || 60,
			});
			if (res.status) {
				formPageContainer.value.notify.success(res.msg);
				// 重置表单并切换回列表视图
				newUrl.value = '';
				newFrequency.value = '30';
				newCycle.value = '60';
				// 清除错误状态
				errors.value = { url: '', frequency: '', cycle: '' };
				activeTab.value = 'list';
				getGlobalConfig();
			} else {
				formPageContainer.value.notify.error(res.msg);
			}
		} catch (error) {
			console.error('添加URL失败:', error);
		}
	};

	const deleteUrl = async (url) => {
		try {
			const res = await deleteNginxFirewallCcUriFrequency({ url });
			if (res.status) {
				formPageContainer.value.notify.success(res.msg || '删除成功');
				// 重新获取数据更新列表
				getGlobalConfig();
			} else {
				formPageContainer.value.notify.error(res.msg || '删除失败');
			}
		} catch (error) {
			console.error('删除URL失败:', error);
			formPageContainer.value.notify.error('删除失败，请稍后重试');
		}
	};

	const getGlobalConfig = async () => {
		try {
			const res = await getNginxFirewallGlobalConfig();

			// 将 API 返回的数据格式转换为组件期望的格式
			if (res.cc_uri_frequency && typeof res.cc_uri_frequency === 'object') {
				urls.value = Object.entries(res.cc_uri_frequency).map(([url, config], index) => ({
					id: (index + 1).toString(),
					url: url,
					frequency: parseInt(config.frequency) || 30,
					cycle: parseInt(config.cycle) || 60,
				}));
			} else {
				urls.value = [];
			}
		} catch (error) {
			console.error('获取全局配置失败:', error);
			urls.value = [];
		}
	};

	onMounted(() => {
		getGlobalConfig();
	});
</script>

<style lang="scss" scoped>
	// Variables
	$primary-color: #20a50a;
	$primary-hover: #189008;
	$background-color: #f5f5f5;
	$text-color: var(--text-color-primary);
	$light-text: var(--text-color-secondary);
	$border-color: #eaeaea;
	$danger-color: #e53935;
	$card-background: var(--dialog-bg-color);
	$bg-color: var(--dialog-bg-color);
	$base-spacing: 20rpx;

	.container {
		padding: $base-spacing;
	}

	.card {
		background: $bg-color;
		border-radius: 16rpx;
		box-shadow: 0 4rpx 24rpx rgba(0, 0, 0, 0.15);
		overflow: hidden;
	}

	.header {
		display: flex;
		justify-content: space-between;
		align-items: center;
	}

	.header-title {
		font-size: 32rpx;
		font-weight: 500;
		color: $text-color;
	}

	.btn-add,
	.btn-back {
		display: flex;
		align-items: center;
		padding: 16rpx 40rpx;
		background-color: $primary-color;
		border-radius: 8rpx;
		color: white;
		font-size: 28rpx;

		.btn-text {
			margin-left: 10rpx;
		}
	}

	.btn-hover {
		opacity: 0.9;
		transform: scale(0.98);
	}

	// 内容区域
	.content-wrapper {
		margin-top: $base-spacing;
	}

	// 空状态样式
	.url-list-container {
		position: relative;
	}

	.empty-state {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding: 80rpx 0;
		background-color: $bg-color;
		border-radius: 16rpx;
		box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.12);
		color: $light-text;

		.empty-text {
			font-size: 28rpx;
			margin-top: 20rpx;
		}
	}

	// 列表卡片样式
	.url-list {
		display: flex;
		flex-direction: column;
		gap: $base-spacing;
	}

	.url-card {
		background-color: $card-background;
		border-radius: 12rpx;
		box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.12);
		overflow: hidden;
		border: 2rpx solid #f0f0f0;

		&__header {
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: 20rpx;
			background-color: $card-background;
			border-bottom: 2rpx solid #f0f0f0;
		}

		&__title {
			font-size: 28rpx;
			font-weight: 500;
			color: $primary-color;
			word-break: break-all;
		}

		&__action {
			width: 60rpx;
			height: 60rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			background-color: rgba($danger-color, 0.05);
			border-radius: 30rpx;

			&:active {
				background-color: rgba($danger-color, 0.15);
			}
		}

		&__content {
			padding: 20rpx;
		}

		&__info-row {
			display: flex;
			margin-bottom: 16rpx;

			&:last-child {
				margin-bottom: 0;
			}
		}

		&__info-item {
			flex: 1;
			margin-right: 20rpx;

			&:last-child {
				margin-right: 0;
			}
		}
	}

	.info-label {
		font-size: 24rpx;
		color: $light-text;
		display: block;
		margin-bottom: 8rpx;
	}

	.info-value {
		font-size: 28rpx;
		color: $text-color;
		background-color: var(--border-color);
		padding: 12rpx 16rpx;
		border-radius: 6rpx;
		display: block;
		word-break: break-all;
	}

	.form-item {
		margin-bottom: 30rpx;
	}

	.form-item-label {
		display: block;
		font-size: 28rpx;
		color: $text-color;
		margin-bottom: 10rpx;
	}

	.form-item-content {
		width: 100%;
	}

	.form-input {
		width: 100%;
		height: 80rpx;
		border: 2rpx solid #ddd;
		border-radius: 6rpx;
		padding: 0 20rpx;
		font-size: 28rpx;
		background-color: #fff;
		box-sizing: border-box;

		&.input-error {
			border-color: #e53935;
		}
	}

	.error-text {
		color: #e53935;
		font-size: 24rpx;
		margin-top: 8rpx;
		display: block;
	}

	.input-with-unit {
		display: flex;
		align-items: center;

		.form-input {
			flex: 1;
		}

		.unit {
			margin-left: 16rpx;
			color: $light-text;
		}
	}

	.form-tips {
		margin: 40rpx 0;
	}

	.form-tip-item {
		display: flex;
		align-items: flex-start;
		margin-bottom: 16rpx;
		font-size: 26rpx;
		color: $light-text;
		line-height: 1.5;

		.dot {
			margin-right: 10rpx;
		}
	}

	.form-submit {
		margin: 40rpx 0 20rpx 0;
	}

	.submit-btn {
		width: 100%;
		height: 90rpx;
		line-height: 90rpx;
		text-align: center;
		background-color: $primary-color;
		color: white;
		font-size: 32rpx;
		border-radius: 6rpx;
	}

	.submit-btn-hover {
		background-color: $primary-hover;
	}
</style>
