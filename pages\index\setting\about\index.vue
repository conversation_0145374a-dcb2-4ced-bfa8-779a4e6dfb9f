<template>
  <page-container ref="pageContainerRef" :title="$t('setting.about')">
    <view class="about-container">
      <!-- 顶部Logo及版本信息 -->
      <view class="header-section">
        <image class="app-logo" :src="logoImg" mode="aspectFit" />
        <text class="app-name text-primary">{{ $t('server.btPanel') }}</text>
        <text class="app-version">{{ versionNumber }}</text>
      </view>

      <!-- 功能列表 -->
      <view class="function-list dialog-bg">
        <view class="function-item" @click="showPrivacyPolicy">
          <text class="item-name text-primary">{{ $t('setting.privacyPolicy') }}</text>
          <view class="item-arrow"></view>
        </view>

        <view class="function-item" v-if="panelType === 'btPanel'" @click="showServiceAgreement">
          <text class="item-name text-primary">{{ $t('setting.serviceAgreement') }}</text>
          <view class="item-arrow"></view>
        </view> 
      </view>

      <!-- 检查更新按钮（单独放置，更加突出） -->
      <view class="function-list dialog-bg update-list">
        <view class="function-item check-update" @click="checkUpdate">
          <text class="item-name text-primary">{{ $t('setting.checkUpdate') }}</text>
          <view class="item-arrow"></view>
        </view>
      </view>

      <!-- 底部版权信息 -->
      <view class="footer" v-if="panelType === 'btPanel'">
        <text class="text-secondary">{{ $t('setting.recordNumber') }}</text>
        <view class="link-row">
          <text class="text-secondary">{{ $t('setting.queryLink') }}</text>
          <uni-link href="https://beian.miit.gov.cn/" text="https://beian.miit.gov.cn/" color="#20A53A"></uni-link>
        </view>
      </view>
    </view>
  </page-container>
</template>

<script setup>
  import { ref, onMounted, computed } from 'vue';
  import PageContainer from '@/components/PageContainer/index.vue';
  import { $t } from '@/locale/index.js';

  const pageContainerRef = ref(null);
  const versionNumber = ref($t('appUpdate.gettingVersion'));
  const panelType = import.meta.env.VITE_PANEL;
  const id = 'ct.bt.app'

  const logoImg = computed(() => {
    return panelType === 'btPanel' ? '/static/logo.png' : '/static/aapanel_logo.png'
  })

  // 显示隐私政策
  const showPrivacyPolicy = () => {
    if (panelType === 'btPanel') {
      uni.navigateTo({
        url: '/pages/privacy/privacy?type=privacy',
        animationType: 'zoom-fade-out',
      });
    } else {
      uni.navigateTo({
        url: '/pages/privacy/aapanelPrivacy',
        animationType: 'zoom-fade-out',
      });
    }
  };

  // 显示服务协议
  const showServiceAgreement = () => {
    uni.navigateTo({
      url: '/pages/privacy/privacy?type=service',
      animationType: 'zoom-fade-out',
    });
  };

  // 检查更新方法
  const checkUpdate = () => {
    // 显示加载中
    uni.showLoading({
      title: $t('setting.checkingUpdate'),
      mask: true,
    });

    // 模拟检查更新过程
    setTimeout(() => {
      uni.hideLoading();

      // 根据平台跳转到对应的应用商店
      uni.getSystemInfo({
        success: (res) => {
          if (res.platform === 'ios') {
            // iOS跳转到App Store
            plus.runtime.openURL(`https://itunes.apple.com/cn/app/id1513713028?mt=8`);
          } else if (res.platform === 'android') {
            // 检查是否为鸿蒙系统
            if (res.osName && res.osName.toLowerCase().includes('harmony')) {
              // 鸿蒙系统跳转到华为应用市场
              plus.runtime.openURL(`store://appgallery.huawei.com/app/detail?id=${id}`);
            } else {
              // Android跳转到Google Play或其他应用商店
              plus.runtime.openURL(`market://details?id=${id}`);
            }
          }
        },
        fail: () => {
          // 获取系统信息失败时的默认处理
          plus.runtime.openURL(`market://details?id=${id}`);
        }
      });
    }, 1500);
  };

  const getSystemInfo = () => {
    uni.getSystemInfo({
      success: (res) => {
        versionNumber.value = `v${res.appWgtVersion}`
      },
    });
  };

  onMounted(() => {
    getSystemInfo();
  });
</script>

<style lang="scss" scoped>
  .about-container {
    display: flex;
    flex-direction: column;
    background-color: var(--bg-color, #f7f7f7);

    // 顶部Logo和应用信息
    .header-section {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 60rpx 0;

      .app-logo {
        width: 140rpx;
        height: 140rpx;
        border-radius: 28rpx;
        margin-bottom: 24rpx;
        box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
      }

      .app-name {
        font-size: 34rpx;
        font-weight: 500;
        margin-bottom: 10rpx;
      }

      .app-version {
        font-size: 28rpx;
        color: var(--text-color-secondary, #888888);
      }
    }

    // 功能列表
    .function-list {
      margin: 0 auto 20rpx;
      width: calc(100% - 40rpx);
      border-radius: 16rpx;
      overflow: hidden;
      box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);

      &.update-list {
        margin-top: 0;
        margin-bottom: 30rpx;
      }

      .function-item {
        position: relative;
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 110rpx;
        padding: 0 30rpx;

        &:not(:last-child) {
          border-bottom: 1rpx solid var(--border-color, #eaeaea);
        }

        &:active {
          background-color: rgba(var(--bg-color-secondary-rgb), 0.4);
        }

        .item-name {
          font-size: 32rpx;
        }

        .item-arrow {
          width: 16rpx;
          height: 16rpx;
          border-top: 3rpx solid var(--arrow-color, #c8c8cc);
          border-right: 3rpx solid var(--arrow-color, #c8c8cc);
          transform: rotate(45deg);
        }

        &.check-update {
          color: var(--primary-color, #18b566);

          .item-name {
            font-weight: 500;
          }
        }
      }
    }

    // 底部版权信息
    .footer {
      text-align: center;
      width: 100%;
      font-size: 26rpx;
      position: absolute;
      bottom: 100rpx;
      left: 50%;
      margin-left: -50%;
      
      .link-row {
        display: flex;
        justify-content: center;
        margin-top: 10rpx;
      }
    }
  }
</style>
