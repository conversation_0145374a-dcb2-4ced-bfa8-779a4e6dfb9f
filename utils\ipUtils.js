/**
 * IP地址处理工具函数
 * 支持IPv4和IPv6地址的各种操作
 * <AUTHOR> 4.0 sonnet
 */

/**
 * 检测IP地址类型
 * @param {string} ip - IP地址字符串
 * @returns {string} 'ipv4' | 'ipv6' | 'domain' | 'unknown'
 */
export const getIPType = (ip) => {
	if (!ip || typeof ip !== 'string') return 'unknown';

	// 移除可能的方括号（IPv6在URL中的表示形式）
	const cleanIP = ip.replace(/^\[|\]$/g, '');

	// IPv6地址包含冒号
	if (cleanIP.includes(':')) {
		// 简单的IPv6格式验证
		const ipv6Pattern = /^([0-9a-fA-F]{0,4}:){1,7}[0-9a-fA-F]{0,4}$/;
		const ipv6CompressedPattern = /^::([0-9a-fA-F]{0,4}:){0,6}[0-9a-fA-F]{0,4}$/;
		const ipv6MixedPattern = /^([0-9a-fA-F]{0,4}:){1,6}:$/;

		if (
			ipv6Pattern.test(cleanIP) ||
			ipv6CompressedPattern.test(cleanIP) ||
			ipv6MixedPattern.test(cleanIP) ||
			cleanIP === '::1' ||
			cleanIP === '::'
		) {
			return 'ipv6';
		}
	}

	// IPv4地址包含点号
	if (cleanIP.includes('.')) {
		// 简单的IPv4格式验证
		const ipv4Pattern = /^(\d{1,3}\.){3}\d{1,3}$/;
		if (ipv4Pattern.test(cleanIP)) {
			return 'ipv4';
		}
	}

	// 域名检测（在IPv4和IPv6检测之后）
	if (isDomainName(cleanIP)) {
		return 'domain';
	}

	return 'unknown';
};

/**
 * 检测是否为域名
 * @param {string} str - 待检测的字符串
 * @returns {boolean} 是否为域名
 */
const isDomainName = (str) => {
	if (!str || typeof str !== 'string') return false;

	// 域名长度限制（1-253字符）
	if (str.length < 1 || str.length > 253) return false;

	// 域名不能以点号开头或结尾
	if (str.startsWith('.') || str.endsWith('.')) return false;

	// 域名不能包含连续的点号
	if (str.includes('..')) return false;

	// 域名基本格式验证：只能包含字母、数字、点号、连字符
	const domainPattern = /^[a-zA-Z0-9.-]+$/;
	if (!domainPattern.test(str)) return false;

	// 域名必须包含至少一个字母（区别于纯数字IP）
	if (!/[a-zA-Z]/.test(str)) return false;

	// 检查每个标签（点号分隔的部分）
	const labels = str.split('.');
	for (const label of labels) {
		// 标签不能为空
		if (label.length === 0) return false;

		// 标签长度限制（1-63字符）
		if (label.length > 63) return false;

		// 标签不能以连字符开头或结尾
		if (label.startsWith('-') || label.endsWith('-')) return false;

		// 标签必须包含有效字符
		if (!/^[a-zA-Z0-9-]+$/.test(label)) return false;
	}

	// 顶级域名必须包含字母
	const tld = labels[labels.length - 1];
	if (!/[a-zA-Z]/.test(tld)) return false;

	return true;
};

/**
 * 生成安全的canvas-id（兼容CSS选择器规范）
 * @param {string} prefix - ID前缀
 * @param {string} ip - IP地址
 * @returns {string} 安全的canvas-id
 */
export const generateSafeCanvasId = (prefix, ip) => {
	if (!ip || !prefix) return `${prefix || 'chart'}-unknown`;

	// 标准化IP地址
	const normalizedIP = normalizeIP(ip);
	if (!normalizedIP) return `${prefix}-invalid`;

	// 检测IP类型以进行特殊处理
	const ipType = getIPType(normalizedIP);

	let safeId;

	if (ipType === 'ipv6') {
		// IPv6特殊处理：确保生成的ID更加稳定和唯一
		// 2001:db8::1 -> ipv6-2001-db8-0-0-0-0-0-1
		// ::1 -> ipv6-0-0-0-0-0-0-0-1
		let processedIP = normalizedIP.toLowerCase();

		// 处理IPv6地址的双冒号压缩表示法
		if (processedIP.includes('::')) {
			// 计算需要补充的零段数量
			const segments = processedIP.split(':').filter((seg) => seg !== '');
			const missingSegments = 8 - segments.length;
			const zeroSegments = Array(missingSegments).fill('0').join('-');

			if (processedIP.startsWith('::')) {
				// 开头的双冒号：::1 -> 0-0-0-0-0-0-0-1
				processedIP = processedIP.replace(/^::/, zeroSegments + '-');
			} else if (processedIP.endsWith('::')) {
				// 结尾的双冒号：2001:db8:: -> 2001-db8-0-0-0-0-0-0
				processedIP = processedIP.replace(/::$/, '-' + zeroSegments);
			} else {
				// 中间的双冒号：2001:db8::1 -> 2001-db8-0-0-0-0-0-1
				processedIP = processedIP.replace(/::/, '-' + zeroSegments + '-');
			}
		}

		safeId = processedIP
			.replace(/:/g, '-') // 替换所有冒号
			.replace(/--+/g, '-') // 合并多个连字符
			.replace(/^-|-$/g, ''); // 移除首尾连字符

		// 为IPv6添加特殊前缀，避免与IPv4冲突
		safeId = `ipv6-${safeId}`;
	} else if (ipType === 'ipv4') {
		// IPv4处理：*********** -> ipv4-192-168-1-1
		safeId = normalizedIP.replace(/\./g, '-');

		// 为IPv4添加特殊前缀
		safeId = `ipv4-${safeId}`;
	} else if (ipType === 'domain') {
		// 域名处理：example.com -> domain-example-com
		safeId = normalizedIP
			.replace(/\./g, '-') // 替换点号为连字符
			.replace(/[^a-zA-Z0-9-]/g, '-') // 替换其他特殊字符
			.replace(/--+/g, '-') // 合并多个连字符
			.replace(/^-|-$/g, ''); // 移除首尾连字符

		// 为域名添加特殊前缀
		safeId = `domain-${safeId}`;
	} else {
		// 未知格式的处理
		safeId = normalizedIP
			.replace(/[^a-zA-Z0-9]/g, '-') // 替换所有非字母数字字符
			.replace(/--+/g, '-') // 合并多个连字符
			.replace(/^-|-$/g, ''); // 移除首尾连字符

		safeId = `unknown-${safeId}`;
	}

	// 确保ID不为空且符合CSS选择器规范
	if (!safeId || safeId === 'ipv4-' || safeId === 'ipv6-' || safeId === 'domain-' || safeId === 'unknown-') {
		safeId = 'invalid';
	}

	// 生成最终的canvas-id，确保唯一性
	const finalId = `${prefix}-${safeId}`;

	// 验证生成的ID是否符合CSS选择器规范
	if (!/^[a-zA-Z][a-zA-Z0-9_-]*$/.test(finalId)) {
		console.warn(`生成的canvas-id可能不符合CSS规范: ${finalId}`);
		// 降级处理：使用哈希值
		const hash = simpleHash(ip);
		return `${prefix}-hash-${hash}`;
	}

	return finalId;
};

/**
 * 简单哈希函数，用于生成稳定的ID后缀
 * @param {string} str - 输入字符串
 * @returns {string} 哈希值
 */
const simpleHash = (str) => {
	let hash = 0;
	if (str.length === 0) return hash.toString();

	for (let i = 0; i < str.length; i++) {
		const char = str.charCodeAt(i);
		hash = (hash << 5) - hash + char;
		hash = hash & hash; // 转换为32位整数
	}

	return Math.abs(hash).toString(36); // 转换为36进制字符串
};

/**
 * 标准化URL格式，用于比较
 * @param {string} url - 原始URL
 * @returns {string} 标准化后的URL
 */
export const normalizeURL = (url) => {
	if (!url || typeof url !== 'string') return '';

	try {
		// 提取IP地址和端口
		const ip = extractIPFromURL(url);
		if (!ip) return url.toLowerCase();

		// 提取端口号
		let port = '';
		const portMatch = url.match(/:(\d+)(?:\/|$)/);
		if (portMatch) {
			port = portMatch[1];
		}

		// 标准化IP地址
		const normalizedIP = normalizeIP(ip);

		// 重新构建标准化的URL
		const ipType = getIPType(normalizedIP);
		let normalizedURL;

		if (ipType === 'ipv6') {
			// IPv6地址需要方括号
			normalizedURL = `http://[${normalizedIP}]`;
		} else if (ipType === 'domain') {
			// 域名地址
			normalizedURL = `http://${normalizedIP}`;
		} else {
			// IPv4地址或其他格式
			normalizedURL = `http://${normalizedIP}`;
		}

		// 添加端口号
		if (port) {
			normalizedURL += `:${port}`;
		}

		return normalizedURL.toLowerCase();
	} catch (error) {
		console.warn('URL标准化失败:', error);
		return url.toLowerCase();
	}
};

/**
 * 从URL中提取IP地址或域名（支持IPv4、IPv6和域名）
 * @param {string} url - 完整的URL字符串
 * @returns {string} 提取的IP地址或域名
 */
export const extractIPFromURL = (url) => {
	if (!url || typeof url !== 'string') return '';

	// 检查URL构造函数是否可用（在某些环境下可能不可用）
	const hasURLConstructor = typeof URL !== 'undefined' && URL.constructor;

	if (hasURLConstructor) {
		try {
			// 尝试使用URL构造函数解析
			const urlObj = new URL(url);
			let hostname = urlObj.hostname;

			// 如果是IPv6地址，移除方括号
			if (hostname.startsWith('[') && hostname.endsWith(']')) {
				hostname = hostname.slice(1, -1);
			}

			return hostname;
		} catch (error) {
			// URL构造函数解析失败，继续使用正则表达式
			console.warn('URL构造函数解析失败，使用正则表达式降级处理:', error.message);
		}
	}

	// 降级处理：使用正则表达式（兼容所有环境）
	try {
		// 匹配 http://[IPv6地址]:port 或 https://[IPv6地址]:port 格式
		const ipv6Match = url.match(/^https?:\/\/\[([^\]]+)\](?::\d+)?/);
		if (ipv6Match) {
			return ipv6Match[1];
		}

		// 匹配 http://主机名:port 或 https://主机名:port 格式（包括IPv4地址和域名）
		const hostMatch = url.match(/^https?:\/\/([^:\/?#]+)(?::\d+)?/);
		if (hostMatch) {
			const extracted = hostMatch[1];
			const extractedType = getIPType(extracted);
			// 返回IPv4地址或域名
			if (extractedType === 'ipv4' || extractedType === 'domain') {
				return extracted;
			}
		}

		// 兼容旧的提取方式：从路径中提取
		const lastSlashIndex = url.lastIndexOf('/');
		const lastColonIndex = url.lastIndexOf(':');

		if (lastSlashIndex !== -1 && lastColonIndex !== -1 && lastColonIndex > lastSlashIndex) {
			const extracted = url.substring(lastSlashIndex + 1, lastColonIndex);
			// 验证提取的结果是否为有效IP
			if (getIPType(extracted) !== 'unknown') {
				return extracted;
			}
		}

		// 如果以上都失败，尝试更宽松的匹配
		const generalMatch = url.match(/\/\/([^\/\?#]+)/);
		if (generalMatch) {
			let hostname = generalMatch[1];

			// 移除端口号
			const portIndex = hostname.lastIndexOf(':');
			if (portIndex !== -1) {
				// 对于IPv6，需要特殊处理
				if (hostname.startsWith('[') && hostname.endsWith(']')) {
					// IPv6格式：[2001:db8::1]:8080
					return hostname.slice(1, -1);
				} else if (hostname.includes(':')) {
					const beforePort = hostname.substring(0, portIndex);
					const beforePortType = getIPType(beforePort);
					// IPv4地址或域名格式：***********:8080 或 example.com:8080
					if (beforePortType !== 'unknown') {
						hostname = beforePort;
					}
				}
			}

			// 移除IPv6的方括号
			if (hostname.startsWith('[') && hostname.endsWith(']')) {
				hostname = hostname.slice(1, -1);
			}

			// 最终验证：接受IP地址和域名
			const finalType = getIPType(hostname);
			if (finalType !== 'unknown') {
				return hostname;
			}
		}

		return '';
	} catch (error) {
		console.error('IP地址提取完全失败:', error);
		return '';
	}
};

/**
 * 检查是否为内网地址（支持IPv4、IPv6和域名）
 * @param {string} ip - IP地址或域名字符串
 * @returns {boolean} 是否为内网地址
 */
export const isLocalAddress = (ip) => {
	if (!ip || typeof ip !== 'string') return false;

	const cleanIP = ip.replace(/^\[|\]$/g, '');
	const ipType = getIPType(cleanIP);

	if (ipType === 'ipv4') {
		// IPv4内网地址检测
		if (cleanIP === '127.0.0.1' || cleanIP === 'localhost') return true;
		if (cleanIP.startsWith('192.168.')) return true;
		if (cleanIP.startsWith('10.')) return true;

		// ********** - **************
		const parts = cleanIP.split('.');
		if (parts.length === 4 && parts[0] === '172') {
			const secondOctet = parseInt(parts[1], 10);
			if (secondOctet >= 16 && secondOctet <= 31) return true;
		}

		// *********** - *************** (链路本地地址)
		if (cleanIP.startsWith('169.254.')) return true;
	} else if (ipType === 'ipv6') {
		// IPv6内网地址检测
		const lowerIP = cleanIP.toLowerCase();

		// 回环地址
		if (lowerIP === '::1' || lowerIP === '::') return true;

		// 链路本地地址 (fe80::/10)
		if (lowerIP.startsWith('fe80:')) return true;

		// 唯一本地地址 (fc00::/7)
		if (lowerIP.startsWith('fc00:') || lowerIP.startsWith('fd00:')) return true;

		// 站点本地地址 (fec0::/10) - 已废弃但仍需检测
		if (lowerIP.startsWith('fec0:')) return true;

		// IPv4映射的IPv6地址中的内网地址
		if (lowerIP.startsWith('::ffff:')) {
			const ipv4Part = lowerIP.substring(7);
			return isLocalAddress(ipv4Part);
		}
	} else if (ipType === 'domain') {
		// 域名本地地址检测
		const lowerDomain = cleanIP.toLowerCase();

		// 常见的本地域名
		if (lowerDomain === 'localhost') return true;
		if (lowerDomain === 'local') return true;
		if (lowerDomain.endsWith('.local')) return true;
		if (lowerDomain.endsWith('.localhost')) return true;

		// 内网域名模式
		if (lowerDomain.includes('internal')) return true;
		if (lowerDomain.includes('intranet')) return true;
		if (lowerDomain.startsWith('dev.') || lowerDomain.startsWith('test.') || lowerDomain.startsWith('staging.'))
			return true;
	}

	return false;
};

/**
 * 格式化IP显示（支持IPv4、IPv6和域名隐藏）
 * @param {string} ip - IP地址或域名字符串
 * @param {boolean} isHidden - 是否隐藏IP地址
 * @returns {string} 格式化后的IP显示字符串
 */
export const formatIPDisplay = (ip, isHidden = false) => {
	if (!isHidden || !ip) return ip || '';

	const ipType = getIPType(ip);

	if (ipType === 'ipv4') {
		return 'xxx.xxx.xxx.xxx';
	} else if (ipType === 'ipv6') {
		return 'xxxx:xxxx:xxxx:xxxx';
	} else if (ipType === 'domain') {
		return formatDomainDisplay(ip);
	}

	return 'xxx';
};

/**
 * 格式化域名隐藏显示
 * @param {string} domain - 域名字符串
 * @returns {string} 格式化后的域名隐藏显示
 */
const formatDomainDisplay = (domain) => {
	if (!domain || typeof domain !== 'string') return 'xxx';

	const labels = domain.split('.');
	const labelCount = labels.length;

	if (labelCount === 1) {
		// 单级域名（如 localhost）
		return 'xxx';
	} else if (labelCount === 2) {
		// 二级域名（如 example.com）
		return 'xxx.xxx';
	} else if (labelCount === 3) {
		// 三级域名（如 www.example.com）
		return 'xxx.xxx.xxx';
	} else {
		// 多级域名，保持相同的层级结构
		return Array(labelCount).fill('xxx').join('.');
	}
};

/**
 * 验证IP地址或域名格式是否有效
 * @param {string} ip - IP地址或域名字符串
 * @returns {boolean} 是否为有效的IP地址或域名
 */
export const isValidIP = (ip) => {
	return getIPType(ip) !== 'unknown';
};

/**
 * 标准化IP地址格式（移除不必要的字符）
 * @param {string} ip - IP地址字符串
 * @returns {string} 标准化后的IP地址
 */
export const normalizeIP = (ip) => {
	if (!ip || typeof ip !== 'string') return '';

	// 移除方括号（IPv6在URL中的表示形式）
	return ip.replace(/^\[|\]$/g, '');
};

/**
 * 比较两个IP地址是否相同
 * @param {string} ip1 - 第一个IP地址
 * @param {string} ip2 - 第二个IP地址
 * @returns {boolean} 是否相同
 */
export const compareIPs = (ip1, ip2) => {
	if (!ip1 || !ip2) return false;

	const normalizedIP1 = normalizeIP(ip1).toLowerCase();
	const normalizedIP2 = normalizeIP(ip2).toLowerCase();

	// 直接比较
	if (normalizedIP1 === normalizedIP2) return true;

	// 检查是否都是IPv6地址，如果是则进行标准化比较
	const ip1Type = getIPType(normalizedIP1);
	const ip2Type = getIPType(normalizedIP2);

	if (ip1Type === 'ipv6' && ip2Type === 'ipv6') {
		return compareIPv6Addresses(normalizedIP1, normalizedIP2);
	}

	return false;
};

/**
 * 比较两个IPv6地址是否相同（处理不同的表示格式）
 * @param {string} ipv6_1 - 第一个IPv6地址
 * @param {string} ipv6_2 - 第二个IPv6地址
 * @returns {boolean} 是否相同
 */
const compareIPv6Addresses = (ipv6_1, ipv6_2) => {
	try {
		// 将IPv6地址展开为完整格式进行比较
		const expanded1 = expandIPv6(ipv6_1);
		const expanded2 = expandIPv6(ipv6_2);

		return expanded1 === expanded2;
	} catch (error) {
		console.warn('IPv6地址比较失败:', error);
		return false;
	}
};

/**
 * 将IPv6地址展开为完整格式
 * @param {string} ipv6 - IPv6地址
 * @returns {string} 展开后的IPv6地址
 */
const expandIPv6 = (ipv6) => {
	if (!ipv6 || typeof ipv6 !== 'string') return '';

	let expanded = ipv6.toLowerCase();

	// 处理双冒号压缩
	if (expanded.includes('::')) {
		const parts = expanded.split('::');
		const leftParts = parts[0] ? parts[0].split(':') : [];
		const rightParts = parts[1] ? parts[1].split(':') : [];

		// 计算需要补充的零段数量
		const totalParts = leftParts.length + rightParts.length;
		const missingParts = 8 - totalParts;

		// 构建完整的8段地址
		const fullParts = [...leftParts, ...Array(missingParts).fill('0'), ...rightParts];

		expanded = fullParts.join(':');
	}

	// 确保每段都是4位数字
	const segments = expanded.split(':');
	const paddedSegments = segments.map((segment) => segment.padStart(4, '0'));

	return paddedSegments.join(':');
};
