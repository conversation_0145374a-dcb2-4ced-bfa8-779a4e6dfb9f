<template>
	<view>
		<!-- 备份数据库 -->
		<view class="form-group">
			<view class="form-label-row">
				<text>备份数据库</text>
			</view>
			<button class="region-select-button" @click="showDatabasePicker" :disabled="isEditMode">
				<text>{{ getDatabaseLabel() || '请选择数据库' }}</text>
				<uv-icon name="arrow-down" size="14" color="#666666"></uv-icon>
			</button>
		</view>

		<!-- 表选择 -->
		<view class="form-group" v-if="formData.sName !== 'ALL' && tableOptions.length > 0">
			<view class="form-label-row">
				<text>表</text>
			</view>
			<button class="region-select-button" @click="showTablePicker" :disabled="isEditMode">
				<text>{{ getTableLabel() || '请选择表' }}</text>
				<uv-icon name="arrow-down" size="14" color="#666666"></uv-icon>
			</button>
		</view>

		<!-- 备份到选择 -->
		<view class="form-group">
			<view class="form-label-row">
				<text>备份到</text>
			</view>
			<button class="region-select-button" @click="showBackupToPicker" :disabled="isEditMode">
				<text>{{ getBackupToLabel() || '请选择备份位置' }}</text>
				<uv-icon name="arrow-down" size="14" color="#666666"></uv-icon>
			</button>
		</view>

		<!-- 压缩密码 -->
		<view class="form-group">
			<view class="form-label-row">
				<text>压缩密码</text>
			</view>
			<view class="input-wrapper">
				<uv-input
					:value="formData.zip_password"
					@input="updateField('zip_password', $event)"
					placeholder="请输入压缩密码"
					type="password"
					border="surround"
					:customStyle="{ backgroundColor: 'var(--dialog-bg-color)', fontSize: '28rpx' }"
				/>
			</view>
		</view>

		<!-- 进程锁 -->
		<view class="form-group">
			<view class="form-row">
				<text>进程锁</text>
				<uv-switch :value="formData.flock" @change="updateField('flock', $event)" activeColor="#20a53a"></uv-switch>
			</view>
		</view>

		<!-- Picker组件 -->
		<uv-picker
			ref="databasePicker"
			:columns="[databaseOptions]"
			keyName="label"
			@confirm="onDatabaseConfirm"
		></uv-picker>

		<uv-picker
			ref="tablePicker"
			:columns="[tableOptions]"
			keyName="label"
			@confirm="onTableConfirm"
		></uv-picker>

		<uv-picker
			ref="backupToPicker"
			:columns="[backupToOptions]"
			keyName="label"
			@confirm="onBackupToConfirm"
		></uv-picker>
	</view>
</template>

<script setup>
	import { ref, onMounted, getCurrentInstance } from 'vue';
	import { getCrontabDataList, getDatabases } from '@/api/crontab';
	import { truncateText } from '../useController';

	const { proxy } = getCurrentInstance();

	const props = defineProps({
		formData: {
			type: Object,
			required: true
		},
		isEditMode: {
			type: Boolean,
			default: false
		}
	});

	const emit = defineEmits(['update:formData']);

	// 数据选项
	const databaseOptions = ref([{ label: '所有[所有数据]', value: 'ALL' }]);
	const tableOptions = ref([]);
	const backupToOptions = ref([{ label: '服务器磁盘', value: 'localhost' }]);

	// 更新字段
	const updateField = (field, event) => {
		const value = event.detail?.value || event.target?.value || event;
		emit('update:formData', { [field]: value });
	};

	// 显示选择器
	const showDatabasePicker = () => proxy.$refs.databasePicker?.open();
	const showTablePicker = () => proxy.$refs.tablePicker?.open();
	const showBackupToPicker = () => proxy.$refs.backupToPicker?.open();

	// 获取显示标签
	const getDatabaseLabel = () => {
		const option = databaseOptions.value.find(item => item.value === props.formData.sName);
		return option ? truncateText(option.label) : '';
	};

	const getTableLabel = () => {
		const option = tableOptions.value.find(item => item.value === props.formData.table_list);
		return option ? truncateText(option.label) : '';
	};

	const getBackupToLabel = () => {
		const option = backupToOptions.value.find(item => item.value === props.formData.backupTo);
		return option ? truncateText(option.label) : '';
	};

	// 确认选择
	const onDatabaseConfirm = (e) => {
		const selectedValue = e.value[0].value;
		
		if (selectedValue === 'ALL') {
			emit('update:formData', { 
				sName: selectedValue,
				table_list: 'ALL',
				name: `数据库增量备份[ 所有 ]`
			});
		} else {
			// 加载表列表
			const database = databaseOptions.value.find(item => item.value === selectedValue);
			if (database && database.table_list) {
				tableOptions.value = database.table_list.map(item => ({
					label: item.tb_name,
					value: item.value === '' ? 'ALL' : item.value
				}));
			}
			
			emit('update:formData', { 
				sName: selectedValue,
				table_list: 'ALL',
				name: `数据库增量备份[ ${selectedValue} ]`
			});
		}
	};

	const onTableConfirm = (e) => {
		const selectedValue = e.value[0].value;
		const selectedLabel = e.value[0].label;
		emit('update:formData', { 
			table_list: selectedValue,
			name: `数据库增量备份[ ${props.formData.sName} - ${selectedLabel} ]`
		});
	};

	const onBackupToConfirm = (e) => {
		const selectedValue = e.value[0].value;
		emit('update:formData', { backupTo: selectedValue });
	};

	// 加载数据库列表
	const loadDatabases = async () => {
		try {
			const res = await getDatabases({ db_type: 'mysql' });
			databaseOptions.value = [{ label: '所有[所有数据]', value: 'ALL' }].concat(
				res.data.map(item => ({
					label: item.name + (item.ps ? `[${item.ps}]` : ''),
					value: item.name,
					table_list: item.table_list
				}))
			);
		} catch (error) {
			console.error('加载数据库列表失败:', error);
		}
	};

	// 加载备份位置
	const loadBackupTo = async () => {
		try {
			const res = await getCrontabDataList({ type: 'sites' });
			// 只显示已配置的选项
			backupToOptions.value = [{ label: '服务器磁盘', value: 'localhost' }].concat(
				res.orderOpt
					.filter(item => item.status) // 只保留已配置的项
					.map(item => ({
						label: item.name,
						value: item.value
					}))
			);
		} catch (error) {
			console.error('加载备份位置失败:', error);
		}
	};

	onMounted(async () => {
		await loadDatabases();
		await loadBackupTo();
		
		// 初始化默认值
		if (!props.formData.sName) {
			emit('update:formData', { 
				sName: 'ALL',
				table_list: 'ALL',
				backupTo: 'localhost',
				zip_password: '',
				name: '数据库增量备份[ 所有 ]'
			});
		}
	});
</script>

<style lang="scss" scoped>
	@import './form-styles.scss';

	.form-row {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-top: 20rpx;

		text {
			font-size: 28rpx;
			font-weight: 500;
			color: var(--text-color-primary);
		}
	}
</style>
