import { defineStore } from 'pinia';
import { ref } from 'vue';
import { storeToRefs } from 'pinia';

export const useUserStore = defineStore('user', () => {
    const user = ref({
        id: null,
        name: '',
        email: '',
        isLoggedIn: false
    });

    function login(userData) {
        user.value = {
            ...userData,
            isLoggedIn: true
        };
    }

    function logout() {
        user.value = {
            id: null,
            name: '',
            email: '',
            isLoggedIn: false
        };
    }

    // 辅助函数，获取响应式状态
    function getReactiveState() {
        const store = useUserStore();
        return {
            ...storeToRefs(store),
            login,
            logout
        };
    }

    return { user, login, logout, getReactiveState };
}); 