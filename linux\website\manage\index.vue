<template>
  <page-container ref="pageContainer" :title="handleUrlTitle(currentItem.rname)" :showTabBar="true" :tabData="tabData" @tabClick="onTabClick">
    <view class="p-20">
      <domain-manage v-if="currentTab === 0" />
      <ssl-manage v-if="currentTab === 1" />
    </view>
    <!-- <uni-fab
      :pattern="{ buttonColor: '#20a50a' }"
      horizontal="right"
      vertical="bottom"
      :popMenu="false"
    >
    </uni-fab> -->
  </page-container>
</template>

<script setup>
  import { onLoad, onUnload } from '@dcloudio/uni-app';
  import btTabs from '@/components/BtTabs/index.vue';
  import PageContainer from '@/components/PageContainer/index.vue';
  import DomainManage from './domain.vue';
  import SslManage from './ssl.vue';
  import { ref } from 'vue';
  import { currentItem, handleUrlTitle, currentTab, pageContainer } from './useController';
  import { $t } from '@/locale/index.js';

  const tabData = [$t('website.manage.domainManagement'), $t('website.manage.sslManagement')];
  
  const onTabClick = (index) => {
    currentTab.value = index;
  };

  onLoad((option) => {
    currentItem.value = JSON.parse(option?.item);
  });

  onUnload(() => {
    currentTab.value = 0;
  });
</script>

<style lang="scss" scoped>

</style>
