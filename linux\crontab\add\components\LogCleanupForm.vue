<template>
	<view>
		<!-- 清理类型 -->
		<view class="form-group">
			<view class="form-label-row">
				<text>清理类型</text>
			</view>
			<button class="region-select-button" @click="showCleanupTypePicker" :disabled="isEditMode">
				<text>{{ getCleanupTypeLabel() || '请选择清理类型' }}</text>
				<uv-icon name="arrow-down" size="14" color="#666666"></uv-icon>
			</button>
		</view>

		<!-- 日志类型 (选择类型时显示) -->
		<view class="form-group" v-if="formData.cleanup_type === 'select'">
			<view class="form-label-row">
				<text>日志类型</text>
			</view>
			<view class="checkbox-group">
				<view 
					v-for="logType in logTypeOptions" 
					:key="logType.value"
					class="checkbox-item"
					@click="toggleLogType(logType.value)"
				>
					<uv-checkbox 
						:checked="isLogTypeSelected(logType.value)"
						:label="logType.label"
						activeColor="#20a53a"
					></uv-checkbox>
				</view>
			</view>
		</view>

		<!-- 自定义目录 (自定义路径时显示) -->
		<view class="form-group" v-if="formData.cleanup_type === 'custom'">
			<view class="form-label-row">
				<text>自定义目录</text>
			</view>
			<view class="textarea-wrapper">
				<uv-textarea
					:value="formData.sBody"
					@input="updateField('sBody', $event)"
					placeholder="请输入要清理的目录路径，每行一个"
					height="200"
					:textStyle="{ fontSize: '28rpx', color: 'var(--text-color-primary)' }"
					:customStyle="{ backgroundColor: 'var(--dialog-bg-color)' }"
				/>
			</view>
		</view>

		<!-- 进程锁 -->
		<view class="form-group">
			<view class="form-row">
				<text>进程锁</text>
				<uv-switch :value="formData.flock" @change="updateField('flock', $event)" activeColor="#20a53a"></uv-switch>
			</view>
		</view>

		<!-- Picker组件 -->
		<uv-picker
			ref="cleanupTypePicker"
			:columns="[cleanupTypeOptions]"
			keyName="label"
			@confirm="onCleanupTypeConfirm"
		></uv-picker>
	</view>
</template>

<script setup>
	import { ref, onMounted, getCurrentInstance } from 'vue';

	const { proxy } = getCurrentInstance();

	const props = defineProps({
		formData: {
			type: Object,
			required: true
		},
		isEditMode: {
			type: Boolean,
			default: false
		}
	});

	const emit = defineEmits(['update:formData']);

	// 清理类型选项
	const cleanupTypeOptions = ref([
		{ value: 'select', label: '选择类型' },
		{ value: 'custom', label: '自定义路径' },
	]);

	// 日志类型选项
	const logTypeOptions = ref([
		{ value: 'nginx', label: 'Nginx日志' },
		{ value: 'apache', label: 'Apache日志' },
		{ value: 'mysql', label: 'MySQL日志' },
		{ value: 'php', label: 'PHP日志' },
		{ value: 'system', label: '系统日志' },
	]);

	// Picker引用
	const cleanupTypePicker = ref(null);

	// 更新字段
	const updateField = (field, event) => {
		const value = event.detail?.value || event.target?.value || event;
		emit('update:formData', { [field]: value });
	};

	// 显示选择器
	const showCleanupTypePicker = () => {
		proxy.$refs.cleanupTypePicker?.open();
	};

	// 获取显示标签
	const getCleanupTypeLabel = () => {
		const option = cleanupTypeOptions.value.find(item => item.value === props.formData.cleanup_type);
		return option ? option.label : '';
	};

	// 切换日志类型选择
	const toggleLogType = (value) => {
		const selectedTypes = props.formData.selected_log_types || [];
		const index = selectedTypes.indexOf(value);
		
		let newSelectedTypes;
		if (index > -1) {
			newSelectedTypes = selectedTypes.filter(type => type !== value);
		} else {
			newSelectedTypes = [...selectedTypes, value];
		}
		
		emit('update:formData', { 
			selected_log_types: newSelectedTypes,
			sBody: newSelectedTypes.join(',')
		});
	};

	// 检查日志类型是否被选中
	const isLogTypeSelected = (value) => {
		const selectedTypes = props.formData.selected_log_types || [];
		return selectedTypes.includes(value);
	};

	// 确认选择
	const onCleanupTypeConfirm = (e) => {
		const selectedValue = e.value[0].value;
		emit('update:formData', { 
			cleanup_type: selectedValue,
			sBody: '',
			selected_log_types: [],
			name: `定时清理日志[ ${selectedValue === 'select' ? '选择类型' : '自定义路径'} ]`
		});
	};

	onMounted(() => {
		// 初始化默认值
		if (!props.formData.cleanup_type) {
			emit('update:formData', { 
				cleanup_type: 'select',
				selected_log_types: [],
				sBody: '',
				name: '定时清理日志[ 选择类型 ]'
			});
		}
	});
</script>

<style lang="scss" scoped>
	@import './form-styles.scss';
	
	.checkbox-group {
		display: flex;
		flex-direction: column;
		gap: 20rpx;
	}

	.checkbox-item {
		display: flex;
		align-items: center;
		padding: 20rpx 0;
		border-bottom: 1px solid #f0f0f0;

		&:last-child {
			border-bottom: none;
		}
	}

	.form-row {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-top: 20rpx;

		text {
			font-size: 28rpx;
			font-weight: 500;
			color: var(--text-color-primary);
		}
	}
</style>
