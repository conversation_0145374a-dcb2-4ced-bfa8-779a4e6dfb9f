import { ref } from 'vue';
import { triggerVibrate } from '@/utils/common'
import {
    getFirewallStatus,
    setFirewallStatus as setFirewallStatusApi,
    getPortList,
    delPortRules,
    editPortRules
} from '@/api/firewall'
import { $t } from '@/locale/index.js';

/**
 * 防火墙加载状态
 */
export const loading = ref(false);

/**
 * 防火墙状态
 */
export const firewallEnabled = ref(true);

/**
 * 当前选中的规则
 */
export const currentRule = ref(null);
export const currentRuleIndex = ref(-1);

/**
 * 端口规则列表
 */
export const portRules = ref([]);
export const rulePaging = ref(null);
export const pageContainer = ref(null);
/**
 * @description 端口提示
 */
export const portsPs = {
    '3306': $t('firewall.portList.mysql'),
    '888': $t('firewall.portList.phpmyadmin'),
    '22': $t('firewall.portList.ssh'),
    '20': $t('firewall.portList.ftpData'),
    '21': $t('firewall.portList.ftp'),
    '39000-40000': $t('firewall.portList.ftpPassiveRange1'),
    '30000-40000': $t('firewall.portList.ftpPassiveRange2'),
    '11211': $t('firewall.portList.memcached'),
    '873': $t('firewall.portList.rsync'),
    '8888': $t('firewall.portList.panel'),
}

/**
 * @description 处理行数据
 */
export const handleRowParams = (row, operation) => {
    const params = {
        operation,
        protocol: row.Protocol,
        address: row.Address,
        port: row.Port,
        strategy: row.Strategy,
        chain: row.Chain,
        brief: row.brief,
    }
    return params
}

/**
 * 防火墙弹窗状态
 */
export const showFirewallDialog = ref(false);
export const firewallDisabledPopup = ref(null);

/**
 * 上下文菜单状态
 */
export const showContextMenu = ref(false);
export const showDeleteDialog = ref(false);
export const menuPosition = ref({ top: '0px', left: '0px', class: '' });
export const clonePosition = ref({ top: '0px', left: '0px' });

// 长按相关常量
export const LONG_PRESS_THRESHOLD = 500;
export const MOVE_THRESHOLD = 10;

// 触摸状态变量
let touchStartTime = 0;
let touchStartPos = { x: 0, y: 0 };
let isTouchMoved = false;
let longPressTimer = null;

/**
 * 获取状态样式
 */
export const getStatusClass = (status) => {
    if (status) {
        return 'status-normal';
    } else {
        return 'status-unused';
    }
};

/**
 * 加载防火墙状态
 */
export const loadFirewallStatus = async () => {
    loading.value = true;
    try {
        const result = await getFirewallStatus();
        firewallEnabled.value = result;
    } catch (error) {
        pageContainer.value.notify.error('获取防火墙状态失败')
    } finally {
        loading.value = false;

    }
};

/**
 * 设置防火墙状态
 * @param {Boolean} status 要设置的状态
 * @returns {Promise} 设置结果Promise
 */
export const setFirewallStatus = async (status) => {
    loading.value = true;
    try {
        const result = await setFirewallStatusApi({
            status: status ? 1 : 0
        })

        if (result.status) {
            firewallEnabled.value = status;
            if (status) {
                rulePaging.value.reload();
            } else {
                rulePaging.value.complete([]);
            }
            pageContainer.value.notify.success(result.msg)
        }
        return result;
    } catch (error) {
        pageContainer.value.notify.error($t('firewall.setStatusFailed'))
    } finally {
        loading.value = false;
    }
};

/**
 * 确认开启防火墙
 */
export const confirmEnableFirewall = () => {
    setFirewallStatus(true);
    showFirewallDialog.value = false;
};

/**
 * 取消开启防火墙
 */
export const cancelEnableFirewall = () => {
    firewallEnabled.value = false;
    showFirewallDialog.value = false;
};

/**
 * 取消关闭防火墙
 */
export const cancelCloseFirewall = () => {
    firewallEnabled.value = true;
    firewallDisabledPopup.value = false;
};

/**
 * 确认关闭防火墙
 */
export const confirmCloseFirewall = () => {
    setFirewallStatus(false);
    firewallDisabledPopup.value = false
}

/**
 * 处理防火墙开关
 */
export const handleFirewallSwitch = (e) => {
    const switchValue = e;
    if (switchValue) {
        // 打开防火墙时显示确认弹窗
        showFirewallDialog.value = true;
    } else {
        // 直接关闭防火墙
        firewallDisabledPopup.value = true
    }
};

/**
 * 获取端口规则列表
 * @returns {Promise} 端口规则列表Promise
 */
export const getPortRules = async (page, pageSize) => {
    loading.value = true;
    try {
        const res = await getPortList({
            p: page,
            row: pageSize,
            chain: 'ALL'
        })
        return res.data
    } catch (e) {
        console.log(e);
    } finally {
        loading.value = false;
    }
};

/**
 * 刷新成功提示
 */
export const reload = (reloadType) => {
    if (reloadType === 'complete') {
        pageContainer.value.notify.success($t('common.refreshSuccess'));
    }
};

/**
 * 添加端口规则
 * @param {Object} rule 规则对象
 * @returns {Promise} 添加结果Promise
 */
export const addPortRule = (rule) => {
    loading.value = true;
    // 这里替换为实际的API调用
    return new Promise((resolve) => {
        setTimeout(() => {
            loading.value = false;
            resolve({ success: true });
        }, 500);
    });
};

/**
 * 处理切换规则
 */
export const handleToggleRule = async () => {
    if (!currentRule.value) return;
    loading.value = true;
    const isTypes = currentRule.value.Strategy === 'accept' ? false : true
    try {
        const params = {
            old_data: JSON.stringify({ ...currentRule.value }),
            new_data: JSON.stringify({ ...handleRowParams({ ...currentRule.value, Strategy: isTypes ? 'accept' : 'drop' }, 'add') }),
        }

        const result = await editPortRules(params);
        if (result.status) {
            // 更新成功后刷新列表
            rulePaging.value.reload();
            pageContainer.value.notify.success(result.msg)
        } else {
            pageContainer.value.notify.error(result.msg)
        }
    } catch (error) {
        console.log(error);
    } finally {
        loading.value = false;
    }
    hideContextMenu();
};

/**
 * 确认删除规则
 */
export const confirmDeleteRule = async (close) => {
    if (!currentRule.value) return;
    loading.value = true;
    try {
        const params = {
            ...handleRowParams(currentRule.value, 'remove'),
            domain: currentRule.value.domain,
        }
        const result = await delPortRules(params);
        close && close()
        if (result.status) {            // 删除成功后刷新列表
            rulePaging.value.reload();
            pageContainer.value.notify.success(result.msg)
        } else {
            pageContainer.value.notify.error(result.msg)
        }
    } catch (error) {
        console.log(error);
    } finally {
        loading.value = false;
    }
    showDeleteDialog.value = false;
    setTimeout(() => {
        hideContextMenu();
    }, 300);
};

/**
 * 长按触摸事件处理 - 开始触摸
 */
export const handleTouchStart = (event) => {
    if (!firewallEnabled.value) return;

    // 清除可能存在的定时器
    if (longPressTimer) {
        clearTimeout(longPressTimer);
    }

    // 记录触摸开始时间和位置
    touchStartTime = Date.now();
    touchStartPos = {
        x: event.touches[0].clientX,
        y: event.touches[0].clientY
    };
    isTouchMoved = false;

    // 设置长按定时器
    longPressTimer = setTimeout(() => {
        if (!isTouchMoved) {
            const dataset = event.currentTarget.dataset;
            const rule = JSON.parse(dataset.rule);
            const index = dataset.index;
            showFloatingMenu(rule, event, index);
        }
    }, LONG_PRESS_THRESHOLD);
};

/**
 * 长按触摸事件处理 - 移动触摸
 */
export const handleTouchMove = (event) => {
    if (!firewallEnabled.value || !touchStartPos) return;

    // 计算移动距离
    const moveX = Math.abs(event.touches[0].clientX - touchStartPos.x);
    const moveY = Math.abs(event.touches[0].clientY - touchStartPos.y);

    // 如果移动超过阈值，标记为已移动并取消长按定时器
    if (moveX > MOVE_THRESHOLD || moveY > MOVE_THRESHOLD) {
        isTouchMoved = true;

        if (longPressTimer) {
            clearTimeout(longPressTimer);
            longPressTimer = null;
        }

        if (showContextMenu.value) {
            hideContextMenu();
        }
    }
};

/**
 * 长按触摸事件处理 - 结束触摸
 */
export const handleTouchEnd = (event) => {
    if (!firewallEnabled.value) return;

    // 清除长按定时器
    if (longPressTimer) {
        clearTimeout(longPressTimer);
        longPressTimer = null;
    }

    // 如果未移动且是短触摸（非长按），则打开详情
    if (!isTouchMoved && (Date.now() - touchStartTime < LONG_PRESS_THRESHOLD)) {
        const rule = JSON.parse(event.currentTarget.dataset.rule);
        // 这里可以添加点击规则时的处理逻辑
        // handleRuleDetail(rule);
    }
};

/**
 * 长按触摸事件处理 - 取消触摸
 */
export const handleTouchCancel = () => {
    if (!firewallEnabled.value) return;

    // 清除长按定时器
    if (longPressTimer) {
        clearTimeout(longPressTimer);
        longPressTimer = null;
    }

    if (showContextMenu.value) {
        hideContextMenu();
    }
};

/**
 * 显示悬浮菜单
 */
export const showFloatingMenu = (rule, event, index) => {
    // 触感反馈
    triggerVibrate();

    currentRule.value = rule;
    currentRuleIndex.value = index;

    // 获取被长按元素的位置
    uni
        .createSelectorQuery()
        .selectAll('.rule-item-container')
        .boundingClientRect((rects) => {
            if (!rects || !rects[index]) return;

            const rect = rects[index];
            const systemInfo = uni.getSystemInfoSync();
            const screenHeight = systemInfo.windowHeight;
            const screenWidth = systemInfo.windowWidth;

            // 设置克隆项位置
            clonePosition.value = {
                position: 'fixed',
                top: `${rect.top}px`,
                left: `${rect.left}px`,
                width: `${rect.width}px`,
                height: `${rect.height}px`,
                transform: 'scale(1.02)',
                zIndex: '901',
                backgroundColor: 'rgba(255, 255, 255, 0.95)',
                borderRadius: '14rpx',
                boxShadow: '0 4rpx 20rpx rgba(0, 0, 0, 0.1)'
            };

            // 菜单尺寸（rpx转px）
            const menuWidth = uni.upx2px(240);
            const menuHeight = uni.upx2px(160);
            const gap = uni.upx2px(20); // 菜单与元素之间的间距

            let menuTop, menuLeft, menuClass;

            // 计算最佳显示位置
            const spaceRight = screenWidth - rect.right;
            const spaceLeft = rect.left;
            const spaceTop = rect.top;
            const spaceBottom = screenHeight - rect.bottom;

            // 水平位置计算
            if (spaceRight >= menuWidth + gap) {
                // 右侧有足够空间
                menuLeft = rect.right + gap;
                menuClass = 'menu-right';
            } else if (spaceLeft >= menuWidth + gap) {
                // 左侧有足够空间
                menuLeft = rect.left - menuWidth - gap;
                menuClass = 'menu-left';
            } else {
                // 水平居中
                menuLeft = rect.left + rect.width / 2;
                menuClass = 'menu-center';
            }

            // 垂直位置计算
            if (spaceBottom >= menuHeight + gap) {
                // 下方有足够空间
                menuTop = rect.bottom + (gap - 2);
                menuClass += ' menu-bottom';
            } else if (spaceTop >= menuHeight + gap) {
                // 上方有足够空间
                menuTop = rect.top - menuHeight - gap;
                menuClass += ' menu-top';
            } else {
                // 垂直居中
                menuTop = rect.top + rect.height / 2 - menuHeight / 2;
                menuClass += ' menu-middle';
            }

            // 确保菜单不超出屏幕边界
            menuTop = Math.max(gap, Math.min(screenHeight - menuHeight - gap, menuTop));
            if (menuClass.includes('menu-center')) {
                // 水平居中时，不需要调整左边距，因为会使用transform来居中
                menuLeft = Math.max(menuWidth / 2 + gap, Math.min(screenWidth - menuWidth / 2 - gap, menuLeft));
            } else {
                menuLeft = Math.max(gap, Math.min(screenWidth - menuWidth - gap, menuLeft));
            }

            // 设置菜单位置
            menuPosition.value = {
                top: `${menuTop}px`,
                left: `${menuLeft}px`,
                class: menuClass,
                width: `${menuWidth}px`,
            };

            // 显示菜单
            showContextMenu.value = true;
        })
        .exec();
};

/**
 * 隐藏上下文菜单
 */
export const hideContextMenu = () => {
    showContextMenu.value = false;
    currentRule.value = null;
    currentRuleIndex.value = -1;
};