<template>
  <page-container ref="sslFolderPageContainer" :title="$t('website.manage.certificateFolder')">
    <view class="ssl-container">
      <!-- SSL证书列表区域 -->
      <view class="ssl-list">
        <z-paging
          ref="sslPaging"
          class="mt-170"
          :default-page-size="100"
          use-virtual-list
          :force-close-inner-list="true"
          :auto-hide-loading-after-first-loaded="false"
          :auto-show-system-loading="true"
          @virtualListChange="virtualListChange"
          @query="queryList"
          @refresherStatusChange="reload"
          :refresher-complete-delay="200"
        >
          <view
            v-for="item in sslList"
            :id="`zp-id-${item.zp_index}`"
            :key="item.zp_index"
            class="px-20 mt-20 "
          >
            <view class="bg-primar rd-14 ssl-card p-20">
              <!-- 主要信息行 -->
              <view class="ssl-card-header flex">
                <view class="domain flex-1">
                  <text class="text-28 font-bold text-bt-primary">{{ item.dns?.join('\n') || item.subject }}</text>
                </view>
                <view
                  :class="[
                    'status flex-shrink-0 overflow-hidden max-w-300',
                    (item.info?.issuer ?? item.issuer) === 'R11' ? 'status-r11' : 'status-r10',
                  ]"
                >
                  <text class="text-24 block ws-nowrap overflow-hidden text-ellipsis">{{ item.info?.issuer ?? item.issuer }}</text>
                </view>
              </view>

              <!-- 详细信息区 -->
              <view class="ssl-card-info mt-16">
                <view class="info-row flex justify-between">
                  <view class="flex">
                    <view class="info-label text-26 text-color-secondary mr-10">{{ $t('website.manage.sslFolder.expiryDate') }}</view>
                    <view class="text-color-secondary text-26">{{ item.not_after ?? item.notAfter }}</view>
                  </view>
                  <view class="flex">
                    <view class="info-label text-26 text-color-secondary mr-10">{{ $t('website.manage.sslFolder.location') }}</view>
                    <view class="text-color-secondary text-26">{{ item?.cloud_id > 0 ? $t('website.manage.sslFolder.cloud') : $t('website.manage.sslFolder.local') }}</view>
                  </view>
                </view>
              </view>

              <!-- 操作按钮区 -->
              <view class="ssl-card-actions mt-16 flex">
                <view class="action-btn mr-16 flex items-center justify-center" @click="handleDeployCert(item)">
                  <text class="text-26 text-bt-primary">{{ $t('website.manage.sslFolder.deploy') }}</text>
                </view>
                <view class="action-btn flex items-center justify-center" @click="handleDeleteCert(item)">
                  <text class="text-26 text-color-danger">{{ $t('website.manage.sslFolder.delete') }}</text>
                </view>
              </view>
            </view>
          </view>
        </z-paging>
      </view>
    </view>
    <CustomDialog
      contentHeight="200rpx"
      v-model="isShowDeleteCert"
      :title="$t('website.warning')"
      :confirmText="$t('website.delete')"
      :confirmStyle="{
        backgroundColor: '#FF3B30',
      }"
      @confirm="confirmDeleteCert"
    >
      <view class="text-secondary flex justify-center items-center h-full">
        {{ $t('website.manage.sslFolder.deleteConfirm', { subject: currentCert?.subject }) }}
      </view>
    </CustomDialog>
  </page-container>
</template>

<script setup>
  import { ref } from 'vue';
  import { onUnload } from '@dcloudio/uni-app';
  import CustomDialog from '@/components/CustomDialog/index.vue';
  import PageContainer from '@/components/PageContainer/index.vue';
  import { $t } from '@/locale/index.js';
  import {
    sslPaging,
    handleCertList,
    handleDeployCert,
    isShowDeleteCert,
    currentCert,
    handleDeleteCert,
    confirmDeleteCert,
    sslFolderPageContainer,
  } from './useController';

  const sslList = ref([]);

  const virtualListChange = (vList) => {
    sslList.value = vList;
  };

  const queryList = async (page, pageSize) => {
    try {
      const res = await handleCertList(page, pageSize);
      sslPaging.value.complete(res);
      sslPaging.value.updateVirtualListRender();
    } catch (error) {
      sslPaging.value.complete([]);
      console.error(error);
    }
  };

  const reload = (reloadType) => {
    if (reloadType === 'complete') {
      sslFolderPageContainer.value.notify.success($t('website.refreshSuccess'));
    }
  };

  onUnload(() => {
    isShowDeleteCert.value = false;
  });
</script>

<style lang="scss" scoped>
  .ssl-container {
    position: relative;
  }

  .filter-section {
    border: 2rpx solid var(--color-border, #eee);
  }

  .filter-item {
    display: flex;
    align-items: center;
  }

  .ssl-card {
    border: 1rpx solid rgba(255, 255, 255, 0.3);
    box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.15);
  }

  .ssl-card-header {
    align-items: flex-start;
  }

  .domain {
    padding-right: 16rpx;
  }

  .status {
    padding: 4rpx 16rpx;
    border-radius: 20rpx;
    font-size: 22rpx;
    margin-top: 4rpx;
  }

  .status-r11 {
    background-color: rgba(15, 156, 88, 0.1);
    color: #0f9c58;
  }

  .status-r10 {
    background-color: rgba(66, 133, 244, 0.1);
    color: #4285f4;
  }

  .ssl-card-info {
    background-color: var(--bg-color-secondary, #f8f8f8);
    padding: 16rpx;
    border-radius: 8rpx;
  }

  .text-color-secondary {
    color: var(--text-color-secondary, #666);
  }

  .text-color-danger {
    color: var(--color-error, #e64340);
  }

  .ssl-card-actions {
    border-top: 1rpx solid var(--color-border, #eee);
    padding-top: 16rpx;
  }

  .action-btn {
    flex: 1;
    height: 70rpx;
    border-radius: 8rpx;
    background-color: var(--bg-color-secondary, #f8f8f8);
  }

  .float-btn {
    position: fixed;
    right: 40rpx;
    bottom: 120rpx;
    width: 100rpx;
    height: 100rpx;
    border-radius: 50%;
    background-color: var(--color-primary, #20a50a);
    z-index: 99;
    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.2);
  }
</style>
