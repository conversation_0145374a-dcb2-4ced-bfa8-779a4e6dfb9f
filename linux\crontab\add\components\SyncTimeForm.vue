<template>
	<view>
		<!-- 时区选择 -->
		<view class="form-group">
			<view class="form-label-row">
				<text>时区</text>
			</view>
			<button class="region-select-button" @click="showTimezonePicker" :disabled="isEditMode">
				<text>{{ getTimezoneLabel() || '请选择时区' }}</text>
				<uv-icon name="arrow-down" size="14" color="#666666"></uv-icon>
			</button>
		</view>

		<!-- 进程锁 -->
		<view class="form-group">
			<view class="form-row">
				<text>进程锁</text>
				<uv-switch :value="formData.flock" @change="updateField('flock', $event)" activeColor="#20a53a"></uv-switch>
			</view>
		</view>

		<!-- Picker组件 -->
		<uv-picker
			ref="timezonePicker"
			:columns="[timezoneOptions]"
			keyName="label"
			@confirm="onTimezoneConfirm"
		></uv-picker>
	</view>
</template>

<script setup>
	import { ref, onMounted, getCurrentInstance } from 'vue';
	import { truncateText } from '../useController';

	const { proxy } = getCurrentInstance();

	const props = defineProps({
		formData: {
			type: Object,
			required: true
		},
		isEditMode: {
			type: Boolean,
			default: false
		}
	});

	const emit = defineEmits(['update:formData']);

	// 时区选项
	const timezoneOptions = ref([
		{ value: 'Asia/Shanghai', label: 'Asia/Shanghai' },
		{ value: 'Asia/Hong_Kong', label: 'Asia/Hong_Kong' },
		{ value: 'Asia/Tokyo', label: 'Asia/Tokyo' },
		{ value: 'America/New_York', label: 'America/New_York' },
		{ value: 'Europe/London', label: 'Europe/London' },
		{ value: 'UTC', label: 'UTC' },
	]);

	// Picker引用
	const timezonePicker = ref(null);

	// 更新字段
	const updateField = (field, event) => {
		const value = event.detail?.value || event.target?.value || event;
		emit('update:formData', { [field]: value });
	};

	// 显示选择器
	const showTimezonePicker = () => {
		proxy.$refs.timezonePicker?.open();
	};

	// 获取显示标签
	const getTimezoneLabel = () => {
		const option = timezoneOptions.value.find(item => item.value === props.formData.sBody);
		return option ? truncateText(option.label) : '';
	};

	// 确认选择
	const onTimezoneConfirm = (e) => {
		const selectedValue = e.value[0].value;
		emit('update:formData', { 
			sBody: selectedValue,
			name: `同步时间[ ${selectedValue} ]`
		});
	};

	onMounted(() => {
		// 初始化默认值
		if (!props.formData.sBody) {
			emit('update:formData', { 
				sBody: 'Asia/Shanghai',
				name: '同步时间[ Asia/Shanghai ]'
			});
		}
	});
</script>

<style lang="scss" scoped>
	@import './form-styles.scss';

	.form-row {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-top: 20rpx;

		text {
			font-size: 28rpx;
			font-weight: 500;
			color: var(--text-color-primary);
		}
	}
</style>
