<template>
	<view class="content">
		<view class="report-switch flex">
			<view class="text-secondary">网站：</view>
			<uni-data-select
				ref="dataSelect"
				:clear="false"
				v-model="siteName"
				:localdata="siteList"
				@change="changeSitePath"
				emptyTips="暂无网站"
			></uni-data-select>
		</view>

		<view class="flow-charts-pie flex">
			<view
				class="chartsPie"
				id="spiderChartPie"
				:prop="chartsPieData"
				:change:prop="monitorSpider.changePieChartsData"
			></view>
		</view>

		<view class="flow-charts flex">
			<view
				class="charts"
				id="spiderChartLine"
				:prop="chartsData"
				:change:prop="monitorSpider.changeChartsData"
			></view>
		</view>

		<view class="websize-box">
			<view class="title-info flex">
				<view style="width: 25%">日期</view>
				<view style="width: 15%" v-for="(item, index) in fixedKeys" :key="index">{{ item }}</view>
				<view style="width: 15%"></view>
			</view>

			<view class="content-info">
				<view class="query-box" v-for="(item, index) in spiderList" :key="index" @click="openSet(index)">
					<view
						class="info-query flex"
						@click="eventClick(index)"
						:style="{ 'background-color': flagColor && index == activeIndex ? 'var(--bg-color-secondary)' : 'var(--bg-color)' }"
					>
						<view style="width: 25%">
							<text>{{ item.date }}</text>
						</view>

						<view
							class="hidden-text"
							style="width: 15%"
							v-for="(value, key) in getLimitedSpiders(item.spdier)"
							:key="key"
							>{{ value.total }}</view
						>

						<view
							style="width: 15%"
							class="set-img"
							:class="flagInfo && activeIndex == index ? 'icon-iconxiala2' : 'icon-iconxiala'"
						>
							<uni-icons type="down" size="20"></uni-icons>
						</view>
					</view>

					<view :class="flagInfo && activeIndex == index ? 'show-info' : 'hidden-info'">
						<view class="spider-view">
							<view v-for="(value1, key1) in item.spdier" :key="key1" class="spider-text">
								<text style="font-size: 28rpx; flex: 1; text-align: right; color: var(--text-color-secondary)">{{ key1 }}：</text>
								<text style="font-size: 28rpx; color: #20a53a; flex: 1">{{ value1.total }}</text>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script setup>
	import { ref, onMounted, getCurrentInstance } from 'vue';
	import { getSiteSpiderTotalByNDay, getSiteNames, setDefaultSite } from '@/api/monitorReport';

	const siteName = ref('');
	const siteList = ref([]);
	const chartsData = ref({});
	const chartsPieData = ref({});
	const spiderList = ref([]);
	const activeIndex = ref(0);
	const flagInfo = ref(false);
	const flagColor = ref(false);
	const fixedKeys = ref(['360', '谷歌', '百度', '必应']);

	const popupRef = ref(null);

	const getSiteList = () => {
		getSiteNames({
			is_verify: false,
		}).then((data) => {
			siteList.value = data.map((item) => ({
				...item,
				text: item.name,
				value: item.name,
				disable: false,
			}));
			if (siteList.value.length) {
				const allFalse = siteList.value.every((item) => !item.default);
				if (allFalse) {
					siteName.value = siteList.value[0].name;
				} else {
					siteList.value.forEach((item) => {
						if (item.default) {
							siteName.value = item.name;
						}
					});
				}
				getSpiderData();
			}
		});
	};

	const eventClick = (index) => {
		activeIndex.value = index;
	};

	const openSet = (index) => {
		flagInfo.value = !flagInfo.value;
	};

	const getLimitedSpiders = (spdier) => {
		return fixedKeys.value.reduce((obj, key) => {
			if (spdier && spdier[key]) {
				// Added null check for spdier
				obj[key] = spdier[key];
			}
			return obj;
		}, {});
	};

	const getSpiderData = () => {
		getSiteSpiderTotalByNDay({
			SiteName: siteName.value,
			is_verify: false,
		}).then((data) => {
			chartsData.value = data.today;
			chartsPieData.value = data.today_total;
			spiderList.value = data.list;
		});
	};

	const changeSitePath = (e) => {
		// siteName.value is already updated by v-model
		setDefaultSite({
			SiteName: siteName.value,
			is_verify: false,
		}).then((data) => {
			getSpiderData();
		});
	};

	onMounted(() => {
		getSiteList();
	});

	// Expose to renderjs module if needed, though :prop binding should handle data transfer
	// defineExpose({ chartsData, chartsPieData }); // Not strictly necessary for :prop
</script>

<script module="monitorSpider" lang="renderjs">
	var echarts = require("../../../static/echarts/echarts.min.js")

	var chartsView = null
	var chartsView2 = null

	// res is not used in the original renderjs script, so it's removed.

	export default {
		data() {
			return {
				// chartsData and chartsPieData are received via props, no need to declare here
				legendData: [],
				seriesData: [],
				colorList: ['#37a2da', '#32c5e9', '#9fe6b8', '#ffdb5c', '#ff9f7f', '#fb7293', '#e7bcf3', '#8378ea',
					'#e8ae75', '#efea9a', '#65c9be', '#0f6ef2', '#b48fff', '#ff8f92', "#F86464", "#19DC7C", "#FA9022",
					"#2A4AD1", "#E76FE3", "#168FB2"
				],
				chartsOption: {},
				pieData: [],
				chartsPieOption: {}
			}
		},
		methods: {
			changePieChartsData(newVal, oldVal) { // Renamed value to newVal for clarity, oldVal is also available
				if (newVal) { // Ensure newVal is not undefined
					this.chartsPieInit(newVal);
				}
			},
			chartsPieInit(chartsPieData) { // Pass data as argument
				const that = this; // 'that' can be replaced with 'this' in most cases
				this.pieData = [];
				if (chartsView2 == null) {
					const pieElement = document.getElementById("spiderChartPie");
					if (pieElement) { // Check if element exists
						chartsView2 = echarts.init(pieElement);
					} else {
						console.error("Element with id 'spiderChartPie' not found for ECharts initialization.");
						return;
					}
				}
				for (let i in chartsPieData) {
					if (chartsPieData.hasOwnProperty(i)) { // Good practice to check hasOwnProperty
						this.pieData.push({
							value: chartsPieData[i].percentage,
							name: i,
							total: chartsPieData[i].total
						});
					}
				}
				this.chartsPieOption = {
					tooltip: {
						trigger: 'item',
						formatter: function(params) {
							return params.marker + params.name + '：' + params.data.total + '/次 (' + params
								.percent + '%)';
						},
						position: function(pos, params, dom, rect, size) {
							// 计算tooltip的宽高
							var tooltipWidth = size.contentSize[0];
							var tooltipHeight = size.contentSize[1];
							// 获取视窗宽高
							var viewWidth = size.viewSize[0];
							var viewCenter = viewWidth / 2;
							
							var x = pos[0];
							var y = pos[1] + 10; // 稍微偏下，避免遮挡鼠标
							
							// 判断点击位置是在饼图的左侧还是右侧
							// 如果在右侧，则将tooltip显示在左侧
							if (x > viewCenter) {
								x = x - tooltipWidth - 20; // 向左偏移，确保不遮挡饼图
							} else {
								x = x + 20; // 在左侧点击时，向右偏移一点
							}
							
							// 确保tooltip靠边界显示并向内延伸
							// 左侧边界处理
							if (x < 0) {
								x = 0; // 紧贴左边界
							}
							// 右侧边界处理
							if (x + tooltipWidth > viewWidth) {
								x = viewWidth - tooltipWidth; // 右对齐到屏幕边缘
							}
							
							return [x, y];
						}
					},
					series: [{
						type: 'pie',
						clockwise: true,
						avoidLabelOverlap: true,
						radius: ['0%', '55%'],
						center: ["50%", "45%"],
						hoverOffset: 15,
						itemStyle: {
							normal: {
								color: function(params) {
									return that.colorList[params.dataIndex % that.colorList.length]; // Added modulo for safety
								}
							}
						},
						label: {
							show: true,
							position: 'outside',
							formatter: '{a|{b}:{d}%}\n{hr|}',
							rich: {
								hr: {
									backgroundColor: 't',
									borderRadius: 3,
									width: 3,
									height: 3,
									padding: [3, 3, 0, -12]
								},
								a: {
									padding: [-5, 10, -5, 5]
								}
							}
						},
						labelLayout: {
							hideOverlap: false
						},
						labelLine: {
							normal: {
								length: 5,
								length2: 7,
								lineStyle: {
									width: 1
								}
							}
						},
						data: this.pieData
					}]
				};
				if (chartsView2) {
					chartsView2.setOption(this.chartsPieOption, true); // Added true for notMerge
				}
			},
			changeChartsData(newVal, oldVal) { // Renamed value to newVal
				if (newVal) { // Ensure newVal is not undefined
					this.chartsInit(newVal);
				}
			},
			chartsInit(chartsData) { // Pass data as argument
				const that = this; // 'that' can be replaced with 'this'
				this.legendData = [];
				this.seriesData = [];
				if (chartsView == null) {
					const lineElement = document.getElementById("spiderChartLine");
					if (lineElement) { // Check if element exists
						chartsView = echarts.init(lineElement);
					} else {
						console.error("Element with id 'spiderChartLine' not found for ECharts initialization.");
						return;
					}
				}
				let num = 0;
				for (let i in chartsData) {
					if (chartsData.hasOwnProperty(i)) { // Good practice
						this.legendData.push(i);
						this.seriesData.push({
							name: i,
							type: 'line',
							data: chartsData[i],
							smooth: true,
							areaStyle: {
								color: this.colorList[num % this.colorList.length] // Added modulo
							},
							lineStyle: {
								color: this.colorList[num % this.colorList.length] // Added modulo
							},
							itemStyle: {
								normal: {
									color: this.colorList[num % this.colorList.length], // Added modulo
									borderColor: this.colorList[num % this.colorList.length] // Added modulo
								}
							},
						});
						num++;
					}
				}
				this.chartsOption = {
					tooltip: {
						trigger: 'axis',
						position: function(pt, params, dom, rect, size) {
							var fixedY = 0;
							var tooltipWidth = size.contentSize[0];
							var viewWidth = size.viewSize[0];
							var x = pt[0];
							if (x + tooltipWidth > viewWidth) {
								x -= tooltipWidth;
							}
							return [x, fixedY];
						},
						formatter: function(params) {
							var res = '时间：' + (params[0].name > 9 ? params[0].name : '0' + params[0].name) +
								':00<br/>';
							for (var i = 0, l = params.length; i < l; i++) {
								res += params[i].marker + params[i].seriesName + ' : ' + params[i].value + '<br/>';
							}
							return res;
						}
					},
					grid: {
						containLabel: true,
						x: 10,
						y: 10,
						x2: 10,
						y2: 10,
						bottom: '15%',
						top: '30%' // 添加顶部边距百分比
					},
					legend: { // legend.data should be dynamically set if you want to show/hide series by clicking legend items
						data: this.legendData // Set legend data
					},
					xAxis: {
						type: 'category',
						boundaryGap: false,
						data: ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12', '13', '14', '15',
							'16', '17', '18', '19', '20', '21', '22', '23'
						]
					},
					yAxis: {
						type: 'value',
						splitNumber: 5, // 控制刻度数量，设为5表示大约会有5个刻度（最少4个，最多6个）
						axisTick: {
							alignWithLabel: true // 刻度线与标签对齐
						}
					},
					dataZoom: [{
						type: 'slider',
						start: 0,
						end: 100,
						handleIcon: 'M10.7,11.9v-1.3H9.3v1.3c-4.9,0.3-8.8,4.4-8.8,9.4c0,5,3.9,9.1,8.8,9.4v1.3h1.3v-1.3c4.9-0.3,8.8-4.4,8.8-9.4C19.5,16.3,15.6,12.2,10.7,11.9z M13.3,24.4H6.7V23h6.6V24.4z M13.3,19.6H6.7v-1.4h6.6V19.6z',
						handleSize: '80%',
						handleStyle: {
							color: '#fff',
							shadowBlur: 3,
							shadowColor: 'rgba(0, 0, 0, 0.6)',
							shadowOffsetX: 2,
							shadowOffsetY: 2
						},
						left: '5%',
						right: '5%',
					}],
					series: this.seriesData
				};
				if (chartsView) {
					chartsView.setOption(this.chartsOption, true); // Added true for notMerge
				}
			},
		}
	}
</script>

<style scoped>
	.content {
		height: 100%;
		overflow: scroll;
	}

	.spider-view {
		width: 100%;
		display: grid;
		grid-template-columns: repeat(3, 1fr);
		/* 三列均分 */
		gap: 4rpx;
		/* 列间距 */
		box-sizing: border-box;
		text-align: left;
		margin-left: 16rpx;
		background-color: var(--bg-color-secondary);
	}

	.spider-text {
		width: 100%;
		display: flex;
		align-items: center;
	}

	.websize-box {
		padding: 2% 0 5% 0;
		width: 100%;
		text-align: center;
	}

	.title-info {
		background-color: var(--bg-color-secondary);
		justify-content: space-around;
	}

	.title-info view {
		font-size: 28rpx;
		color: var(--text-color-secondary);
		height: 100rpx;
		line-height: 100rpx;
		text-align: center;
	}

	.info-query {
		border-bottom: 1rpx solid #eff1f3;
		justify-content: space-around;
	}

	.info-query view {
		font-size: 28rpx;
		color: #20a53a;
		height: 90rpx;
		line-height: 90rpx;
		text-align: center;
	}

	.info-query > view:last-child {
		border-bottom: none;
	}

	.set-img text {
		vertical-align: middle;
		color: #999;
		font-size: 42rpx;
	}

	.iconyunhangchengxu {
		vertical-align: middle;
	}

	.iconicon-test {
		color: #f39c12;
		font-size: 32rpx;
	}

	/* 隐藏内容 */
	.hidden-info {
		height: 0;
		overflow: hidden;
		transition: all 0.4s;
		-webkit-transition: all 0.4s;
	}

	.show-info {
		height: 250rpx;
		overflow: hidden;
		margin-bottom: 40rpx;
		transition: all 0.8s;
		-webkit-transition: all 0.8s;
	}

	.icon-iconxiala {
		transform: rotate(0deg);
		transition: all 0.4s;
		-webkit-transition: all 0.4s;
	}

	.icon-iconxiala2 {
		transform: rotate(180deg);
		transition: all 0.8s;
		-webkit-transition: all 0.8s;
	}

	.flow-charts {
		width: 100%;
		height: 650rpx;
		background-color: #fff;
	}

	.charts {
		min-width: 100%;
		width: 100%;
		height: 650rpx;
		background-color: var(--bg-color);
	}

	.flow-charts-pie {
		width: 100%;
		height: 600rpx;
		background-color: var(--bg-color);
	}

	.chartsPie {
		min-width: 100%;
		width: 100%;
		height: 600rpx;
		background-color: var(--bg-color);
	}

	.report-switch {
		height: 100rpx;
		align-items: center;
		justify-content: space-between;
		border-bottom: 1rpx solid #d3d8d8;
		padding: 0 40rpx;
	}

	.report-switch view {
		font-size: 30rpx;
	}
</style>
