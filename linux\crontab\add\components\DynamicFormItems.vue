<template>
	<view>
		<!-- Shell脚本 -->
		<template v-if="formData.sType === 'toShell'">
			<ShellForm
				:form-data="formData"
				:is-edit-mode="isEditMode"
				@update:form-data="updateFormData"
			/>
		</template>

		<!-- 访问URL-GET -->
		<template v-if="formData.sType === 'toUrl'">
			<view class="form-group">
				<view class="form-label-row">
					<text>URL地址</text>
				</view>
				<view class="input-wrapper">
					<uv-input
						:value="formData.urladdress"
						@input="handleUrlInput"
						placeholder="请输入URL地址"
						border="surround"
						:customStyle="{ backgroundColor: 'var(--dialog-bg-color)', fontSize: '28rpx' }"
					/>
				</view>
			</view>
			<view class="form-group">
				<view class="form-label-row">
					<text>User-Agent</text>
				</view>
				<view class="textarea-wrapper">
					<uv-textarea
						:value="formData.user_agent"
						@input="updateField('user_agent', $event)"
						placeholder="请输入User-Agent"
						height="120"
						:textStyle="{ fontSize: '28rpx', color: 'var(--text-color-primary)' }"
						:customStyle="{ backgroundColor: 'var(--dialog-bg-color)' }"
					/>
				</view>
			</view>

			<!-- 进程锁 -->
			<view class="form-group">
				<view class="form-row">
					<text>进程锁</text>
					<uv-switch :value="formData.flock" @change="updateField('flock', $event)" activeColor="#20a53a"></uv-switch>
				</view>
			</view>
		</template>

		<!-- 访问URL-POST -->
		<template v-if="formData.sType === 'to_post'">
			<view class="form-group">
				<view class="form-label-row">
					<text>URL地址</text>
				</view>
				<view class="input-wrapper">
					<uv-input
						:value="formData.urladdress"
						@input="handleUrlInput"
						placeholder="请输入URL地址"
						border="surround"
						:customStyle="{ backgroundColor: 'var(--dialog-bg-color)', fontSize: '28rpx' }"
					/>
				</view>
			</view>
			<view class="form-group">
				<view class="form-label-row">
					<text>User-Agent</text>
				</view>
				<view class="textarea-wrapper">
					<uv-textarea
						:value="formData.user_agent"
						@input="updateField('user_agent', $event)"
						placeholder="请输入User-Agent"
						height="120"
						:textStyle="{ fontSize: '28rpx', color: 'var(--text-color-primary)' }"
						:customStyle="{ backgroundColor: 'var(--dialog-bg-color)' }"
					/>
				</view>
			</view>

			<!-- 进程锁 -->
			<view class="form-group">
				<view class="form-row">
					<text>进程锁</text>
					<uv-switch :value="formData.flock" @change="updateField('flock', $event)" activeColor="#20a53a"></uv-switch>
				</view>
			</view>
		</template>

		<!-- 备份网站 -->
		<template v-if="formData.sType === 'site'">
			<SiteBackupForm
				:formData="formData"
				:isEditMode="isEditMode"
				@update:formData="updateFormData"
			/>
		</template>

		<!-- 备份数据库 -->
		<template v-if="formData.sType === 'database'">
			<DatabaseBackupForm
				:formData="formData"
				:isEditMode="isEditMode"
				@update:formData="updateFormData"
			/>
		</template>

		<!-- 备份目录 -->
		<template v-if="formData.sType === 'path'">
			<PathBackupForm
				:formData="formData"
				:isEditMode="isEditMode"
				@update:formData="updateFormData"
			/>
		</template>

		<!-- 网站日志切割 -->
		<template v-if="formData.sType === 'logs'">
			<LogsForm
				:formData="formData"
				:isEditMode="isEditMode"
				@update:formData="updateFormData"
			/>
		</template>

		<!-- 木马查杀 -->
		<template v-if="formData.sType === 'webshell'">
			<WebshellForm
				:formData="formData"
				:isEditMode="isEditMode"
				@update:formData="updateFormData"
			/>
		</template>

		<!-- 释放内存 -->
		<template v-if="formData.sType === 'rememory'">
			<view class="info-section">
				<text>释放PHP、MYSQL、PURE-FTPD、APACHE、NGINX的内存占用,建议在每天半夜执行!</text>
			</view>

			<!-- 进程锁 -->
			<view class="form-group">
				<view class="form-row">
					<text>进程锁</text>
					<uv-switch :value="formData.flock" @change="updateField('flock', $event)" activeColor="#20a53a"></uv-switch>
				</view>
			</view>
		</template>

		<!-- 同步时间 -->
		<template v-if="formData.sType === 'sync_time'">
			<SyncTimeForm
				:formData="formData"
				:isEditMode="isEditMode"
				@update:formData="updateFormData"
			/>
		</template>

		<!-- 网站启停 -->
		<template v-if="formData.sType === 'site_restart'">
			<SiteRestartForm
				:formData="formData"
				:isEditMode="isEditMode"
				@update:formData="updateFormData"
			/>
		</template>

		<!-- 定时清理日志 -->
		<template v-if="formData.sType === 'log_cleanup'">
			<LogCleanupForm
				:formData="formData"
				:isEditMode="isEditMode"
				@update:formData="updateFormData"
			/>
		</template>

		<!-- 数据库增量备份 -->
		<template v-if="formData.sType === 'enterpriseBackup'">
			<EnterpriseBackupForm
				:formData="formData"
				:isEditMode="isEditMode"
				@update:formData="updateFormData"
			/>
		</template>
	</view>
</template>

<script setup>
	import { defineProps, defineEmits } from 'vue';
	
	// 导入子组件
	import ShellForm from './ShellForm.vue';
	import SiteBackupForm from './SiteBackupForm.vue';
	import DatabaseBackupForm from './DatabaseBackupForm.vue';
	import PathBackupForm from './PathBackupForm.vue';
	import LogsForm from './LogsForm.vue';
	import WebshellForm from './WebshellForm.vue';
	import SyncTimeForm from './SyncTimeForm.vue';
	import SiteRestartForm from './SiteRestartForm.vue';
	import LogCleanupForm from './LogCleanupForm.vue';
	import EnterpriseBackupForm from './EnterpriseBackupForm.vue';

	const props = defineProps({
		formData: {
			type: Object,
			required: true
		},
		isEditMode: {
			type: Boolean,
			default: false
		}
	});

	const emit = defineEmits(['update:formData', 'url-input']);

	// 更新单个字段
	const updateField = (field, event) => {
		const value = event.detail?.value || event.target?.value || event;
		emit('update:formData', { ...props.formData, [field]: value });
	};

	// 更新整个表单数据
	const updateFormData = (newData) => {
		emit('update:formData', { ...props.formData, ...newData });
	};

	// 处理URL输入
	const handleUrlInput = (event) => {
		const value = event.detail?.value || event.target?.value || event;
		updateField('urladdress', value);
		emit('url-input', event);
	};
</script>

<style lang="scss" scoped>
	.form-group {
		margin-bottom: 30rpx;
	}

	.form-label-row {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 20rpx;

		text {
			font-size: 28rpx;
			font-weight: 500;
			color: var(--text-color-primary);
		}
	}

	.input-wrapper {
		width: 100%;
	}

	.textarea-wrapper {
		width: 100%;
	}

	.info-section {
		margin-top: 40rpx;

		text {
			display: block;
			font-size: 24rpx;
			color: #666;
			line-height: 1.6;
			margin-bottom: 16rpx;

			&:last-child {
				margin-bottom: 0;
			}
		}
	}

	.form-row {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-top: 20rpx;

		text {
			font-size: 28rpx;
			font-weight: 500;
			color: var(--text-color-primary);
		}
	}
</style>
