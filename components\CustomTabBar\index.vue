<template>
  <page-container :isShowNav="false">
    <view class="custom-tab-bar">
      <!-- 顶部内容区域 -->
      <swiper
        class="tab-content"
        :style="{ height: tabContentHeight }"
        :current="currentTab"
        @change="handleSwiperChange"
        @animationfinish="handleAnimationFinish"
        :disable-touch="true"
        :duration="0"
        :skip-hidden-item-layout="true"
      >
        <swiper-item
          v-for="(item, index) in tabs"
          :key="index"
          class="swiper-item"
        >
          <component
            v-if="item.component"
            :is="item.component"
            v-bind="item.props || {}"
            v-on="item.events || {}"
          />
          <slot v-else :name="'content-' + index" :active="currentTab === index">
            <view class="default-content">{{ item.title || `Tab ${index + 1}` }}</view>
          </slot>
        </swiper-item>
      </swiper>

      <!-- 底部导航栏 -->
      <view class="tab-bar" :style="{ paddingBottom: tabBarPaddingBottom }">
        <view
          v-for="(item, index) in tabs"
          :key="index"
          class="tab-item"
          :class="{ 'tab-active': currentTab === index }"
          @click="handleTabClick(index)"
        >
          <view class="icon-wrapper" :class="{ 'icon-active': currentTab === index }">
            <slot :name="'icon-' + index" :active="currentTab === index">
              <uni-icons
                v-if="item.icon"
                :type="item.icon"
                size="24"
                class="custom-icon"
                :color="currentTab === index ? 'var(--primary-color)' : 'var(--text-secondary-color, #666666)' "
                :class="{ 'icon-active': currentTab === index }"
              />
              <view v-else class="default-icon"></view>
            </slot>
          </view>
          <text class="tab-text" :class="{ 'text-active': currentTab === index }">{{ item.title }}</text>
        </view>
      </view>
    </view>
  </page-container>
</template>

<script setup>
import { ref, watch, computed } from 'vue';
import pageContainer from '@/components/pageContainer/index.vue'

const props = defineProps({
  tabs: {
    type: Array,
    default: () => [],
    validator: (value) => {
      return value.every(tab => {
        return typeof tab === 'object' &&
               ('title' in tab || 'component' in tab || 'icon' in tab);
      });
    }
  },
  defaultTab: {
    type: Number,
    default: 0
  }
});

const emit = defineEmits(['change']);

const currentTab = ref(props.defaultTab);

// 获取系统信息
const systemInfo = uni.getSystemInfoSync();
// 优先使用新的 osName 字段，向下兼容 platform 字段
const osName = systemInfo.osName || systemInfo.platform;

// 动态计算 tab-bar 的 padding-bottom
const tabBarPaddingBottom = computed(() => {
  if (osName === 'android') {
    return '10rpx';
  } else if (osName === 'ios') {
    return '30rpx';
  } else if (osName === 'harmonyos') {
    return '20rpx';
  } else {
    // 其他平台默认值
    return '20rpx';
  }
});

// 动态计算 tab-content 的高度
const tabContentHeight = computed(() => {
  if (osName === 'ios') {
    return 'calc(100vh - 150rpx)';
  } else {
    return 'calc(100vh - 120rpx)';
  }
});

// 处理滑动切换
const handleSwiperChange = (e) => {
  const { current } = e.detail;
  if (current !== currentTab.value) {
    currentTab.value = current;
    emit('change', current);
  }
};

// 处理过渡动画结束
const handleAnimationFinish = () => {
  emit('change', currentTab.value);
};

// 处理标签点击
const handleTabClick = (index) => {
  if (currentTab.value === index) return;
  currentTab.value = index;
  emit('change', index);
};

// 监听props.defaultTab的变化
watch(() => props.defaultTab, (newVal) => {
  if (newVal !== currentTab.value) {
    currentTab.value = newVal;
  }
});
</script>

<style lang="scss" scoped>
.custom-tab-bar {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: var(--bg-color-secondary);
  position: relative;
  overflow: hidden;

  .tab-content {
    flex: 1;
    width: 100%;
    position: relative;

    .swiper-item {
      height: 100%;

      :deep(.scroll-view) {
        height: 100%;
      }
    }

    .default-content {
      padding: 30rpx;
      text-align: center;
      color: var(--text-color, #333);
    }
  }

  .tab-bar {
    display: flex;
    height: 120rpx;
    background: var(--bg-color-secondary);
    box-shadow: 0 -2rpx 10rpx var(--shadow-color, rgba(0, 0, 0, 0.05));
    position: relative;
    z-index: 100;
    flex-shrink: 0;

    .tab-item {
      flex: 1;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      position: relative;
      transition: all 0.3s ease;

      &::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%) scaleX(0);
        width: 40rpx;
        height: 4rpx;
        background-color: var(--primary-color);
        transition: transform 0.3s ease;
        border-radius: 2rpx;
      }

      &.tab-active {
        &::after {
          transform: translateX(-50%) scaleX(1);
        }
      }

      .icon-wrapper {
        width: 56rpx;
        height: 56rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 6rpx;
        position: relative;
        transition: all 0.3s ease;

        &.icon-active {
          transform: scale(1.1);
        }

        .default-icon {
          width: 44rpx;
          height: 44rpx;
          background: var(--icon-gradient, linear-gradient(135deg, #e0e0e0, #f5f5f5));
          border-radius: 12rpx;
          transition: all 0.3s ease;
        }
      }

      .tab-text {
        font-size: 24rpx;
        color: var(--text-secondary-color, #666666);
        transition: all 0.3s ease;
        transform: scale(0.95);
        text-align: center;

        &.text-active {
          color: var(--primary-color);
          transform: scale(1);
          font-weight: 500;
        }
      }
    }
  }
}

.custom-icon {
  transition: all 0.3s ease;

  &.icon-active {
    transform: scale(1.1);
  }
}
</style>