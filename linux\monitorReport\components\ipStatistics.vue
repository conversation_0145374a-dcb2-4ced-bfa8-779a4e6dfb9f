<template>
	<view class="content">
		<view class="report-switch flex">
			<view class="text-secondary">网站：</view>
			<uni-data-select
				v-model="siteName"
				:localdata="siteList"
				:clear="false"
				@change="changeSitePath"
				emptyTips="暂无网站"
			></uni-data-select>
		</view>

		<view class="report-switch flex">
			<view class="text-secondary">时间：</view>
			<view class="" style="width: 75%">
				<uni-segmented-control
					:current="currentPeriodIndex"
					:values="timePeriods"
					@clickItem="onClickPeriod"
					styleType="button"
					activeColor="#20a53a"
				></uni-segmented-control>
			</view>
		</view>

		<view class="site-box">
			<view class="site-list rank" v-for="item in ipList" :key="item.ip" @click="parenClick($event, item.ip)">
				<view class="site-check-query flex">
					<view class="flex" style="width: 100%">
						<text class="check-title">IP：</text>
						<text class="check-value table-info-text" style="color: #20a53a">{{ item.ip }}</text>
					</view>
					<view class="flex" style="width: 70%">
						<text class="check-title">归属地：</text>
						<view class="check-value">
							<text v-if="item.spider" style="color: #e77c42">[{{ item.spider }}蜘蛛]</text>
							<text v-else-if="item.is_malicious_ip" style="color: red">[恶意IP]</text>
							<text>{{ item.area || '-' }}</text>
						</view>
					</view>
					<view class="flex" style="width: 30%">
						<text class="check-title">请求数：</text>
						<text class="check-value">{{ formatNumber(item.request) }}</text>
					</view>
					<view class="flex" style="width: 70%" v-show="!!item.is_shield">
						<text class="check-title">状态：</text>
						<view class="check-value" style="color: red">已封禁</view>
					</view>
				</view>

				<view v-if="!item.is_shield" class="global-button flex">
					<view class="" @click.stop="handleBanIpClick(item.ip)">封IP</view>
				</view>
			</view>

			<view class="not-data" v-if="ipList.length === 0">IP列表为空...</view>
		</view>

		<!-- 封IP -->
		<CustomDialog
			v-model="banIpPopupRef"
			:title="`封禁【${currentIp}】`"
			confirmText="封禁"
			contentHeight="400rpx"
			@confirm="submitBanIp"
			@cancel="cancelBanIp"
		>
			<view class="uni-tip pre-content pt-20">
				<view class="uni-tip-content">
					<view class="form-content">
						<view class="form-item flex items-center" style="padding: 15rpx 0">
							<view class="form-name text-28 text-primary" style="width: 30%">封禁类型：</view>
							<view class="form-input-box" style="width: 70%">
								<uni-segmented-control
									:current="banTypeIndex"
									:values="BAN_TYPE_OPTIONS"
									active-color="#20a53a"
									@clickItem="onBanTypeChange"
								/>
							</view>
						</view>

						<view class="form-line"></view>
						<view class="form-item flex items-center" style="padding: 15rpx 0">
							<view class="form-name text-28 text-primary" style="width: 30%">解封时间：</view>
							<view class="form-input-box" style="width: 70%">
								<uni-segmented-control
									:current="banDurationIndex"
									:values="BAN_DURATION_OPTIONS"
									active-color="#20a53a"
									@clickItem="onBanDurationChange"
								/>
							</view>
						</view>
						<view class="form-line"></view>

						<view class="form-item flex items-center" style="padding: 15rpx 0">
							<view class="form-name text-28 text-primary" style="width: 30%">封禁原因：</view>
							<view class="form-input-box" style="width: 70%">
								<uni-easyinput
									:clearable="false"
									primaryColor="#20a53a"
									v-model="banReason"
									placeholder="备注封禁原因"
								></uni-easyinput>
							</view>
						</view>
					</view>
				</view>
			</view>
		</CustomDialog>

		<!-- 解封ip -->
		<CustomDialog
			v-model="unbanIpPopupRef"
			@confirm="confirmUnbanIp"
			title="解封IP"
			confirmText="解封"
			contentHeight="200rpx"
			:confirmStyle="{ backgroundColor: '#FF3B30' }"
		>
			<view class="text-secondary flex justify-center items-center h-full">
				{{ `解封后将允许【${currentIp}】进行访问，是否继续操作？` }}
			</view>
		</CustomDialog>
	</view>
</template>

<script setup>
	import { ref, onMounted, computed } from 'vue';
	import { getSiteIpTotal, unshieldIp, shieldIp, getSiteNames, setDefaultSite } from '@/api/monitorReport';
	import CustomDialog from '@/components/CustomDialog/index.vue';

	// 常量定义
	const TIME_PERIODS = ['今天', '昨天', '近7天', '近30天'];
	const BAN_TYPE_OPTIONS = ['普通封禁', 'WAF封禁'];
	const BAN_DURATION_OPTIONS = ['永久封禁', '封禁24小时'];
	const THEME_COLOR = '#20a53a';
	const ERROR_COLOR = '#FF3B30';

	// 弹窗状态
	const banIpPopupRef = ref(false);
	const unbanIpPopupRef = ref(false);

	// 数据状态
	const ipList = ref([]);
	const currentPeriodIndex = ref(0);
	const timePeriods = ref(TIME_PERIODS);
	const siteName = ref('');
	const siteList = ref([]);
	const currentIp = ref('');
	const banDurationIndex = ref(1);
	const banTypeIndex = ref(0);
	const banReason = ref('');

	/**
	 * 格式化日期为 YYYY-MM-DD 格式
	 * @param {Date} date - 要格式化的日期对象
	 * @returns {string} 格式化后的日期字符串
	 */
	const formatDate = (date) => {
		const year = date.getFullYear();
		const month = String(date.getMonth() + 1).padStart(2, '0');
		const day = String(date.getDate()).padStart(2, '0');
		return `${year}-${month}-${day}`;
	};

	/**
	 * 根据时间段类型获取开始和结束日期
	 * @param {number} periodType - 时间段类型索引
	 * @returns {Object} 包含开始和结束日期的对象
	 */
	const getDateRangeByPeriodType = (periodType) => {
		const dateRange = {};
		const today = new Date();
		
		switch (periodType) {
			case 0: // 今天
				dateRange.start_date = formatDate(today);
				dateRange.end_date = formatDate(today);
				break;
			case 1: // 昨天
				const yesterday = new Date(today);
				yesterday.setDate(today.getDate() - 1);
				dateRange.start_date = formatDate(yesterday);
				dateRange.end_date = formatDate(yesterday);
				break;
			case 2: // 近7天
				const last7Days = new Date(today);
				last7Days.setDate(today.getDate() - 7);
				dateRange.start_date = formatDate(last7Days);
				dateRange.end_date = formatDate(today);
				break;
			case 3: // 近30天
				const last30Days = new Date(today);
				last30Days.setDate(today.getDate() - 30);
				dateRange.start_date = formatDate(last30Days);
				dateRange.end_date = formatDate(today);
				break;
			default:
				dateRange.start_date = '';
				dateRange.end_date = '';
				break;
		}
		return dateRange;
	};

	/**
	 * 获取IP列表数据
	 */
	const getIpList = async () => {
		const dateRange = getDateRangeByPeriodType(currentPeriodIndex.value);
		const params = {
			SiteName: siteName.value,
			start_date: dateRange.start_date,
			end_date: dateRange.end_date,
			is_verify: false,
		};
		
		try {
			const res = await getSiteIpTotal(params);
			ipList.value = res;
		} catch (error) {
			console.error('获取IP列表失败:', error);
			showToast('获取IP列表失败');
		}
	};

	/**
	 * 显示统一格式的提示信息
	 * @param {string} message - 提示消息
	 * @param {string} icon - 图标类型
	 */
	const showToast = (message, icon = 'none') => {
		uni.showToast({
			title: message,
			icon: icon
		});
	};

	/**
	 * 设置当前操作的IP
	 * @param {Event} event - 事件对象
	 * @param {string} ip - IP地址
	 */
	const parenClick = (event, ip) => {
		currentIp.value = ip;
	};

	/**
	 * 处理封禁时长选择
	 * @param {Object} e - 事件对象
	 */
	const onBanDurationChange = (e) => {
		banDurationIndex.value = e.currentIndex;
	};

	/**
	 * 处理封禁类型选择
	 * @param {Object} e - 事件对象
	 */
	const onBanTypeChange = (e) => {
		banTypeIndex.value = e.currentIndex;
	};

	/**
	 * 点击封禁IP按钮
	 * @param {string} ipToBan - 要封禁的IP
	 */
	const handleBanIpClick = (ipToBan) => {
		currentIp.value = ipToBan;
		banIpPopupRef.value = true;
	};

	/**
	 * 点击解封IP按钮
	 * @param {string} ipToUnban - 要解封的IP
	 */
	const handleUnbanIpClick = (ipToUnban) => {
		currentIp.value = ipToUnban;
		unbanIpPopupRef.value = true;
	};

	/**
	 * 确认解封IP
	 * @param {Function} close - 关闭弹窗的函数
	 */
	const confirmUnbanIp = async (close) => {
		try {
			const res = await unshieldIp({
				ip: currentIp.value,
				is_temporary: 1
			});
			
			showToast(res.msg);
			
			if (res.status) {
				getIpList();
			}
			close && close();
		} catch (error) {
			console.error('解封IP失败:', error);
			showToast('解封IP失败');
		}
	};

	/**
	 * 提交封禁IP请求
	 * @param {Function} close - 关闭弹窗的函数
	 */
	const submitBanIp = async (close) => {
		uni.showLoading({
			title: '封禁中...',
			mask: true,
		});
		
		const params = {
			ip: currentIp.value,
			end_time: banDurationIndex.value,
			msg: banReason.value,
			to_waf: banTypeIndex.value || 0,
			ip_net: 0,
		};
		
		try {
			const res = await shieldIp(params);
			showToast(res.msg);
			
			if (res.status) {
				resetBanForm();
				getIpList();
			}
			close && close();
		} catch (error) {
			console.error('封禁IP失败:', error);
			showToast('封禁IP失败');
		} finally {
			uni.hideLoading();
		}
	};

	/**
	 * 重置封禁表单
	 */
	const resetBanForm = () => {
		banDurationIndex.value = 1;
		banReason.value = '';
		banTypeIndex.value = 0;
	};

	/**
	 * 取消封禁IP
	 */
	const cancelBanIp = () => {
		banIpPopupRef.value = false;
		resetBanForm();
	};

	/**
	 * 获取站点列表
	 */
	const getSiteList = async () => {
		try {
			const data = await getSiteNames();
			siteList.value = data.map((item) => ({
				...item,
				text: item.name,
				value: item.name,
				disable: false,
			}));

			if (siteList.value.length) {
				selectDefaultSite();
			}
		} catch (error) {
			console.error('获取站点列表失败:', error);
			showToast('获取站点列表失败');
		}
	};

	/**
	 * 选择默认站点
	 */
	const selectDefaultSite = () => {
		const defaultSite = siteList.value.find((item) => item.default);
		if (defaultSite) {
			siteName.value = defaultSite.name;
		} else {
			siteName.value = siteList.value[0].name;
		}
		getIpList();
	};

	/**
	 * 切换站点
	 * @param {string} newSiteName - 新选择的站点名称
	 */
	const changeSitePath = async (newSiteName) => {
		try {
			await setDefaultSite({ SiteName: newSiteName });
			getIpList();
		} catch (error) {
			console.error('切换站点失败:', error);
			showToast('切换站点失败');
		}
	};

	/**
	 * 点击切换时间段
	 * @param {Object} e - 事件对象
	 */
	const onClickPeriod = (e) => {
		currentPeriodIndex.value = e.currentIndex;
		getIpList();
	};

	/**
	 * 格式化数字，添加千分位分隔符
	 * @param {number|string} num - 要格式化的数字
	 * @returns {string} 格式化后的字符串
	 */
	const formatNumber = (num) => {
		const number = Number(num);
		if (isNaN(number)) {
			return num;
		}
		if (number === 0) {
			return '0';
		}
		
		const sign = number < 0 ? '-' : '';
		const absNumber = Math.abs(Math.round(number));
		let result = absNumber.toString();
		result = result.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
		return sign + result;
	};

	// 生命周期钩子
	onMounted(() => {
		getSiteList();
	});
</script>

<style scoped>
	.content {
		height: 100%;
		overflow: scroll;
	}

	.report-switch {
		height: 100rpx;
		align-items: center;
		justify-content: space-between;
		border-bottom: 1rpx solid #d3d8d8;
		padding: 0 40rpx;
	}

	.report-switch view {
		font-size: 30rpx;
	}
	/* 站点列表 */
	.site-box {
		overflow: hidden;
		background-color: var(--bg-color);
		padding-bottom: 10rpx;
		padding-top: 20rpx;
	}

	.site-box > view:first-child {
		margin-top: 0 !important;
	}

	.site-list {
		background-color: var(--dialog-bg-color);
		justify-content: center;
		padding: 15rpx 30rpx 15rpx 30rpx;
		margin-top: 5%;
		box-shadow: 0 8rpx 16rpx rgba(0, 0, 0, 0.1);
	}

	.site-title-box {
		align-items: center;
		justify-content: space-between;
		height: 100rpx;
		border-bottom: 1rpx dashed #ccc;
	}

	.site-title {
		color: #20a53a;
		font-size: 28rpx;
	}

	.site-switch text {
		font-size: 30rpx;
		color: #444;
	}

	.site-check-query {
		width: 100%;
		align-items: center;
		justify-content: space-between;
		flex-wrap: wrap;
		padding-bottom: 20rpx;
	}

	.site-check-query > view {
		width: 50%;
		height: 80rpx;
		align-items: center;
	}

	.check-title {
		color: var(--text-color-secondary);
		font-size: 26rpx;
		white-space: nowrap;
	}

	.check-value {
		font-size: 28rpx;
		color: var(--text-color-secondary);
	}

	/* 无数据提示 */
	.not-data {
		width: 100%;
		font-size: 30rpx;
		color: var(--text-color-secondary);
		text-align: center;
		padding: 10% 0;
	}

	.global-button {
		width: 100%;
		background-color: var(--dialog-bg-color);
		align-items: center;
		justify-content: flex-end;
		border-top: 1rpx solid #d7dde1;
	}

	.global-button > view {
		padding: 0 30rpx;
		text-align: center;
		height: 60rpx;
		line-height: 60rpx;
		color: #fff;
		font-size: 26rpx;
		margin: 22rpx 30rpx 22rpx 10rpx;
		border-radius: 6rpx;
		background-color: #20a53a;
	}

	.disabledIP {
		background-color: red !important;
	}
</style>
