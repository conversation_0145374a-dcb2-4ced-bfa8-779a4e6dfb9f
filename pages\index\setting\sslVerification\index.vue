<template>
    <view class="ssl-verification-container">
        <uv-switch v-model="harmonySslVerification" @change="handleSslVerificationChange" :active-color="'#20a50a'" :inactive-color="'#f0f0f0'" space="2" />
    </view>
</template>

<script setup>
import { ref } from 'vue';
import { useConfigStore } from '@/store/modules/config';

const { harmonySslVerification } = useConfigStore().getReactiveState();

const handleSslVerificationChange = (value) => {
    uni.setStorageSync('harmonySslVerification', value);
}
</script>

