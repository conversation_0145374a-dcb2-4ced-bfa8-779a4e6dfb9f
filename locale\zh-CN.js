export default {
	common: {
		confirm: '确认',
		cancel: '取消',
		submit: '提交',
		loading: '加载中...',
		success: '成功',
		fail: '失败',
		language: '语言',
		delete: '删除',
		deleteConfirm: '确定要删除吗？',
		deleteSuccess: '删除成功',
		deleteFail: '删除失败',
		refresh: '刷新',
		refreshSuccess: '刷新成功',
		refreshFail: '刷新失败',
		rename: '重命名',
		modify: '修改',
		tip: '温馨提示',
	},
	home: {
		welcome: '欢迎使用本应用',
		dashboard: '仪表盘',
		settings: '设置',
		fileManagement: '文件管理',
	},
	server: {
		serverList: '服务器列表',
		dynamicAuth: '动态口令',
		servers: '服务器',
		memory: '内存',
		cpu: 'CPU',
		disk: '硬盘',
		network: '网络',
		serverConnectionFailed: '服务器连接失败，请检查连接情况',
		load: '负载',
		smooth: '流畅',
		connecting: '连接中...',
		deleteConfirmText: '确定要删除IP为【{ip}】的服务器吗？',
		btPanel: '宝塔面板',
		online: '在线',
		offline: '离线',
		open: '打开',
		deleteServer: '删除服务器',
		day: '{days}天',
		internalNetworkError: '当前使用内网地址访问面板,请使用外网地址访问面板后重新绑定!',
		alreadyBound: '该服务器已绑定,请勿重复绑定!',
		apiKeyTooOld: 'API秘钥太老,请重置API秘钥后再进行绑定!',
		abnormalStatus: '信息状态不正常,请删除浏览器缓存后,再刷新二维码,然后重新扫码绑定!',
		waitingForPC: '等待PC端确认',
		pcConfirmTimeout: '等待PC端确认超时',
		bindBeforeLogin: '请先绑定该服务器,然后再扫码登录!',
		cloudBindFailed: '云控绑定失败',
		cloudConfirmTimeout: '等待云控PC端确认超时',
		iosPrompt: '查看教程',
		sort: '排序',
		sortServers: '服务器排序',
		sortTip: '长按拖拽调整服务器顺序',
		sortComplete: '排序已保存',
	},
	setting: {
		settings: '设置',
		theme: '主题',
		checkUpdate: '检查更新',
		dark: '暗黑',
		light: '亮色',
		currentVersion: '当前版本：',
		about: '关于APP',
		appUpdate: 'App版本更新',
		cancelUpdate: '取消更新',
		confirmUpdate: '确定更新',
		iosUpdate: '前往AppStore更新',
		androidUpdate: '下载并安装',
		updateInProgress: '更新版本中...',
		importantNotice: '重要提示：本次大版本更新将导致数据重置。请务必在更新前备份所有重要数据。',
		newVersionAvailable: '堡塔App已发布最新版[{version}]。',
		versionNotes: '版本更新说明：',
		newVersionDetected: '检测到新版本[{version}]，建议更新到新版本！',
		about: '关于',
		general: '常规',
		security: '安全',
		graphicUnlock: '图形解锁',
		enabled: '已开启',
		disabled: '未开启',
		modifyGraphicPassword: '修改图形解锁密码',
		aboutBtApp: '关于宝塔APP',
		privacyPolicy: '隐私政策',
		serviceAgreement: '服务协议',
		checkingUpdate: '检查更新中',
		latestVersion: '当前已是最新版本',
		closeConfirm: '关闭确认',
		confirmCloseUnlock: '确定要关闭图形解锁吗？',
		closeSuccess: '关闭成功！',
		enableGraphicUnlockFirst: '请先开启图形解锁！',
		feedback: '意见反馈',
		recordNumber: 'APP备案编号：粤ICP备17030143号-3A',
		queryLink: '查询链接：',
	},
	scan: {
		scanSuccess: '扫码成功',
		scanFail: '扫码失败',
		scanCancel: '扫码取消',
		bindSuccess: '绑定成功',
		bindFail: '绑定失败',
		loginSuccess: '登录成功',
		loginFail: '登录失败',
	},
	blank: {
		lock: '图形解锁',
		unlock: '解锁成功',
		unlockFail: '解锁失败',
		unlockCancel: '解锁取消',
		notSet: '暂不设置',
		back: '返回',
		forget: '忘记密码',
		notChange: '暂不修改密码',
		reset: '还原确认',
		resetApp: '还原App中...',
		resetPwd: '请绘制原手势密码',
		lockPwd: '请解锁',
		confirmApp: '确定使用这套手势作为App解锁密码吗？',
		forgetConfirm: '忘记密码会解绑您此App绑定的所有服务器，并还原App为默认设置！确定操作吗？',
		blankTitle: '堡塔，让运维安全高效',
		drawPattern: '请绘制解锁图案，不少于4个点',
		patternError: '图案错误，请重试',
		welcome: '欢迎！',
		drawAgain: '再次绘制图案进行确认',
		patternCompleted: '图案绘制完成，点击下方确定保存！',
		patternMismatch: '两次图案不一致，请重试',
		patternTooShort: '图案绘制不可少于4个点，请重试',
		drawNewPattern: '请绘制新密码，不少于4个点',
		originalPatternError: '原密码错误！请重试',
		resetSuccess: '还原成功！',
		saveSuccess: '保存成功！',
		addServer: '添加宝塔面板',
	},
	appUpdate: {
		gettingVersion: '获取中...',
		checkUpdateFailed: '检查更新失败：',
		updateCompleted: '更新完成，将重新启动App',
		installFailed: '安装失败：',
	},
	checkAuth: {
		title: '动态口令',
		copyCode: '复制验证码',
		rename: '重命名',
		delete: '删除',
		emptyTip: '暂无口令和认证...',
		checkTutorial: '点我查看新手教程',
		securityTip: '用于登录面板二次验证，保障面板安全',
		scanToAdd: '点击扫码添加',
		supportTip: 'PS：支持【动态口令】、【SSH二次认证】',
		longPressTip: '支持长按操作',
		modify: '修改',
		inputName: '请输入名称',
		deleteConfirm: '删除确认',
		deleteWarning:
			'App中删除动态口令，建议先关闭面板的动态口令认证功能再删除，否则需要连接SSH才能关闭口令认证。\n您确定要删除【{issuer}】的口令验证吗？',
		addSuccess: '添加成功！',
		modifySuccess: '修改成功',
		deleteSuccess: '删除成功',
		unrecognizedQRCode: '无法识别的二维码，不能添加！',
		timeoutText: '未识别到二维码？',
		copySuccess: '复制成功',
	},
	linux: {
		switchPanel: '切换面板',
		runningTime: '持续运行',
		load: '负载',
		memory: '内存',
		disk: '磁盘',
		multiDiskTips: '若有多个磁盘，可左右滑动查看',
		website: '网站',
		database: '数据库',
		securityRisk: '安全风险',
		modulePlugin: '模块/插件',
		securitySetting: '安全/设置',
		networkFlow: '网络',
		setting: '面板设置',
		siteMonitor: '站点监控',
		nginx: 'nginx防火墙',
		ssh: 'SSH管理',
		diskIO: '磁盘IO',
		crontab: '计划任务',
		all: '全部',
		fileManagement: '文件管理',
		security: '安全',
		terminal: '终端',
		monitoring: '监控',
		crontab: '计划任务',
		blocked: '堵塞',
		slow: '缓慢',
		normal: '正常',
		smooth: '流畅',
		unitKBS: '单位：KB/s',
		unitMBS: '单位：MB/s',
		cores: '核心',
		control: {
			title: '监控管理',
			monitorSwitch: '监控开关',
			resourceUsage: '资源使用率',
			loadDetails: '负载详情',
			average: '平均值',
			maximum: '最大值',
			today: '今天',
			yesterday: '昨天',
			readBytes: '读取字节数',
			writeBytes: '写入字节数',
			networkIO: '网络IO',
			processName: '进程名',
			cpuUsage: 'CPU占用',
			startUser: '启动用户',
			resourceUsagePercent: '资源使用率%',
			percentUnit: '百分比',
			time: '时间',
			minutes1: '1分钟',
			minutes5: '5分钟',
			minutes15: '15分钟',
		},
		network: {
			up: '上行',
			down: '下行',
		},
		io: {
			read: '读取',
			write: '写入',
		},
		install: {
			selectVersion: '选择版本',
			installProcess: '安装进程',
			completeInstallation: '完成安装',
			selectVersion: '选择版本',
			speedInstall: '极速安装',
			installLog: '安装日志',
			installing: '正在安装...',
			error: '发生错误',
			progress: '安装进度',
			retry: '重试',
			complete: '完成',
			previous: '上一步',
			next: '下一步',
			installSuccess: '安装成功',
			successMessage: '已成功安装并配置完成',
			details: {
				version: '版本:',
				status: '状态:',
				running: '运行中',
			},
			logs: {
				startInstall: '开始安装',
				preparing: '正在准备安装环境...',
				installComplete: '安装完成！',
				installFailed: '安装失败',
				error: '安装过程发生错误:',
			},
			return: '返回',
		},
	},
	website: {
		management: '网站管理',
		running: '运行中',
		stopped: '已停止',
		noWebServer: 'Web 服务器未安装',
		refreshSuccess: '刷新成功',
		editRemark: '修改备注',
		editName: '修改名称',
		delete: '删除',
		warning: '温馨提示',
		confirmDelete: '确定要删除 {name} 吗？',
		modify: '修改',
		deleting: '删除中...',
		status: {
			notDeployed: '未部署',
			expired: '已过期',
			certExpired: '证书已过期',
			sslExpireDays: 'SSL证书到期：剩余{days}天',
		},
		errors: {
			keyRequired: '请填入密钥(KEY)！',
			certificateRequired: '请填入证书(PEM格式)！',
		},
		install: {
			installServer: '安装所选服务器',
			nginx: {
				title: '安装 Nginx',
				description: '高性能、低内存占用的Web服务器',
			},
			apache: {
				title: '安装 Apache',
				description: '功能丰富、成熟稳定的Web服务器',
			},
		},
		manage: {
			domainManagement: '域名管理',
			sslManagement: 'SSL管理',
			closeSSL: '关闭SSL',
			certificateFolder: '证书夹',
			forceHTTPS: '强制HTTPS',
			saveCertificate: '保存并启用证书',
			certificateDetails: {
				brand: '证书品牌：',
				domains: '认证域名：',
				expiryDate: '到期时间：',
				deploymentStatus: '部署状态',
				autoRenewal: '将在距离到期时间1个月内尝试自动续签',
				renewalReminder: '请在证书到期之前更换新的证书',
				deployed: '已部署成功',
			},
			keyTitle: '密钥（KEY）',
			certificateTitle: '证书（PEM格式）',
			certificateSaving: '证书保存中...',
			closingCertificate: '关闭证书中...',
			closeSSLSuccess: '关闭SSL成功！',
			port: '端口：',
			cannotBeOperated: '不可操作',
			sslFolder: {
				certificateBrand: '证书品牌：',
				certifiedDomains: '认证域名：',
				expiryDate: '到期时间:',
				location: '位置:',
				cloud: '云端',
				local: '本地',
				deploy: '部署',
				delete: '删除',
				deleteConfirm: '确定从证书夹删除 {subject} 证书吗？',
				deploying: '部署证书中...',
				deleting: '删除证书中...',
			},
		},
	},
	database: {
		management: '数据库管理',
		copyPassword: '复制密码',
		modifyRemark: '修改备注',
		username: '用户名',
		location: '位置',
		backup: '备份',
		backupExists: '有备份({count})',
		noBackup: '未备份',
		remark: '备注',
		password: '密码',
		noPassword: '未设置密码',
		passwordCopied: '密码已复制',
		confirmDelete: '确定要删除数据库 {name} 吗？',
		noPasswordSet: '未设置密码',
		localDatabase: '本地数据库',
		databaseServiceNotInstalled: '数据库服务未安装',
		installMySQL: '安装 MySQL',
		mysqlDescription: '最流行的开源关系型数据库，性能稳定',
		installSelectedDatabase: '安装所选数据库',
	},
	editor: {
		unsaved: '未保存',
		save: '保存',
		dontSave: '不保存',
		fileModified: '检测到文件已被修改，是否保存？',
		saveSuccess: '保存成功',
		saveFail: '保存失败',
		noChange: '文件未修改，无需保存！',
		saving: '保存中...',
		getContentFailed: '获取文件内容失败',
	},
	files: {
		rootDirectory: '根目录',
		newFolder: '新建文件夹',
		newFile: '新建文件',
		modifyRemark: '修改备注',
		confirmDelete: '确定要删除 {name} 吗？',
		enterRemark: '请输入备注',
		create: '新建',
		enterFolderName: '请输入文件夹名称',
		enterFileName: '请输入文件名称',
		recycleError: '此为回收站目录，无法打开',
		imageNotSupported: '图片文件暂时无法打开',
		compressNotSupported: '压缩文件暂时无法打开',
		mediaNotSupported: '视频、音频文件暂时无法打开',
		systemFileError: '系统目录文件不支持编辑',
		fileTypeNotSupported: '暂不支持该类型文件编辑',
		fileSizeTooLarge: '抱歉！文件大小超过3M，暂不支持在线编辑！',
		nameEmpty: '名称不能为空',
		invalidChars: '名称不能包含以下字符：\\ / : * ? " < > |',
		nameNoChange: '文件名没有变化',
		renaming: '正在重命名，请稍候...',
		deleting: '删除中...',
		creatingFolder: '正在创建文件夹，请稍候...',
		creatingFile: '正在创建文件，请稍候...',
	},
	firewall: {
		title: '安全管理',
		firewallSwitch: '防火墙开关',
		block: '拦截',
		allow: '放行',
		delete: '删除',
		port: '端口',
		ipv6: '（ipv6）',
		status: '状态',
		strategy: '策略',
		note: '备注',
		notUsed: '未使用',
		externalNetworkDisconnected: '外网不通',
		normal: '正常',
		enableFirewall: '启用系统防火墙',
		enablePrompt: '推荐启用，启用系统防火墙后，可以更好的防护当前的服务器安全，是否继续操作?',
		disableFirewall: '停用系统防火墙',
		disablePrompt: '停用系统防火墙，服务器将失去安全防护，是否继续操作？',
		deleteRuleConfirm: '确定要删除端口 {port} 的规则吗？',
		loading: '加载中',
		setStatusFailed: '设置防火墙状态失败',
		portList: {
			mysql: 'MySQL服务默认端口',
			phpmyadmin: 'phpMyAdmin默认端口',
			ssh: 'SSH远程服务',
			ftpData: 'FTP主动模式数据端口',
			ftp: 'FTP协议默认端口',
			ftpPassiveRange1: 'FTP被动模式端口范围',
			ftpPassiveRange2: 'FTP被动模式端口范围',
			memcached: 'Memcached服务端口',
			rsync: 'rsync数据同步服务',
			panel: '宝塔Linux面板默认端口',
		},
	},
	crontab: {
		title: '计划任务管理',
		addTask: '添加任务',
		editTask: '编辑任务',
		noTasks: '暂无计划任务',
		addFirstTask: '点击右上角按钮添加第一个任务',
		taskName: '任务名称',
		taskNamePlaceholder: '请输入任务名称',
		executionCycle: '执行周期',
		command: '执行命令',
		commandPlaceholder: '请输入要执行的命令',
		remark: '备注',
		remarkPlaceholder: '请输入备注信息（可选）',
		schedule: '执行周期',
		enabled: '已启用',
		disabled: '已禁用',
		everyMinute: '每分钟',
		everyHour: '每小时',
		everyDay: '每天',
		everyWeek: '每周',
		everyMonth: '每月',
		nameRequired: '请输入任务名称',
		commandRequired: '请输入执行命令',
		addSuccess: '添加任务成功',
		addFailed: '添加任务失败',
		updateSuccess: '更新任务成功',
		updateFailed: '更新任务失败',
		enableSuccess: '启用任务成功',
		disableSuccess: '禁用任务成功',
		toggleFailed: '切换任务状态失败',
		loadFailed: '加载任务列表失败',
	},
};
