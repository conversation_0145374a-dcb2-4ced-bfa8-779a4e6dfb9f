//
//  TBScanResult.h
//  TBScanSDK
//
//  Created by <PERSON> on 15/7/29.
//  Copyright (c) 2015年 Taobao.com. All rights reserved.
//

#import <TBDecodeSDK/TBDecodeResult.h>

#import <TBScanSDK/TBScanMacros.h>

TBScanSDK_EXPORT NSString * const TBScanResultTypeGoodsBarcode; // 商品条码
TBScanSDK_EXPORT NSString * const TBScanResultTypeExpressCode; // 快递单
TBScanSDK_EXPORT NSString * const TBScanResultTypeITFCode; // ITF条码(水电煤码)
TBScanSDK_EXPORT NSString * const TBScanResultTypeQRCode; // QR码
TBScanSDK_EXPORT NSString * const TBScanResultTypeDataMatrixCode; // DM码
TBScanSDK_EXPORT NSString * const TBScanResultTypePDF417Code; // PDF417
TBScanSDK_EXPORT NSString * const TBScanResultTypeVLGen3Code; // 视觉码三代码
TBScanSDK_EXPORT NSString * const TBScanResultTypeARCode; // AR码
TBScanSDK_EXPORT NSString * const TBScanResultTypeNarrowCode; // 窄条码
TBScanSDK_EXPORT NSString * const TBScanResultTypeAPXCode; // AP码
TBScanSDK_EXPORT NSString * const TBScanResultTypeHMCode;   // 动态异构码
//////////////////////额外信息Dictionary中的Key///////////////////////
TBScanSDK_EXPORT NSString * const TBScanExtDataQRCodeResultType; // QR码的类型

enum{
    APPROACH_CAMERA = 0,//通过摄像头取帧识别
    APPROACH_LOCALPHOTO = 1,//通过扫描本地图库识别
};
typedef int TBScanApproachOfAchieving;

enum{
    QR_TAOBAO_LIST = 0,//网址二维码,淘系名单
    QR_UNKNOWN_LIST = 1,//网址二维码,不是淘系名单
    QR_TEXT = 2,//文本二维码
};
typedef int TBScanQRCodeResultType;

@interface TBScanResult : NSObject

@property (nonatomic, strong) NSString *resultType; // 码类型
@property (nonatomic, assign) int subType; // 码子类型，一般情况下忽略
@property (nonatomic, strong) NSString* data; // 码包含的信息(码值)
@property (nonatomic, strong) NSData* rawData; // 包含了码值原始信息的 bytes
@property (nonatomic, strong) NSString* hiddenData; // 隐藏码的隐藏信息
@property (nonatomic, assign) CGRect    rect;//码在预览帧里的位置
@property (nonatomic, assign) NSStringEncoding stringEncode;  // 字符编码格式
@property (nonatomic, strong) NSString *hmCodeTypeName;     // 动态码的码类型描述

@property (nonatomic, assign) TBScanApproachOfAchieving approach; // 识别的途径

@property (nonatomic, strong) NSMutableDictionary *extData; // 额外信息

//@property (nonatomic, strong) 

- (instancetype)initWithTBDecodeResult:(TBDecodeResult *)decodeResult;

@end
