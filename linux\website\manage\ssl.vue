<template>
  <view class="flex flex-col p-20">
    <view class="flex flex-row items-center justify-between">
      <view class="flex flex-row items-center">
        <BtButton
          type="default"
          :disabled="!(cert_data.status != undefined)"
          :customStyle="{
            fontSize: '28rpx',
            minHeight: '60rpx',
            lineHeight: 1.2,
            border: '2rpx solid #ccc',
            color: 'var(--text-color-secondary)',
          }"
          @click="closeOperation"
          >{{ $t('website.manage.closeSSL') }}</BtButton
        >
        <BtButton
          class="ml-20"
          type="default"
          @click="skipSSLFolder"
          :customStyle="{
            fontSize: '28rpx',
            minHeight: '60rpx',
            lineHeight: 1.2,
            border: '2rpx solid #ccc',
            color: 'var(--text-color-secondary)',
          }"
          >{{ $t('website.manage.certificateFolder') }}</BtButton
        >
      </view>
      <view class="flex flex-row items-center">
        <view class="text-secondary text-28 mx-15">{{ $t('website.manage.forceHTTPS') }}</view>
        <uv-switch
          v-model="switchHttps"
          :disabled="!(cert_data.status != undefined)"
          activeColor="#20a50a"
          asyncChange
          @change="handleHttps"
        ></uv-switch>
      </view>
    </view>
    <view class="flex flex-col mt-40 p-20 bg-#D2EFCD rd-8" v-if="cert_data.status != undefined">
      <view class="text-28 px-5 py-20">
        <text class="text-bt-primary font-600">{{ $t('website.manage.certificateDetails.brand') }}</text>
        <text class="text-bt-primary font-400">{{ cert_data.issuer }}</text>
      </view>
      <view class="text-28 px-5 py-20">
        <text class="text-bt-primary font-600">{{ $t('website.manage.certificateDetails.domains') }}</text>
        <text class="text-bt-primary font-400" v-for="(item, index) in cert_data.dns" :key="index"
          >{{ item }}{{ index < cert_data.dns.length - 1 ? '、' : '' }}</text
        >
      </view>
      <view class="text-28 px-5 py-20">
        <text class="text-bt-primary font-600">{{ $t('website.manage.certificateDetails.expiryDate') }}</text>
        <text class="text-bt-primary font-400">{{ cert_data.notAfter }}</text>
      </view>
      <view class="text-28 px-5 py-20">
        <text class="text-bt-primary font-600">{{ $t('website.manage.certificateDetails.deploymentStatus') }}：</text>
        <text class="text-bt-primary font-400">{{ cert_data.status }}</text>
      </view>
    </view>
    <view class="my-20">
      <view class="text-secondary text-30 mt-40 mb-20">{{ $t('website.manage.keyTitle') }}</view>
      <view>
        <uv-textarea
          v-model="infoKey"
          height="120"
          :textStyle="{ fontSize: '28rpx', color: 'var(--text-color-primary)' }"
          placeholder="请输入内容"
          :customStyle="{ backgroundColor: 'var(--dialog-bg-color)' }"
        ></uv-textarea>
      </view>
    </view>
    <view class="mb-20">
      <view class="text-secondary text-30 mt-40 mb-20">{{ $t('website.manage.certificateTitle') }}</view>
      <view>
        <uv-textarea
          v-model="infoPem"
          height="120"
          :textStyle="{ fontSize: '28rpx', color: 'var(--text-color-primary)' }"
          placeholder="请输入内容"
          :customStyle="{ backgroundColor: 'var(--dialog-bg-color)' }"
        ></uv-textarea>
      </view>
    </view>
    <view class="my-20">
      <BtButton type="success" :customStyle="{ fontSize: '28rpx' }" @click="saveDeployment">{{ $t('website.manage.saveCertificate') }}</BtButton>
    </view>
  </view>
</template>

<script setup>
  import BtButton from '@/components/BtButton/index.vue';
  import { ref } from 'vue';
  import { onShow } from '@dcloudio/uni-app';
  import { $t } from '@/locale/index.js';
  import {
    switchHttps,
    infoKey,
    infoPem,
    hanleSSLInfo,
    cert_data,
    saveDeployment,
    closeOperation,
    handleHttps,
    skipSSLFolder,
  } from './useController';

  onShow(() => {
    hanleSSLInfo();
  });
</script>

<style scoped></style>
