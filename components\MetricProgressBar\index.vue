<template>
	<view class="metric-progress-bar" :class="progressBarClass">
		<!-- 垂直柱状图模式（负载） -->
		<template v-if="mode === 'vertical'">
			<view class="progress-container vertical-container" :style="containerStyle">
				<view
					class="progress-fill vertical-fill"
					:style="{
						height: progressHeight,
						background: progressBackground,
					}"
				>
					<text class="progress-text vertical-text" :style="{ color: textColor }">
						{{ displayValue }}%
					</text>
				</view>
			</view>
		</template>

		<!-- 水平条形图模式（磁盘） -->
		<template v-else>
			<view class="progress-container horizontal-container" :style="containerStyle">
				<view
					class="progress-fill horizontal-fill"
					:style="{
						width: progressWidth,
						background: progressBackground,
					}"
				></view>
			</view>
			<text class="progress-text horizontal-text" :style="{ color: textColor }">
				{{ displayValue }}%
			</text>
		</template>
	</view>
</template>

<script setup>
	import { computed } from 'vue';

	// Props 定义
	const props = defineProps({
		// 进度值（0-100）
		value: {
			type: Number,
			default: 0,
			validator: (value) => value >= 0 && value <= 100,
		},
		// 进度条模式：vertical（垂直柱状图）或 horizontal（水平条形图）
		mode: {
			type: String,
			default: 'horizontal',
			validator: (value) => ['vertical', 'horizontal'].includes(value),
		},
		// 进度条宽度（仅水平模式有效）
		width: {
			type: String,
			default: '100%',
		},
		// 进度条高度（仅垂直模式有效）
		height: {
			type: String,
			default: '200rpx',
		},
		// 进度条厚度（垂直模式为宽度，水平模式为高度）
		thickness: {
			type: String,
			default: '16rpx',
		},
		// 自定义颜色（可选，如果不提供则使用内置颜色逻辑）
		color: {
			type: String,
			default: '',
		},
		// 背景颜色
		backgroundColor: {
			type: String,
			default: '#E7E7E7',
		},
		// 文字颜色
		textColor: {
			type: String,
			default: '#fff',
		},
		// 是否显示百分比文字
		showText: {
			type: Boolean,
			default: true,
		},
		// 是否启用动画
		animated: {
			type: Boolean,
			default: true,
		},
		// 服务器状态（用于离线时的灰色显示）
		serverStatus: {
			type: Boolean,
			default: true,
		},
	});

	// 计算属性：进度条样式类
	const progressBarClass = computed(() => {
		return {
			'progress-bar-vertical': props.mode === 'vertical',
			'progress-bar-horizontal': props.mode === 'horizontal',
			'progress-bar-animated': props.animated,
		};
	});

	// 计算属性：容器样式
	const containerStyle = computed(() => {
		const baseStyle = {
			background: props.backgroundColor,
		};

		if (props.mode === 'vertical') {
			return {
				...baseStyle,
				width: props.thickness,
				height: props.height,
			};
		} else {
			return {
				...baseStyle,
				width: props.width,
				height: props.thickness,
			};
		}
	});

	// 计算属性：进度高度（垂直模式）
	const progressHeight = computed(() => {
		const percentage = Math.max(5, Math.min(95, props.value));
		return percentage + '%';
	});

	// 计算属性：进度宽度（水平模式）
	const progressWidth = computed(() => {
		const percentage = Math.max(5, Math.min(95, props.value));
		return percentage + '%';
	});

	// 计算属性：进度背景色
	const progressBackground = computed(() => {
		// 如果服务器离线，显示灰色
		if (!props.serverStatus) {
			return '#7E7E7E';
		}

		// 如果提供了自定义颜色，使用自定义颜色
		if (props.color) {
			return props.color;
		}

		// 使用内置颜色逻辑
		const usage = props.value;
		if (usage >= 81) {
			// 81-100% 从红色到黄色的渐变
			if (props.mode === 'vertical') {
				return 'linear-gradient(180deg, #FFEB3B 0%, #D32F2F 100%)';
			} else {
				return 'linear-gradient(90deg, #D32F2F 0%, #FFEB3B 100%)';
			}
		} else if (usage >= 51) {
			// 51-80% 从绿色到黄色的渐变
			if (props.mode === 'vertical') {
				return 'linear-gradient(180deg, #FFEB3B 0%, #4CAF50 100%)';
			} else {
				return 'linear-gradient(90deg, #4CAF50 0%, #FFEB3B 100%)';
			}
		} else {
			// 0-50% 纯绿色
			return '#4CAF50';
		}
	});

	// 计算属性：显示的数值
	const displayValue = computed(() => {
		return Math.round(props.value);
	});
</script>

<style lang="scss" scoped>
	.metric-progress-bar {
		display: flex;
		align-items: center;
		position: relative;
	}

	.progress-bar-vertical {
		flex-direction: column;
		justify-content: flex-end;
		align-items: center;
	}

	.progress-bar-horizontal {
		flex-direction: row;
		align-items: center;
		gap: 16rpx;
	}

	.progress-container {
		position: relative;
		border-radius: 16rpx;
		overflow: hidden;
		border: 2rpx solid rgba(0, 0, 0, 0.1);
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
	}

	.vertical-container {
		display: flex;
		align-items: flex-end;
		justify-content: center;
	}

	.horizontal-container {
		display: flex;
		align-items: center;
		justify-content: flex-start;
	}

	.progress-fill {
		position: relative;
		display: flex;
		align-items: center;
		justify-content: center;
		min-width: 0%;
		min-height: 0%;
	}

	.vertical-fill {
		width: 100%;
		border-radius: 0 0 16rpx 16rpx;
		align-items: center;
		justify-content: center;
	}

	.horizontal-fill {
		height: 100%;
		border-radius: 8rpx;
	}

	.progress-bar-animated .progress-fill {
		transition:
			width 0.8s cubic-bezier(0.4, 0, 0.2, 1),
			height 0.8s cubic-bezier(0.4, 0, 0.2, 1),
			background 0.6s ease-in-out;
	}

	.progress-text {
		font-size: 24rpx;
		font-weight: bold;
		text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.3);
		transition: color 0.3s ease;
	}

	.vertical-text {
		position: absolute;
		bottom: 30rpx;
		left: 50%;
		transform: translateX(-50%);
		font-size: 28rpx;
	}

	.horizontal-text {
		min-width: 60rpx;
		text-align: right;
		text-shadow: none;
	}
</style>
