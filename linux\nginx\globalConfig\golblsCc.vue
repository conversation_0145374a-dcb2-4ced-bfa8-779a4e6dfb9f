<template>
	<view class="container">
		<!-- 头部卡片 -->
		<view class="card header-card">
			<view class="header">
				<text class="header-title">{{ activeTab === 'list' ? '人机验证白名单' : '添加人机验证白名单' }}</text>
				<view v-if="activeTab === 'list'" class="btn-add" hover-class="btn-hover" @tap="activeTab = 'add'">
					<uv-icon name="plus" size="16" color="#ffffff"></uv-icon>
					<text class="btn-text">添加</text>
				</view>
				<view v-else class="btn-back" hover-class="btn-hover" @tap="activeTab = 'list'">
					<uv-icon name="arrow-left" size="16" color="#ffffff"></uv-icon>
					<text class="btn-text">返回</text>
				</view>
			</view>

			<!-- 添加表单 -->
			<view v-if="activeTab === 'add'">
				<view class="pt-20">
					<!-- URL表单 -->
					<view class="form-item">
						<text class="form-item-label">URL地址</text>
						<view class="form-item-content">
							<input
								v-model="newUrl"
								type="text"
								class="form-input"
								placeholder="例如: /admin/update"
								@blur="validateUrlField"
								:class="{ 'input-error': errors.url }"
							/>
						</view>
						<text v-if="errors.url" class="error-text">{{ errors.url }}</text>
					</view>

					<!-- 提示信息 -->
					<view class="form-tips">
						<view class="form-tip-item">
							<text class="dot">•</text>
							<text>开启人机验证时候需要不验证某些页面时使用</text>
						</view>
						<view class="form-tip-item">
							<text class="dot">•</text>
							<text>例子1 : /admin/admin.php?system=abc 添加地址: /admin/admin.php</text>
						</view>
						<view class="form-tip-item">
							<text class="dot">•</text>
							<text>例子2: /admin/1 1 为可变 1-99 添加地址为: /admin/[1-99]</text>
						</view>
					</view>

					<!-- 操作按钮 -->
					<view class="form-submit">
						<button
							class="submit-btn"
							:class="{ 'btn-loading': isAddLoading }"
							hover-class="btn-hover"
							:disabled="isAddLoading"
							@tap="addUrl"
						>
							<text v-if="isAddLoading">添加中...</text>
							<text v-else>添加</text>
						</button>
					</view>
				</view>
			</view>
		</view>

		<!-- 主内容区域 -->
		<view class="content-wrapper">
			<!-- 列表视图 -->
			<view v-if="activeTab === 'list'" class="url-list-container">
				<!-- 加载状态 -->
				<view v-if="isLoading" class="loading-state">
					<uv-loading-icon size="32" color="#20a50a"></uv-loading-icon>
					<text class="loading-text">加载中...</text>
				</view>

				<!-- 空状态 -->
				<view v-else-if="urls.length === 0" class="empty-state">
					<uv-icon name="info-circle" size="32" color="#909399"></uv-icon>
					<text class="empty-text">当前数据为空，点击右上角添加</text>
				</view>

				<!-- 列表数据 -->
				<view v-else class="url-list">
					<view v-for="(item, index) in urls" :key="index" class="url-card">
						<view class="url-card__header">
							<text class="url-card__title">URL: {{ item }}</text>
							<view class="url-card__action" @tap="deleteUrl(index)">
								<uv-icon name="trash" size="16" color="#e53935"></uv-icon>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script setup>
	import { ref, onMounted } from 'vue';
	import { getGolblsCcList, addGolblsCc, deleteGolblsCc } from '@/api/nginx';
	import { formPageContainer } from './useController.js';

	// 数据
	const activeTab = ref('list');
	const newUrl = ref('');
	const urls = ref([]);
	const isLoading = ref(false);
	const isAddLoading = ref(false);

	// 表单验证错误状态
	const errors = ref({
		url: ''
	});

	/**
	 * 验证URL字段
	 */
	const validateUrlField = () => {
		const value = newUrl.value.trim();

		// 清除之前的错误
		errors.value.url = '';

		// 检查是否为空
		if (!value) {
			errors.value.url = 'URL地址不能为空';
			return false;
		}

		// 检查URL格式（简单验证，必须以/开头）
		if (!value.startsWith('/')) {
			errors.value.url = 'URL必须以 / 开头';
			return false;
		}

		return true;
	};

	/**
	 * 验证所有表单字段
	 */
	const validateAllFields = () => {
		return validateUrlField();
	};

	/**
	 * 获取人机验证白名单列表
	 */
	const getUrlList = async () => {
		try {
			isLoading.value = true;
			const res = await getGolblsCcList();
			if (res.status) {
				// API 返回的是数组格式，直接使用
				urls.value = res.msg || [];
			} else {
				formPageContainer.value?.notify?.error(res.msg || '获取列表失败');
			}
		} catch (error) {
			console.error('获取人机验证白名单列表失败:', error);
			formPageContainer.value?.notify?.error('获取列表失败，请稍后重试');
		} finally {
			isLoading.value = false;
		}
	};

	/**
	 * 添加URL
	 */
	const addUrl = async () => {
		// 验证表单
		if (!validateAllFields()) {
			return;
		}

		try {
			isAddLoading.value = true;
			const res = await addGolblsCc({ text: newUrl.value.trim() });
			if (res.status) {
				formPageContainer.value?.notify?.success(res.msg || '添加成功');
				newUrl.value = ''; // 清空输入框
				// 清除错误状态
				errors.value.url = '';
				activeTab.value = 'list'; // 切换回列表页
				// 重新获取列表数据
				await getUrlList();
			} else {
				formPageContainer.value?.notify?.error(res.msg || '添加失败');
			}
		} catch (error) {
			console.error('添加URL失败:', error);
			formPageContainer.value?.notify?.error('添加失败，请稍后重试');
		} finally {
			isAddLoading.value = false;
		}
	};

	/**
	 * 删除URL
	 * @param {number} index - 要删除的索引
	 */
	const deleteUrl = async (index) => {
		const urlToDelete = urls.value[index];
		if (!urlToDelete) {
			return;
		}

		try {
			const res = await deleteGolblsCc({ text: urlToDelete });
			if (res.status) {
				formPageContainer.value?.notify?.success(res.msg || '删除成功');
				// 重新获取列表数据
				await getUrlList();
			} else {
				formPageContainer.value?.notify?.error(res.msg || '删除失败');
			}
		} catch (error) {
			console.error('删除URL失败:', error);
			formPageContainer.value?.notify?.error('删除失败，请稍后重试');
		}
	};

	// 页面加载时获取数据
	onMounted(() => {
		getUrlList();
	});
</script>

<style lang="scss" scoped>
	// Variables
	$primary-color: #20a50a;
	$primary-hover: #189008;
	$text-color: var(--text-color-primary);
	$light-text: var(--text-color-secondary);
	$border-color: #eaeaea;
	$danger-color: #e53935;
	$bg-color: var(--dialog-bg-color);
	$base-spacing: 20rpx;

	.container {
		padding: $base-spacing;
	}

	.card {
		background: $bg-color;
		border-radius: 16rpx;
		box-shadow: 0 4rpx 24rpx rgba(0, 0, 0, 0.15);
		overflow: hidden;
		padding: 20rpx;
	}

	.header {
		display: flex;
		justify-content: space-between;
		align-items: center;
	}

	.header-title {
		font-size: 32rpx;
		font-weight: 500;
		color: $text-color;
	}

	.btn-add,
	.btn-back {
		display: flex;
		align-items: center;
		padding: 16rpx 40rpx;
		background-color: $primary-color;
		border-radius: 8rpx;
		color: white;
		font-size: 28rpx;

		.btn-text {
			margin-left: 10rpx;
		}
	}

	.btn-hover {
		opacity: 0.9;
		transform: scale(0.98);
	}

	// 表单样式
	.form-item {
		margin-bottom: 30rpx;
	}

	.form-item-label {
		display: block;
		font-size: 28rpx;
		color: $text-color;
		margin-bottom: 10rpx;
	}

	.form-item-content {
		width: 100%;
	}

	.form-input {
		width: 100%;
		height: 80rpx;
		border: 2rpx solid #ddd;
		border-radius: 6rpx;
		padding: 0 20rpx;
		font-size: 28rpx;
		background-color: #fff;
		box-sizing: border-box;

		&.input-error {
			border-color: #e53935;
		}
	}

	.error-text {
		color: #e53935;
		font-size: 24rpx;
		margin-top: 8rpx;
		display: block;
	}

	.form-tips {
		margin: 40rpx 0;
	}

	.form-tip-item {
		display: flex;
		align-items: flex-start;
		margin-bottom: 16rpx;
		font-size: 26rpx;
		color: $light-text;
		line-height: 1.5;

		.dot {
			margin-right: 10rpx;
		}
	}

	.form-submit {
		margin: 40rpx 0 20rpx 0;
	}

	.submit-btn {
		width: 100%;
		height: 90rpx;
		line-height: 90rpx;
		text-align: center;
		background-color: $primary-color;
		color: white;
		font-size: 32rpx;
		border-radius: 6rpx;
	}

	// 内容区域
	.content-wrapper {
		margin-top: $base-spacing;
	}

	// 空状态样式
	.empty-state {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding: 80rpx 0;
		background-color: $bg-color;
		border-radius: 16rpx;
		box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.12);
		color: $light-text;

		.empty-text {
			font-size: 28rpx;
			margin-top: 20rpx;
		}
	}

	// 加载状态样式
	.loading-state {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding: 80rpx 0;
		background-color: $bg-color;
		border-radius: 16rpx;
		box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.12);
		color: $light-text;

		.loading-text {
			font-size: 28rpx;
			margin-top: 20rpx;
		}
	}

	// 按钮加载状态
	.btn-loading {
		opacity: 0.6;
		pointer-events: none;
	}

	// 卡片式列表
	.url-list {
		display: flex;
		flex-direction: column;
		gap: $base-spacing;
	}

	.url-card {
		border-radius: 12rpx;
		box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.12);
		overflow: hidden;
		border: 2rpx solid #f0f0f0;

		&__header {
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: 20rpx;
			border-bottom: 2rpx solid #f0f0f0;
		}

		&__title {
			font-size: 28rpx;
			font-weight: 500;
			word-break: break-all;
			color: $text-color;
		}

		&__action {
			width: 60rpx;
			height: 60rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			background-color: rgba($danger-color, 0.05);
			border-radius: 30rpx;

			&:active {
				background-color: rgba($danger-color, 0.15);
			}
		}
	}

	.pt-20 {
		padding-top: 20rpx;
	}
</style>
