import axios from '@/api/request';
import { useConfigStore } from '@/store/modules/config';

const DEFAULT_API_TYPE = import.meta.env.VITE_PANEL_OWNER || 'domestic';

/**
 * @description 获取防火墙开关状态
 */
export const getFirewallStatus = () => {
    const url = DEFAULT_API_TYPE === 'domestic' ? '/firewall/com/get_status' : '/v2/firewall/com/get_status';
    const { panelVersion } = useConfigStore().getReactiveState();
    return axios(url, {}, 'POST', (res) => {
        if (DEFAULT_API_TYPE === 'domestic' && panelVersion.value === '9.6.0') {
            const status = res.status
            return status
        }
        if (DEFAULT_API_TYPE === 'international') {
            return res.status
        }
        return res;
    });
}

/**
 * @description 设置防火墙开关状态
 * @param { number } data.status 状态
 */
export const setFirewallStatus = (data) => {
    const url = DEFAULT_API_TYPE === 'domestic' ? '/firewall/com/set_status' : '/v2/firewall/com/set_status';
    return axios(url, data, 'POST', (res) => {
        if (DEFAULT_API_TYPE === 'international') {
            return {
                status: res?.result,
                msg: res?.result
            }
        }
        return res;
    });
}

/**
 * @description 获取所有的端口规则
 * @param { string } data.chain 数据类型  	ALL是获取所有方向，INPUT是获取入站，OUTPUT是获取出站
 */
export const getPortList = (data) => {
    const url = DEFAULT_API_TYPE === 'domestic' ? '/firewall/com/port_rules_list' : '/v2/firewall/com/port_rules_list';
    return axios(url, data, 'POST', (res) => {
        if (DEFAULT_API_TYPE === 'international') {
            return {
                data: res
            }
        }
        return res;
    });
}

/**
 * @description 删除端口规则
 * @param { string } data 数据
 */
export const delPortRules = (data) => {
    const url = DEFAULT_API_TYPE === 'domestic' ? '/firewall/com/set_port_rule' : '/v2/firewall/com/set_port_rule';
    return axios(url, data, 'POST', (res) => {
        if (DEFAULT_API_TYPE === 'international') {
            return {
                status: res?.result,
                msg: res?.result
            }
        }
        return res;
    });
}

/**
 * @description 编辑端口规则
 * @param { string } data 数据
 */
export const editPortRules = (data) => {
    const url = DEFAULT_API_TYPE === 'domestic' ? '/firewall/com/modify_port_rule' : '/v2/firewall/com/modify_port_rule';
    return axios(url, data, 'POST', (res) => {
        if (DEFAULT_API_TYPE === 'international') {
            return {
                status: res?.result,
                msg: res?.result
            }
        }
        return res;
    });
}

/**
 * @description 获取安全配置数据
 */
export const getSSHInfo = () => {
    return axios('/safe/ssh/GetSshInfo')
}

/**
 * @description 获取ssh登录信息
 */
export const getSSHLoginInfo = () => {
    return axios('/mod/ssh/com/get_ssh_intrusion')
}

/**
 * @description 获取ssh基础设置
 */
export const getSSHBasicConfig = () => {
    return axios('/ssh_security?action=get_config')
}

/**
 * @description 设置root密码
 * @param { string } data.password 密码
 * @param { string } data.username 用户名
 */
export const setSSHPassword = (data) => {
    return axios('/ssh_security?action=set_root_password', data)
}

/**
 * @description 设置root登录
 * @param { string } data.p_type 登录方式
 */
export const setRootLogin = (data) => {
    return axios('/ssh_security?action=set_root', data)
}

/**
 * @description 更改ssh端口号
 * @param { number } data.port 端口号
 */
export const setSshPort = (data) => {
    return axios('/firewall?action=SetSshPort', data)
}

/**
 * @description 是否启用SSH密码登录
 * @param { boolean } status 是否启用
 */
export const setSshPwd = (status) => {
    const url = status ? 'set_password' : 'stop_password'
    return axios(`/ssh_security?action=${url}`)
}

/**
 * @description 关闭SSH密钥登录
 */
export const stopSshKeyLogin = () => {
    return axios('/ssh_security?action=stop_key')
}

/**
 * @description 开启SSH密钥登录
 * @param { string } data.type 类型
 * @param { string } data.ssh ssh密钥
 */
export const openSshKey = (data) => {
    return axios('/ssh_security?action=set_sshkey', data)
}

/**
 * @description 是否启用ssh
 * @param { string } data.status 是否启用 '0'禁用  '1' 启用
 */
export const setSsh = (data) => {
    return axios('/firewall?action=SetSshStatus', data)
}

/**
 * @description 获取禁ping信息
 */
export const getPingInfo = () => {
    return axios('/firewall/com/get_firewall_info')
}

/**
 * @description 是否启用ping
 */
export const setPing = (data) => {
    return axios('/firewall?action=SetPing', data)
}

/**
 * @description 获取密钥
 */
export const getSshKey = () => {
    return axios('/ssh_security?action=get_key')
}