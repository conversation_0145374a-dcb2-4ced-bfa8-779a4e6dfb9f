import { ref, computed, reactive, getCurrentInstance } from 'vue';
import { addCrontab, modifyCrond } from '@/api/crontab';
import { throttle } from '@/uni_modules/uni-easyinput/components/uni-easyinput/common.js';

/**
 * 文本截断工具函数
 * @param {string} text - 要截断的文本
 * @param {number} maxLength - 最大长度，默认30个字符
 * @returns {string} 截断后的文本，超出部分显示省略号
 */
export const truncateText = (text, maxLength = 30) => {
	if (!text) return '';
	return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;
};

/**
 * 获取显示标签的通用方法
 * @param {Array} options - 选项数组
 * @param {string|number} value - 当前值
 * @param {number} maxLength - 最大长度，默认30个字符
 * @returns {string} 截断后的标签文本
 */
export const getDisplayLabel = (options, value, maxLength = 30) => {
	if (!options || !Array.isArray(options)) return '';
	const option = options.find((item) => item.value === value);
	return option ? truncateText(option.label, maxLength) : '';
};

// 文件拆分表单
export const fileSplitForm = ref({
	split_value: 5, // 文件拆分数量
	split_type: '0',
	save_local: 0,
});

export const useAddCrontab = () => {
	const { proxy } = getCurrentInstance();
	const pageContainer = ref(null);
	const formRef = ref(null);
	const submitting = ref(false);

	// 编辑状态相关
	const isEditMode = ref(false);
	const editTaskId = ref(null);
	const originalTaskType = ref('');

	// Picker refs
	const taskTypePicker = ref(null);

	// 表单数据 - 与动态表单组件保持一致
	const formData = reactive({
		sType: 'toShell', // 任务类型
		name: '', // 任务名称
		sBody: '', // 脚本内容
		urladdress: '', // URL地址
		user_agent: '', // User-Agent
		save: '', // 保存数量
		backupTo: '', // 备份到
		datab_name: '', // 数据库名称
		tables_name: '', // 表名
		user: 'root', // 执行用户
		flock: true, // 进程锁
		// 时间相关字段（由 CycleForm 组件管理）
		type: 'day', // 周期类型
		week: '1', // 周几
		hour: '1', // 小时
		minute: '30', // 分钟
		second: '5', // 秒
		where1: '1', // x天xxx
		timeSet: '', // 周几 多选 或者 几号 多选
		timeType: 'sday', // 备份周期类型
		specialHour: [0], // 特殊小时
		specialMinute: [0], // 特殊分钟
		// 新增字段
		sName: '', // 网站名称/数据库名称/目录路径等
		siteName: '', // 网站备份表单专用的网站名称字段
		db_type: '', // 数据库类型
		table_list: '', // 表列表
		backup_mode: 0, // 备份模式
		db_backup_path: '', // 备份路径
		start_time: '', // 开启时间
		stop_time: '', // 停止时间
		cleanup_type: '', // 清理类型
		selected_log_types: [], // 选中的日志类型
		zip_password: '', // 压缩密码
		more: '', // 更多参数
		// 文件拆分和备份设置
		split_type: '0', // 拆分类型：0-不拆分，size-按大小拆分，num-按数量拆分
		split_value: 5, // 拆分数值
		save_local: 0, // 是否保留本地备份：0-否，1-是
	});

	// 告警表单 - 与动态表单组件保持一致
	const noticeForm = reactive({
		notice: 0, // 消息通知
		notice_channel: 'all', // 通知渠道
		keyword: '', // 失败关键字匹配
	});

	// 默认表单数据
	const getDefaultFormData = () => ({
		sType: 'toShell', // 任务类型
		name: '', // 任务名称
		sBody: '', // 脚本内容
		urladdress: '', // URL地址
		user_agent: '', // User-Agent
		save: '', // 保存数量
		backupTo: '', // 备份到
		datab_name: '', // 数据库名称
		tables_name: '', // 表名
		user: 'root', // 执行用户
		flock: true, // 进程锁
		// 时间相关字段（由 CycleForm 组件管理）
		type: 'day', // 周期类型
		week: '1', // 周几
		hour: '1', // 小时
		minute: '30', // 分钟
		second: '5', // 秒
		where1: '1', // x天xxx
		timeSet: '', // 周几 多选 或者 几号 多选
		timeType: 'sday', // 备份周期类型
		specialHour: [0], // 特殊小时
		specialMinute: [0], // 特殊分钟
		// 新增字段
		sName: '', // 网站名称/数据库名称/目录路径等
		db_type: '', // 数据库类型
		table_list: '', // 表列表
		backup_mode: 0, // 备份模式
		db_backup_path: '', // 备份路径
		start_time: '', // 开启时间
		stop_time: '', // 停止时间
		cleanup_type: '', // 清理类型
		selected_log_types: [], // 选中的日志类型
		zip_password: '', // 压缩密码
		more: '', // 更多参数
		// 文件拆分和备份设置
		split_type: '0', // 拆分类型：0-不拆分，size-按大小拆分，num-按数量拆分
		split_value: 5, // 拆分数值
		save_local: 0, // 是否保留本地备份：0-否，1-是
	});

	// 表单验证规则
	const rules = computed(() => {
		const baseRules = {
			sType: [{ required: true, message: '请选择任务类型', trigger: 'change' }],
			name: [{ required: true, message: '请输入任务名称', trigger: 'blur' }],
		};

		// 根据任务类型添加动态验证规则
		switch (formData.sType) {
			case 'toShell':
				baseRules.sBody = [{ required: true, message: '请输入脚本内容', trigger: 'blur' }];
				break;
			case 'toUrl':
			case 'to_post':
				baseRules.urladdress = [{ required: true, message: '请输入URL地址', trigger: 'blur' }];
				break;
			case 'site':
			case 'database':
			case 'path':
				baseRules.save = [
					{ required: true, message: '请输入保存数量', trigger: 'blur' },
					{ pattern: /^[1-9]\d*$/, message: '保存数量必须为正整数', trigger: 'blur' },
				];
				break;
		}

		return baseRules;
	});

	// 表单验证方法
	const validateForm = () => {
		const errors = [];

		// 基础验证
		if (!formData.name.trim()) {
			errors.push('请输入任务名称');
		}

		if (!formData.sType) {
			errors.push('请选择任务类型');
		}

		// 根据任务类型进行特定验证
		switch (formData.sType) {
			case 'toShell':
				if (!formData.sBody.trim()) {
					errors.push('请输入脚本内容');
				}
				break;
			case 'toUrl':
			case 'to_post':
				if (!formData.urladdress.trim()) {
					errors.push('请输入URL地址');
				} else if (!isValidUrl(formData.urladdress)) {
					errors.push('请输入有效的URL地址');
				}
				break;
			case 'site':
			case 'database':
			case 'path':
				if (!formData.save || formData.save <= 0) {
					errors.push('请输入有效的保存数量');
				}
				break;
			case 'logs':
				if (!formData.sName.trim()) {
					errors.push('请输入存储路径');
				}
				break;
			case 'webshell':
				if (!formData.sName) {
					errors.push('请选择查杀站点');
				}
				break;
			case 'sync_time':
				if (!formData.sBody) {
					errors.push('请选择时区');
				}
				break;
			case 'site_restart':
				if (!formData.sName) {
					errors.push('请选择网站');
				}
				if (!formData.start_time || !formData.stop_time) {
					errors.push('请设置开启和停止时间');
				}
				break;
			case 'log_cleanup':
				if (!formData.cleanup_type) {
					errors.push('请选择清理类型');
				}
				if (formData.cleanup_type === 'custom' && !formData.sBody.trim()) {
					errors.push('请输入自定义目录');
				}
				if (
					formData.cleanup_type === 'select' &&
					(!formData.selected_log_types || formData.selected_log_types.length === 0)
				) {
					errors.push('请选择至少一种日志类型');
				}
				break;
		}

		return errors;
	};

	// URL验证
	const isValidUrl = (url) => {
		try {
			new URL(url);
			return true;
		} catch {
			return false;
		}
	};

	// 任务类型选项 - 与动态表单组件保持一致
	const taskTypeOptions = [
		{ value: 'toShell', label: 'Shell脚本' },
		{ value: 'site', label: '备份网站' },
		{ value: 'database', label: '备份数据库' },
		// { value: 'enterpriseBackup', label: '数据库增量备份' },
		// { value: 'logs', label: '网站日志切割' },
		// { value: 'path', label: '备份目录' },
		// { value: 'webshell', label: '木马查杀' },
		// { value: 'sync_time', label: '同步时间' },
		// { value: 'rememory', label: '释放内存' },
		// { value: 'toUrl', label: '访问URL-GET' },
		// { value: 'to_post', label: '访问URL-POST' },
		// { value: 'site_restart', label: '网站启停' },
		// { value: 'log_cleanup', label: '定时清理日志' },
	];

	// 是否禁用任务名称
	const disableName = computed(() => {
		const arr = ['logs', 'webshell', 'site_restart', 'path', 'site', 'database', 'rememory'];
		return arr.includes(formData.sType);
	});

	// 禁用任务类型选择（编辑模式下）
	const disableTaskType = computed(() => {
		return isEditMode.value;
	});

	// 计算属性 - 显示标签
	const taskTypeLabel = computed(() => {
		const option = taskTypeOptions.find((item) => item.value === formData.sType);
		return option ? option.label : '';
	});

	// 执行提示
	const executeTips = computed(() => {
		if (!formData.type) return '';

		const addZero = (val) => (!isNaN(Number(val)) && Number(val) < 10 ? '0' + val : val);
		const weekObj = {
			1: '周一',
			2: '周二',
			3: '周三',
			4: '周四',
			5: '周五',
			6: '周六',
			7: '周日',
		};

		const stype = formData.type;
		const shour = formData.hour?.toString() || '--';
		const sminute = formData.minute?.toString() || '--';
		const sday = formData.where1?.toString() || '--';
		const sweek = formData.week?.toString() || '--';
		const ssecond = formData.second?.toString() || '--';

		const stime = `${addZero(shour)}:${addZero(sminute)} 执行一次`;

		switch (stype) {
			case 'day':
				return `每天的 ${stime}`;
			case 'day-n':
				return `每隔 ${sday} 天的 ${stime}`;
			case 'hour':
				return `每小时的第 ${sminute} 分钟执行一次`;
			case 'week':
				return `每周 ${weekObj[sweek]} 的 ${stime}`;
			case 'month':
				return `每月 ${sday} 号 ${stime}`;
			case 'minute-n':
				return `每小时的第0分钟开始，每隔 ${sminute} 分钟执行一次`;
			case 'hour-n':
				return `每天0点开始，每隔 ${shour} 小时的第 ${sminute} 分钟执行一次`;
			case 'second-n':
				return `每隔 ${ssecond} 秒执行一次`;
			default:
				return '';
		}
	});

	// 任务类型改变 - 与动态表单组件保持一致
	const onTaskTypeChange = (value) => {
		// 如果选择的是当前类型，不做任何处理
		if (formData.sType === value) {
			return;
		}

		// 获取默认值
		const defaultData = getDefaultFormData();

		// 重置表单数据到默认值，但保留当前选择的任务类型
		Object.keys(formData).forEach((key) => {
			if (key !== 'sType') {
				formData[key] = defaultData[key];
			}
		});

		// 设置新的任务类型
		formData.sType = value;

		// 设置默认名称和初始值 - 与动态表单组件保持一致
		switch (value) {
			case 'rememory':
				formData.name = '释放内存';
				break;
			case 'sync_time':
				formData.name = '同步时间';
				break;
			case 'webshell':
				formData.name = '木马查杀';
				break;
			case 'logs':
				formData.name = '网站日志切割';
				break;
			case 'log_cleanup':
				formData.name = '定时清理日志';
				break;
			case 'toUrl':
				formData.name = '访问URL-[ http:// ]';
				formData.urladdress = 'http://';
				break;
			case 'to_post':
				formData.name = 'POST请求-[ http:// ]';
				formData.urladdress = 'http://';
				break;
		}
	};

	// URL输入处理 - 与动态表单组件保持一致
	const onUrlInput = (e) => {
		const value = e.detail.value || e.target.value;
		formData.urladdress = value;

		if (formData.sType === 'toUrl') {
			formData.name = `访问URL-[ ${value} ]`;
		} else if (formData.sType === 'to_post') {
			formData.name = `POST请求-[ ${value} ]`;
		}
	};

	// 更新表单数据
	const updateFormData = (newData) => {
		console.log('🔍 [调试] 更新表单数据:', newData);
		console.log('🔍 [调试] 更新前的formData:', JSON.stringify(formData, null, 2));
		Object.assign(formData, newData);
		console.log('🔍 [调试] 更新后的formData:', JSON.stringify(formData, null, 2));
	};

	// 初始化编辑数据
	const initEditData = (options) => {
		if (options?.mode === 'edit' && options?.data) {
			try {
				isEditMode.value = true;
				editTaskId.value = options?.id;

				const editData = JSON.parse(decodeURIComponent(options?.data));
				originalTaskType.value = editData.sType;

				// 填充表单数据
				Object.assign(formData, {
					sType: editData.sType,
					name: editData.rname || editData.name || '',
					sBody: editData.sBody || '',
					urladdress: editData.urladdress || '',
					user_agent: editData.user_agent || '',
					save: editData.save !== undefined && editData.save !== null ? editData.save.toString() : '',
					backupTo: editData.backupTo || '',
					datab_name: editData.datab_name || '',
					tables_name: editData.tables_name || '',
					user: editData.user || 'root',
					flock: editData.flock == '1' ? true : false,
					// 时间相关字段
					type: editData.type || 'day',
					week: editData.week || '1',
					hour: editData.where_hour ? editData.where_hour.toString() : editData.hour || '1',
					minute: editData.where_minute ? editData.where_minute.toString() : editData.minute || '30',
					second: editData.second || '5',
					where1: editData.where1 || '1',
					timeSet: editData.timeSet || '',
					timeType: editData.timeType || 'sday',
					// 网站备份相关字段
					sName: editData.sName || editData.more || '',
					siteName: editData.sName || editData.more || '', // 网站备份表单使用的字段
					// 数据库备份相关字段
					db_type: editData.db_type || '',
					table_list: editData.table_list || '',
					backup_mode: editData.backup_mode || 0,
					db_backup_path: editData.db_backup_path || '',
					// 其他字段
					start_time: editData.start_time || '',
					stop_time: editData.stop_time || '',
					cleanup_type: editData.cleanup_type || '',
					selected_log_types: editData.selected_log_types || [],
					zip_password: editData.zip_password || '',
					more: editData.more || '',
					split_type: editData.split_type || '0',
					split_value: editData.split_value || 5,
					save_local: editData.save_local || 0,
				});
			} catch (error) {
				console.error('解析编辑数据失败:', error);
				uni.showToast({
					icon: 'error',
					title: '数据解析失败',
				});
			}
		}
	};

	// Picker显示方法
	const showTaskTypePicker = () => {
		taskTypePicker.value?.open();
	};

	// Picker确认方法
	const onTaskTypeConfirm = (e) => {
		const selectedValue = e.value[0].value;
		onTaskTypeChange(selectedValue);
	};

	// 处理提交参数 - 与动态表单组件保持一致
	const handleSubmitParams = (data) => {
		const params = {
			// 基础必需参数
			name: data.name,
			test: data.test || '',
			sType: data.sType || 'toShell',
			sBody: data.sBody || '',
			sName: data.sName || '',
			backupTo: data.backupTo || '',
			save: data.save || '',
			urladdress: data.urladdress || '',
			save_local: data.save_local || '0',
			notice: noticeForm.notice || '0',
			notice_channel: noticeForm.notice_channel || '',
			datab_name: data.datab_name || '',
			tables_name: data.tables_name || '',
			keyword: noticeForm.keyword || '',
			flock: data.flock ? '1' : '0',
			version: data.version || '',
			user: data.user || 'root',
			stop_site: data.stop_site || '0',
			// 时间相关参数 - 从 formData 获取（CycleForm 组件会同步数据到 formData）
			type: data.type || 'day',
			week: data.week || '1',
			hour: data.hour || '1',
			minute: data.minute || '30',
			second: data.second || '5',
			where1: data.where1 || '1',
			timeSet: data.timeSet || '1',
			timeType: data.timeType || 'sday',
			// 其他字段
			user_agent: data.user_agent || '',
			db_type: data.db_type || '',
			table_list: data.table_list || '',
			backup_mode: data.backup_mode || 0,
			db_backup_path: data.db_backup_path || '',
			start_time: data.start_time || '',
			stop_time: data.stop_time || '',
			cleanup_type: data.cleanup_type || '',
			selected_log_types: data.selected_log_types || [],
			zip_password: data.zip_password || '',
			more: data.more || '',
			// 文件拆分和备份设置
			split_type: data.split_type || '0',
			split_value: data.split_value || 5,
		};

		// 只有在N秒模式下才传入second参数
		if (data.type !== 'second-n') {
			delete params.second;
		}

		return params;
	};

	// 提交表单的核心逻辑
	const submitForm = async () => {
		try {
			// 表单验证
			const errors = validateForm();
			if (errors.length > 0) {
				uni.showToast({
					icon: 'error',
					title: errors[0],
				});
				return;
			}

			submitting.value = true;
			uni.showLoading({ title: isEditMode.value ? '正在保存...' : '正在提交...' });

			const params = handleSubmitParams(formData);

			// 编辑模式需要添加 id 参数
			if (isEditMode.value) {
				params.id = editTaskId.value;
			}

			const res = isEditMode.value ? await modifyCrond(params) : await addCrontab(params);

			if (res.status) {
				uni.showToast({
					icon: 'success',
					title: res.msg || (isEditMode.value ? '保存成功' : '添加成功'),
				});
				// 延迟返回上一页
				setTimeout(() => {
					uni.navigateBack();
				}, 1500);
			} else {
				uni.showToast({
					icon: 'error',
					title: res.msg || (isEditMode.value ? '保存失败' : '添加失败'),
				});
			}
		} catch (error) {
			console.error('提交失败:', error);
			uni.showToast({
				icon: 'error',
				title: '提交失败，请检查表单',
			});
		} finally {
			submitting.value = false;
			uni.hideLoading();
		}
	};

	// 带节流的提交表单方法
	const handleSubmit = throttle(submitForm, 2000, 1);

	// 取消
	const handleCancel = () => {
		uni.navigateBack();
	};

	return {
		pageContainer,
		formRef,
		formData,
		rules,
		noticeForm,
		taskTypePicker,
		taskTypeOptions,
		disableName,
		disableTaskType,
		submitting,
		taskTypeLabel,
		showTaskTypePicker,
		onTaskTypeConfirm,
		onTaskTypeChange,
		onUrlInput,
		updateFormData,
		validateForm,
		handleSubmit,
		handleCancel,
		// 编辑相关
		isEditMode,
		editTaskId,
		originalTaskType,
		initEditData,
	};
};
