<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>APLog-Info.plist</key>
		<data>
		Po//BA5tM1BE58borc33LNpqXyw=
		</data>
		<key>Headers/APFileLog.h</key>
		<data>
		TzFJIQJZbF5XGq/hVt3OQZMhTYA=
		</data>
		<key>Headers/APLog.h</key>
		<data>
		CLtq/dozwrjT96kPW/nNpMi6JbQ=
		</data>
		<key>Headers/APLogIO.h</key>
		<data>
		niXfJWcmT1WmtZTTvkk8JlDfUOM=
		</data>
		<key>Headers/APLogMgr.h</key>
		<data>
		KCYAreW8HmtSds9eN4fLx3Pk5r4=
		</data>
		<key>Headers/APPrimitiveLog.h</key>
		<data>
		z/r3IO1cwY4QR/4zicGZt4b+Yfs=
		</data>
		<key>Info.plist</key>
		<data>
		8HgBzOkISnxkPpITVplmpgv5qIs=
		</data>
		<key>en.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			zmV6UqBSo6r1NOz798vd5O4zTBA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
	</dict>
	<key>files2</key>
	<dict>
		<key>APLog-Info.plist</key>
		<dict>
			<key>hash</key>
			<data>
			Po//BA5tM1BE58borc33LNpqXyw=
			</data>
			<key>hash2</key>
			<data>
			B2XyqSNoreUIQ8uDojzpys2o9qi8XuLbypPrkCR+l8w=
			</data>
		</dict>
		<key>Headers/APFileLog.h</key>
		<dict>
			<key>hash</key>
			<data>
			TzFJIQJZbF5XGq/hVt3OQZMhTYA=
			</data>
			<key>hash2</key>
			<data>
			rKTpdWkuBQA7K+Fi075DCX/8ZlPgw74898o643guX0o=
			</data>
		</dict>
		<key>Headers/APLog.h</key>
		<dict>
			<key>hash</key>
			<data>
			CLtq/dozwrjT96kPW/nNpMi6JbQ=
			</data>
			<key>hash2</key>
			<data>
			UiPBv2wBUspUA7j4CAD6he9s8SpkUIIhh8a1XYRDQvM=
			</data>
		</dict>
		<key>Headers/APLogIO.h</key>
		<dict>
			<key>hash</key>
			<data>
			niXfJWcmT1WmtZTTvkk8JlDfUOM=
			</data>
			<key>hash2</key>
			<data>
			b7aHNIY7oSnSNnhhKiSCvuyOH2OOhWqo+7AGCgD61XM=
			</data>
		</dict>
		<key>Headers/APLogMgr.h</key>
		<dict>
			<key>hash</key>
			<data>
			KCYAreW8HmtSds9eN4fLx3Pk5r4=
			</data>
			<key>hash2</key>
			<data>
			ffH9bWVKekx4k4vLPbtzG5ikluCeL0RVrdiOW7GtVjw=
			</data>
		</dict>
		<key>Headers/APPrimitiveLog.h</key>
		<dict>
			<key>hash</key>
			<data>
			z/r3IO1cwY4QR/4zicGZt4b+Yfs=
			</data>
			<key>hash2</key>
			<data>
			ku5mCCOQkHHjk2qjxgpg3blQPdZWuuRab+WvWJwpoCg=
			</data>
		</dict>
		<key>en.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			zmV6UqBSo6r1NOz798vd5O4zTBA=
			</data>
			<key>hash2</key>
			<data>
			kmHsztpgjvF0JW5f3HdMHm49z1M0CcG8OT1JDQHHE/E=
			</data>
			<key>optional</key>
			<true/>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
