// #ifdef APP-HARMONY
import { vibrateFunc } from '@/uni_modules/czh-vibrator';
// #endif

// 获取设备信息
export const getDevicesInfo = () => {
	return new Promise((resolve, reject) => {
		uni.getSystemInfo({
			success(res) {
				resolve({
					brand: res.deviceBrand,
					model: res.deviceModel,
				});
			},
			fail(err) {
				reject(err);
			},
		});
	});
};

// 校验字段是否为空
export const checkField = (field) => {
	if (field !== '' && field !== null && field !== undefined) {
		return false;
	} else {
		return true;
	}
};

/**
 * @description 字节转换，到指定单位结束
 * @param { number } bytes 字节数
 * @param { boolean } isUnit 是否显示单位
 * @param { number } fixed 小数点位置
 * @param { string } endUnit 结束单位
 * @returns { string }
 */
export const getByteUnit = (bytes = 0, isUnit = true, fixed = 2, endUnit = '') => {
	// 确保bytes是数字类型
	let newBytes = typeof bytes === 'string' ? parseFloat(bytes) || 0 : bytes;
	const c = 1024;
	const units = ['B', 'KB', 'MB', 'GB', 'TB'];

	for (let i = 0; i < units.length; i++) {
		const unit = units[i];
		// 对所有单位应用统一的格式化规则
		const formattedValue = fixed === 0 ? Math.round(newBytes) : parseFloat(newBytes.toFixed(fixed));

		if (endUnit) {
			if (unit === endUnit.trim()) {
				return isUnit ? `${formattedValue} ${unit}` : `${formattedValue}`;
			}
		} else if (newBytes < c || i === units.length - 1) {
			return isUnit ? `${formattedValue} ${unit}` : `${formattedValue}`;
		}
		newBytes /= c;
	}

	// 处理超出最大单位的情况，返回最大单位
	const lastUnit = units[units.length - 1];
	const formattedValue = fixed === 0 ? Math.round(newBytes) : parseFloat(newBytes.toFixed(fixed));

	return isUnit ? `${formattedValue} ${lastUnit}` : `${formattedValue}`;
};

/**
 * 触感反馈
 */
export const triggerVibrate = () => {
	// #ifdef APP-HARMONY
	vibrateFunc();
	return;
	// #endif

	uni.vibrateLong();

	const vibrateWithDelay = (type, delay) => {
		return new Promise((resolve) => {
			setTimeout(() => {
				uni.vibrateShort({ type });
				resolve();
			}, delay);
		});
	};

	vibrateWithDelay('medium', 200)
		.then(() => vibrateWithDelay('heavy', 150))
		.then(() => vibrateWithDelay('medium', 120));
};

/**
 * 申请相机权限
 * @returns {Promise<boolean>} 返回权限申请结果，true表示已授权，false表示被拒绝
 */
export const requestCameraPermission = () => {
	return new Promise((resolve, reject) => {
		// #ifdef APP-PLUS
		// 判断平台类型
		const isIOS = plus.os.name.toLowerCase() === 'ios';
		const isAndroid = plus.os.name.toLowerCase() === 'android';

		if (isAndroid) {
			try {
				// Android平台申请相机权限
				plus.android.requestPermissions(
					['android.permission.CAMERA'],
					(result) => {
						// 检查权限申请结果
						if (result.granted && result.granted.length > 0) {
							// 权限已授权
							resolve(true);
						} else {
							// 权限被拒绝
							reject(new Error('没有相机权限'));
						}
					},
					(error) => {
						// 权限申请过程中发生错误
						console.error('申请相机权限失败:', error);
						reject(error);
					},
				);
			} catch (error) {
				// 捕获其他可能的错误
				console.error('申请相机权限异常:', error);
				reject(error);
			}
		} else if (isIOS) {
			resolve(true);
		} else {
			// 其他平台，假设已授权
			resolve(true);
		}
		// #endif

		// #ifndef APP-PLUS
		// 非APP-PLUS平台（H5、小程序等），直接返回已授权
		resolve(true);
		// #endif
	});
};

/**
 * 获取标题
 * @param {string} mode 选择模式
 * @param {boolean} multiple 是否多选
 * @returns {string}
 */
const getTitle = (mode, multiple) => {
	const modeText = mode === 'file' ? '文件' : mode === 'folder' ? '文件夹' : '文件/文件夹';
	const multipleText = multiple ? '多选' : '单选';
	return `选择${modeText}（${multipleText}）`;
};

/**
 * 打开文件选择器
 * @param {string} mode 选择模式 'all', 'file', 'folder'
 * @param {boolean} multiple 是否多选
 */
export const openSelector = (mode, multiple) => {
	const url = `/pages/file-selector/index?mode=${mode}&multiple=${multiple}&title=${encodeURIComponent(
		getTitle(mode, multiple),
	)}`;
	uni.navigateTo({
		url: url,
	});
};
