//
//  TBScanUtils.h
//  TBScanSDK
//
//  Created by <PERSON> on 15/7/28.
//  Copyright (c) 2015 Taobao.com. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <UIKit/UIKit.h>
#import <Accelerate/Accelerate.h>
#import <CoreMedia/CoreMedia.h>
#import <CoreVideo/CoreVideo.h>

extern const float kGetBrightnessFailedValue; ///< 提取失败时返回此值

@interface TBScanUtils : NSObject

// iOS 每帧的数据中，包含光传感器的光度值。Wiki中的测光表此值范围 -6 ~ 16 .
+ (float)brightnessValueFromSampleBuffer:(CMSampleBufferRef)sampleBuffer;

+ (BOOL)cloudAutoFocusRangeRestrictionNear;

+(BOOL)isURLString:(NSString*)str;

+(BOOL)isValidUrl:(NSString*)inUrlString forHost:(NSString*)inHostStr;

+(UIImage*)grayImageFromCVImageBuffer:(CVImageBufferRef)imageBuffer;

+ (CVPixelBufferRef)deepCopyCVPixelBuffer:(CVPixelBufferRef)pixelBufferRef;

// remember to vImage_Buffer.data by free()
+ (vImage_Buffer)vImageBufferFromCVPixelBufferRef:(CVPixelBufferRef)pixelBuffer WithVImageCVImageFormatRef:(vImageCVImageFormatRef)cvImageFormatRef;

// this method will not free vImageBuffer.data, you need free it by yourself
+ (UIImage *)imageFromVImageBuffer:(vImage_Buffer)vImageBuffer;

// iphone6s 6～7 毫秒
+(UIImage *)imageFromCVPixelBufferRef:(CVPixelBufferRef)pixelBuffer;

// 很耗时，使用上面👆那个
+(UIImage*)dataFromSampleBuffer:(CMSampleBufferRef)nextBuffer isFrontCam:(BOOL)isFront;

+(NSString*)base64forData:(NSData*)theData;

+(CGRect)expandRect:(CGRect)rect LimitInBigRect:(CGRect)bigRect ExpandLength:(CGFloat)expandLength;

+ (BOOL)boolConfigForKey:(NSString *) key
                  config:(NSDictionary *) config
            defaultValue:(BOOL) defaultValue;

//+ (NSString *)stringForConfigKey:(NSString *)key;

+ (BOOL)boolForConfigKey:(NSString *)key defaultValue:(BOOL)defaultValue;

+ (BOOL)isEmptyString:(NSString *)string;

UIEdgeInsets AUGetWindowSafeAreaInsets_mp();
CGFloat AUCommonUIGetScreenWidthForPortrait_mp();
CGFloat AUCommonUIGetScreenHeightForPortrait_mp();
@end
