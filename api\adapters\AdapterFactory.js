import DomesticApiAdapter from './DomesticApiAdapter';
import InternationalApiAdapter from './InternationalApiAdapter';

/**
 * 适配器工厂类
 * 根据类型创建对应的适配器实例
 */
export default class AdapterFactory {
  /**
   * 获取适配器实例
   * @param {string} type 适配器类型 'domestic'|'international'
   * @returns {ApiAdapter} 适配器实例
   */
  static getAdapter(type) {
    switch (type) {
      case 'international':
        return new InternationalApiAdapter();
      case 'domestic':
      default:
        return new DomesticApiAdapter();
    }
  }
}
