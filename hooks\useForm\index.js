import { ref, defineComponent, h } from 'vue';
import UvForm from '@/uni_modules/uv-form/components/uv-form/uv-form.vue'
import UvFormItem from '@/uni_modules/uv-form/components/uv-form-item/uv-form-item.vue'
import UvInput from '@/uni_modules/uv-input/components/uv-input/uv-input.vue'
import UvSelect from '@/uni_modules/uv-picker/components/uv-picker/uv-picker.vue'
import UvSubsection from '@/uni_modules/uv-subsection/components/uv-subsection/uv-subsection.vue'
// import UvCheckbox from '@/uni_modules/uv-checkbox/components/uv-checkbox.vue'

/**
 * 表单钩子 - 用于生成可配置的表单组件
 * @param {Object} options - 表单配置选项
 * @param {Array} options.formItems - 表单项配置数组
 * @param {Object} options.labelOptions - 标签配置选项
 * @param {Object} options.initialValues - 表单初始值
 * @returns {Object} - 返回表单组件、表单数据和表单方法
 */
export default function useForm(options) {
  const {
    formItems = [],
    labelOptions = {
      position: 'left',
      width: 80
    },
    initialValues = {}
  } = options || {};

  // 创建表单数据模型
  const formModel = ref({ ...initialValues });
  
  // 创建表单规则
  const rules = ref({});

  // 表单引用
  const formRef = ref(null);

  // 生成规则
  formItems.forEach(item => {
    if (item.rules) {
      rules.value[item.prop] = item.rules;
    }
  });

  // 表单验证
  const validate = () => {
    return new Promise((resolve, reject) => {
      if (!formRef.value) {
        console.error('表单实例不存在');
        reject(new Error('表单实例不存在'));
        return;
      }
      
      try {
        // 等待验证结果
        formRef.value.validate().then(valid => {
          if (valid) {
            resolve({
              ...formModel.value,
              isValid: true
            });
          } else {
            reject({
              isValid: false,
              message: '表单验证失败',
              formData: formModel.value
            });
          }
        }).catch(error => {
          console.error('验证过程出错:', error);
          reject({
            isValid: false,
            message: error.message || '表单验证失败',
            formData: formModel.value,
            error: error
          });
        });
      } catch (error) {
        console.error('验证过程出错:', error);
        reject({
          isValid: false,
          message: error.message || '表单验证失败',
          formData: formModel.value,
          error: error
        });
      }
    });
  };

  // 重置表单
  const resetForm = () => {
    if (formRef.value) {
      formRef.value.resetFields();
      Object.keys(formModel.value).forEach(key => {
        formModel.value[key] = initialValues[key] !== undefined ? initialValues[key] : '';
      });
    }
  };

  // 设置表单值
  const setFormValues = (values) => {
    Object.keys(values).forEach(key => {
      formModel.value[key] = values[key];
    });
  };

  // 获取表单值
  const getFormValues = () => {
    return { ...formModel.value };
  };

  // 创建表单组件 - 使用h函数创建虚拟DOM
  const FormComponent = defineComponent({
    name: 'DynamicForm',
    setup() {
      // 更新表单值方法
      const updateModelValue = (prop, val) => {
        formModel.value[prop] = val;
      };

      // 渲染表单项
      const renderFormItem = (item) => {
        // 根据类型渲染不同的输入组件
        const props = item.props || {};
        
        switch (item.type) {
          case 'textarea':
            return h(UvInput, {
              type: 'textarea',
              modelValue: formModel.value[item.prop],
              'onUpdate:modelValue': (val) => updateModelValue(item.prop, val),
              placeholder: item.placeholder || `请输入${item.label}`,
              ...props
            });
          case 'radio':
            // 将选项转换为分段器需要的格式
            const list = item.options.map(option => option.label);
            // 根据当前值找到对应的索引
            const currentIndex = item.options.findIndex(option => option.value === formModel.value[item.prop]) || 0;
            return h(UvSubsection, {
              current: currentIndex,
              list,
              mode: 'subsection',
              activeColor: '#ffffff',
              inactiveColor: '#909399',
              customItemStyle: {
                backgroundColor: '#20a50a'
              },
              customStyle: {
                border: '1px solid #20a50a',
              },
              onChange: (index) => {
                const selectedOption = item.options[index];
                updateModelValue(item.prop, selectedOption.value);
              },
              ...props
            });
          case 'checkbox':
            return h(UvCheckbox, {
              modelValue: formModel.value[item.prop],
              'onUpdate:modelValue': (val) => updateModelValue(item.prop, val),
              options: item.options || [],
              ...props
            });
          case 'select':
            return h(UvSelect, {
              modelValue: formModel.value[item.prop],
              'onUpdate:modelValue': (val) => updateModelValue(item.prop, val),
              options: item.options || [],
              ...props
            });
          case 'date':
            return h(UvInput, {
              type: 'date',
              modelValue: formModel.value[item.prop],
              'onUpdate:modelValue': (val) => updateModelValue(item.prop, val),
              ...props
            });
          case 'custom':
            return item.render ? item.render(formModel.value, item) : null;
          default:
            return h(UvInput, {
              type: 'text',
              modelValue: formModel.value[item.prop],
              'onUpdate:modelValue': (val) => updateModelValue(item.prop, val),
              placeholder: item.placeholder || `请输入${item.label}`,
              ...props
            });
        }
      };

      return () => {
        return h('view', { style: 'padding: 40rpx 20rpx;' }, [
          h(UvForm, { 
            ref: formRef,
            labelPosition: labelOptions.position,
            labelWidth: labelOptions.width,
            rules: rules.value,
            model: formModel.value
          }, () => 
            formItems.map(item => 
              h(UvFormItem, { 
                label: item.label,
                prop: item.prop,
                rules: item.rules,
                ...item.props 
              }, () => [
                renderFormItem(item)
              ])
            )
          )
        ]);
      };
    }
  });

  return {
    FormComponent,
    formModel,
    rules,
    formRef,
    validate,
    resetForm,
    setFormValues,
    getFormValues
  };
} 