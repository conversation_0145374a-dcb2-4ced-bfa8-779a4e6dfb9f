/* 主题样式变量 */

// 全局过渡动画
page,
view,
text,
image,
button {
	transition:
		background-color 0.3s ease,
		color 0.3s ease,
		border-color 0.3s ease,
		box-shadow 0.3s ease;
}

// 主题变量
page {
	// 主题色
	--primary-color: #20a50a;

	// 背景色
	--bg-color: #f8f8f8;
	--bg-color-secondary: #ffffff;
	--bg-color-disabled: #f5f7fa;
	--bg-status-normal: #E0FFDF; // 正常状态背景色
	--bg-status-offline: #FFEBEB; // 离线状态背景色
	// 文本颜色
	--text-color-primary: #333333;
	--text-color-secondary: #666666;
	--text-color-tertiary: #999999;
	--text-color-light: #cccccc;
	--text-color-error: #f44336;

	// 边框颜色
	--border-color: #eeeeee;

	// 卡片阴影
	--box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

// 亮色主题变量 - 显式类名
.theme-light {
	// 主题色
	--primary-color: #20a50a;

	// 背景色
	--bg-color: #ffffff;
	--bg-color-secondary: #ededed;
	--bg-color-secondary-rgb: 237, 237, 237;
	--bg-color-disabled: #f5f7fa;

	// 文本颜色
	--text-color-primary: #333333;
	--text-color-secondary: #666666;
	--text-color-tertiary: #999999;
	--text-color-light: #cccccc;
	--text-color-error: #f44336;

	// 边框颜色
	--border-color: #efefef;

	// 卡片阴影
	--box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

	// 弹窗
	--dialog-bg-color: #ffffff;
}

// 暗色主题变量 - 显式类名
.theme-dark {
	// 主题色保持不变以保持品牌一致性
	--primary-color: #20a50a;

	// 背景色
	--bg-color: #1a1a1a;
	--bg-color-secondary: #2c2c2c;
	--bg-color-secondary-rgb: 44, 44, 44;
	--bg-color-disabled: #3a3a3a;

	// 文本颜色
	--text-color-primary: #ffffff;
	--text-color-secondary: #cccccc;
	--text-color-tertiary: #999999;
	--text-color-light: #aaaaaa;
	--text-color-error: #f44336;

	// 边框颜色
	--border-color: #3d3d3d;

	// 卡片阴影
	--box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.3);

	// 弹窗
	--dialog-bg-color: #2c2c2c;
}

/* 通用样式 */
page {
	background-color: var(--bg-color);
	color: var(--text-color-primary);
	transition:
		background-color 0.3s,
		color 0.3s;
}

/* 文本颜色 */
.text-primary {
	color: var(--text-color-primary);
}

.text-secondary {
	color: var(--text-color-secondary);
}

.text-tertiary {
	color: var(--text-color-tertiary);
}

.text-light {
	color: var(--text-color-light);
}

.text-bt-primary {
	color: var(--primary-color);
}

.text-error {
	color: var(--text-color-error);
}

/* 背景颜色 */
.bg-primary {
	background-color: var(--bg-color);
}

.bg-secondary {
	background-color: var(--bg-color-secondary);
}

.bg-status-normal {
	background-color: var(--bg-status-normal);
}

.bg-status-offline {
	background-color: var(--bg-status-offline);
}
/* 边框 */
.border {
	border: 1px solid var(--border-color);
}

.border-top {
	border-top: 1px solid var(--border-color);
}

.border-bottom {
	border-bottom: 1px solid var(--border-color);
}

/* 弹窗 */
.dialog-bg {
	background-color: var(--dialog-bg-color);
}

/* 卡片 */
.card {
	background-color: var(--bg-color-secondary);
	border-radius: 8px;
	box-shadow: var(--box-shadow);
	padding: 16px;
}
