// 共享表单样式 - 精致版本，支持暗黑模式
.form-group {
	margin-bottom: 32rpx;
	padding: 24rpx;
	background: var(--bg-color);
	border-radius: 20rpx;
	box-shadow: var(--box-shadow);
	border: 1px solid var(--border-color);
	transition: all 0.3s ease;

	&:last-child {
		margin-bottom: 0;
	}

	&:hover {
		box-shadow: var(--box-shadow);
		transform: translateY(-2rpx);
	}
}

.form-label-row {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 16rpx;

	text {
		font-size: 28rpx;
		font-weight: 600;
		color: var(--text-color-primary);
		position: relative;

		&::before {
			content: '';
			position: absolute;
			left: -12rpx;
			top: 50%;
			transform: translateY(-50%);
			width: 6rpx;
			height: 20rpx;
			background: var(--primary-color);
			border-radius: 3rpx;
		}
	}
}

.form-row {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 16rpx;
	padding: 16rpx 20rpx;
	background: var(--bg-color);
	border-radius: 16rpx;
	border: 1px solid var(--border-color);

	text {
		flex: 1;
		font-size: 28rpx;
		font-weight: 500;
		color: var(--text-color-primary);
	}
}

.input-wrapper {
	width: 100%;
}

.input-wrapper-small {
	width: 240rpx;
}

.form-input {
	width: 100%;
	height: 80rpx;
	padding: 0 24rpx;
	border: 1px solid var(--border-color);
	border-radius: 16rpx;
	font-size: 26rpx;
	font-weight: 500;
	color: var(--text-color-primary);
	background: var(--bg-color-secondary);
	box-shadow: var(--box-shadow);
	transition: all 0.3s ease;

	&:focus {
		border-color: var(--primary-color);
		box-shadow: 0 0 0 4rpx rgba(32, 165, 10, 0.1);
		outline: none;
		background: var(--bg-color-secondary);
	}

	&::placeholder {
		color: var(--text-color-tertiary);
		font-weight: 400;
	}
}

.textarea-wrapper {
	width: 100%;
}

.form-textarea {
	width: 100%;
	min-height: 200rpx;
	padding: 20rpx 24rpx;
	border: 1px solid var(--border-color);
	border-radius: 16rpx;
	font-size: 26rpx;
	font-weight: 400;
	color: var(--text-color-primary);
	background: var(--bg-color-secondary);
	box-shadow: var(--box-shadow);
	resize: vertical;
	line-height: 1.6;
	transition: all 0.3s ease;

	&:focus {
		border-color: var(--primary-color);
		box-shadow: 0 0 0 4rpx rgba(32, 165, 10, 0.1);
		outline: none;
		background: var(--bg-color-secondary);
	}

	&::placeholder {
		color: var(--text-color-tertiary);
		line-height: 1.6;
	}
}

.region-select-button {
	width: 100%;
	height: 80rpx;
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 0 24rpx;
	background: var(--bg-color);
	border: 1px solid var(--border-color);
	border-radius: 16rpx;
	transition: all 0.3s ease;

	&:active {
		transform: scale(0.98);
		box-shadow: var(--box-shadow);
	}

	&:disabled {
		border-color: var(--border-color-disabled);
		opacity: 0.6;
		cursor: not-allowed;

		&:active {
			transform: none;
			box-shadow: none;
		}

		text {
			color: var(--text-color-disabled);
		}
	}

	text {
		font-size: 26rpx;
		font-weight: 500;
		color: var(--text-color-primary);
		flex: 1;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
		text-align: left;
	}
}

.info-section {
	margin-top: 32rpx;
	padding: 20rpx 24rpx;
	background: var(--bg-color-secondary);
	border-radius: 16rpx;
	border-left: 4rpx solid var(--primary-color);

	text {
		display: block;
		font-size: 24rpx;
		color: var(--text-color-secondary);
		line-height: 1.6;
		margin-bottom: 12rpx;
		font-weight: 400;

		&:last-child {
			margin-bottom: 0;
		}
	}
}

.form-help {
	margin-top: 12rpx;
	padding: 16rpx 20rpx;
	background: var(--bg-color-secondary);
	border-radius: 12rpx;
	border: 1px solid var(--border-color);

	text {
		font-size: 22rpx;
		color: var(--text-color-secondary);
		line-height: 1.5;
		font-weight: 400;
	}
}
