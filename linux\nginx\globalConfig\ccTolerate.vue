<template>
	<view class="container">
		<view class="card">
			<view class="card-content">
				<view class="form-group">
					<view class="form-row">
						<text>访问时间</text>
						<view class="input-with-unit">
							<input
								type="number"
								v-model="formData.retry_cycle"
								placeholder="请输入访问时间"
								@blur="validateIntegerField('retry_cycle', '访问时间')"
								:class="{ 'input-error': errors.retry_cycle }"
							/>
							<text class="unit">秒</text>
						</view>
					</view>
					<text v-if="errors.retry_cycle" class="error-text">{{ errors.retry_cycle }}</text>
				</view>

				<view class="form-group">
					<view class="form-row">
						<text>攻击次数</text>
						<view class="input-with-unit">
							<input
								type="number"
								v-model="formData.retry"
								placeholder="请输入攻击次数"
								@blur="validateIntegerField('retry', '攻击次数')"
								:class="{ 'input-error': errors.retry }"
							/>
							<text class="unit">次</text>
						</view>
					</view>
					<text v-if="errors.retry" class="error-text">{{ errors.retry }}</text>
				</view>

				<view class="form-group">
					<view class="form-row">
						<text>封锁时间</text>
						<view class="input-with-unit">
							<input
								type="number"
								v-model="formData.retry_time"
								placeholder="请输入封锁时间"
								@blur="validateIntegerField('retry_time', '封锁时间')"
								:class="{ 'input-error': errors.retry_time }"
							/>
							<text class="unit">秒</text>
						</view>
					</view>
					<text v-if="errors.retry_time" class="error-text">{{ errors.retry_time }}</text>
				</view>

				<view class="info-section">
					<text
						>• 当IP {{ formData.retry_cycle }} 秒内累计请求超过URL超过 {{ formData.retry }} 次，封锁IP
						{{ formData.retry_time }} 秒</text
					>
				</view>
			</view>
			<view class="card-footer pt-20">
				<button class="submit-button" @click="submitForm">全局应用</button>
			</view>
		</view>
	</view>
</template>

<script setup>
	import { reactive, ref, onMounted } from 'vue';
	import { formPageContainer } from './useController.js';
	import { setNginxFirewallCcTolerate, getNginxFirewallGlobalConfig } from '@/api/nginx.js';

	// 表单数据
	const formData = reactive({
		retry_cycle: 120,
		retry: 20,
		retry_time: 1800,
		is_open_global: 1,
	});

	// 表单验证错误状态
	const errors = ref({
		retry_cycle: '',
		retry: '',
		retry_time: ''
	});

	/**
	 * 验证整数字段
	 * @param {string} field - 字段名
	 * @param {string} fieldName - 字段显示名称
	 */
	const validateIntegerField = (field, fieldName) => {
		const value = formData[field];

		// 清除之前的错误
		errors.value[field] = '';

		// 检查是否为空
		if (!value && value !== 0) {
			errors.value[field] = `${fieldName}不能为空`;
			return false;
		}

		// 检查是否为数字
		if (isNaN(value)) {
			errors.value[field] = `${fieldName}必须是数字`;
			return false;
		}

		// 检查是否为整数
		if (!Number.isInteger(Number(value))) {
			errors.value[field] = `${fieldName}必须是整数，不能包含小数点`;
			return false;
		}

		// 检查是否为正数
		if (Number(value) <= 0) {
			errors.value[field] = `${fieldName}必须大于0`;
			return false;
		}

		return true;
	};

	/**
	 * 验证所有表单字段
	 */
	const validateAllFields = () => {
		const retryCycleValid = validateIntegerField('retry_cycle', '访问时间');
		const retryValid = validateIntegerField('retry', '攻击次数');
		const retryTimeValid = validateIntegerField('retry_time', '封锁时间');

		return retryCycleValid && retryValid && retryTimeValid;
	};

	const submitForm = async () => {
		// 验证表单
		if (!validateAllFields()) {
			return;
		}

		// 这里可以实现表单提交逻辑
		try {
			const res = await setNginxFirewallCcTolerate(formData);
			if (res.status) {
				formPageContainer.value.notify.success(res.msg);
				setTimeout(() => {
					uni.navigateBack();
				}, 1000);
			} else {
				formPageContainer.value.notify.error(res.msg);
			}
		} catch (error) {
			console.log(error);
		}
	};

	const getGlobalConfig = async () => {
		const res = await getNginxFirewallGlobalConfig();
		formData.retry_cycle = res.retry_cycle;
		formData.retry = res.retry;
		formData.retry_time = res.retry_time;
	};

	onMounted(() => {
		getGlobalConfig();
	});
</script>

<style lang="scss" scoped>
	// 主题色变量
	$primary-color: #20a50a;
	$primary-light: lighten($primary-color, 45%);
	$text-color: var(--text-color-primary);
	$border-color: #ddd;
	$bg-color: var(--dialog-bg-color);
	$muted-color: #666;
	$light-bg: #f5f5f5;

	.container {
		padding: 20rpx;
	}

	.card {
		background: $bg-color;
		border-radius: 20rpx;
		box-shadow: 0 2rpx 16rpx rgba(0, 0, 0, 0.1);
		overflow: hidden;
	}

	.card-footer {
		padding: 30rpx;
		border-top: 1px solid $border-color;
	}

	.form-group {
		margin-bottom: 30rpx;

		&:last-child {
			margin-bottom: 0;
		}
	}

	.form-row {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 20rpx;

		text {
			flex: 1;
			font-size: 28rpx;
			color: $text-color;
		}

		input {
			color: $text-color;
		}
	}

	.input-with-unit {
		display: flex;
		width: 240rpx;

		input {
			width: 100%;
			height: 80rpx;
			padding: 0 20rpx;
			border: 1px solid $border-color;
			border-right: none;
			border-top-left-radius: 12rpx;
			border-bottom-left-radius: 12rpx;
			font-size: 28rpx;

			&.input-error {
				border-color: #e53935;
			}
		}

		.unit {
			display: flex;
			align-items: center;
			padding: 0 20rpx;
			background-color: $light-bg;
			border: 1px solid $border-color;
			border-top-right-radius: 12rpx;
			border-bottom-right-radius: 12rpx;
			font-size: 24rpx;
			color: $muted-color;
		}
	}

	.error-text {
		color: #e53935;
		font-size: 24rpx;
		margin-top: 8rpx;
		display: block;
	}

	.info-section {
		margin-top: 40rpx;

		text {
			display: block;
			font-size: 24rpx;
			color: $muted-color;
			line-height: 1.6;
			margin-bottom: 16rpx;

			&:last-child {
				margin-bottom: 0;
			}
		}
	}

	.submit-button {
		width: 100%;
		height: 88rpx;
		background-color: $primary-color;
		color: white;
		border: none;
		border-radius: 12rpx;
		font-size: 32rpx;
		font-weight: 500;

		&:active {
			background-color: darken($primary-color, 5%);
		}
	}

	// 适配暗黑模式
	@media (prefers-color-scheme: dark) {
		.container {
			background-color: #1a1a1a;
		}

		.card {
			background-color: #2a2a2a;
		}

		.input-with-unit input {
			background-color: #333;
			border-color: #444;
		}

		text {
			color: #fff;
		}

		.unit,
		.muted-text {
			color: #999;
		}
	}
</style>
