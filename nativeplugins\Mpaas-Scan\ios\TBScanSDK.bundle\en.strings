//Written by 多语言翻译管理系统 
//Sun Sep 18 2016 13:36:03 GMT+0800 (CST)
//如有问题请联系去疾
"IDCard二维码" = "QR Code";
"IDCard二维码/条形码" = "QR Code/Bar Code";
"IDCard关闭闪光灯" = "Turn off the Flashlight";
"IDCard取消" = "Cancel";
"IDCard相册" = "Album";
"IDCard将二维码放入扫描框内，即可自动扫描" = "Put QR Code within the frame to scan";
"IDCard将条码放入框内，即可自动扫描" = "Put Bar Code in the frame";
"IDCard打开闪光灯" = "Turn on the Flashlight";
"IDCard扫码" = "Code";
"IDCard支付宝无法处理微信的二维码" = "WeChat QR codes can't be processed";
"IDCard重新扫码" = "Scan again";
"IDCard请对准支付宝二维码" = "Please align Alipay QR code";
"IDCard扫描完成" = "Scan complete";
"IDCard正在准备打开" = "Now opening...";
"IDCard放入框内，自动扫描" = "Put QR/Bar Code within frame to scan";
"IDCard更多" = "More";
"IDCard条形码" = "Bar Code";
"IDCard正在加载..." = "loading...";
"IDCard确定" = "Confirm";
"IDCard确认" = "Confirm";
"IDCard返回" = "back";
"IDCard未获得授权使用摄像头" = "Alipay has no access to camera";
"IDCard请在iOS\"设置\"-\"隐私\"-\"相机\"中打开" = "Please enable camera access in \"Settings\"-\"Privacy\"-\"Camera\" on your iOS device";
"IDCard知道了" = "Got it";
"IDCard照片中未识别到二维码／条码" = "QR Code/barcode not found";
"IDCard轻点照亮" = "Turn on flashlight";
"IDCard轻点关闭" = "Turn off flashlight";
"IDCard未扫描到二维码？" = "No QR code found？";
"IDCard未扫描到二维码？获取帮助" = "No QR code found？Get Help";
"IDCard未扫描到二维码？点此反馈" = "No QR code found? Click to feedback";
"IDCard请对准二维码／条码，耐心等待" = "Align the QR code/barcode and wait";
"IDCard扫了很久没反应" = "No response after scan";
"IDCard相册图片里的码无法识别" = "Can't recognize QR code in album";
"IDCard二维码破损、涂改、移位" = "QR code damaged, modified or has been moved";
"IDCard扫描后跳转失败（白屏、错误页面）" = "Failed to redirect after scan (white screen, error page)";
"IDCard其他（打开黑屏等）" = "Others (open as blank screen)";
"IDCard无法连接网络，请检查网络设置" = "Network unavailable. Check your network settings";
"IDCard摄像头启动失败" = "Camera Failed to Launch";
"IDCard内存不足，摄像头临时关闭" = "Out of memory. Camera closed temporarily";
"IDCard无法连接网络" = "Network unavailable\nCheck your network settings";
"IDCard付款码" = "Pay";
"IDCard扫二维码/条码" = "Scan QR code/barcode";
"IDCard扫码遇到问题？告诉小宝" = "Problem scanning? Tell us";
"IDCard点击蓝点，选择要扫的码" = "Tap blue spot and select code";
