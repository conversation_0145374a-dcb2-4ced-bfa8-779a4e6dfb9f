import { vibrator } from '@kit.SensorServiceKit';
import { BusinessError } from '@kit.BasicServicesKit';

const effect = {
  type: 'preset',
  effectId: 'haptic.effect.hard',
  count: 1,
} as vibrator.VibrateEffect
const attribute = {
    id: 0,
    usage: 'simulateReality'
} as vibrator.VibrateAttribute

export const vibrateFunc = function (){
    try {
     vibrator.startVibration(effect as vibrator.VibrateEffect, attribute as vibrator.VibrateAttribute, (error: BusinessError) => {
       if (error) {
         console.error(`Failed to start vibration. Code: ${error.code}, message: ${error.message}`);
         return;
       }
     });
    } catch (err) {
     let e: BusinessError = err as BusinessError;
     console.error(`An unexpected error occurred. Code: ${e.code}, message: ${e.message}`);
    }
}