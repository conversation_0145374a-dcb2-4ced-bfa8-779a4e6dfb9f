import { isUndefined, isObject, isArray } from './type'

// 计算两个数之间的百分比
export const getPercentage = (count, number) => {
  return Math.round((number / count) * 1000) / 10; // 返回number 占 count的百分比
};

/**
 * 返回字符串 为n个char构成
 * @param {string} char 重复的字符
 * @param {number} count 次数
 * @return {string}
 */
export const getRepeatChar = (char, count) => {
  let str = '';
  while (count--) {
    str += char;
  }
  return str;
};

/**
 * @description 深拷贝
 * @param {object} obj 需要拷贝的对象
 * @returns {object} 拷贝后的对象
 */
export const deepClone = (obj) => {
  if (isUndefined(obj) || (!isObject(obj) && !isArray(obj))) {
    return obj
  }
  const newObj = Array.isArray(obj) ? [] : {}
  for (const key in obj) {
    newObj[key] = deepClone(obj[key])
  }
  return newObj
}