<template>
	<view class="install-container">
		<view class="header">
			<view class="alert">
				<i class="icon-warning"></i>
				<text>当前未安装Nginx，是否安装？</text>
			</view>
		</view>

		<view class="cards-container">
			<view class="card active">
				<view class="card-icon">
					<uni-icons fontFamily="iconfont" class="icon-nginx" size="24" color="#20a50a"></uni-icons>
				</view>
				<view class="card-content">
					<h3>Nginx Web服务器</h3>
					<p>轻量级，占有内存少，并发能力强(可选Tengine/openresty)。</p>
				</view>
				<view class="card-check">
					<text class="check-icon">✓</text>
				</view>
			</view>
		</view>

		<view class="actions">
			<button class="btn-install" @click="installNginx">点击安装Nginx</button>
		</view>
	</view>
</template>

<script setup>
	import { ref } from 'vue';

	const installNginx = () => {
		uni.navigateTo({
			url: '/linux/install/index?name=nginx',
			animationType: 'zoom-fade-out',
		});
	};
</script>

<style lang="scss" scoped>
	.install-container {
		padding: 40rpx;
		display: flex;
		flex-direction: column;
		gap: 40rpx;
		background-color: var(--page-bg-color);
	}

	.header {
		.alert {
			background-color: #fef3c7;
			border: 2rpx solid #f59e0b;
			border-radius: 16rpx;
			padding: 24rpx;
			display: flex;
			align-items: center;
			gap: 16rpx;

			.icon-warning {
				font-size: 32rpx;
				color: #f59e0b;
			}

			text {
				font-size: 28rpx;
				color: #92400e;
				font-weight: 500;
			}
		}
	}

	.cards-container {
		flex: 1;
		display: flex;
		flex-direction: column;
		gap: 24rpx;
	}

	.card {
		background-color: var(--dialog-bg-color);
		border-radius: 20rpx;
		padding: 32rpx;
		box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.1);
		border: 2rpx solid transparent;
		transition: all 0.3s ease;
		position: relative;

		&.active {
			border-color: #20a50a;
			background: linear-gradient(135deg, rgba(32, 165, 10, 0.05) 0%, rgba(32, 165, 10, 0.02) 100%);
		}

		.card-icon {
			width: 80rpx;
			height: 80rpx;
			background: linear-gradient(135deg, rgba(32, 165, 10, 0.15) 0%, rgba(32, 165, 10, 0.05) 100%);
			border-radius: 20rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			margin-bottom: 24rpx;
		}

		.card-content {
			h3 {
				font-size: 36rpx;
				font-weight: 600;
				color: var(--text-color-primary);
				margin-bottom: 16rpx;
			}

			p {
				font-size: 26rpx;
				color: var(--text-color-secondary);
				line-height: 1.6;
			}
		}

		.card-check {
			position: absolute;
			top: 24rpx;
			right: 24rpx;
			width: 48rpx;
			height: 48rpx;
			background-color: #20a50a;
			border-radius: 50%;
			display: flex;
			align-items: center;
			justify-content: center;

			.check-icon {
				color: white;
				font-size: 24rpx;
				font-weight: bold;
			}
		}
	}

	.actions {
		padding-top: 24rpx;

		.btn-install {
			width: 100%;
			height: 88rpx;
			background: linear-gradient(135deg, #20a50a 0%, #16a34a 100%);
			border: none;
			border-radius: 16rpx;
			color: white;
			font-size: 32rpx;
			font-weight: 600;
			display: flex;
			align-items: center;
			justify-content: center;
			box-shadow: 0 8rpx 24rpx rgba(32, 165, 10, 0.3);
			transition: all 0.3s ease;

			&:active {
				transform: translateY(2rpx);
				box-shadow: 0 4rpx 12rpx rgba(32, 165, 10, 0.3);
			}
		}
	}
</style>
