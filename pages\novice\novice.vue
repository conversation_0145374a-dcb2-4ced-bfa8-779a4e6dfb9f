<template>
	<page-container title="新手教程">
		<view class="content">
			<view v-if="type == 'server'">
				<view class="qianyan">
					<view class="title" style="margin-top: 0">App简介：</view>
					<view class="query">
						1、宝塔面板App支持宝塔Linux面板，宝塔Windows面板。请更新面板版本到最新版再进行绑定！</view
					>
					<view class="query"> 2、宝塔面板App属于一个快速迭代更新的产品，App可更新时请进行更新</view>
					<view class="query"> 3、强烈建议随时保持App最新版本</view>
					<view class="query" style="color: #20a53a; font-weight: 600" @click="clickGreen">
						4、绿色文字都是可点击的！！！</view
					>
				</view>

				<view class="title">如何扫码绑定服务器</view>

				<view class="query"
					>1、打开面板，点击菜单栏上的软件商店，找到堡塔App插件，点击名字即可，如下图所示。</view
				>

				<view class="img-one" @click="clickPreview(0)">
					<image src="@/static/novice/novice-one.png" mode="widthFix"></image>
				</view>

				<view class="query"
					>2、此时我们会看到二维码，然后可直接在app上进行扫码，扫码后，面板上会弹出二次确认哦，点确认授权即可完成绑定！</view
				>

				<view class="img-one" @click="clickPreview(1)">
					<image src="@/static/novice/novice-two.png" mode="widthFix"></image>
				</view>
        
				<uv-button type="success" @click="clickUse" shape="circle">开始使用App管理服务器</uv-button>
			</view>

			<view v-if="type == 'ios'">
				<view class="query"> 苹果手机绑定服务器失败解决方案 </view>
				<view class="query"
					>第一种方案：打开面板，点击菜单栏上的面板设置，在安全设置选项栏里，找到面板SSL，点击关闭，让按钮变灰即可，如下图所示。<text
						style="color: red"
						>如果您不想关闭面板SSL，请看第二种方案！</text
					></view
				>

				<view class="img-one" @click="clickPreview(2)">
					<image src="@/static/novice/novice-three.png" mode="widthFix"></image>
				</view>

				<view class="query"
					>第二种方案：打开面板，点击菜单栏上的面板设置，在安全设置选项栏里，找到免端口访问，点击免端口访问配置，配置非自签证书即可，如下图所示。</view
				>

				<view class="img-one" @click="clickPreview(3)">
					<image src="@/static/novice/novice-four.png" mode="widthFix"></image>
				</view>

				<view class="img-one" @click="clickPreview(4)">
					<image src="@/static/novice/novice-five.png" mode="widthFix"></image>
				</view>
			</view>

			<view v-if="type == 'message'">
				<view class="query">
					消息中心是您在浏览宝塔面板App时，App会去主动检测您当前选中的服务器，如下图，点进这个192.168.1.245服务器就视为选中，那么就会去检测这个服务器的状态。
				</view>

				<view class="img-one" @click="messageImg(0)">
					<image src="@/static/novice/selected.png" mode="widthFix"></image>
				</view>

				<view class="query">
					当检测到服务器异常，如CPU使用率过高、下行流量峰值不正常，服务器高负载等等，App就会主动提示哪个服务器什么不正常，此时即可去消息中心查看提示的消息。如下图所示
				</view>

				<view class="img-one" @click="messageImg(1)">
					<image src="@/static/novice/tips.png" mode="widthFix"></image>
				</view>
				<uv-button type="success" @click="clickUse" shape="circle">我明白了</uv-button>
			</view>

			<view v-if="type == 'auth'">
				<view class="query">
					动态口令与Google身份验证可起到相同作用，当您通过扫码绑定了口令时，那么在下次登录中，除了要输入账户密码外，还需要输入一次动态口令才能验证成功，绑定面板口令的步骤如下：
				</view>

				<view class="img-one" @click="checkImg(0)">
					<image src="@/static/novice/notive1.png" mode="widthFix"></image>
				</view>

				<view class="query">
					面板中出现了动态口令的绑定二维码，此时我们点击App的扫码按钮去进行扫码。添加口令后，下次登录即需要输入动态口令进行验证。如下图：
				</view>

				<view class="img-one" @click="checkImg(1)">
					<image src="@/static/novice/notive2.png" mode="widthFix"></image>
				</view>
				<uv-button type="success" @click="clickUse" shape="circle">我明白了</uv-button>
			</view>
		</view>
	</page-container>
</template>

<script setup>
	import PageContainer from '@/components/PageContainer/index.vue';
	import { onLoad } from '@dcloudio/uni-app';
	import { ref } from 'vue';

	const type = ref('');
	const tap = ref('not'); // mixin

	// 使用import.meta.url方式引入图片
	const images = [
		'/static/novice/novice-one.png',
		'/static/novice/novice-two.png',
		'/static/novice/novice-three.png',
		'/static/novice/novice-four.png',
		'/static/novice/novice-five.png',
	];

	const messageImages = ['/static/novice/selected.png', '/static/novice/tips.png'];
	const checkImages = ['/static/novice/notive1.png', '/static/novice/notive2.png'];

	onLoad((options) => {
		type.value = options.type;
	});

	const clickUse = () => {
		uni.navigateBack({
			delta: 1,
		});
	};

	const clickGreen = () => {
		uni.$utils.showToast('对，绿色文字点了都是有效果的', 2000);
	};

	const checkImg = (index) => {
		uni.previewImage({
			urls: checkImages,
			current: index,
		});
	};

	const messageImg = (index) => {
		uni.previewImage({
			urls: messageImages,
			current: index,
		});
	};

	const clickPreview = (index) => {
		uni.previewImage({
			urls: images,
			current: index,
		});
	};
</script>

<style>
	.content {
		padding: 40rpx;
	}

	.qianyan view {
		font-size: 30rpx;
		padding-top: 15rpx;
		color: var(--text-color-secondary);
	}

	.img-one {
		margin-bottom: 20rpx;
	}

	.title {
		font-size: 30rpx;
		line-height: 52rpx;
		color: var(--text-color-primary);
		font-weight: 600;
		margin-top: 40rpx;
		margin-bottom: 20rpx;
	}

	.query {
		font-size: 28rpx;
		color: var(--text-color-secondary);
		line-height: 48rpx;
		margin-bottom: 20rpx;
		padding-left: 10rpx;
	}

	.use-app {
		width: 80%;
		background-color: #20a53a;
		height: 80rpx;
		border-radius: 40rpx;
		align-items: center;
		justify-content: center;
		font-size: 32rpx;
		color: #fff;
		margin: 10% auto;
	}
</style>
