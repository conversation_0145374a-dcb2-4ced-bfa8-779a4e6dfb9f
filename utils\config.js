import { ref } from 'vue';
import CryptoJS from 'crypto-js/crypto-js';

const PATH = ref('');
const UUID = ref('');
const SECRET = ref('');
const AESKEY = ref('');
const SYSTEM = ref('');

// aes加密
const encrypt = (content, key) => {
  let sKey = CryptoJS.enc.Utf8.parse(key);

  let sContent = CryptoJS.enc.Utf8.parse(content);

  let encrypted = CryptoJS.AES.encrypt(sContent, sKey, {
    mode: CryptoJS.mode.ECB,
    padding: CryptoJS.pad.ZeroPadding,
  });

  return encrypted.toString();
};

// aes解密
const decrypt = (content, key) => {
  let sKey = CryptoJS.enc.Utf8.parse(key);

  let decrypt = CryptoJS.AES.decrypt(content, sKey, {
    mode: CryptoJS.mode.ECB,
    padding: CryptoJS.pad.ZeroPadding,
  });

  return CryptoJS.enc.Utf8.stringify(decrypt).toString();
};

export { PATH, UUID, SECRET, AESKEY, SYSTEM, encrypt, decrypt };
