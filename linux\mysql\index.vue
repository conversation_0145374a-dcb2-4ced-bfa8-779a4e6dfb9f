<template>
	<page-container ref="pageContainer" :is-back="true" title="MySQL环境管理">
		<view class="mysql-env-container" v-if="isMySQLInstalled">
			<!-- 服务状态卡片 -->
			<view class="status-card">
				<view class="status-header">
					<view class="status-info">
						<text class="status-title">MySQL服务</text>
						<text class="status-version">{{ serviceStatus.version }}</text>
					</view>
					<view class="status-control">
						<text class="status-badge" :class="serviceStatus.isRunning ? 'badge-success' : 'badge-danger'">
							{{ serviceStatus.isRunning ? '运行中' : '已停止' }}
						</text>
						<uv-switch
							:model-value="serviceStatus.isRunning"
							size="24"
							activeColor="#20a50a"
							:loading="serviceLoading"
							:disabled="!!currentExecutingActionId"
							@change="toggleService"
						></uv-switch>
					</view>
				</view>

				<!-- 快速操作按钮 -->
				<view class="quick-actions">
					<view
						v-for="(action, index) in quickActions"
						:key="index"
						class="action-btn"
						:class="{
							'action-disabled': serviceLoading || (currentExecutingActionId && currentExecutingActionId !== action.id),
						}"
						@tap="handleQuickAction(action)"
						:hover-class="(serviceLoading || currentExecutingActionId) ? '' : 'action-hover'"
					>
						<uv-loading-icon
							v-if="currentExecutingActionId === action.id"
							mode="spinner"
							size="32rpx"
							color="#20a50a"
						></uv-loading-icon>
						<text class="action-text" :class="{ 'loading-text': currentExecutingActionId === action.id }">
							{{ currentExecutingActionId === action.id ? '执行中...' : action.title }}
						</text>
					</view>
				</view>
			</view>

			<!-- 存储位置配置 -->
			<view class="config-card">
				<view class="config-header">
					<view class="config-icon">
						<uv-icon name="folder" size="24" color="#20a50a"></uv-icon>
					</view>
					<text class="config-title">存储位置</text>
				</view>
				<view class="config-content flex">
					<uv-input
						v-model="mysqlInfo.datadir"
						placeholder="/www/server/data"
						border="surround"
						:customStyle="{ backgroundColor: 'var(--dialog-bg-color)', fontSize: '28rpx' }"
					>
						<template v-slot:suffix>
							<view
								class="config-folder-button"
								:class="{ 'config-disabled': storageLoading }"
								@click="selectStoragePath"
							>
								<uv-icon name="folder" size="16" color="#ffffff"></uv-icon>
							</view>
						</template>
					</uv-input>
					<view class="config-button-wrapper">
						<uv-button class="ml-10" type="success" :disabled="storageLoading" @click="saveStorageConfig">
							<uv-loading-icon
								v-if="storageLoading"
								mode="spinner"
								size="32rpx"
								color="#ffffff"
							></uv-loading-icon>
							<text :class="{ 'loading-text': storageLoading }">
								{{ storageLoading ? '迁移中...' : '迁移' }}
							</text>
						</uv-button>
					</view>
				</view>
				<!-- 风险提示 -->
				<view class="risk-warning">
					<text class="warning-text">• 风险提示：迁移过程中Mysql将会停止运行，请谨慎操作</text>
					<text class="warning-text">• 风险提示：迁移过程中请等待迁移完成后再进行其他操作</text>
				</view>
			</view>

			<!-- 端口配置 -->
			<view class="config-card">
				<view class="config-header">
					<view class="config-icon">
						<uv-icon name="list" size="24" color="#20a50a"></uv-icon>
					</view>
					<text class="config-title">端口配置</text>
				</view>
				<view class="config-content flex">
					<uv-input
						v-model="mysqlInfo.port"
						placeholder="3306"
						border="surround"
						type="number"
						:customStyle="{ backgroundColor: 'var(--dialog-bg-color)', fontSize: '28rpx' }"
						@input="handlePortInput"
						@change="handlePortChange"
						@blur="handlePortBlur"
					>
					</uv-input>
					<view class="config-button-wrapper">
						<uv-button class="ml-10" type="success" :disabled="portLoading || !!portError" @click="savePortConfig">
							<uv-loading-icon
								v-if="portLoading"
								mode="spinner"
								size="32rpx"
								color="#ffffff"
							></uv-loading-icon>
							<text :class="{ 'loading-text': portLoading }">
								{{ portLoading ? '修改中...' : '修改' }}
							</text>
						</uv-button>
					</view>
				</view>
				<!-- 端口错误提示 -->
				<view v-if="portError" class="port-error-message">
					<text class="error-text">{{ portError }}</text>
				</view>
			</view>

			<!-- 功能模块 -->
			<view class="function-modules">
				<view v-for="(module, index) in functionModules" :key="index" class="module-card">
					<view class="module-header" @tap="handleModuleClick(module)">
						<view class="module-icon">
							<uv-icon :name="module.icon" size="24" color="#20a50a"></uv-icon>
						</view>
						<text class="module-title">{{ module.title }}</text>
						<!-- 可跳转模块的向右箭头 -->
						<view v-if="module.navigatable" class="navigate-icon">
							<uv-icon name="arrow-right" size="16" color="var(--text-color-secondary)"></uv-icon>
						</view>
					</view>
					<view class="module-desc">{{ module.desc }}</view>
					<view v-if="module.showData" class="module-data" @tap="handleModuleClick(module)">
						<view class="data-content">
							<text class="data-value">{{ module.dataValue }}</text>
							<text class="data-unit">{{ module.dataUnit }}</text>
						</view>
					</view>
				</view>
			</view>
		</view>

		<!-- MySQL未安装提示 -->
		<install v-else />

		<!-- 快速操作确认对话框 -->
		<CustomDialog
			v-model="showConfirmDialog"
			:title="confirmDialogConfig.title"
			:confirmText="confirmDialogConfig.confirmText"
			cancelText="取消"
			contentHeight="200rpx"
			@confirm="confirmQuickAction"
			@cancel="cancelQuickAction"
			:confirmStyle="{
				backgroundColor: confirmDialogConfig.action?.id === 'stop' ? '#FF3B30' : '#20a50a',
			}"
		>
			<view class="text-secondary flex justify-center items-center h-full">
				{{ confirmDialogConfig.content }}
			</view>
		</CustomDialog>
	</page-container>
</template>

<script setup>
	import PageContainer from '@/components/PageContainer/index.vue';
	import CustomDialog from '@/components/CustomDialog/index.vue';
	import install from './install.vue';
	import { onShow } from '@dcloudio/uni-app';
	import {
		pageContainer,
		serviceStatus,
		serviceLoading,
		currentExecutingActionId,
		portLoading,
		storageLoading,
		mysqlInfo,
		functionModules,
		quickActions,
		isMySQLInstalled,
		toggleService,
		handleModuleClick,
		handleQuickAction,
		initMySQLData,
		// 确认对话框相关
		showConfirmDialog,
		confirmDialogConfig,
		confirmQuickAction,
		cancelQuickAction,
		// 配置相关
		selectStoragePath,
		savePortConfig,
		saveStorageConfig,
		// 编辑状态相关
		storageEditMode,
		portEditMode,
		toggleStorageEditMode,
		togglePortEditMode,
		// 临时路径状态
		tempStoragePath,
		hasTempStoragePath,
		resetTempStoragePath,
		// 端口验证相关
		portError,
		handlePortInput,
		handlePortBlur,
		handlePortChange,
		validatePort,
	} from './useController.js';

	onShow(async () => {
		await initMySQLData();
	});
</script>

<style lang="scss" scoped>
	.mysql-env-container {
		padding: 24rpx;
		display: flex;
		flex-direction: column;
		gap: 24rpx;
		background-color: var(--page-bg-color);
	}

	/* 服务状态卡片 */
	.status-card {
		background-color: var(--dialog-bg-color);
		border-radius: 20rpx;
		padding: 32rpx;
		box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.1);
		border: 2rpx solid rgba(32, 165, 10, 0.1);
	}

	.status-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 24rpx;
	}

	.status-info {
		.status-title {
			font-size: 32rpx;
			font-weight: 600;
			color: var(--text-color-primary);
			display: block;
		}

		.status-version {
			font-size: 24rpx;
			color: var(--text-color-secondary);
			display: block;
			margin-top: 8rpx;
		}
	}

	.status-control {
		display: flex;
		align-items: center;
		gap: 24rpx;
	}

	.status-badge {
		font-size: 24rpx;
		font-weight: 500;
		padding: 8rpx 16rpx;
		border-radius: 20rpx;

		&.badge-success {
			background-color: #dcfce7;
			color: #16a34a;
		}

		&.badge-danger {
			background-color: #fee2e2;
			color: #dc2626;
		}
	}

	/* 快速操作按钮 */
	.quick-actions {
		display: flex;
		justify-content: space-around;
		gap: 24rpx;
		margin-top: 24rpx;
		padding-top: 24rpx;
		border-top: 2rpx solid var(--border-color);
	}

	.action-btn {
		display: flex;
		flex-direction: row;
		align-items: center;
		justify-content: center;
		padding: 20rpx 16rpx;
		border-radius: 12rpx;
		background-color: transparent;
		border: 2rpx solid rgba(32, 165, 10, 0.3);
		transition: all 0.2s ease;
		flex: 1;
		gap: 12rpx;

		.action-text {
			font-size: 24rpx;
			color: var(--text-color-primary);
			font-weight: 500;
		}
	}

	.action-hover {
		background-color: rgba(32, 165, 10, 0.1);
		border-color: rgba(32, 165, 10, 0.6);
		transform: translateY(-2rpx);
	}

	/* 停止按钮特殊样式 */
	.action-btn.stop-btn {
		border-color: rgba(220, 38, 38, 0.3);
		background-color: rgba(220, 38, 38, 0.05);
	}

	.action-btn.stop-btn.action-hover {
		border-color: rgba(220, 38, 38, 0.6);
		background-color: rgba(220, 38, 38, 0.1);
	}

	/* 禁用状态样式 */
	.action-btn.action-disabled {
		opacity: 0.8;
		pointer-events: none;
		background-color: rgba(32, 165, 10, 0.1);
		border-color: rgba(32, 165, 10, 0.3);
		box-shadow: 0 2rpx 8rpx rgba(32, 165, 10, 0.15);
	}

	.action-btn.action-disabled .action-text {
		color: rgba(32, 165, 10, 0.8);
	}

	.loading-text {
		margin-left: 16rpx;
		font-weight: 500;
		font-size: 26rpx;
	}

	/* 配置卡片 */
	.config-card {
		background-color: var(--dialog-bg-color);
		border-radius: 20rpx;
		padding: 32rpx;
		box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
		border: 2rpx solid rgba(0, 0, 0, 0.05);
	}

	.config-header {
		display: flex;
		align-items: center;
		gap: 16rpx;
		margin-bottom: 24rpx;

		.config-icon {
			width: 52rpx;
			height: 52rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			background: linear-gradient(135deg, rgba(32, 165, 10, 0.15) 0%, rgba(32, 165, 10, 0.05) 100%);
			border-radius: 16rpx;
			border: 2rpx solid rgba(32, 165, 10, 0.1);
		}

		.config-title {
			font-size: 32rpx;
			font-weight: 600;
			color: var(--text-color-primary);
		}
	}

	.config-content {
		position: relative;
	}

	/* 配置操作按钮容器 */
	.config-action-buttons {
		display: flex;
		align-items: center;
		gap: 8rpx;
	}

	/* 编辑按钮样式 */
	.config-edit-button {
		width: 56rpx;
		height: 56rpx;
		background: linear-gradient(135deg, rgba(32, 165, 10, 0.1) 0%, rgba(32, 165, 10, 0.05) 100%);
		border: 2rpx solid rgba(32, 165, 10, 0.3);
		border-radius: 12rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		cursor: pointer;
		transition: all 0.2s ease;

		&:hover {
			background: linear-gradient(135deg, rgba(32, 165, 10, 0.15) 0%, rgba(32, 165, 10, 0.08) 100%);
			border-color: rgba(32, 165, 10, 0.5);
			transform: translateY(-2rpx);
		}

		&:active {
			transform: translateY(0);
		}
	}

	/* 保存按钮样式 */
	.config-save-button {
		width: 56rpx;
		height: 56rpx;
		background: linear-gradient(135deg, #20a50a 0%, #16a085 100%);
		border-radius: 12rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		cursor: pointer;
		transition: all 0.2s ease;
		box-shadow: 0 4rpx 12rpx rgba(32, 165, 10, 0.3);

		&:hover {
			transform: translateY(-2rpx);
			box-shadow: 0 6rpx 16rpx rgba(32, 165, 10, 0.4);
		}

		&:active {
			transform: translateY(0);
		}
	}

	/* 文件夹选择按钮样式 */
	.config-folder-button {
		width: 56rpx;
		height: 56rpx;
		background: #20a50a;
		border-radius: 12rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		cursor: pointer;
		transition: all 0.2s ease;
	}

	/* 禁用状态的文件夹按钮 */
	.config-folder-button.config-disabled {
		opacity: 0.6;
		pointer-events: none;
		background: rgba(32, 165, 10, 0.6);
	}

	/* 配置按钮包装器 */
	.config-button-wrapper {
		display: flex;
		align-items: center;
	}

	/* 配置按钮 loading 状态增强 */
	.uv-button[disabled] {
		opacity: 0.8 !important;
		box-shadow: 0 2rpx 8rpx rgba(32, 165, 10, 0.2) !important;
	}

	/* Loading 状态脉冲动画 */
	.action-btn.action-disabled {
		animation: loading-pulse 2s ease-in-out infinite;
	}

	@keyframes loading-pulse {
		0%,
		100% {
			box-shadow: 0 2rpx 8rpx rgba(32, 165, 10, 0.15);
		}
		50% {
			box-shadow: 0 4rpx 16rpx rgba(32, 165, 10, 0.25);
		}
	}

	/* 按钮组样式 */
	.config-button-group {
		display: flex;
		align-items: center;
		gap: 8rpx;
	}

	/* 功能模块 */
	.function-modules {
		display: flex;
		flex-direction: column;
		gap: 16rpx;
	}

	.module-card {
		background-color: var(--dialog-bg-color);
		border-radius: 20rpx;
		padding: 28rpx;
		box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
		border: 2rpx solid rgba(0, 0, 0, 0.05);
	}

	.module-header {
		display: flex;
		align-items: center;
		gap: 16rpx;
		margin-bottom: 16rpx;
		cursor: pointer;

		.module-icon {
			width: 52rpx;
			height: 52rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			background: linear-gradient(135deg, rgba(32, 165, 10, 0.15) 0%, rgba(32, 165, 10, 0.05) 100%);
			border-radius: 16rpx;
			border: 2rpx solid rgba(32, 165, 10, 0.1);
		}

		.module-title {
			font-size: 32rpx;
			font-weight: 600;
			color: var(--text-color-primary);
			flex: 1;
		}

		.navigate-icon {
			margin-left: auto;
			display: flex;
			align-items: center;
			justify-content: center;
			transition: all 0.2s ease;
		}
	}

	.module-desc {
		font-size: 26rpx;
		color: var(--text-color-secondary);
		margin-bottom: 20rpx;
		line-height: 1.5;
	}

	.module-data {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 20rpx;
		background: linear-gradient(135deg, rgba(32, 165, 10, 0.05) 0%, rgba(32, 165, 10, 0.02) 100%);
		border-radius: 12rpx;
		border: 2rpx solid rgba(32, 165, 10, 0.1);
		cursor: pointer;
		transition: all 0.2s ease;

		&:hover {
			background: linear-gradient(135deg, rgba(32, 165, 10, 0.08) 0%, rgba(32, 165, 10, 0.04) 100%);
			border-color: rgba(32, 165, 10, 0.2);
		}

		.data-content {
			display: flex;
			align-items: baseline;
			gap: 8rpx;
		}

		.data-value {
			font-size: 28rpx;
			font-weight: 600;
			color: #20a50a;
		}

		.data-unit {
			font-size: 24rpx;
			color: var(--text-color-secondary);
		}
	}

	/* 风险提示样式 */
	.risk-warning {
		margin-top: 20rpx;
		padding: 20rpx;
		background: linear-gradient(135deg, rgba(128, 128, 128, 0.05) 0%, rgba(128, 128, 128, 0.02) 100%);
		border: 2rpx solid rgba(128, 128, 128, 0.2);
		border-radius: 12rpx;

		.warning-text {
			display: block;
			font-size: 26rpx;
			color: #666666;
			line-height: 1.6;
			margin-bottom: 8rpx;

			&:last-child {
				margin-bottom: 0;
			}
		}
	}

	/* 端口错误图标样式 */
	.port-error-icon {
		position: absolute;
		right: 10rpx;
		top: 50%;
		transform: translateY(-50%);
		z-index: 10;
	}

	/* 端口错误信息样式 */
	.port-error-message {
		margin-top: 16rpx;
		padding: 16rpx 20rpx;
		background: linear-gradient(135deg, rgba(255, 59, 48, 0.08) 0%, rgba(255, 59, 48, 0.04) 100%);
		border: 2rpx solid rgba(255, 59, 48, 0.2);
		border-radius: 12rpx;
		display: flex;
		align-items: center;
		gap: 12rpx;

		.error-text {
			font-size: 26rpx;
			color: #FF3B30;
			font-weight: 500;
			line-height: 1.4;
		}
	}
</style>
