<template>
	<page-container ref="pageContainer" :is-back="true" title="NGINX防火墙">
		<view class="nginx-firewall" v-if="isFirewallInstalled">
			<!-- 主内容 -->
			<view class="main-content">
				<!-- 防火墙开关 -->
				<view class="card firewall-switch">
					<view class="switch-info">
						<text class="switch-title text-primary">防火墙开关</text>
						<text class="switch-desc text-secondary">控制全局防火墙状态</text>
					</view>
					<view class="switch-control">
						<text class="badge" :class="isFirewallOn ? 'badge-success' : 'badge-danger'">
							{{ isFirewallOn ? '已启用' : '已禁用' }}
						</text>
						<uv-switch
							:model-value="isFirewallOn"
							size="24"
							activeColor="#20a50a"
							:loading="firewallLoading"
							@change="switchChange"
						></uv-switch>
					</view>
				</view>

				<!-- 分类区域 -->
				<view v-for="(category, index) in categories" :key="index" class="category-section">
					<view class="category-header" @tap="toggleCategory(category.id)">
						<view class="category-title">
							<view v-if="category.useIconfont" class="icon-container">
								<text
									:class="['iconfont', category.icon]"
									:style="{ color: getColorByClass(category.iconClass) }"
								></text>
							</view>
							<uv-icon
								v-else
								:name="getIconName(category.icon)"
								:color="getColorByClass(category.iconClass)"
								size="20"
							></uv-icon>
							<text class="category-heading text-primary">{{ category.title }}</text>
						</view>
						<uv-icon
							name="arrow-down"
							class="category-arrow-icon"
							:class="{ 'icon-rotated': openCategories[category.id] }"
							size="16"
						></uv-icon>
					</view>

					<view class="category-content" :class="{ 'category-content--open': openCategories[category.id] }">
						<!-- 安全概览 -->
						<view v-if="category.id === 'overview'" class="metrics-grid metrics-grid-2">
							<view
								v-for="(metric, mIndex) in securityMetrics"
								:key="mIndex"
								class="metric-card"
								:class="metric.colorClass"
							>
								<view class="metric-header">
									<view v-if="metric.useIconfont" class="icon-container">
										<text
											:class="['iconfont', metric.icon]"
											:style="{ color: getColorByClass(metric.iconClass) }"
										></text>
									</view>
									<uv-icon
										v-else
										:name="getIconName(metric.icon)"
										:color="getColorByClass(metric.iconClass)"
										size="20"
									></uv-icon>
									<text class="metric-title text-primary">{{ metric.title }}</text>
								</view>
								<text class="metric-value text-primary">
									{{ metric.count }}
									<text class="metric-unit text-secondary">{{ metric.unit }}</text>
								</text>
							</view>
						</view>

						<!-- 拦截类型 -->
						<view v-if="category.id === 'blockTypes'" class="metrics-grid metrics-grid-3">
							<view
								v-for="(type, tIndex) in blockTypes"
								:key="tIndex"
								class="block-type-card"
								@tap="showBlockTypeDetails(type)"
								hover-class="card-hover"
							>
								<text class="block-title text-primary">{{ type.name }}</text>
								<text class="block-count text-primary">{{ type.value }}</text>
							</view>
						</view>

						<!-- 管理功能 -->
						<view v-if="category.id === 'management'" class="metrics-grid metrics-grid-3">
							<view
								v-for="(feature, fIndex) in managementFeatures"
								:key="fIndex"
								class="feature-card"
								@tap="navigateToFeature(feature)"
								hover-class="card-hover"
							>
								<view v-if="feature.useIconfont" class="icon-container">
									<text
										:class="['iconfont', feature.icon]"
										:style="{ color: getColorByClass(feature.iconClass) }"
									></text>
								</view>
								<uv-icon
									v-else
									:name="getIconName(feature.icon)"
									:color="getColorByClass(feature.iconClass)"
									size="20"
								></uv-icon>
								<text class="feature-title text-primary">{{ feature.title }}</text>
							</view>
						</view>

						<!-- 设置选项 -->
						<view v-if="category.id === 'settings'" class="metrics-grid metrics-grid-2">
							<view
								v-for="(option, oIndex) in settingsOptions"
								:key="oIndex"
								class="setting-card"
								@tap="navigateToSetting(option)"
								hover-class="card-hover"
							>
								<view v-if="option.useIconfont" class="icon-container">
									<text
										:class="['iconfont', option.icon]"
										:style="{ color: getColorByClass(option.iconClass) }"
									></text>
								</view>
								<uv-icon
									v-else
									:name="getIconName(option.icon)"
									:color="getColorByClass(option.iconClass)"
									size="20"
								></uv-icon>
								<text class="setting-title">{{ option.title }}</text>
							</view>
						</view>
					</view>
				</view>

				<!-- 注意事项 -->
				<view class="notice-card">
					<view class="notice-header">
						<text class="iconfont icon-insecurity" style="color: #f59e0b; font-size: 20px"></text>
						<text class="notice-title">注意事项</text>
					</view>
					<text class="notice-desc">当前防火墙开关为总开关，关闭后全部站点将会失去保护，请谨慎操作</text>
				</view>
			</view>
		</view>
		<install v-else />
	</page-container>
</template>

<script setup>
	import PageContainer from '@/components/PageContainer/index.vue';
	import install from './install.vue';
	import { onShow } from '@dcloudio/uni-app';
	import {
		isFirewallOn,
		openCategories,
		categories,
		securityMetrics,
		blockTypes,
		managementFeatures,
		settingsOptions,
		toggleCategory,
		switchChange,
		showBlockTypeDetails,
		navigateToFeature,
		navigateToSetting,
		getIconName,
		getColorByClass,
		isFirewallInstalled,
		isNginxFirewallInstalled,
		getNginxFirewallInfo,
		pageContainer,
		firewallLoading,
	} from './useController.js';

	onShow(async () => {
		await isNginxFirewallInstalled();
		if (isFirewallInstalled.value) {
			await getNginxFirewallInfo();
		}
	});
</script>

<style lang="scss" scoped>
	/* 基础样式 */
	.nginx-firewall {
		font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans',
			'Helvetica Neue', sans-serif;
		color: #333;
		display: flex;
		flex-direction: column;
	}

	.category-arrow-icon {
		transform: rotate(0deg);
		transition: transform 0.3s ease;
	}

	.icon-rotated {
		transform: rotate(180deg);
	}

	.icon-container {
		display: flex;
		align-items: center;
		justify-content: center;
		width: 48rpx;
		height: 48rpx;

		.iconfont {
			font-size: 36rpx;
			color: #666666;
		}
	}

	/* 颜色定义 - 使用单一色调 */
	$color-primary: #666666;
	$color-danger: #dc2626;
	$color-success: #16a34a;
	$color-warning: #92400e;
	$color-border: #e5e7eb;
	$color-bg-light: #f9fafb;

	/* 主内容区域 */
	.main-content {
		flex: 1;
		padding: 32rpx;
		display: flex;
		flex-direction: column;
		gap: 32rpx;
	}

	/* 卡片样式 */
	.card {
		background-color: var(--dialog-bg-color);
		border-radius: 16rpx;
		box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.12);
		padding: 32rpx;
	}

	.card-hover {
		background-color: $color-bg-light;
		transform: translateY(-4rpx);
		box-shadow: 0 6rpx 12rpx rgba(0, 0, 0, 0.1);
		transition: all 0.2s ease;
	}

	/* 防火墙开关 */
	.firewall-switch {
		display: flex;
		justify-content: space-between;
		align-items: center;

		.switch-info {
			.switch-title {
				font-size: 32rpx;
				font-weight: 500;
				display: block;
			}

			.switch-desc {
				font-size: 24rpx;
				color: $color-primary;
				display: block;
				margin-top: 8rpx;
			}
		}

		.switch-control {
			display: flex;
			align-items: center;
			gap: 24rpx;
		}
	}

	/* 徽章样式 */
	.badge {
		font-size: 24rpx;
		font-weight: 500;
		padding: 4rpx 16rpx;
		border-radius: 9999rpx;

		&.badge-success {
			background-color: #dcfce7;
			color: $color-success;
		}

		&.badge-danger {
			background-color: #fee2e2;
			color: $color-danger;
		}
	}

	/* 分类区域 */
	.category-section {
		background-color: var(--dialog-bg-color);
		border-radius: 16rpx;
		box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.12);
		overflow: hidden;

		.category-header {
			padding: 24rpx 32rpx;
			display: flex;
			justify-content: space-between;
			align-items: center;
			position: relative;
		}

		.category-title {
			display: flex;
			align-items: center;
			gap: 16rpx;

			.category-heading {
				font-size: 32rpx;
				font-weight: 500;
			}
		}
	}

	/* 分类内容 */
	.category-content {
		padding-left: 32rpx;
		padding-right: 32rpx;
		padding-top: 0;
		padding-bottom: 0;
		border-top: 2rpx solid #f3f4f6;
		max-height: 0;
		overflow: hidden;
		transition: all 0.3s;

		&.category-content--open {
			max-height: 40rem; // Sufficiently large value for content
			padding-top: 32rpx;
			padding-bottom: 32rpx;
		}
	}

	/* 网格布局 */
	.metrics-grid {
		display: grid;
		gap: 24rpx;

		&.metrics-grid-2 {
			grid-template-columns: repeat(2, 1fr);
		}

		&.metrics-grid-3 {
			grid-template-columns: repeat(3, 1fr);
		}
	}

	/* 指标卡片 */
	.metric-card {
		background-color: var(--dialog-bg-color);
		border-radius: 16rpx;
		padding: 24rpx;
		display: flex;
		flex-direction: column;
		border: 2rpx solid $color-border;
		transition:
			transform 0.2s ease,
			box-shadow 0.2s ease;

		&.card-normal {
			background-color: var(--dialog-bg-color);
			border-color: $color-border;
		}

		.metric-header {
			display: flex;
			align-items: center;
			gap: 16rpx;

			.metric-title {
				font-size: 28rpx;
				font-weight: 500;
			}
		}

		.metric-value {
			font-size: 48rpx;
			font-weight: 700;
			margin-top: 16rpx;

			.metric-unit {
				font-size: 24rpx;
				font-weight: 400;
				margin-left: 4rpx;
			}
		}
	}

	/* 拦截类型卡片 */
	.block-type-card {
		background-color: var(--dialog-bg-color);
		border-radius: 16rpx;
		padding: 24rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.05);
		transition:
			transform 0.2s ease,
			box-shadow 0.2s ease;

		.block-title {
			font-size: 22rpx;
			font-weight: 400;
			text-align: center;
			margin: 16rpx 0 8rpx;
		}

		.block-count {
			font-size: 28rpx;
			font-weight: 600;
		}
	}

	/* 功能卡片 */
	.feature-card,
	.setting-card {
		background-color: var(--dialog-bg-color);
		border-radius: 16rpx;
		padding: 24rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.05);
		transition:
			transform 0.2s ease,
			box-shadow 0.2s ease;

		.feature-title {
			font-size: 24rpx;
			font-weight: 400;
			text-align: center;
			margin-top: 16rpx;
		}
	}

	.setting-card {
		flex-direction: row;
		justify-content: flex-start;
		gap: 24rpx;

		.setting-title {
			margin-top: 0;
			font-size: 28rpx;
		}
	}

	/* 注意事项 */
	.notice-card {
		background-color: var(--dialog-bg-color);
		border: 2rpx solid $color-border;
		border-radius: 16rpx;
		padding: 24rpx;

		.notice-header {
			display: flex;
			align-items: center;
			gap: 16rpx;
			margin-bottom: 16rpx;

			.notice-title {
				font-size: 28rpx;
				font-weight: 500;
				color: $color-primary;
			}
		}

		.notice-desc {
			font-size: 24rpx;
			color: $color-primary;
		}
	}

	/* 响应式调整 */
	@media (max-width: 360px) {
		.metrics-grid-3 {
			grid-template-columns: repeat(2, 1fr);
		}
	}
</style>
