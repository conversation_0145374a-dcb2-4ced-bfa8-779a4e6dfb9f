import { ref, reactive, onMounted } from 'vue';
import {
	getNginxFirewallSiteCity,
	getNginxFirewallGlobalConfig,
	setNginxFirewallStopCcStatus,
	setNginxFirewallStartCcStatus,
} from '@/api/nginx';
import { formPageContainer } from './useController.js';

/**
 * CC配置控制器
 * 管理CC防护配置的状态和逻辑
 */

// 表单数据
export const formData = reactive({
	cc_mode: 1,
	cc_type_status: 1,
	cycle: 60,
	limit: 120,
	endtime: 1800,
	is_cc_url: 0,
	cc_ip_max: 0,
	is_open_global: 1,
	cc_increase_type: 'js',
	country: {},
	increase: 0,
	increase_wu_heng: 1,
});

// picker 索引
export const formatIndex = ref(0);
export const verificationIndex = ref(1);
export const urlProtectionIndex = ref(0);
export const ipProtectionIndex = ref(0);
export const verificationTypeIndex = ref(0);

// 时间设置折叠面板状态
export const timeSettingsOpen = ref(false);

// 地区选择器状态
export const showRegionSelector = ref(false);
export const selectedRegions = ref([]);

// 地区数据
export const regions = ref([]);

// 配置选项
export const modeList = [
	{ label: '标准模式', value: 1 },
	{ label: '增强模式', value: 2 },
];

export const verificationList = [
	{ label: '宽松', value: 1 },
	{ label: '标准(推荐)', value: 2 },
	{ label: '严格', value: 3 },
	{ label: '超严格', value: 4 },
];

export const urlProtectionList = [
	{ label: '开启', value: 1 },
	{ label: '关闭', value: 0 },
];

export const ipProtectionList = [
	{ label: '开启', value: 1 },
	{ label: '关闭', value: 0 },
];

export const verificationTypeList = [
	{ label: '浏览器验证', value: 'browser' },
	{ label: '跳转验证', value: 'js' },
	{ label: '验证码验证', value: 'code' },
	{ label: '人机验证', value: 'renji' },
	{ label: '滑动验证', value: 'huadong' },
];

/**
 * 地区名称映射函数
 * 将后端返回的地区名称转换为前端显示的本地化名称
 */
export const mapRegionName = (regionName) => {
	const regionMap = {
		海外: '中国大陆以外的地区(包括[中国特别行政区:港,澳,台])',
		中国: '中国大陆(不包括[中国特别行政区:港,澳,台])',
		香港: '中国香港',
		澳门: '中国澳门',
		台湾: '中国台湾',
	};
	return regionMap[regionName] || regionName;
};

/**
 * 将显示名称转换回原始地区名称
 */
export const mapDisplayNameToOriginal = (displayName) => {
	const reverseMap = {
		'中国大陆以外的地区(包括[中国特别行政区:港,澳,台])': '海外',
		'中国大陆(不包括[中国特别行政区:港,澳,台])': '中国',
		中国香港: '香港',
		中国澳门: '澳门',
		中国台湾: '台湾',
	};
	return reverseMap[displayName] || displayName;
};

// 处理 picker 改变事件
export const handleFormatChange = (e) => {
	const index = e.detail.value;
	formData.cc_mode = modeList[index].value == 1 ? 1 : 4;
	formatIndex.value = index;
	formData.increase = index === 0 ? 0 : 1;
};

export const handleVerificationChange = (e) => {
	const index = e.detail.value;
	formData.cc_type_status = verificationList[index].value;
	verificationIndex.value = index;
};

export const handleUrlProtectionChange = (e) => {
	const index = e.detail.value;
	formData.is_cc_url = urlProtectionList[index].value;
	urlProtectionIndex.value = index;
};

export const handleIpProtectionChange = (e) => {
	const index = e.detail.value;
	formData.cc_ip_max = ipProtectionList[index].value;
	ipProtectionIndex.value = index;
};

export const handleVerificationTypeChange = (e) => {
	const index = e.detail.value;
	formData.cc_increase_type = verificationTypeList[index].value;
	verificationTypeIndex.value = index;
};

// 处理地区选择改变
export const handleRegionChange = (e) => {
	// 在uni-app中，checkbox-group的change事件返回的是所有选中项的value数组
	const selectedValues = e.detail.value;

	// 直接更新selectedRegions为新的选中值数组
	selectedRegions.value = [...selectedValues];
};

// 方法
export const toggleTimeSettings = () => {
	timeSettingsOpen.value = !timeSettingsOpen.value;
};

export const clearRegions = () => {
	selectedRegions.value = [];
	console.log('Cleared regions:', selectedRegions.value);
};

export const selectAllRegions = () => {
	selectedRegions.value = [...regions.value];
	console.log('Selected all regions:', selectedRegions.value);
};

/**
 * 获取城市/地区列表
 */
export const getRegions = async () => {
	try {
		const res = await getNginxFirewallSiteCity();
		console.log('Raw regions from API:', res);
		// 对地区名称进行映射处理
		const mappedRegions = res.map((region) => mapRegionName(region));
		console.log('Mapped regions:', mappedRegions);
		regions.value = [...mappedRegions]; // 使用展开运算符确保响应式更新
	} catch (error) {
		console.error('获取地区列表失败:', error);
		regions.value = []; // 确保出错时初始化为空数组
	}
};

const hanldeConfigIndex = (res) => {
	let modeIndex = res.cc_mode == 1 ? 1 : 2;
	formatIndex.value = modeList.findIndex((item) => item.value == modeIndex);
	verificationIndex.value = verificationList.findIndex((item) => item.value == res.cc_type_status);
	urlProtectionIndex.value = urlProtectionList.findIndex((item) => item.value == res.cc.is_cc_url);
	ipProtectionIndex.value = ipProtectionList.findIndex((item) => item.value == res.cc.cc_ip_max.open);
	verificationTypeIndex.value = verificationTypeList.findIndex((item) => item.value == res.cc.cc_increase_type);
};

/**
 * 获取全局配置并设置已选择的地区
 */
export const getGlobalConfig = async () => {
	try {
		const res = await getNginxFirewallGlobalConfig();
		// 保存原始的国家配置
		formData.country = res.cc.countrys;
		// 处理已选择的地区
		if (res.cc && res.cc.countrys) {
			const configuredRegions = Object.keys(res.cc.countrys);
			console.log('Configured regions from API:', configuredRegions);
			// 将配置中的地区名称映射为显示名称，然后设置为已选择
			const mappedSelectedRegions = configuredRegions.map((region) => mapRegionName(region));
			console.log('Mapped selected regions:', mappedSelectedRegions);
			selectedRegions.value = [...mappedSelectedRegions]; // 使用展开运算符确保响应式更新
		} else {
			selectedRegions.value = []; // 确保初始化为空数组
		}
		formData.cycle = res.cc.cycle;
		formData.limit = res.cc.limit;
		formData.endtime = res.cc.endtime;
		formData.cc_type_status = res.cc_type_status;
		formData.is_cc_url = res.cc.is_cc_url;
		formData.cc_ip_max = res.cc.cc_ip_max.open;
		formData.cc_increase_type = res.cc.cc_increase_type;
		hanldeConfigIndex(res);
	} catch (error) {
		console.error('获取全局配置失败:', error);
	}
};

export const submitForm = async () => {
	// 将选中的显示名称转换回原始名称
	const originalSelectedRegions = selectedRegions.value.map((region) => mapDisplayNameToOriginal(region));

	// 构建国家配置对象
	const countryConfig = {};
	originalSelectedRegions.forEach((region) => {
		countryConfig[region] = formData.country[region] || {};
	});

	let params = {
		cc_mode: formData.cc_mode,
		cc_type_status: formData.cc_type_status,
		cycle: formData.cycle,
		limit: formData.limit,
		endtime: formData.endtime,
		is_open_global: 1,
		// 确保 cc_increase_type 始终存在，如果为空则使用默认值
		cc_increase_type: formData.cc_increase_type || 'js',
		increase: formData.increase,
		increase_wu_heng: formData.increase_wu_heng,
		cc_ip_max: JSON.stringify({
			open: formData.cc_ip_max ? true : false,
			ip_max: formData.cc_ip_max ? 20000 : 0,
			static: false,
		}),
	};

	if (formData.cc_mode == 1) {
		params.country = originalSelectedRegions.join(',');
		params.is_cc_url = formData.is_cc_url ? 1 : 0;
	}

	console.log('提交表单数据:', params);

	try {
		const stopRes = await setNginxFirewallStopCcStatus();
		if (stopRes.status) {
			const startRes = await setNginxFirewallStartCcStatus(params);
			if (startRes.status) {
				formPageContainer.value.notify.success(startRes.msg);
				setTimeout(() => {
					uni.navigateBack();
				}, 1000);
			} else {
				formPageContainer.value.notify.error(startRes.msg);
			}
		}
	} catch (error) {
		console.error('提交表单失败:', error);
	}
};

// 初始化函数
export const initializeCcConfig = () => {
	onMounted(async () => {
		await getRegions();
		await getGlobalConfig();
	});
};
