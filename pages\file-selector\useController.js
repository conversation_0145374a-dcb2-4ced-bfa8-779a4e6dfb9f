import { ref } from 'vue';
import {
	getFilesList,
	currentPath,
	currentDisk,
	paging,
	pathList,
	cutDirPath,
	showDiskSelector,
	showNavMenu,
} from '@/linux/files/useController';

export const useFileSelectorController = () => {
	// 文件选择器特有的状态
	const filesList = ref([]);
	const selectedItems = ref(new Set());
	const selectionMode = ref('all'); // 'all', 'file', 'folder'
	const multipleSelection = ref(true);
	const pageTitle = ref('选择文件');
	const isConfirming = ref(false); // 标志是否正在确认选择
	const fileContainer = ref(null);

	// 页面参数存储，用于恢复状态
	const originalPath = ref('/');
	const originalDisk = ref('/');

	// 虚拟列表变化处理
	const virtualListChange = (vList) => {
		filesList.value = vList;
	};

	// 查询文件列表
	const queryList = async (page, pageSize) => {
		try {
			const res = await getFilesList(page, pageSize);
			paging.value.complete(res);
			paging.value.updateVirtualListRender();
		} catch (error) {
			paging.value.complete([]);
			console.error(error);
		}
	};

	// 检查是否已选择
	const isSelected = (itemPath) => {
		return selectedItems.value.has(itemPath);
	};

	// 处理项目点击
	const handleItemClick = (item) => {
		if (item.ext === 'folder') {
			// 如果是文件夹
			if (selectionMode.value === 'file') {
				// 仅选择文件模式下，点击文件夹进入
				cutDirPath(item.path);
			} else {
				// 其他模式下可以选择文件夹
				toggleSelection(item.path);
			}
		} else {
			// 文件，根据选择模式决定是否可选
			if (selectionMode.value !== 'folder') {
				toggleSelection(item.path);
			}
		}
	};

	// 处理文件夹进入（用于文件夹选择模式下的导航）
	const handleFolderEnter = (item) => {
		if (item.ext === 'folder') {
			cutDirPath(item.path);
		}
	};

	// 处理文件夹进入点击事件（带事件阻止冒泡）
	const handleFolderEnterClick = (e, item) => {
		// 阻止事件冒泡
		if (e && e.stopPropagation) {
			e.stopPropagation();
		}
		handleFolderEnter(item);
	};

	// 切换选择状态
	const toggleSelection = (itemPath) => {
		const newSelectedItems = new Set(selectedItems.value);

		if (newSelectedItems.has(itemPath)) {
			newSelectedItems.delete(itemPath);
		} else {
			if (!multipleSelection.value) {
				// 单选模式，清除其他选择
				newSelectedItems.clear();
			}
			newSelectedItems.add(itemPath);
		}

		selectedItems.value = newSelectedItems;
	};

	// 清除选择
	const clearSelection = () => {
		selectedItems.value = new Set();
	};

	// 确认选择并返回结果
	const handleConfirmSelection = async () => {
		const hasSelection = selectedItems.value.size > 0;

		if (hasSelection) {
			const selectedPaths = Array.from(selectedItems.value);

			// 设置确认标志，避免 onBackPress 干扰
			isConfirming.value = true;

			// 使用全局状态管理文件选择结果
			const { setSelectedPaths } = await import('@/stores/fileSelector.js');
			setSelectedPaths(selectedPaths);

			// 延迟一下再关闭页面，确保状态设置完成
			setTimeout(() => {
				uni.navigateBack();
			}, 100);
		} else {
			uni.showToast({
				title: '请先选择文件',
				icon: 'none',
			});
		}
	};

	// 初始化页面参数
	const initializePageParams = (options) => {
		// 保存原始状态
		originalPath.value = currentPath.value;
		originalDisk.value = currentDisk.value;

		// 处理传入参数
		if (options.path) {
			currentPath.value = decodeURIComponent(options.path);
		}
		if (options.mode) {
			selectionMode.value = options.mode;
		}
		if (options.multiple !== undefined) {
			multipleSelection.value = options.multiple === 'true';
		}
		if (options.title) {
			pageTitle.value = decodeURIComponent(options.title);
		}
	};

	// 恢复原始状态
	const restoreOriginalState = () => {
		// 恢复原始路径和磁盘状态
		currentPath.value = originalPath.value;
		currentDisk.value = originalDisk.value;

		// 清理选择器状态
		selectedItems.value = new Set();
		selectionMode.value = 'all';
		multipleSelection.value = true;
		pageTitle.value = '选择文件';

		// 清理弹窗状态
		showDiskSelector.value = false;
		showNavMenu.value = false;
	};

	return {
		// 状态
		filesList,
		selectedItems,
		selectionMode,
		multipleSelection,
		pageTitle,
		originalPath,
		originalDisk,
		isConfirming,

		// 方法
		virtualListChange,
		queryList,
		isSelected,
		handleItemClick,
		handleFolderEnter,
		handleFolderEnterClick,
		toggleSelection,
		clearSelection,
		handleConfirmSelection,
		initializePageParams,
		restoreOriginalState,
		fileContainer,
	};
};
