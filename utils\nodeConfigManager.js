import { PATH, UUID, SECRET, AESKEY, SYSTEM } from '@/utils/config';
import { getServerList } from '@/api/serverList';
import { useConfigStore } from '@/store';

/**
 * 节点配置管理工具
 * 用于处理节点访问时的配置切换和恢复
 */

// 使用变量保存节点访问状态，而不是本地存储
let nodeAccessState = {
	isFromNodeAccess: false,
	currentNodeId: null,
};

/**
 * 保存当前面板配置
 */
export const saveCurrentPanelConfig = () => {
	const { currentServerInfo } = useConfigStore().getReactiveState();
	const currentConfig = {
		path: PATH.value,
		uuid: UUID.value,
		secret: SECRET.value,
		aeskey: AESKEY.value,
		system: SYSTEM.value,
		serverInfo: currentServerInfo.value, // 保存当前服务器信息
	};
	uni.setStorage({
		key: 'tempPanelConfig',
		data: currentConfig,
		success: () => {
			console.log('已保存当前面板配置:', currentConfig);
		},
		fail: (err) => {
			console.error('保存当前面板配置失败:', err);
		},
	});
};

/**
 * 恢复原面板配置
 */
export const restoreOriginalPanelConfig = () => {
	const { currentServerInfo } = useConfigStore().getReactiveState();
	const savedConfig = uni.getStorageSync('tempPanelConfig');
	if (savedConfig) {
		PATH.value = savedConfig.path;
		UUID.value = savedConfig.uuid;
		SECRET.value = savedConfig.secret;
		AESKEY.value = savedConfig.aeskey;
		SYSTEM.value = savedConfig.system;

		// 恢复服务器信息
		if (savedConfig.serverInfo) {
			currentServerInfo.value = savedConfig.serverInfo;
		}

		uni.removeStorageSync('tempPanelConfig');
		console.log('已恢复原面板配置:', savedConfig);
	}
};

/**
 * 设置节点访问标记
 * @param {boolean} flag - 是否从节点访问
 * @param {string} nodeId - 节点ID
 */
export const setNodeAccessFlag = (flag, nodeId = null) => {
	nodeAccessState.isFromNodeAccess = flag;
	if (nodeId) {
		nodeAccessState.currentNodeId = nodeId;
	}
};

/**
 * 检查是否从节点访问
 * @returns {boolean}
 */
export const isFromNodeAccess = () => {
	return nodeAccessState.isFromNodeAccess;
};

/**
 * 清除节点访问标记
 */
export const clearNodeAccessFlag = () => {
	nodeAccessState.isFromNodeAccess = false;
	nodeAccessState.currentNodeId = null;
};

/**
 * 生成随机UUID
 * @returns {string}
 */
export const generateUUID = () => {
	return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
		const r = (Math.random() * 16) | 0;
		const v = c === 'x' ? r : (r & 0x3) | 0x8;
		return v.toString(16);
	});
};

/**
 * 生成随机SECRET
 * @returns {string}
 */
export const generateSecret = () => {
	return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
};

/**
 * 检测系统类型
 * @returns {string} 固定返回 'linux'，因为只有Linux才有节点功能
 */
export const detectSystemType = () => {
	return 'linux';
};

/**
 * 设置节点配置
 * @param {Object} nodeData - 节点数据
 */
export const setupNodeConfig = async (nodeData) => {
	try {
		const { currentServerInfo } = useConfigStore().getReactiveState();

		// 1. 设置基本配置
		PATH.value = nodeData.address; // 来自接口的 address
		SECRET.value = nodeData.api_key; // 来自接口的 api_key
		UUID.value = generateUUID(); // 随机生成
		AESKEY.value = generateSecret(); // 随机生成

		// 2. 设置系统类型为固定的 Linux
		SYSTEM.value = detectSystemType();

		// 3. 更新当前服务器信息为节点信息
		const nodeServerInfo = {
			name: nodeData.name || `节点${nodeData.id}`,
			ip: nodeData.server_ip || nodeData.address?.replace(/^https?:\/\//, '').split(':')[0] || '未知IP',
			rawPath: nodeData.address,
			status: nodeData.status === 'online',
			// 保留其他可能需要的字段
			id: nodeData.id,
			isFromNode: true, // 标记这是来自节点的服务器信息
		};
		currentServerInfo.value = nodeServerInfo;
		console.log('已更新节点服务器信息:', nodeServerInfo);
	} catch (error) {
		console.error('设置节点配置失败:', error);
		throw error;
	}
};

/**
 * 获取当前节点ID
 * @returns {string|null}
 */
export const getCurrentNodeId = () => {
	return nodeAccessState.currentNodeId;
};

/**
 * 安全恢复配置（带错误处理）
 */
export const safeRestoreConfig = () => {
	try {
		if (isFromNodeAccess()) {
			restoreOriginalPanelConfig();
			clearNodeAccessFlag();
		}
	} catch (error) {
		console.error('恢复配置时出错:', error);
		// 即使出错也要清除标记，避免状态不一致
		clearNodeAccessFlag();
	}
};
