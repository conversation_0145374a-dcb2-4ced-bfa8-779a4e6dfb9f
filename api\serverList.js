import axios from '@/api/request';

const DEFAULT_API_TYPE = import.meta.env.VITE_PANEL_OWNER || 'domestic';

// 获取服务器列表
export const getServerList = (serverConfig = null) => {
	const url = DEFAULT_API_TYPE === 'domestic' ? '/system?action=GetNetWork' : '/v2/system?action=GetNetWork';
	return axios(url, {}, 'POST', null, {}, DEFAULT_API_TYPE, serverConfig);
};

// 获取服务器详情统计
export const getServerDetail = () => {
	const url = DEFAULT_API_TYPE === 'domestic' ? '/panel/overview/GetOverview' : '/v2/warning?action=get_list';
	return axios(url, {}, 'POST', (res) => {
		// 获取安全数据
		if (DEFAULT_API_TYPE === 'domestic') {
			const safetyData = res.data.find((item) => item.name === 'safety_risk');
			return safetyData?.value[0] || '--';
		} else {
			const safetyData = res.risk.length;
			return safetyData;
		}
	});
};

// 扫码登录
export const scanLogin = (data) => {
	return axios('/api?action=login_for_app', data, 'POST');
};
