import { ref, nextTick } from 'vue';
import { getMySQLErrorLog, clearMySQLErrorLog } from '@/api/database';
import { throttle } from '@/uni_modules/uni-easyinput/components/uni-easyinput/common.js';

// 页面引用
export const pageContainer = ref(null);

// 日志内容
export const logContent = ref('');

// 滚动位置
export const scrollTop = ref(0);

// 清空日志相关状态
export const showClearDialog = ref(false);
export const clearing = ref(false);

// 初始化错误日志数据
export const initErrorLogData = async () => {
	try {
		await loadLogContent();
	} catch (error) {
		console.error('初始化错误日志数据失败:', error);
		logContent.value = '加载日志失败，请检查网络连接或重试';
	}
};

// 滚动到底部
const scrollToBottom = async () => {
	await nextTick();
	// 设置一个很大的值来确保滚动到底部
	scrollTop.value = 999999;
};

// 加载日志内容
const loadLogContent = async () => {
	try {
		const response = await getMySQLErrorLog();
		// 检查响应是否为空字符串或空白字符串
		if (!response || response.trim() === '') {
			logContent.value = '当前暂无数据';
		} else {
			logContent.value = response;
		}
		// 加载完成后滚动到底部
		await scrollToBottom();
	} catch (error) {
		console.error('加载日志内容失败:', error);
		logContent.value = '加载日志失败: ' + (error.msg || error.message || '未知错误');
	}
};

// 刷新日志
export const refreshLogs = async () => {
	try {
		await loadLogContent();
		pageContainer.value?.notify.success('日志刷新成功');
	} catch (error) {
		console.error('刷新日志失败:', error);
		pageContainer.value?.notify.error('刷新失败，请重试');
	}
};

/**
 * 清空日志
 */
const clearLogCore = () => {
	showClearDialog.value = true;
};

export const clearLog = throttle(clearLogCore, 2000, 1);

/**
 * 确认清空日志
 */
export const confirmClearLog = async (close) => {
	clearing.value = true;
	try {
		// 调用清空日志的API
		const response = await clearMySQLErrorLog();

		if (response && response.status) {
			// 清空成功
			pageContainer.value?.notify.success(response.msg || 'MySQL错误日志已清空!');

			// 清空本地显示内容
			logContent.value = '';

			// 重新加载日志内容
			await loadLogContent();

			// 关闭对话框
			close();
		} else {
			const errorMsg = response?.msg || '清空日志失败';
			pageContainer.value?.notify.error(errorMsg);
		}
	} catch (error) {
		const errorMsg = error.msg || error.message || '清空失败';
		pageContainer.value?.notify.error(errorMsg);
	} finally {
		clearing.value = false;
	}
};
