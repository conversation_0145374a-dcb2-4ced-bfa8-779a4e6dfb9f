import axios from '@/api/request';

const DEFAULT_API_TYPE = import.meta.env.VITE_PANEL_OWNER || 'domestic';

/**
 * @description 获取节点列表
 * @returns { Promise } 返回值
 */
export const getNodeList = (data) => {
	const url = '/mod/node/node/get_node_list';
	return axios(url, data);
};

/**
 * @description 删除节点
 * @param { Object } data - 请求参数
 * @param { string|number } data.ids - 要删除的节点ID
 * @returns { Promise } 返回值
 */
export const delNode = (data) => {
	const url = '/mod/node/node/del_node';
	return axios(url, data, 'POST');
};
