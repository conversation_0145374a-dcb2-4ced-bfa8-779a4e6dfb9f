<template>
	<page-container :is-show-nav="false">
		<view class="wrapper">
			<!-- App版本更新相关弹窗 -->
			<!-- 整包更新提示 -->
			<CustomDialog
				v-model="showPackagePopup"
				:isMaskClick="false"
				:zIndex="9999"
				:title="$t('setting.appUpdate')"
				:showCancel="true"
				:showConfirm="true"
				:cancelText="$t('setting.cancelUpdate')"
				:confirmText="phoneType == 'ios' ? $t('setting.iosUpdate') : $t('setting.androidUpdate')"
				@cancel="cancelVersion"
				@confirm="downloadVersion"
				contentHeight="500rpx"
			>
				<scroll-view class="pt-20" scroll-y="true" style="white-space: nowrap; max-height: 500rpx">
					<text v-if="versionNumber <= 400" :decode="true" style="white-space: pre-wrap; color: red"
						>{{ $t('setting.importantNotice') }}\n</text
					>
					<text :decode="true"
						>{{ $t('setting.newVersionAvailable', { version: updateVersionCode }) }}\n\n{{
							$t('setting.versionNotes')
						}}\n{{ versionMsg }}</text
					>
					<text :decode="true" style="color: red; font-size: 32rpx">\n\n{{ versionTip }}</text>
				</scroll-view>
			</CustomDialog>

			<!-- 正在更新中 进度条 -->
			<CustomDialog
				v-model="showProgressPopup"
				:isMaskClick="false"
				:zIndex="9999"
				:title="$t('setting.updateInProgress')"
				:showCancel="false"
				:showConfirm="false"
				contentHeight="200rpx"
			>
				<view class="h-full pt-20">
					<text class="progress-download">{{ showDownloadSize }} / {{ showSumSize }}</text>
					<view>
						<progress :percent="percentage" :show-info="true" activeColor="#20A53A"></progress>
					</view>
				</view>
			</CustomDialog>

			<!-- 热更新提示 -->
			<CustomDialog
				v-model="showHotUpdatePopup"
				:isMaskClick="false"
				:zIndex="9999"
				:title="$t('setting.appUpdate')"
				:showCancel="true"
				:showConfirm="true"
				:cancelText="$t('setting.cancelUpdate')"
				:confirmText="$t('setting.confirmUpdate')"
				@cancel="cancelVersion"
				@confirm="updateAffirm"
				contentHeight="500rpx"
			>
				<scroll-view class="mt-20" style="white-space: nowrap; max-height: 500rpx" scroll-y="true">
					<text :decode="true"
						>{{ $t('setting.currentVersion') }}{{ versionName }}。\n\n{{
							$t('setting.newVersionDetected', { version: updateVersionCode })
						}}\n\n{{ $t('setting.versionNotes') }}\n{{ versionMsg }}</text
					>
					<text :decode="true" style="color: red; font-size: 32rpx">\n\n{{ versionTip }}</text>
				</scroll-view>
			</CustomDialog>

			<CustomDialog
				v-model="agreementModel"
				contentHeight="25vh"
				title="服务协议与隐私政策"
				@cancel="out_app"
				@confirm="confirmAgreement"
				confirmText="同意并继续"
				cancelText="不同意"
				:isMaskClick="false"
			>
				<view class="py-20 text-secondary">
					<text
						>感谢您使用宝塔面板App！\n我们非常重视您的个人信息和隐私保护。为了更好的保障您的个人权益，在您使用我们的产品前，请务必审慎阅读、充分理解<text
							class="color-20a53a font-600"
							@click="openPage('service')"
							>《服务协议》</text
						>和<text class="color-20a53a font-600" @click="openPage('privacy')">《隐私政策》</text
						>各条款。\n若您同意使用我们的服务，请点击“同意并继续”，开始接受我们的服务！</text
					>
				</view>
			</CustomDialog>
		</view>
	</page-container>
</template>

<script lang="uts" setup>
	import { reactive, ref } from 'vue';
	import { getTheme, setTheme, getFollowSystem } from '@/hooks/useTheme.js';
	import { onLoad } from '@dcloudio/uni-app';
	import CustomDialog from '@/components/CustomDialog/index.vue';
	import { $t } from '@/locale/index.js';
	import { useAppUpdate, useAgreement } from './useController.js';
	import ServerList from './serverList/old-index.vue';
	import Files from './files/index.vue';
	import CheckAuth from './checkAuth/index.vue';
	import Settings from './setting/index.vue';
	import PageContainer from '@/components/PageContainer/index.vue';
	// #ifdef APP-HARMONY
	import { outApp } from '@/uni_modules/czh-outapp';
	// #endif

	// 获取App更新相关功能
	const {
	  phoneType,
	  updateVersionCode,
	  versionName,
	  versionNumber,
	  versionMsg,
	  versionTip,
	  showSumSize,
	  showDownloadSize,
	  percentage,
	  showPackagePopup,
	  showProgressPopup,
	  showHotUpdatePopup,
	  cancelVersion,
	  downloadVersion,
	  updateAffirm,
	} = useAppUpdate();

	const { agreementModel, confirmAgreement, openPage } = useAgreement();

	const out_app = () => {
	  // #ifdef APP-HARMONY
	  outApp();
	  // #endif
	};

	const envLanguage = import.meta.env.VITE_DEFAULT_LANGUAGE

	onLoad(() => {
	  // 初始化语言
	  if (envLanguage === 'en') {
	    uni.setLocale('en');
	  } else {
	    uni.setLocale('zh-Hans');
	  }
	  // 初始化主题
	  const theme = getTheme();
	  setTheme(theme);
	  // #ifdef APP-HARMONY
	  setTimeout(() => {
	    // 是否第一次进App，如果是第一次进App，则需要弹出服务协议及隐私政策
	    // 启用原生隐私政策弹窗
	    if (uni.getStorageSync('firstApp') !== false && uni.getStorageSync('firstApp') !== undefined) {
	      agreementModel.value = true;
	    }
	  }, 200);
	  // #endif
	});
</script>

<style scoped>
	.progress-download {
		font-weight: 400 !important;
		font-size: 26rpx !important;
		display: block;
		margin-bottom: 10rpx;
		text-align: center;
	}
</style>
