<template>
  <view class="bt-button">
    <uv-button
      :text="text"
      :type="type"
      :size="size"
      :shape="shape"
      :plain="plain"
      :hairline="hairline"
      :disabled="disabled"
      :loading="loading"
      :icon="icon"
      :color="color"
      :custom-style="customStyle"
      :custom-text-style="customTextStyle"
      @click="handleClick"
    >
      <slot></slot>
      <template v-slot:suffix>
        <slot name="suffix"></slot>
      </template>
    </uv-button>
  </view>
</template>

<script setup>
import uvButton from '@/uni_modules/uv-button/components/uv-button/uv-button.vue';
import { defineProps, defineEmits } from 'vue';

const props = defineProps({
  // 按钮文字
  text: {
    type: [String, Number],
    default: ''
  },
  // 按钮类型，primary、info、warning、error、success
  type: {
    type: String,
    default: 'success'
  },
  // 按钮尺寸，large、normal、small、mini
  size: {
    type: String,
    default: 'normal'
  },
  // 按钮形状，circle（两边为半圆）、square（带圆角）
  shape: {
    type: String,
    default: 'square'
  },
  // 是否镂空
  plain: {
    type: Boolean,
    default: false
  },
  // 是否细边框
  hairline: {
    type: Boolean,
    default: false
  },
  // 是否禁用
  disabled: {
    type: Boolean,
    default: false
  },
  // 是否加载中
  loading: {
    type: Boolean,
    default: false
  },
  // 图标名称，使用uv-icons的图标
  icon: {
    type: String,
    default: ''
  },
  // 按钮颜色，支持传入linear-gradient渐变色
  color: {
    type: String,
    default: ''
  },
  // 自定义按钮样式
  customStyle: {
    type: [Object, String],
    default: () => ({})
  },
  // 自定义按钮文字样式
  customTextStyle: {
    type: [Object, String],
    default: () => ({})
  }
});

const emit = defineEmits(['click']);

// 点击事件处理
const handleClick = (e) => {
  if (props.disabled || props.loading) return;
  emit('click', e);
};
</script>

<style lang="scss" scoped>
.bt-button {
  display: flex;
  flex-direction: column;
}
</style>
