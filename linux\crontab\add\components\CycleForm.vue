<template>
	<view>
		<!-- 执行周期 -->
		<view class="form-group">
			<view class="form-label-row">
				<text>执行周期</text>
			</view>
			<view class="cycle-container">
				<view class="cycle-type-row">
					<button class="cycle-select-button" @click="showCyclePicker">
						<text>{{ getCycleLabel() }}</text>
						<uv-icon name="arrow-down" size="14" :color="iconColor"></uv-icon>
					</button>

					<!-- 周选择 -->
					<button v-if="timeForm.type === 'week'" class="week-select-button" @click="showWeekPicker">
						<text>{{ getWeekLabel() }}</text>
						<uv-icon name="arrow-down" size="14" :color="iconColor"></uv-icon>
					</button>
				</view>

				<!-- 自定义周期 -->
				<!-- <view v-if="timeForm.type === 'sweek'" class="custom-cycle">
					<view class="custom-type-row">
						<button class="custom-type-button" @click="showCustomTypePicker">
							<text>{{ getCustomTypeLabel() }}</text>
							<uv-icon name="arrow-down" size="14" :color="iconColor"></uv-icon>
						</button>
					</view>

					<view v-if="timeForm.timeType === 'sweek'" class="week-multi-select">
						<button class="multi-select-button" @click="showWeekMultiPicker">
							<text>{{ getWeekMultiLabel() }}</text>
							<uv-icon name="arrow-down" size="14" :color="iconColor"></uv-icon>
						</button>
					</view>

					<view v-if="timeForm.timeType === 'smonth'" class="month-multi-select">
						<button class="multi-select-button" @click="showMonthMultiPicker">
							<text>{{ getMonthMultiLabel() }}</text>
							<uv-icon name="arrow-down" size="14" :color="iconColor"></uv-icon>
						</button>
					</view>

					<view class="hour-multi-select">
						<button class="multi-select-button" @click="showHourMultiPicker">
							<text>{{ getHourMultiLabel() }}</text>
							<uv-icon name="arrow-down" size="14" :color="iconColor"></uv-icon>
						</button>
					</view>

					<view class="minute-multi-select">
						<button class="multi-select-button" @click="showMinuteMultiPicker">
							<text>{{ getMinuteMultiLabel() }}</text>
							<uv-icon name="arrow-down" size="14" :color="iconColor"></uv-icon>
						</button>
					</view>
				</view> -->

				<!-- 普通时间输入 -->
				<view v-if="timeForm.type !== 'sweek'" class="time-inputs">
					<!-- 天数输入 -->
					<uv-input
						v-if="showDayInput()"
						v-model="timeForm.where1"
						@blur="handleTimeFieldChange('where1', $event)"
						:placeholder="timeForm.type === 'month' ? '1-31' : '1-365'"
						type="number"
						border="surround"
						:maxlength="timeForm.type === 'month' ? '2' : '3'"
						:customStyle="{ width: '150rpx', height: '60rpx', textAlign: 'center', fontSize: '26rpx' }"
					>
						<template v-slot:suffix>
							<text class="time-unit">天</text>
						</template>
					</uv-input>

					<!-- 小时间隔输入 -->
					<uv-input
						v-if="showHourIntervalInput()"
						v-model="timeForm.where1"
						@blur="handleTimeFieldChange('where1', $event)"
						placeholder="1-24"
						type="number"
						border="surround"
						maxlength="2"
						:customStyle="{ width: '150rpx', height: '60rpx', textAlign: 'center', fontSize: '26rpx' }"
					>
						<template v-slot:suffix>
							<text class="time-unit">小时</text>
						</template>
					</uv-input>

					<!-- 分钟间隔输入 -->
					<uv-input
						v-if="showMinuteIntervalInput()"
						v-model="timeForm.where1"
						@blur="handleTimeFieldChange('where1', $event)"
						placeholder="1-59"
						type="number"
						border="surround"
						maxlength="2"
						:customStyle="{ width: '150rpx', height: '60rpx', textAlign: 'center', fontSize: '26rpx' }"
					>
						<template v-slot:suffix>
							<text class="time-unit">分钟</text>
						</template>
					</uv-input>

					<!-- 小时输入 -->
					<uv-input
						v-if="showHourInput()"
						v-model="timeForm.hour"
						@blur="handleTimeFieldChange('hour', $event)"
						placeholder="0-23"
						type="number"
						border="surround"
						maxlength="2"
						:customStyle="{ width: '150rpx', height: '60rpx', textAlign: 'center', fontSize: '26rpx' }"
					>
						<template v-slot:suffix>
							<text class="time-unit">小时</text>
						</template>
					</uv-input>

					<!-- 分钟输入 -->
					<uv-input
						v-if="showMinuteInput()"
						v-model="timeForm.minute"
						@blur="handleTimeFieldChange('minute', $event)"
						placeholder="0-59"
						type="number"
						border="surround"
						maxlength="2"
						:customStyle="{ width: '150rpx', height: '60rpx', textAlign: 'center', fontSize: '26rpx' }"
					>
						<template v-slot:suffix>
							<text class="time-unit">分钟</text>
						</template>
					</uv-input>

					<!-- 秒输入 -->
					<uv-input
						v-if="showSecondInput()"
						v-model="timeForm.second"
						@blur="handleTimeFieldChange('second', $event)"
						placeholder="0-59"
						type="number"
						border="surround"
						maxlength="2"
						:customStyle="{ width: '150rpx', height: '60rpx', textAlign: 'center', fontSize: '26rpx' }"
					>
						<template v-slot:suffix>
							<text class="time-unit">秒</text>
						</template>
					</uv-input>
				</view>
			</view>

			<!-- 进程锁 -->
			<view
				v-if="showProcessLock"
				class="form-row"
				:class="{ 'process-lock-disabled': timeForm.type === 'second-n' }"
			>
				<text>进程锁</text>
				<view class="switch-container">
					<uv-switch
						:model-value="formData.flock"
						@change="updateField('flock', $event)"
						activeColor="#20a50a"
						size="22"
						:disabled="timeForm.type === 'second-n'"
					></uv-switch>
				</view>
			</view>
		</view>

		<!-- Picker组件 -->
		<uv-picker ref="cyclePicker" :columns="[cycleOptions]" keyName="label" @confirm="onCycleConfirm"></uv-picker>

		<uv-picker ref="weekPicker" :columns="[weekOptions]" keyName="label" @confirm="onWeekConfirm"></uv-picker>

		<!-- <uv-picker
			ref="customTypePicker"
			:columns="[customTypeOptions]"
			keyName="label"
			@confirm="onCustomTypeConfirm"
		></uv-picker>

		<uv-picker
			ref="weekMultiPicker"
			:columns="[weekMultiOptions]"
			keyName="label"
			@confirm="onWeekMultiConfirm"
			multiple
		></uv-picker>

		<uv-picker
			ref="monthMultiPicker"
			:columns="[monthMultiOptions]"
			keyName="label"
			@confirm="onMonthMultiConfirm"
			multiple
		></uv-picker>

		<uv-picker
			ref="hourMultiPicker"
			:columns="[hourMultiOptions]"
			keyName="label"
			@confirm="onHourMultiConfirm"
			multiple
		></uv-picker>

		<uv-picker
			ref="minuteMultiPicker"
			:columns="[minuteMultiOptions]"
			keyName="label"
			@confirm="onMinuteMultiConfirm"
			multiple
		></uv-picker> -->
	</view>
</template>

<script setup>
	import { ref, reactive, onMounted, computed, watch } from 'vue';
	import { getTheme, THEME_TYPE } from '@/hooks/useTheme.js';

	const props = defineProps({
		formData: {
			type: Object,
			required: true,
		},
		showProcessLock: {
			type: Boolean,
			default: false,
		},
		isEditMode: {
			type: Boolean,
			default: false,
		},
	});

	const emit = defineEmits(['update:formData']);

	// 主题相关
	const currentTheme = ref(getTheme());
	const iconColor = computed(() => {
		return currentTheme.value === THEME_TYPE.DARK ? '#cccccc' : '#666666';
	});

	// 时间表单
	const timeForm = reactive({
		type: 'day', // 周期类型
		week: '1', // 周几
		hour: '1', // 小时
		minute: '30', // 分钟
		second: '5', // 秒
		where1: '1', // x天xxx
		timeSet: [], // 周几 多选 或者 几号 多选
		timeType: 'sday', // 备份周期类型
		specialHour: [0], // 特殊小时
		specialMinute: [0], // 特殊分钟
	});

	// 基础执行周期选项
	const baseCycleOptions = [
		{ label: '每天', value: 'day' },
		{ label: 'N天', value: 'day-n' },
		{ label: '每小时', value: 'hour' },
		{ label: 'N小时', value: 'hour-n' },
		{ label: 'N分钟', value: 'minute-n' },
		{ label: 'N秒', value: 'second-n' },
		{ label: '每周', value: 'week' },
		{ label: '每月', value: 'month' },
		// { label: '自定义', value: 'sweek' }
	];

	// 执行周期选项 - 根据任务类型动态过滤
	const cycleOptions = computed(() => {
		// 如果不是toShell类型，过滤掉N秒选项
		if (props.formData.sType !== 'toShell') {
			return baseCycleOptions.filter(option => option.value !== 'second-n');
		}
		return baseCycleOptions;
	});

	// 周选择选项
	const weekOptions = ref([
		{ label: '周一', value: '1' },
		{ label: '周二', value: '2' },
		{ label: '周三', value: '3' },
		{ label: '周四', value: '4' },
		{ label: '周五', value: '5' },
		{ label: '周六', value: '6' },
		{ label: '周日', value: '7' },
	]);

	// 自定义类型选项
	/* const customTypeOptions = ref([
		{ label: '每天', value: 'sday' },
		{ label: '每周', value: 'sweek' },
		{ label: '每月', value: 'smonth' }
	]);

	// 周多选选项
	const weekMultiOptions = ref([
		{ label: '周一', value: '1' },
		{ label: '周二', value: '2' },
		{ label: '周三', value: '3' },
		{ label: '周四', value: '4' },
		{ label: '周五', value: '5' },
		{ label: '周六', value: '6' },
		{ label: '周日', value: '7' }
	]);

	// 月多选选项
	const monthMultiOptions = ref([]);
	for (let i = 1; i <= 30; i++) {
		monthMultiOptions.value.push({ label: `${i}号`, value: i.toString() });
	}

	// 小时多选选项
	const hourMultiOptions = ref([]);
	for (let i = 0; i < 24; i++) {
		hourMultiOptions.value.push({ label: `${i}点`, value: i });
	}

	// 分钟多选选项
	const minuteMultiOptions = ref([]);
	for (let i = 0; i < 60; i += 5) {
		minuteMultiOptions.value.push({ label: `${i}分`, value: i });
	} */

	// Picker引用
	const cyclePicker = ref(null);
	const weekPicker = ref(null);
	/* const customTypePicker = ref(null);
	const weekMultiPicker = ref(null);
	const monthMultiPicker = ref(null);
	const hourMultiPicker = ref(null);
	const minuteMultiPicker = ref(null); */

	// 显示选择器
	const showCyclePicker = () => {
		cyclePicker.value?.open();
	};

	const showWeekPicker = () => {
		weekPicker.value?.open();
	};

	/* const showCustomTypePicker = () => {
		customTypePicker.value?.open();
	};

	const showWeekMultiPicker = () => {
		weekMultiPicker.value?.open();
	};

	const showMonthMultiPicker = () => {
		monthMultiPicker.value?.open();
	};

	const showHourMultiPicker = () => {
		hourMultiPicker.value?.open();
	};

	const showMinuteMultiPicker = () => {
		minuteMultiPicker.value?.open();
	}; */

	// 获取显示标签
	const getCycleLabel = () => {
		const option = cycleOptions.value.find((item) => item.value === timeForm.type);
		return option ? option.label : '每天';
	};

	const getWeekLabel = () => {
		const option = weekOptions.value.find((item) => item.value === timeForm.week);
		return option ? option.label : '周一';
	};

	/* const getCustomTypeLabel = () => {
		const option = customTypeOptions.value.find(item => item.value === timeForm.timeType);
		return option ? option.label : '每天';
	};

	const getWeekMultiLabel = () => {
		if (!timeForm.timeSet || timeForm.timeSet.length === 0) return '请选择周几';
		const labels = timeForm.timeSet.map(value => {
			const option = weekMultiOptions.value.find(item => item.value === value);
			return option ? option.label : value;
		});
		return labels.join(', ');
	};

	const getMonthMultiLabel = () => {
		if (!timeForm.timeSet || timeForm.timeSet.length === 0) return '请选择日期';
		const labels = timeForm.timeSet.map(value => {
			const option = monthMultiOptions.value.find(item => item.value === value);
			return option ? option.label : `${value}号`;
		});
		return labels.join(', ');
	};

	const getHourMultiLabel = () => {
		if (!timeForm.specialHour || timeForm.specialHour.length === 0) return '请选择小时';
		const labels = timeForm.specialHour.map(value => {
			const option = hourMultiOptions.value.find(item => item.value === value);
			return option ? option.label : `${value}点`;
		});
		return labels.join(', ');
	};

	const getMinuteMultiLabel = () => {
		if (!timeForm.specialMinute || timeForm.specialMinute.length === 0) return '请选择分钟';
		const labels = timeForm.specialMinute.map(value => {
			const option = minuteMultiOptions.value.find(item => item.value === value);
			return option ? option.label : `${value}分`;
		});
		return labels.join(', ');
	}; */

	// 显示输入控制
	const showDayInput = () => {
		return timeForm.type === 'day-n' || timeForm.type === 'month';
	};

	const showHourIntervalInput = () => {
		return timeForm.type === 'hour-n';
	};

	const showMinuteIntervalInput = () => {
		return timeForm.type === 'minute-n';
	};

	const showHourInput = () => {
		return (
			timeForm.type === 'day' ||
			timeForm.type === 'day-n' ||
			timeForm.type === 'week' ||
			timeForm.type === 'month'
		);
	};

	const showMinuteInput = () => {
		return (
			timeForm.type === 'day' ||
			timeForm.type === 'day-n' ||
			timeForm.type === 'hour' ||
			timeForm.type === 'hour-n' ||
			timeForm.type === 'week' ||
			timeForm.type === 'month'
		);
	};

	const showSecondInput = () => {
		return timeForm.type === 'second-n';
	};

	// 确认选择
	const onCycleConfirm = (e) => {
		const selectedValue = e.value[0].value;
		timeForm.type = selectedValue;
		// 重置相关字段
		if (selectedValue !== 'sweek') {
			timeForm.timeType = 'sday';
			timeForm.timeSet = [];
			timeForm.specialHour = [0];
			timeForm.specialMinute = [0];
		}

		// 根据不同的周期类型重置where1字段为合适的默认值
		if (selectedValue === 'hour-n') {
			// N小时模式：默认1小时
			timeForm.where1 = '1';
		} else if (selectedValue === 'day-n') {
			// N天模式：默认1天
			timeForm.where1 = '1';
		} else if (selectedValue === 'minute-n') {
			// N分钟模式：默认1分钟
			timeForm.where1 = '1';
		} else if (selectedValue === 'month') {
			// 每月模式：默认1号
			timeForm.where1 = '1';
		}

		// 先同步时间表单数据
		syncTimeFormToFormData();

		// 根据选择的类型设置进程锁状态（仅在非编辑模式下）
			if (selectedValue === 'second-n') {
				// N秒时强制禁用进程锁
				updateField('flock', false);
			} else if (!props.isEditMode) {
				// 其他情况默认开启进程锁
				updateField('flock', true);
			}

	};

	const onWeekConfirm = (e) => {
		const selectedValue = e.value[0].value;
		timeForm.week = selectedValue;
		syncTimeFormToFormData();
	};

	/* const onCustomTypeConfirm = (e) => {
		const selectedValue = e.value[0].value;
		timeForm.timeType = selectedValue;
		timeForm.timeSet = [];
		syncTimeFormToFormData();
	};

	const onWeekMultiConfirm = (e) => {
		timeForm.timeSet = e.value.map(item => item.value);
		syncTimeFormToFormData();
	};

	const onMonthMultiConfirm = (e) => {
		timeForm.timeSet = e.value.map(item => item.value);
		syncTimeFormToFormData();
	};

	const onHourMultiConfirm = (e) => {
		timeForm.specialHour = e.value.map(item => item.value);
		syncTimeFormToFormData();
	};

	const onMinuteMultiConfirm = (e) => {
		timeForm.specialMinute = e.value.map(item => item.value);
		syncTimeFormToFormData();
	}; */

	// 更新字段
	const updateField = (field, event) => {
		const value = event.detail?.value || event.target?.value || event;
		emit('update:formData', { ...props.formData, [field]: value });
	};

	// 处理时间字段变化（用于 @blur 事件）
	const handleTimeFieldChange = (field, value) => {
		// 验证和格式化输入值
		let formattedValue = validateAndFormatTimeValue(field, value);

		// 更新 timeForm
		timeForm[field] = formattedValue;

		// 同步到主表单
		syncTimeFormToFormData();
	};

	// 验证和格式化时间值
	const validateAndFormatTimeValue = (field, value) => {
		// 确保输入值是整数
		if (value === '' || value === null || value === undefined) {
			return '';
		}

		// 移除非数字字符
		const cleanValue = value.toString().replace(/[^\d]/g, '');

		// 转换为整数
		const intValue = parseInt(cleanValue);

		// 如果不是有效数字，返回空字符串
		if (isNaN(intValue)) {
			return '';
		}

		// 根据字段类型进行范围限制
		switch (field) {
			case 'hour':
				return Math.min(Math.max(intValue, 0), 23).toString();
			case 'minute':
				return Math.min(Math.max(intValue, 0), 59).toString();
			case 'second':
				return Math.min(Math.max(intValue, 0), 59).toString();
			case 'where1': // 天数、小时间隔或分钟间隔
				if (timeForm.type === 'hour-n') {
					// N小时模式：1-24小时
					return Math.min(Math.max(intValue, 1), 24).toString();
				} else if (timeForm.type === 'minute-n') {
					// N分钟模式：1-59分钟
					return Math.min(Math.max(intValue, 1), 59).toString();
				} else if (timeForm.type === 'month') {
					// 每月模式：1-31天
					return Math.min(Math.max(intValue, 1), 31).toString();
				} else {
					// 天数模式：1-365天
					return Math.min(Math.max(intValue, 1), 365).toString();
				}
			default:
				return intValue.toString();
		}
	};

	// 更新时间字段（保留用于其他用途）
	const updateTimeField = (field, event) => {
		let value = event.detail?.value || event.target?.value || event;
		let formattedValue = validateAndFormatTimeValue(field, value);
		timeForm[field] = formattedValue;
		syncTimeFormToFormData();
	};

	// 同步时间表单到主表单
	const syncTimeFormToFormData = () => {
		emit('update:formData', {
			...props.formData,
			type: timeForm.type,
			week: timeForm.week,
			hour: timeForm.hour,
			minute: timeForm.minute,
			second: timeForm.second,
			where1: timeForm.where1,
			timeSet: Array.isArray(timeForm.timeSet) ? timeForm.timeSet.join(',') : timeForm.timeSet,
			timeType: timeForm.timeType,
			specialHour: Array.isArray(timeForm.specialHour) ? timeForm.specialHour : [timeForm.specialHour],
			specialMinute: Array.isArray(timeForm.specialMinute) ? timeForm.specialMinute : [timeForm.specialMinute],
		});
	};

	// 初始化时间表单
	const initializeTimeForm = () => {
		timeForm.type = props.formData.type || 'day';
		timeForm.hour = props.formData.hour || '1';
		timeForm.minute = props.formData.minute || '30';
		timeForm.week = props.formData.week || '1';
		timeForm.where1 = props.formData.where1 || '1';
		timeForm.second = props.formData.second || '5';
		timeForm.timeType = props.formData.timeType || 'sday';

		// 处理数组字段
		if (props.formData.timeSet) {
			timeForm.timeSet =
				typeof props.formData.timeSet === 'string' ? props.formData.timeSet.split(',') : props.formData.timeSet;
		}
		if (props.formData.specialHour) {
			timeForm.specialHour = Array.isArray(props.formData.specialHour)
				? props.formData.specialHour
				: [props.formData.specialHour];
		}
		if (props.formData.specialMinute) {
			timeForm.specialMinute = Array.isArray(props.formData.specialMinute)
				? props.formData.specialMinute
				: [props.formData.specialMinute];
		}
	};

	// 监听 props.formData 变化
	watch(
		() => props.formData,
		(newFormData) => {
			if (newFormData) {
				initializeTimeForm();
			}
		},
		{ deep: true, immediate: false },
	);

	// 监听 timeForm.type 变化，处理进程锁状态（仅在非编辑模式下）
	watch(
		() => timeForm.type,
		(newType) => {
			if (newType === 'second-n') {
				// 当切换到 N秒 时，强制关闭进程锁
				updateField('flock', false);
			} else if (!props.isEditMode) {
				// 其他情况默认开启进程锁
				updateField('flock', true);
			}
		},
	);

	// 监听任务类型变化，处理N秒选项的可用性
	watch(
		() => props.formData.sType,
		(newSType) => {
			// 如果当前选择的是N秒，但任务类型不是toShell，则自动切换到每天
			if (timeForm.type === 'second-n' && newSType !== 'toShell') {
				timeForm.type = 'day';
				syncTimeFormToFormData();
			}
		},
	);

	onMounted(() => {
		initializeTimeForm();
	});
</script>

<style lang="scss" scoped>
	@import './form-styles.scss';

	.cycle-container {
		display: flex;
		flex-direction: column;
		gap: 16rpx;
	}

	.cycle-type-row {
		display: flex;
		align-items: center;
		gap: 20rpx;
		flex-wrap: wrap;
	}

	.cycle-select-button {
		flex: 1;
		height: 90rpx !important;
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 0 24rpx;
		background: var(--bg-color);
		border: 1px solid var(--border-color);
		border-radius: 16rpx;
		transition: all 0.3s ease;

		&:active {
			transform: scale(0.98);
			box-shadow: var(--box-shadow);
		}

		text {
			font-size: 26rpx;
			font-weight: 500;
			color: var(--text-color-primary);
		}
	}

	.week-select-button {
		width: 200rpx;
		height: 90rpx !important;
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 0 24rpx;
		background: var(--bg-color);
		border: 1px solid var(--border-color);
		border-radius: 16rpx;
		transition: all 0.3s ease;

		&:active {
			transform: scale(0.98);
			box-shadow: var(--box-shadow);
		}

		text {
			font-size: 26rpx;
			font-weight: 500;
			color: var(--text-color-primary);
		}
	}

	/* .custom-cycle {
		display: flex;
		flex-direction: column;
		gap: 12rpx;
	}

	.custom-type-row {
		display: flex;
		align-items: center;
	}

	.custom-type-button {
		width: 200rpx;
		height: 80rpx !important;
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 0 20rpx;
		background: var(--bg-color);
		border: 1px solid var(--border-color);
		border-radius: 12rpx;
		transition: all 0.3s ease;

		&:active {
			transform: scale(0.98);
			box-shadow: var(--box-shadow);
		}

		text {
			font-size: 24rpx;
			font-weight: 500;
			color: var(--text-color-primary);
		}
	}

	.week-multi-select,
	.month-multi-select,
	.hour-multi-select,
	.minute-multi-select {
		display: flex;
		align-items: center;
	}

	.multi-select-button {
		flex: 1;
		min-height: 80rpx !important;
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 16rpx 20rpx;
		background: var(--bg-color);
		border: 1px solid var(--border-color);
		border-radius: 12rpx;
		transition: all 0.3s ease;

		&:active {
			transform: scale(0.98);
			box-shadow: var(--box-shadow);
		}

		text {
			font-size: 24rpx;
			font-weight: 500;
			color: var(--text-color-primary);
			line-height: 1.4;
			word-break: break-all;
		}
	} */

	.time-inputs {
		display: flex;
		align-items: center;
		gap: 12rpx;
		flex-wrap: wrap;
	}

	.time-unit {
		font-size: 26rpx;
		font-weight: 500;
		color: var(--text-color-secondary);
		margin: 0 4rpx;
	}

	// 重写基础样式，使其更精致
	.form-group {
		margin-bottom: 32rpx;
		padding: 24rpx;
		background: var(--bg-color);
		border-radius: 20rpx;
		box-shadow: var(--box-shadow);
		border: 1px solid var(--border-color);

		&:last-child {
			margin-bottom: 0;
		}
	}

	.form-label-row {
		margin-bottom: 16rpx;

		text {
			font-size: 28rpx;
			font-weight: 600;
			color: var(--text-color-primary);
			position: relative;

			&::before {
				content: '';
				position: absolute;
				left: -12rpx;
				top: 50%;
				transform: translateY(-50%);
				width: 6rpx;
				height: 20rpx;
				background: var(--primary-color);
				border-radius: 3rpx;
			}
		}
	}

	.form-row {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-top: 20rpx;
		padding-top: 20rpx;
		border-top: 1px solid var(--border-color);

		text {
			font-size: 26rpx;
			font-weight: 500;
			color: var(--text-color-primary);
		}
	}

	.process-lock-disabled {
		opacity: 0.6;
	}

	.switch-container {
		display: flex;
		align-items: center;
		gap: 16rpx;

		.disabled-text {
			font-size: 24rpx;
			color: var(--text-color-secondary);
			background: var(--bg-color-secondary);
			padding: 4rpx 12rpx;
			border-radius: 8rpx;
			border: 1px solid var(--border-color);
		}
	}
</style>
