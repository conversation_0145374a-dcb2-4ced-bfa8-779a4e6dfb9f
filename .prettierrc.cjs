module.exports = {
  // 一行最多120字符
  printWidth: 120,
  // 使用4个空格缩进
  tabWidth: 4,
  // 使用tab进行缩进
  useTabs: true,
  // 行尾需要分号
  semi: true,
  // 使用单引号
  singleQuote: true,
  // 对象的key仅在必要时用引号
  quoteProps: 'as-needed',
  // jsx不使用单引号，而使用双引号
  jsxSingleQuote: false,
  // 尾随逗号
  trailingComma: 'all',
  // 大括号内的首尾需要空格
  bracketSpacing: true,
  // jsx标签的反尖括号需要换行
  jsxBracketSameLine: false,
  // 箭头函数，只有一个参数的时候，也需要括号
  arrowParens: 'always',
  // 每个文件格式化的范围是文件的全部内容
  rangeStart: 0,
  rangeEnd: Infinity,
  // 不需要写文件开头的 @prettier
  requirePragma: false,
  // 不需要自动在文件开头插入 @prettier
  insertPragma: false,
  // 使用默认的折行标准
  proseWrap: 'preserve',
  // 根据显示样式决定 html 要不要折行
  htmlWhitespaceSensitivity: 'css',
  // vue文件中的script和style内需要缩进
  vueIndentScriptAndStyle: true,
  // 换行符使用 lf
  endOfLine: 'lf',
  // 格式化嵌入的内容
  embeddedLanguageFormatting: 'auto',
  // 存在单独的括号时也强制换行
  bracketSameLine: false,
  // html空格敏感度
  htmlWhitespaceSensitivity: 'strict',
};
