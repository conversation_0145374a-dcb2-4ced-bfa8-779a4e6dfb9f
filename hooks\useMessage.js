/**
 * 消息提示封装
 * <AUTHOR>
 */

class Message {
  // 默认配置
  static defaultOptions = {
    duration: 2000,
    mask: false,
    icon: 'none'
  }

  /**
   * 成功提示
   * @param {string} title 提示内容
   * @param {number|object} options 显示时长或配置项
   */
  static success(title, options = {}) {
    const config = typeof options === 'number' ? { duration: options } : options
    return this.show({
      title,
      icon: 'success',
      ...this.defaultOptions,
      ...config
    })
  }

  /**
   * 错误提示
   * @param {string} title 提示内容
   * @param {number|object} options 显示时长或配置项
   */
  static error(title, options = {}) {
    const config = typeof options === 'number' ? { duration: options } : options
    return this.show({
      title,
      icon: 'error',
      ...this.defaultOptions,
      ...config
    })
  }

  /**
   * 警告提示
   * @param {string} title 提示内容
   * @param {number|object} options 显示时长或配置项
   */
  static warning(title, options = {}) {
    const config = typeof options === 'number' ? { duration: options } : options
    return this.show({
      title,
      icon: 'warn',
      ...this.defaultOptions,
      ...config
    })
  }

  /**
   * 普通提示
   * @param {string} title 提示内容
   * @param {number|object} options 显示时长或配置项
   */
  static info(title, options = {}) {
    const config = typeof options === 'number' ? { duration: options } : options
    return this.show({
      title,
      ...this.defaultOptions,
      ...config
    })
  }

  /**
   * 加载提示
   * @param {string} title 提示内容
   * @param {object} options 配置项
   */
  static loading(title = '加载中...', options = {}) {
    return uni.showLoading({
      title,
      mask: true,
      ...options
    })
  }

  /**
   * 隐藏加载提示
   */
  static hideLoading() {
    uni.hideLoading()
  }

  /**
   * 显示消息提示
   * @param {object} options 配置项
   */
  static show(options) {
    return new Promise((resolve) => {
      uni.showToast({
        ...options,
        success: resolve
      })
    })
  }
}

export default Message
