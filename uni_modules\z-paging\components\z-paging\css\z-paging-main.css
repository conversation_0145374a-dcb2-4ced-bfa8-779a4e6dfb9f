/* [z-paging]公共css*/

.z-paging-content {
	position: relative;
	flex-direction: column;
	/* #ifndef APP-NVUE */
	overflow: hidden;
	/* #endif */
}

.z-paging-content-full {
	/* #ifndef APP-NVUE */
	display: flex;
	width: 100%;
	height: 100%;
	/* #endif */
}

.z-paging-content-fixed, .zp-loading-fixed {
	position: fixed;
	/* #ifndef APP-NVUE */
	height: auto;
	width: auto;
	/* #endif */
	top: 0;
	left: 0;
	bottom: 0;
	right: 0;
}

.zp-f2-content {
	width: 100%;
	position: fixed;
	top: 0;
	left: 0;
	background-color: white;
}

.zp-page-top, .zp-page-bottom {
	/* #ifndef APP-NVUE */
	width: auto;
	/* #endif */
	position: fixed;
	left: 0;
	right: 0;
	z-index: 999;
}

.zp-page-left, .zp-page-right {
	/* #ifndef APP-NVUE */
	height: 100%;
	/* #endif */
}

.zp-scroll-view-super {
	flex: 1;
	overflow: hidden;
	position: relative;
}

.zp-view-super {
	/* #ifndef APP-NVUE */
	display: flex;
	/* #endif */
	flex-direction: row;
}

.zp-scroll-view-container, .zp-scroll-view {
	position: relative;
	/* #ifndef APP-NVUE */
	height: 100%;
	width: 100%;
	/* #endif */
}

.zp-absoulte {
	/* #ifndef APP-NVUE */
	position: absolute;
	top: 0;
	width: auto;
	/* #endif */
}

.zp-scroll-view-absolute {
	position: absolute;
	top: 0;
	left: 0;
}

/* #ifndef APP-NVUE */
.zp-scroll-view-hide-scrollbar ::-webkit-scrollbar {
	display: none;
	-webkit-appearance: none;
	width: 0 !important;
	height: 0 !important;
	background: transparent;
}
/* #endif */

.zp-paging-touch-view {
	width: 100%;
	height: 100%;
	position: relative;
}

.zp-fixed-bac-view {
	position: absolute;
	width: 100%;
	top: 0;
	left: 0;
	height: 200px;
}

.zp-paging-main {
	height: 100%;
	/* #ifndef APP-NVUE */
	display: flex;
	/* #endif */
	flex-direction: column;
}

.zp-paging-container {
	flex: 1;
	position: relative;
	/* #ifndef APP-NVUE */
	display: flex;
	/* #endif */
	flex-direction: column;
}

.zp-chat-record-loading-custom-image {
	width: 35rpx;
	height: 35rpx;
	/* #ifndef APP-NVUE */
	animation: loading-flower 1s linear infinite;
	/* #endif */
}

.zp-page-bottom-keyboard-placeholder-animate {
	transition-property: height;
	transition-duration: 0.15s;
	/* #ifndef APP-NVUE */
	will-change: height;
	/* #endif */
}

.zp-custom-refresher-container {
	overflow: hidden;
}

.zp-custom-refresher-refresh {
	/* #ifndef APP-NVUE */
	display: block;
	/* #endif */
}

.zp-back-to-top {
	z-index: 999;
	position: absolute;
	bottom: 0rpx;
	transition-duration: .3s;
	transition-property: opacity;
}
.zp-back-to-top-rpx {
	width: 76rpx;
	height: 76rpx;
	bottom: 0rpx;
	right: 25rpx;
}
.zp-back-to-top-px {
	width: 38px;
	height: 38px;
	bottom: 0px;
	right: 13px;
}

.zp-back-to-top-show {
	opacity: 1;
}

.zp-back-to-top-hide {
	opacity: 0;
}

.zp-back-to-top-img {
	/* #ifndef APP-NVUE */
	width: 100%;
	height: 100%;
	/* #endif */
	/* #ifdef APP-NVUE */
	flex: 1;
	/* #endif */
	z-index: 999;
}

.zp-back-to-top-img-inversion {
	transform: rotate(180deg);
}

.zp-empty-view {
	/* #ifdef APP-NVUE */
	height: 100%;
	/* #endif */
	flex: 1;
}

.zp-empty-view-center {
	/* #ifndef APP-NVUE */
	display: flex;
	/* #endif */
	flex-direction: column;
	align-items: center;
	justify-content: center;
}

.zp-loading-fixed {
	z-index: 9999;
}

.zp-safe-area-inset-bottom {
	position: absolute;
	/* #ifndef APP-PLUS */
	height: env(safe-area-inset-bottom);
	/* #endif */
}

.zp-n-refresh-container {
	/* #ifndef APP-NVUE */
	display: flex;
	/* #endif */
	justify-content: center;
	width: 750rpx;
}

.zp-n-list-container{
	/* #ifndef APP-NVUE */
	display: flex;
	/* #endif */
	flex-direction: row;
	flex: 1;
}
