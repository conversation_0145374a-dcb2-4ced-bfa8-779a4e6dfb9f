<template>
  <view class="p-10">
    <scroll-view class="ws-nowrap w-full" :scroll-x="true" :scroll-with-animation="true" :scroll-into-view="scrollToId" :show-scrollbar="false">
      <view
        :id="`path-${index}`"
        class="inline-flex items-center text-primary"
        :key="item.path"
        v-for="(item, index) in pathList"
      >
        <text class="text-base text-30" @tap="cutDirPath(item.path)">{{ item.name }}</text>
        <uni-icons type="right" color="var(--text-color-primary)" size="13" class="mx-8"></uni-icons>
      </view>
    </scroll-view>
  </view>
</template>

<script setup>
  import { pathList, cutDirPath } from './useController';
  import { ref, watch, computed } from 'vue';

  const scrollToId = computed(() => {
    return pathList.value.length ? `path-${pathList.value.length - 1}` : '';
  });
</script>
