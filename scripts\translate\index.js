require('dotenv').config()

const axios = require('axios');
const fs = require('fs').promises;
const path = require('path');

// 百度翻译API配置
const BAIDU_APP_ID = process.env.VITE_BAIDU_APP_ID;
const BAIDU_KEY = process.env.VITE_BAIDU_SECRET_KEY;
const BAIDU_API_URL = 'http://api.fanyi.baidu.com/api/trans/vip/translate';

// 目标语言列表
const TARGET_LANGUAGES = ['en'];

// 生成百度翻译API签名
function generateSign(text, salt) {
    const str = BAIDU_APP_ID + text + salt + BAIDU_KEY;
    const crypto = require('crypto');
    return crypto.createHash('md5').update(str).digest('hex');
}

// 更安全的解析对象方法
function safelyParseObject(objectString) {
    try {
        // 使用Function构造函数创建一个函数来安全地解析对象字符串
        // 这比直接使用eval更安全，因为它限制了执行环境
        return (new Function('return ' + objectString))();
    } catch (error) {
        console.error('解析对象失败:', error.message);
        throw new Error('无法安全解析对象: ' + error.message);
    }
}

// 获取已有的翻译
async function getExistingTranslation(key, targetLang) {
    try {
        const filePath = path.join(__dirname, `../../locale/${targetLang}.js`);
        const content = await fs.readFile(filePath, 'utf8');
        // 提取对象内容
        const match = content.match(/export default ({[\s\S]*})/);
        if (match) {
            const translations = safelyParseObject(match[1]);
            // 获取键对应的值
            const keys = key.split('.');
            let current = translations;
            for (const k of keys) {
                if (!current || !current[k]) {
                    return null;
                }
                current = current[k];
            }
            return current;
        }
        return null;
    } catch (error) {
        return null;
    }
}

// 检查文本是否已经翻译
async function isAlreadyTranslated(key, targetLang) {
    try {
        const filePath = path.join(__dirname, `../../locale/${targetLang}.js`);
        const content = await fs.readFile(filePath, 'utf8');
        // 提取对象内容
        const match = content.match(/export default ({[\s\S]*})/);
        if (match) {
            const translations = safelyParseObject(match[1]);
            // 检查键是否存在
            const keys = key.split('.');
            let current = translations;
            for (const k of keys) {
                if (!current || !current[k]) {
                    return false;
                }
                current = current[k];
            }
            return true;
        }
        return false;
    } catch (error) {
        return false;
    }
}

// 翻译单个文本
async function translateText(text, key, from = 'zh', to = 'en') {
    try {
        // 检查是否已翻译
        if (await isAlreadyTranslated(key, to)) {
            console.log(`路径 "${key}" 已经翻译过，跳过`);
            // 返回已有的翻译
            const existingTranslation = await getExistingTranslation(key, to);
            return existingTranslation || text;
        }

        const salt = Date.now();
        const sign = generateSign(text, salt);

        console.log(`正在翻译: "${text}" (${key})`);

        const response = await axios.get(BAIDU_API_URL, {
            params: {
                q: text,
                from,
                to,
                appid: BAIDU_APP_ID,
                salt,
                sign
            }
        });

        if (response.data && response.data.trans_result) {
            return response.data.trans_result[0].dst;
        }
        throw new Error(`翻译失败: ${JSON.stringify(response.data)}`);
    } catch (error) {
        console.error(`翻译出错: ${error.message}`);
        if (error.response) {
            console.error('错误详情:', error.response.data);
        }
        // 不再返回原文，而是抛出错误
        throw new Error(`无法翻译文本 "${text}" (${key}): ${error.message}`);
    }
}

// 格式化对象为JS代码
function formatObject(obj, indent = 0) {
    const spaces = '  '.repeat(indent);
    let result = '{\n';
    
    for (const [key, value] of Object.entries(obj)) {
        if (typeof value === 'object') {
            result += `${spaces}  ${key}: ${formatObject(value, indent + 1)},\n`;
        } else {
            // 使用双引号包裹值，并转义内部可能存在的双引号
            const escapedValue = value.replace(/"/g, '\\"');
            result += `${spaces}  ${key}: "${escapedValue}",\n`;
        }
    }
    
    result += `${spaces}}`;
    return result;
}

// 批量翻译对象
async function translateObject(obj, from = 'zh', parentKey = '') {
    const result = {};
    
    for (const [key, value] of Object.entries(obj)) {
        const fullKey = parentKey ? `${parentKey}.${key}` : key;
        
        if (typeof value === 'string') {
            // 对每个目标语言进行翻译
            for (const targetLang of TARGET_LANGUAGES) {
                try {
                    const translatedText = await translateText(value, fullKey, from, targetLang);
                    // 保存翻译结果
                    await saveTranslation(targetLang, fullKey, translatedText);
                } catch (error) {
                    console.error(`跳过保存翻译, ${error.message}`);
                }
            }
            result[key] = value;
        } else if (typeof value === 'object') {
            result[key] = await translateObject(value, from, fullKey);
        }
    }
    
    return result;
}

// 保存翻译结果到文件
async function saveTranslation(lang, key, value) {
    const filePath = path.join(__dirname, `../../locale/${lang}.js`);
    let translations = {};
    
    try {
        const content = await fs.readFile(filePath, 'utf8');
        // 提取对象内容
        const match = content.match(/export default ({[\s\S]*})/);
        if (match) {
            translations = safelyParseObject(match[1]);
        }
    } catch (error) {
        // 如果文件不存在，使用空对象
    }
    
    // 设置嵌套的key值
    const keys = key.split('.');
    let current = translations;
    for (let i = 0; i < keys.length - 1; i++) {
        if (!current[keys[i]]) {
            current[keys[i]] = {};
        }
        current = current[keys[i]];
    }
    current[keys[keys.length - 1]] = value;
    
    // 生成JS文件内容，保持原有格式
    const fileContent = `export default ${formatObject(translations)}\n`;
    await fs.writeFile(filePath, fileContent, 'utf8');
}

// 检查并删除目标语言文件中多余的key
async function synchronizeKeys(sourceObj, targetObj, parentKey = '') {
    const keysToDelete = [];

    // 递归遍历目标对象，找出在源对象中不存在的key
    for (const [key, value] of Object.entries(targetObj)) {
        const fullKey = parentKey ? `${parentKey}.${key}` : key;

        // 如果源对象中不存在该key，标记为删除
        if (!(key in sourceObj)) {
            keysToDelete.push(fullKey);
            continue;
        }

        // 如果是嵌套对象，需要递归比较
        if (typeof value === 'object' && value !== null &&
            typeof sourceObj[key] === 'object' && sourceObj[key] !== null) {
            // 对源对象中的每个属性，检查目标对象是否有匹配的
            // 并递归检查嵌套对象中是否有需要删除的key
            const nestedKeysToDelete = await synchronizeKeys(sourceObj[key], value, fullKey);
            keysToDelete.push(...nestedKeysToDelete);
        } else if (typeof value === 'object' && value !== null &&
            (typeof sourceObj[key] !== 'object' || sourceObj[key] === null)) {
            // 如果目标是对象但源不是对象，标记整个子树删除
            keysToDelete.push(fullKey);
        }
    }

    return keysToDelete;
}

// 从目标语言文件中删除指定的key
async function deleteKeys(lang, keysToDelete) {
    if (keysToDelete.length === 0) {
        return;
    }

    const filePath = path.join(__dirname, `../../locale/${lang}.js`);
    let translations = {};

    try {
        const content = await fs.readFile(filePath, 'utf8');
        // 提取对象内容
        const match = content.match(/export default ({[\s\S]*})/);
        if (match) {
            translations = safelyParseObject(match[1]);
        }
    } catch (error) {
        console.error(`无法读取文件 ${filePath}: ${error.message}`);
        return;
    }

    // 删除指定的key - 先排序以确保先删除深层路径
    keysToDelete.sort((a, b) => b.split('.').length - a.split('.').length);

    // 删除key
    for (const key of keysToDelete) {
        const keys = key.split('.');
        let current = translations;
        let parent = null;
        let lastKey = null;

        // 找到要删除的key的父级对象
        for (let i = 0; i < keys.length - 1; i++) {
            if (!current || typeof current !== 'object' || !(keys[i] in current)) {
                break;
            }
            parent = current;
            current = current[keys[i]];
            lastKey = keys[i];
        }

        // 删除key
        if (keys.length === 1) {
            // 直接删除顶级key
            delete translations[keys[0]];
        } else if (parent && typeof current === 'object' && lastKey) {
            // 删除嵌套key
            const finalKey = keys[keys.length - 1];
            if (finalKey in current) {
                delete current[finalKey];
            }

            // 如果删除后对象为空，也删除父级key
            if (Object.keys(current).length === 0) {
                delete parent[lastKey];
            }
        }
    }

    // 保存更新后的文件
    const fileContent = `export default ${formatObject(translations)}\n`;
    await fs.writeFile(filePath, fileContent, 'utf8');

    console.log(`已从 ${lang} 文件中删除 ${keysToDelete.length} 个多余的key`);
}

// 同步所有语言文件的key结构
async function synchronizeAllLanguageFiles(sourceObj) {
    for (const targetLang of TARGET_LANGUAGES) {
        try {
            const filePath = path.join(__dirname, `../../locale/${targetLang}.js`);
            let targetObj = {};

            try {
                const content = await fs.readFile(filePath, 'utf8');
                // 提取对象内容
                const match = content.match(/export default ({[\s\S]*})/);
                if (match) {
                    targetObj = safelyParseObject(match[1]);
                }
            } catch (error) {
                console.log(`创建新的目标语言文件: ${targetLang}`);
            }

            // 找出需要删除的key
            const keysToDelete = await synchronizeKeys(sourceObj, targetObj);

            if (keysToDelete.length > 0) {
                console.log(`在 ${targetLang} 中发现 ${keysToDelete.length} 个需要删除的多余key: ${keysToDelete.join(', ')}`);
                if (!process.argv.includes('--dry-run')) {
                    await deleteKeys(targetLang, keysToDelete);
                } else {
                    console.log('模拟模式：不进行实际删除操作');
                }
            } else {
                console.log(`${targetLang} 中没有多余的key需要删除`);
            }
        } catch (error) {
            console.error(`同步 ${targetLang} 文件结构时出错: ${error.message}`);
        }
    }
}

// 主函数
async function main() {
    // 读取中文翻译文件
    const zhFilePath = path.join(__dirname, '../../locale/zh-CN.js');
    const zhContent = await fs.readFile(zhFilePath, 'utf8');
    // 提取对象内容
    const match = zhContent.match(/export default ({[\s\S]*})/);
    if (!match) {
        throw new Error('无法解析中文翻译文件');
    }
    const zhTranslations = safelyParseObject(match[1]);

    // 先同步所有语言文件，删除多余的key
    await synchronizeAllLanguageFiles(zhTranslations);

    // 开始翻译
    await translateObject(zhTranslations);
    console.log('翻译完成！');
}

// 运行脚本
main().catch(console.error); 