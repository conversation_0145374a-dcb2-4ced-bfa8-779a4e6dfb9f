import { ref, reactive } from 'vue';
import {
    getSSHInfo,
    getSSHLoginInfo,
    getSSHBasicConfig,
    setSSHPassword,
    setRootLogin,
    setSshPort,
    setSshPwd,
    stopSshKeyLogin,
    openSshKey,
    setSsh,
    getSshKey,
} from '@/api/firewall';

// 状态管理
export const sshEnabled = ref(true);
export const passwordLoginEnabled = ref(false);
export const keyLoginEnabled = ref(false);
export const sshPort = ref('22');
export const rootPassword = ref('');
export const rootKey = ref('');
export const isLoading = ref(false);
export const saveSuccess = ref(null);
export const pageContainer = ref(null);
export const setPasswordDialog = ref(false);
export const setSSHDialog = ref(false);

// 各功能区域独立的loading状态
export const sshSwitchLoading = ref(false);
export const passwordLoginLoading = ref(false);
export const keyLoginLoading = ref(false);
export const portLoading = ref(false);
export const rootLoginModeLoading = ref(false);
export const rootPasswordLoading = ref(false);
export const statsLoading = ref(false);

// root登录模式选择器
export const rootLoginModes = ref([]);
export const rootLoginModeIndex = ref(0);
export const picker = ref(null);

// 模拟统计数据
export const stats = reactive({
    success: 0,
    todaySuccess: 0,
    failed: 0,
    todayFailed: 0,
});

// 切换开关
export const toggleSSH = (value) => {
    if (sshSwitchLoading.value) return;
    setSSHDialog.value = true;
};

export const handleSetSSH = async (close) => {
    if (sshSwitchLoading.value) return;
    sshSwitchLoading.value = true;
    close && close();
    try {
        const res = await setSsh({
            status: sshEnabled.value ? '1' : '0',
        });
        res.status ? pageContainer.value.notify.success(res.msg) : pageContainer.value.notify.error(res.msg);
        if (res.status) {
            sshEnabled.value = !sshEnabled.value;
        }
    } catch (error) {
        pageContainer.value.notify.error('设置SSH失败');
    } finally {
        sshSwitchLoading.value = false;
    }
};
export const togglePasswordLogin = async (value) => {
    if (passwordLoginLoading.value) return;
    passwordLoginLoading.value = true;
    try {
        const res = await setSshPwd(value);
        console.log(res);
        res.status ? pageContainer.value.notify.success(res.msg) : pageContainer.value.notify.error(res.msg);
    } catch (error) {
        pageContainer.value.notify.error('设置SSH密码登录失败');
    } finally {
        passwordLoginLoading.value = false;
    }
};

export const toggleKeyLogin = async (value) => {
    if (keyLoginLoading.value) return;
    keyLoginLoading.value = true;
    try {
        if (value) {
            const res = await openSshKey({ type: 'ed25519', ssh: 'yes' });
            res.status ? pageContainer.value.notify.success(res.msg) : pageContainer.value.notify.error(res.msg);
            getKey();
        } else {
            const res = await stopSshKeyLogin();
            res.status ? pageContainer.value.notify.success(res.msg) : pageContainer.value.notify.error(res.msg);
            rootKey.value = '';
        }
    } catch (error) {
        pageContainer.value.notify.error('设置SSH密钥登录失败');
    } finally {
        keyLoginLoading.value = false;
    }
};

// 处理root登录模式变化
export const confirmPicker = async (e) => {
    if (rootLoginModeLoading.value) return;
    rootLoginModeLoading.value = true;
    rootLoginModeIndex.value = e.indexs[0];
    try {
        const res = await setRootLogin({ p_type: rootLoginModes.value[rootLoginModeIndex.value].key });
        res.status ? pageContainer.value.notify.success(res.msg) : pageContainer.value.notify.error(res.msg);
    } catch (error) {
        console.log(error);
        pageContainer.value.notify.error('设置root登录方式失败');
    } finally {
        rootLoginModeLoading.value = false;
    }
};

// 保存端口号
export const handleSavePort = async () => {
    if (portLoading.value) return;
    portLoading.value = true;
    if (sshPort.value < 22 || sshPort.value > 65535) {
        pageContainer.value.notify.error('端口号必须在22-65535之间');
        portLoading.value = false;
        return;
    }
    try {
        const res = await setSshPort({ port: sshPort.value });
        res.status ? pageContainer.value.notify.success(res.msg) : pageContainer.value.notify.error(res.msg);
    } catch (error) {
        pageContainer.value.notify.error('设置SSH端口号失败');
    } finally {
        portLoading.value = false;
    }
};

// 处理密码输入
export const handlePasswordBlur = (e) => {
    const password = e.detail.value;
    if (password && password.length < 8) {
        uni.showToast({
            title: '密码长度不能少于8位',
            icon: 'none',
        });
    }
};

// 模拟保存操作
export const handleSave = () => {
    if (isLoading.value) return;
    isLoading.value = true;

    // 模拟API请求
    setTimeout(() => {
        isLoading.value = false;
        saveSuccess.value = true;

        // 重置成功状态
        setTimeout(() => {
            saveSuccess.value = null;
        }, 2000);
    }, 800);
};

// 刷新统计数据
export const refreshStats = async () => {
    if (statsLoading.value) return;
    statsLoading.value = true;
    try {
        const res = await getSSHLoginInfo();
        stats.success = res.success;
        stats.todaySuccess = res.today_success;
        stats.failed = res.error;
        stats.todayFailed = res.today_error;
    } catch (error) {
        pageContainer.value.notify.error('获取SSH登录信息失败');
    } finally {
        statsLoading.value = false;
    }
};

// 生成随机密码
export const generatePassword = () => {
    if (rootPasswordLoading.value) return;
    const chars = 'ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz123456789';
    let password = '';
    for (let i = 0; i < 16; i++) {
        password += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    rootPassword.value = password;
};

export const handleSSHInfo = async () => {
    try {
        const res = await getSSHInfo();
        sshEnabled.value = res.status;
        sshPort.value = res.port;
    } catch (error) {
        pageContainer.value.notify.error('获取SSH信息失败');
    } finally {
        uni.hideLoading();
    }
};

export const handleSSHBasicConfig = async () => {
    try {
        const res = await getSSHBasicConfig();
        passwordLoginEnabled.value = res?.password === 'yes';
        keyLoginEnabled.value = res?.pubkey === 'yes';
        // 将对象格式转换为key-value数组格式
        if (res?.root_login_types) {
            rootLoginModes.value = Object.entries(res.root_login_types).map(([key, value]) => ({
                key,
                value,
            }));
            rootLoginModeIndex.value = rootLoginModes.value.findIndex((item) => item.key === res?.root_login_type);
        }
    } catch (error) {
        pageContainer.value.notify.error('获取SSH基础设置失败');
    }
};

// 设置root密码
export const handleSetPassword = async (close) => {
    if (rootPasswordLoading.value) return;
    rootPasswordLoading.value = true;
    try {
        const res = await setSSHPassword({ username: 'root', password: rootPassword.value });
        res.status ? pageContainer.value.notify.success(res.msg) : pageContainer.value.notify.error(res.msg);
        close && close();
    } catch (error) {
        pageContainer.value.notify.error('设置root密码失败');
    } finally {
        rootPasswordLoading.value = false;
    }
};

// 复制密钥
export const copyKey = () => {
    if (!rootKey.value) {
        pageContainer.value.notify.error('没有可复制的密钥');
        return;
    }

    uni.setClipboardData({
        data: rootKey.value,
        success: () => {
            pageContainer.value.notify.success('密钥已复制到剪贴板');
        },
        fail: () => {
            pageContainer.value.notify.error('复制失败，请手动复制');
        },
    });
};

// 获取密钥
export const getKey = async () => {
    try {
        const res = await getSshKey();
        rootKey.value = res.msg;
    } catch (error) {
        pageContainer.value.notify.error('获取密钥失败');
    }
};

// 生成密钥
export const generateKey = async () => {
    try {
        const res = await openSshKey({ type: 'ed25519', ssh: 'yes' });
        res.status ? pageContainer.value.notify.success('密钥生成成功') : pageContainer.value.notify.error('密钥生成失败');
        await getKey();
    } catch (error) {
        pageContainer.value.notify.error('生成密钥失败');
    }
};
