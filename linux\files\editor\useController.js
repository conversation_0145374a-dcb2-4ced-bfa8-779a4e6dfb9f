import { reactive, ref } from 'vue';
import { getFileBody, saveFileBody } from '@/api/files';
import { $t } from '@/locale/index.js';

export const content = ref('');
export const editorHeight = ref(0);
export const lang = ref('javascript');
export const theme = ref('monokai');
export const fontSize = ref(14);
export const readonly = ref(false);
export const fileItem = ref({});
export const isUnsaved = ref(false); // 未保存
const editorOptionsPath = `/www/server/panel/BTPanel/static/editor/ace.editor.config.json`;
export const editorOption = reactive({
  supportedModes: {},
});
export const pageContainer = ref(null);

// 获取屏幕高度
export const initEditorHeight = () => {
  try {
    const systemInfo = uni.getSystemInfoSync();
    // 将88rpx转换为px
    const navHeightPx = 88 * (systemInfo.windowWidth / 750);
    editorHeight.value = systemInfo.windowHeight - systemInfo.statusBarHeight - navHeightPx;
  } catch (e) {
    console.error('获取系统信息失败：', e);
    editorHeight.value = 600; // 设置默认高度
  }
};

export const initEditorOptions = async () => {
  fontSize.value = uni.getStorageSync('editorFontSize') || 14;
  theme.value = uni.getStorageSync('editorTheme') || 'monokai';
  await handleLang();
  await getContent();
};

export const handleLang = async () => {
  try {
    const res = await useGetFileBody(editorOptionsPath);
    let editorOptions = {};
    if (res.data) {
      editorOptions = JSON.parse(res.data);
    }
    editorOption.supportedModes = editorOptions.supportedModes;
    const { mode } = useGetFileMode(fileItem.value.title);
    lang.value = mode;
  } catch (e) {
    console.error('获取文件语言模型失败：', e);
  }
};

export const getContent = async () => {
  uni.showLoading({
    title: $t('common.loading'),
  });
  try {
    const res = await useGetFileBody(fileItem.value.path);
    if (res.status) {
      fileItem.value = {
        ...fileItem.value,
        state: 0,
        encoding: res.encoding,
        stTime: Number(res.st_mtime),
        isReadOnly: res.only_read,
        next: res?.next || false,
      };
      content.value = res.data;
    } else {
      pageContainer.value.notify.error(res.msg || $t('editor.getContentFailed'));
      content.value = '';
    }
  } finally {
    uni.hideLoading();
  }
};

/**
 * @description 获取文件语言模型
 */
export const useGetFileMode = (fileName) => {
  let filenames = fileName.match(/\.([0-9A-z]*)$/);
  filenames = !Array.isArray(filenames) ? 'text' : filenames[1];
  for (let name in editorOption.supportedModes) {
    var data = editorOption.supportedModes[name],
      suffixs = data[0].split('|'),
      filename = name.toLowerCase();
    for (var i = 0; i < suffixs.length; i++) {
      // toLowerCase排除文件名大小写不匹配的情况
      if (filenames.toLowerCase() == suffixs[i].toLowerCase()) {
        return { name: name, mode: filename };
      }
    }
  }
  return { name: 'Text', mode: 'text' };
};

/**
 * @description 获取文件内容，钩子函数
 * @param {string} path 文件路径
 * @param {string} mode reverse
 * @param {number} p 分页
 * @returns {Promise<void>}
 */
export const useGetFileBody = async (path, mode, p) => {
  let params = {
    path,
    mode,
    p,
  };
  if (!mode) delete params.mode;
  if (!p) delete params.p;
  return await getFileBody(params);
};

/**
 * @description 保存编辑配置
 * @param aceConfig
 */
export const useSaveFileBody = async (data) => {
  let params = {
    path: data.path,
    data: data.data,
    encoding: data.encoding || 'UTF-8',
    st_mtime: data.st_mtime,
    force: data.force || 0,
    skip_conf_check: data.skip_conf_check || false,
  };
  if (!data.skip_conf_check) delete params.skip_conf_check;
  return await saveFileBody(params);
};

/**
 * @description 保存文件内容
 */
export const useSaveFiles = async (item, force = 0) => {
  if (!isUnsaved.value) {
    pageContainer.value.notify.warning($t('editor.noChange'));
    return;
  }
  uni.showLoading({
    title: $t('editor.saving'),
  });
  try {
    let params = {
      path: item.path,
      data: item.data,
      encoding: item.encoding,
      st_mtime: item.stTime,
      force,
    }
    const res = await useSaveFileBody(params);
    res.status ? pageContainer.value.notify.success(res.msg) : pageContainer.value.notify.error(res.msg);
    // 更新文件信息
    fileItem.value.stTime = res.st_mtime;
    isUnsaved.value = false;
  } catch (e) {
    pageContainer.value.notify.error($t('editor.saveFail'));
  } finally {
    uni.hideLoading();
  }
}