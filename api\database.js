import axios from '@/api/request';

const DEFAULT_API_TYPE = import.meta.env.VITE_PANEL_OWNER || 'domestic';

/**
 * @description 列表查询 - mysql
 * @param {string} data.table 查询表名
 * @param {string} data.search 查询条件
 * @param {string} data.limit 分页参数
 * @param {string} data.p 分页参数
 * @param {string} data.order 排序参数
 */
export const getMysqlList = (data) => {
	const url = DEFAULT_API_TYPE === 'domestic' ? '/datalist/data/get_data_list' : '/v2/data?action=getData';
	return axios(url, data);
};

/**
 * @description 列表查询是否配置远程数据库-mysql
 * @param {string} type 查询列表类型
 */
export const getMysqlCloudServer = () => {
	const url =
		DEFAULT_API_TYPE === 'domestic' ? '/database?action=GetCloudServer' : '/v2/database?action=GetCloudServer';
	return axios(url);
};

/**
 * @description 删除数据库
 * @param {number} data.id 数据库id
 * @param {string} data.name 数据库名称
 */
export const deleteDatabase = (data) => {
	const url =
		DEFAULT_API_TYPE === 'domestic' ? '/database?action=DeleteDatabase' : '/v2/database?action=DeleteDatabase';
	return axios(url, data, 'POST', (res) => {
		if (DEFAULT_API_TYPE === 'domestic') {
			return res;
		} else {
			return {
				status: res?.result,
				msg: res.result,
			};
		}
	});
};

/**
 * @description 修改备注
 * @param { AnyObject } data
 * @returns { Promise }
 */
export const updateRemark = (data) => {
	const url = DEFAULT_API_TYPE === 'domestic' ? '/data?action=setPs' : '/v2/data?action=setPs';
	return axios(url, data, 'POST', (res) => {
		if (DEFAULT_API_TYPE === 'domestic') {
			return res;
		} else {
			return {
				status: res?.result,
				msg: res.result,
			};
		}
	});
};

/**
 * @description 备份数据库
 * @param {number} data.id 数据库id
 */
export const backupDatabase = (data) => {
	const url = DEFAULT_API_TYPE === 'domestic' ? '/database?action=ToBackup' : '/v2/database?action=ToBackup';
	return axios(url, data, 'POST');
};

/**
 * @description 获取备份日志
 * @param {number} data.id 数据库id
 */
export const getBackupLog = (data) => {
	const url =
		DEFAULT_API_TYPE === 'domestic' ? '/database?action=GetBackupSize' : '/v2/database?action=GetBackupSize';
	return axios(url, data, 'POST');
};

/**
 * @description 获取MySQL信息
 * @returns {Promise<Object>} MySQL信息，包含datadir和port
 */
export const getMySQLInfo = () => {
	const url = DEFAULT_API_TYPE === 'domestic' ? '/database?action=GetMySQLInfo' : '/v2/database?action=GetMySQLInfo';
	return axios(url, {}, 'POST');
};

/**
 * @description 获取MySQL错误日志
 * @returns {Promise<Object>} MySQL错误日志内容
 */
export const getMySQLErrorLog = () => {
	const url = DEFAULT_API_TYPE === 'domestic' ? '/database?action=GetErrorLog' : '/v2/database?action=GetErrorLog';
	return axios(url, {}, 'POST');
};

/**
 * @description 清空MySQL错误日志
 * @returns {Promise<Object>} 清空结果
 */
export const clearMySQLErrorLog = () => {
	const url = DEFAULT_API_TYPE === 'domestic' ? '/database?action=GetErrorLog' : '/v2/database?action=GetErrorLog';
	return axios(url, { close: 1 }, 'POST');
};

/**
 * @description 设置MySQL端口
 * @param {Object} data - 请求参数
 * @param {number} data.port - 端口号
 * @returns {Promise<Object>} 设置结果
 */
export const setMySQLPort = (data) => {
	const url = DEFAULT_API_TYPE === 'domestic' ? '/database?action=SetMySQLPort' : '/v2/database?action=SetMySQLPort';
	return axios(url, data, 'POST');
};

/**
 * @description 设置MySQL数据目录
 * @param {Object} data - 请求参数
 * @param {string} data.datadir - 数据目录路径，默认值 /www/server/data
 * @returns {Promise<Object>} 设置结果
 */
export const setDataDir = (data) => {
	const url = DEFAULT_API_TYPE === 'domestic' ? '/database?action=SetDataDir' : '/v2/database?action=SetDataDir';
	return axios(url, data, 'POST');
};
