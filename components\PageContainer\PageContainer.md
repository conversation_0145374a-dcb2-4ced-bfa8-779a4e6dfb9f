# PageContainer 组件

## 组件介绍

PageContainer 是一个通用页面容器组件，提供了标准的页面布局结构，包括自定义导航栏和内容区域。该组件支持主题切换，并能够根据配置自动调整内容区域的高度，同时支持下拉刷新功能。

## 功能特点

- 支持主题切换
- 可配置的导航栏
- 自适应的内容滚动区域
- 支持底部 TabBar 适配
- 提供多个插槽以增强可定制性
- 支持下拉刷新功能

## 属性说明

| 属性名 | 类型 | 默认值 | 说明 |
|-------|------|--------|------|
| title | String | '页面标题' | 导航栏标题 |
| isBack | Boolean | true | 是否显示返回按钮 |
| bgColor | String | '' | 导航栏背景色，为空则使用主题默认颜色 |
| isShowNav | Boolean | true | 是否显示导航栏 |
| hasTabBar | Boolean | false | 是否考虑底部 TabBar 的高度 |
| refresherOnly | Boolean | false | 是否仅使用下拉刷新功能 |
| refresherEnabled | Boolean | false | 是否启用下拉刷新功能 |

## 事件

| 事件名 | 说明 | 回调参数 |
|-------|------|---------|
| refresh | 下拉刷新触发事件 | complete (刷新完成回调函数) |

## 插槽

| 插槽名 | 说明 |
|-------|------|
| default | 页面主要内容区域 |
| nav-left | 导航栏左侧区域 |
| nav-right | 导航栏右侧区域 |

## 使用示例

```vue
<template>
  <page-container 
    title="首页" 
    :is-back="false" 
    :has-tab-bar="true"
  >
    <!-- 导航栏右侧添加按钮 -->
    <template #nav-right>
      <view class="nav-right-btn">
        <uni-icons type="plusempty" size="22"></uni-icons>
      </view>
    </template>
    
    <!-- 页面内容 -->
    <view class="home-content">
      页面内容区域
    </view>
  </page-container>
</template>

<script setup>
const onRefresh = (complete) => {
  // 刷新数据逻辑
  setTimeout(() => {
    // 数据加载完成后调用complete结束刷新状态
    complete();
  }, 1000);
}
</script>
```

## 样式定制

组件内部使用 CSS 变量实现主题适配，可通过覆盖相应的 CSS 变量来实现自定义样式：

- `--bg-color`: 页面背景色
- `--status-bar-height`: 状态栏高度

## 注意事项

- 内容区域高度会根据是否显示导航栏和底部 TabBar 自动调整
- 组件依赖于 ThemeProvider、CustomNav 和 Z-Paging 组件
- 下拉刷新事件触发后，需要调用回调函数 complete 来结束刷新状态 