import ApiAdapter from './ApiAdapter';

/**
 * 国际API适配器
 * 将国际API的响应格式转换为国内标准格式
 */
export default class InternationalApiAdapter extends ApiAdapter {
  /**
   * 转换响应数据
   * 将国际版API响应转换为统一格式
   * @param {Object} response 原始响应数据
   * @returns {Object} 转换后的响应数据
   */
  transformResponse(response) {
    return response.message || response;
  }

  /**
   * 处理错误
   * @param {Object} error 原始错误对象
   * @returns {Object} 处理后的错误对象
   */
  handleError(error) {
    return {
      status: false,
      msg: error.message || error.errMsg || 'Request failed',
      error: error,
    };
  }
}
