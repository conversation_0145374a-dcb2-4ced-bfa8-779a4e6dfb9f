<template>
	<view>
		<!-- 查杀站点 -->
		<view class="form-group">
			<view class="form-label-row">
				<text>查杀站点</text>
			</view>
			<button class="region-select-button" @click="showSitePicker" :disabled="isEditMode">
				<text>{{ getSiteLabel() || '请选择查杀站点' }}</text>
				<uv-icon name="arrow-down" size="14" color="#666666"></uv-icon>
			</button>
		</view>

		<!-- 进程锁 -->
		<view class="form-group">
			<view class="form-row">
				<text>进程锁</text>
				<uv-switch :value="formData.flock" @change="updateField('flock', $event)" activeColor="#20a53a"></uv-switch>
			</view>
		</view>

		<!-- Picker组件 -->
		<uv-picker
			ref="sitePicker"
			:columns="[siteOptions]"
			keyName="label"
			@confirm="onSiteConfirm"
		></uv-picker>
	</view>
</template>

<script setup>
	import { ref, onMounted, getCurrentInstance } from 'vue';
	import { getCrontabDataList } from '@/api/crontab';
	import { truncateText } from '../useController';

	const { proxy } = getCurrentInstance();

	const props = defineProps({
		formData: {
			type: Object,
			required: true
		},
		isEditMode: {
			type: Boolean,
			default: false
		}
	});

	const emit = defineEmits(['update:formData']);

	// 数据选项
	const siteOptions = ref([{ label: '所有[所有网站]', value: 'ALL' }]);

	// Picker引用
	const sitePicker = ref(null);

	// 更新字段
	const updateField = (field, event) => {
		const value = event.detail?.value || event.target?.value || event;
		emit('update:formData', { [field]: value });
	};

	// 显示选择器
	const showSitePicker = () => {
		proxy.$refs.sitePicker?.open();
	};

	// 获取显示标签
	const getSiteLabel = () => {
		const option = siteOptions.value.find(item => item.value === props.formData.sName);
		return option ? truncateText(option.label) : '';
	};

	// 确认选择
	const onSiteConfirm = (e) => {
		const selectedValue = e.value[0].value;
		emit('update:formData', { 
			sName: selectedValue,
			name: `木马查杀[ ${selectedValue === 'ALL' ? '所有网站' : selectedValue} ]`
		});
	};

	// 获取数据
	const loadData = async () => {
		try {
			const res = await getCrontabDataList({ type: 'sites' });
			
			// 设置网站选项
			siteOptions.value = [{ label: '所有[所有网站]', value: 'ALL' }].concat(
				res.data.map(item => ({
					label: `${item.name}[${item.ps}]`,
					value: item.name
				}))
			);

			// 初始化默认值
			if (!props.formData.sName) {
				emit('update:formData', { 
					sName: 'ALL',
					name: '木马查杀[ 所有网站 ]'
				});
			}
		} catch (error) {
			console.error('加载数据失败:', error);
		}
	};

	onMounted(() => {
		loadData();
	});
</script>

<style lang="scss" scoped>
	@import './form-styles.scss';

	.form-row {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-top: 20rpx;

		text {
			font-size: 28rpx;
			font-weight: 500;
			color: var(--text-color-primary);
		}
	}
</style>
