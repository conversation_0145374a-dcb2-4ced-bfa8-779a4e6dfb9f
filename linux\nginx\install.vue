<template>
	<view class="install-container">
		<view class="header">
			<view class="alert">
				<i class="icon-warning"></i>
				<text>当前未安装此插件，是否安装？</text>
			</view>
		</view>

		<view class="cards-container">
			<view class="card active">
				<view class="card-icon">
					<uni-icons fontFamily="iconfont" class="icon-nginx" size="24" color="#20a50a"></uni-icons>
				</view>
				<view class="card-content">
					<h3>nginx防火墙</h3>
					<p
						>Nginx防火墙是保护Web应用程序免受各种网络攻击，检测过滤恶意请求限制访问频率，增强应用程序的安全，易于配置和使用，是保护Web应用程序安全的重要工具之一</p
					>
				</view>
				<view class="card-check">
					<text class="check-icon">✓</text>
				</view>
			</view>
		</view>

		<view class="actions">
			<button class="btn-install" @click="installNginxFirewall">点击安装此插件</button>
		</view>
	</view>
</template>

<script setup>
	import { ref } from 'vue';

	const installNginxFirewall = () => {
		uni.navigateTo({
			url: '/linux/install/index?name=btwaf',
			animationType: 'zoom-fade-out',
		});
	};
</script>

<style lang="scss" scoped>
	.install-container {
		padding: 32rpx;
		max-width: 100%;
		margin: 0 auto;
		display: flex;
		flex-direction: column;
		gap: 48rpx;
	}

	.header {
		.alert {
			background-color: #fff3cd;
			border-radius: 16rpx;
			padding: 24rpx 32rpx;
			display: flex;
			align-items: center;
			gap: 16rpx;
			box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);

			.icon-warning {
				color: #ff9800;
				font-size: 40rpx;
				&::before {
					content: '⚠';
				}
			}

			text {
				font-weight: 500;
				color: #664d03;
			}
		}
	}

	.cards-container {
		display: flex;
		flex-direction: column;
		gap: 32rpx;

		@media (min-width: 768px) {
			flex-direction: row;
		}
	}

	.card {
		border-radius: 24rpx;
		padding: 40rpx;
		background-color: var(--dialog-bg-color);
		box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
		display: flex;
		align-items: center;
		cursor: pointer;
		transition: all 0.2s ease;
		position: relative;
		border: 2px solid transparent;

		&.active {
			border-color: var(--primary-color);
			background-color: var(--dialog-bg-color);
		}

		&:hover {
			transform: translateY(-2px);
			box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
		}

		.card-icon {
			width: 48px;
			height: 48px;
			display: flex;
			align-items: center;
			justify-content: center;
			border-radius: 12px;
			margin-right: 32rpx;
			background-color: #f5f5f5;
		}

		.card-content {
			flex: 1;

			h3 {
				margin: 0 0 8rpx 0;
				font-size: 32rpx;
				font-weight: 600;
				color: var(--text-color-primary);
			}

			p {
				margin: 0;
				font-size: 28rpx;
				color: #6b7280;
			}
		}

		.card-check {
			width: 44rpx;
			height: 44rpx;
			border-radius: 50%;
			background-color: var(--primary-color);
			display: flex;
			align-items: center;
			justify-content: center;

			.check-icon {
				color: white;
				font-size: 32rpx;
			}
		}
	}

	.actions {
		margin-top: 32rpx;

		.btn-install {
			width: 100%;
			background-color: var(--primary-color);
			color: white;
			border: none;
			border-radius: 16rpx;
			padding: 14rpx 32rpx;
			font-weight: 500;
			font-size: 32rpx;
			cursor: pointer;
			transition: background-color 0.2s;

			&:hover:not(:disabled) {
				background-color: var(--primary-color);
			}

			&:disabled {
				background-color: #9ca3af;
				cursor: not-allowed;
			}
		}
	}
</style>
