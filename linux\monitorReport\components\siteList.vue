<template>
	<view class="status-box">
		<view class="form">
			<view class="report-switch flex">
				<view class="text-secondary">时间：</view>
				<view class="" style="width: 75%">
					<uni-segmented-control
						:current="currentButton"
						:values="items"
						@clickItem="onClickItem"
						styleType="button"
						activeColor="#20a53a"
					></uni-segmented-control>
				</view>
				<canvas canvas-id="siteCanvas" id="siteCanvas" style="width: 60rpx; height: 60rpx"></canvas>
			</view>

			<!-- 概览数据 -->
			<!-- <view class="firewall-details interval">

				<view class="" v-for="(item,index) in fenceList" :key="index">
					<view class="details-title">{{item.name}}</view>
					<view class="details-count" v-if="item.type !== 'sent_bytes'">{{formatCurrency(item.value)}}</view>
					<view class="details-count" v-else>{{formatBytes(item.value)}}</view>
				</view>

			</view> -->
		</view>

		<view class="site-box">
			<view
				class="site-list rank"
				v-for="(item, index) in preventList"
				:key="item.id"
				@click="parenClick"
				:data-name="item.name"
				:data-index="index"
			>
				<view class="site-check-query flex">
					<view class="flex" style="width: 100%">
						<text class="check-title">网站：</text>
						<text class="check-value table-info-text">{{ item.name }}</text>
					</view>

					<view class="flex" style="width: 50%">
						<text class="check-title">浏览量：</text>
						<text class="check-value">{{ formatCurrency(item.total.pv_number) }}</text>
					</view>
					<view class="flex" style="width: 50%">
						<text class="check-title">访客数：</text>
						<text class="check-value">{{ formatCurrency(item.total.uv_number) }}</text>
					</view>
					<view class="flex" style="width: 50%">
						<text class="check-title">IP数：</text>
						<text class="check-value">{{ formatCurrency(item.total.ip_number) }}</text>
					</view>
					<view class="flex" style="width: 50%">
						<text class="check-title">总流量：</text>
						<text class="check-value">{{ formatBytes(item.total.sent_bytes) }}</text>
					</view>
					<view class="flex" style="width: 50%">
						<text class="check-title">QPS：</text>
						<text class="check-value">{{ formatCurrency(item.total.sec_request) }}</text>
					</view>
					<view class="flex" style="width: 50%">
						<text class="check-title">请求数：</text>
						<text class="check-value">{{ formatCurrency(item.total.request) }}</text>
					</view>
				</view>
			</view>

			<view class="not-data" v-if="preventList.length === 0">网站列表为空...</view>
		</view>
	</view>
</template>

<script setup>
	import { ref, onMounted, onBeforeUnmount, getCurrentInstance } from 'vue';
	import { getSiteList, getSiteConfigInfo, setSiteConfig } from '@/api/monitorReport';

	const instance = getCurrentInstance();

	const totalTime = ref(3); // 总时间60秒
	const remainingTime = ref(3); // 剩余时间
	const intervalId = ref(null); // 用于存储setInterval的ID
	const currentButton = ref(0);
	const preventStatus = ref(false); // 当前选中的状态
	const preventName = ref(''); // 当前选中的网站名
	const preventIndex = ref(0); // 当前选中的下标
	const items = ref(['今天', '昨天', '近7天', '近30天']);
	const fenceList = ref([
		{
			name: '浏览量(PV)',
			value: 0,
			type: 'pv_number',
		},
		{
			name: '访客量(UV)',
			value: 0,
			type: 'uv_number',
		},
		{
			name: 'IP数',
			value: 0,
			type: 'ip_number',
		},
		{
			name: '请求',
			value: 0,
			type: 'request',
		},
		{
			name: '流量',
			value: 0,
			type: 'sent_bytes',
		},
		{
			name: '蜘蛛爬虫',
			value: 0,
			type: 'spider_count',
		},
		{
			name: 'QPS',
			value: 0,
			type: 'sec_request',
		},
	]);
	const preventList = ref([]);
	const pageNum = ref(1);
	const logsScrollFlag = ref(true);
	const siteName = ref({}); // 原始代码中未明确使用，保持定义
	const siteInfo = ref({});

	const getList = () => {
		const dateObj = getNowDateType(currentButton.value);
		let params = {
			start_date: dateObj.start_date,
			end_date: dateObj.end_date,
			rows: 10,
			p: pageNum.value,
			sort_by: 'pv_number',
			sort_reverse: 1,
			is_verify: false,
		};
		getSiteList(params).then((data) => {
			startCountdown();
			uni.getStorage({
				key: 'set_site_config',
				success: function (storageRes) {
					if (storageRes.data && storageRes.data.siteName) {
						getSiteConfigInfo({
							SiteName: storageRes.data.siteName,
						}).then((siteConfigData) => {
							const item = preventList.value.find((item) => item.name === storageRes.data.siteName);
							if (item) {
								item.open = siteConfigData.open;
							}
						});
					}
				},
			});
			if (!logsScrollFlag.value) return;
			if (data.list.data.length < 10) {
				logsScrollFlag.value = false;
			}
			fenceList.value.forEach((item) => {
				item.value = data.global[item.type];
			});
			// 使用 spread operator 和 reduce 来合并和去重，确保响应性
			const newList = [...preventList.value, ...data.list.data];
			preventList.value = newList.reduce((acc, item) => {
				const index = acc.findIndex((v) => v.id === item.id);
				if (index !== -1) {
					acc[index] = item;
				} else {
					acc.push(item);
				}
				return acc;
			}, []);
			pageNum.value++;
		});
	};

	const parenClick = (e) => {
		preventName.value = e.currentTarget.dataset.name;
		preventIndex.value = e.currentTarget.dataset.index;
	};

	const clickSet = (name, index) => {
		uni.navigateTo({
			// 使用 uni-app 标准 API
			url: `/pages/monitorReportNew/setMonitor?SiteName=${name}`, // 路径通常以 /pages 开头
		});
		pageNum.value = Math.floor(index / 10) + 1;
		logsScrollFlag.value = true;
	};

	const switchStatus = (e, name, status) => {
		preventStatus.value = e.detail.value;
		preventName.value = name;

		// 直接修改数组元素的属性以保持响应性
		if (preventList.value[preventIndex.value]) {
			preventList.value[preventIndex.value].open = e.detail.value;
		}

		setSiteConfig({
			SiteName: preventName.value,
			open: preventStatus.value ? 1 : 0,
		}).then((data) => {
			uni.showToast({
				title: data.msg,
				icon: 'none',
				duration: 3000,
			});
			if (!data.status) {
				setTimeout(() => {
					if (preventList.value[preventIndex.value]) {
						preventList.value[preventIndex.value].open = !e.detail.value;
					}
				}, 300);
			}
		});
	};

	const onClickItem = (e) => {
		const { currentIndex } = e;
		currentButton.value = currentIndex;
		pageNum.value = 1;
		preventList.value = []; // 重置列表以便重新加载
		logsScrollFlag.value = true;
		getList();
	};

	const formatCurrency = (num) => {
		let nums = Number(num);
		if (isNaN(nums)) {
			return num;
		}
		if (nums === 0) {
			return '0';
		}
		let sign = nums < 0 ? '-' : '';
		nums = Math.abs(Math.round(nums));
		let integerPart = nums.toString();
		integerPart = integerPart.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
		return sign + integerPart;
	};

	const getNowDateType = (type) => {
		let dateObj = {};
		const today = new Date();
		switch (type) {
			case 0: // 今天
				dateObj.start_date = formatDate(today);
				dateObj.end_date = formatDate(today);
				break;
			case 1: // 昨天
				const yesterday = new Date(today);
				yesterday.setDate(today.getDate() - 1);
				dateObj.start_date = formatDate(yesterday);
				dateObj.end_date = formatDate(yesterday);
				break;
			case 2: // 近7天
				const last7Days = new Date(today);
				last7Days.setDate(today.getDate() - 7);
				dateObj.start_date = formatDate(last7Days);
				dateObj.end_date = formatDate(today);
				break;
			case 3: // 近30天
				const last30Days = new Date(today);
				last30Days.setDate(today.getDate() - 30);
				dateObj.start_date = formatDate(last30Days);
				dateObj.end_date = formatDate(today);
				break;
			default:
				dateObj.start_date = '';
				dateObj.end_date = '';
				break;
		}
		return dateObj;
	};

	const formatDate = (date) => {
		const year = date.getFullYear();
		const month = String(date.getMonth() + 1).padStart(2, '0');
		const day = String(date.getDate()).padStart(2, '0');
		return `${year}-${month}-${day}`;
	};
	const theme = ref(uni.getStorageSync('app_theme'));
	const startCountdown = () => {
		if (intervalId.value) {
			clearInterval(intervalId.value);
		}
		remainingTime.value = totalTime.value;
		drawProgressCircle(); // 初始绘制确保在定时器启动前有显示
		intervalId.value = setInterval(() => {
			remainingTime.value--;
			drawProgressCircle();

			if (remainingTime.value < 0) {
				clearInterval(intervalId.value);
				getList();
			}
		}, 1000);
	};

	const drawProgressCircle = () => {
		let ctx;
		// #ifdef MP-WEIXIN || MP-ALIPAY || MP-BAIDU || MP-TOUTIAO || MP-QQ
		ctx = uni.createCanvasContext('siteCanvas', instance); // 第二个参数在支付宝小程序中是必须的
		// #endif
		// #ifndef MP-WEIXIN || MP-ALIPAY || MP-BAIDU || MP-TOUTIAO || MP-QQ
		ctx = uni.createCanvasContext('siteCanvas');
		// #endif

		const rpxToPx = uni.getSystemInfoSync().screenWidth / 750; // 更准确的 rpx 转 px
		const radius = 25 * rpxToPx;
		const lineWidth = 4 * rpxToPx;
		const centerX = 30 * rpxToPx; // canvas 宽度 60rpx / 2
		const centerY = 30 * rpxToPx; // canvas 高度 60rpx / 2
		const startAngle = -Math.PI / 2;
		const progress = remainingTime.value / totalTime.value;

		ctx.clearRect(0, 0, 60 * rpxToPx, 60 * rpxToPx);

		ctx.beginPath();
		ctx.arc(centerX, centerY, radius, 0, 2 * Math.PI);
		ctx.setLineWidth(lineWidth);
		ctx.setStrokeStyle('#e5e5e5');
		ctx.stroke();

		const endAngle = startAngle + 2 * Math.PI * progress;
		ctx.beginPath();
		ctx.arc(centerX, centerY, radius, startAngle, endAngle);
		ctx.setLineWidth(lineWidth);
		ctx.setStrokeStyle('#4caf50');
		ctx.stroke();

		ctx.setFontSize(14 * rpxToPx); // 字体大小也用rpx转换
		if (theme.value === 'dark') {
			ctx.setFillStyle('#fff');
		} else {
			ctx.setFillStyle('#000');
		}
		ctx.setTextAlign('center');
		ctx.setTextBaseline('middle');
		ctx.fillText(remainingTime.value > 0 ? remainingTime.value : 0, centerX, centerY);

		ctx.draw();
	};

	const formatBytes = (bytes) => {
		if (bytes === 0) return '0 B';
		const units = ['B', 'KB', 'MB', 'GB', 'TB'];
		const k = 1024;
		const i = Math.floor(Math.log(bytes) / Math.log(k));
		const value = parseFloat((bytes / Math.pow(k, i)).toFixed(2));
		return `${value} ${units[i]}`;
	};

	onMounted(() => {
		getList();
	});

	onBeforeUnmount(() => {
		if (intervalId.value) {
			clearInterval(intervalId.value);
		}
	});
</script>

<style scoped>
	.status-box {
		/* padding: 16rpx; */
		height: 100%;
		overflow: scroll;
	}

	.flow-charts {
		width: 100%;
		height: 650rpx;
		background-color: #fff;
	}

	.charts {
		min-width: 100%;
		width: 100%;
		height: 650rpx;
		background-color: #fff;
	}

	.report-switch {
		height: 100rpx;
		align-items: center;
		justify-content: space-between;
		border-bottom: 1rpx solid #d3d8d8;
		padding: 0 40rpx;
	}

	.report-switch view {
		font-size: 30rpx;
	}

	/* 网站数据 */
	.firewall-details {
		display: grid;
		grid-template-columns: repeat(3, 1fr);
		gap: 10rpx;
		/* 可根据需要调整列间距 */
		margin: 3% 30rpx 3%;
		padding: 20rpx 0;
		background-color: #fafafa;
		border: 1rpx solid #ddd;
		border-radius: 16rpx;
	}

	.firewall-details > view {
		height: 80px;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
	}

	.details-query-li {
		justify-content: flex-start;
		align-items: center;
	}

	.details-title {
		color: #666;
		font-size: 24rpx;
		display: flex;
		align-items: center;
	}

	.details-count {
		color: #333;
		font-size: 30rpx;
		font-weight: 700;
		padding-top: 25rpx;
	}

	/* 站点列表 */
	.site-scroll {
		height: calc(100vh - 100rpx);
	}

	.site-box {
		overflow: hidden;
		background-color: var(--bg-color);
		padding-bottom: 10rpx;
		padding-top: 20rpx;
	}

	.site-box > view:first-child {
		margin-top: 0 !important;
	}

	.site-list {
		background-color: var(--dialog-bg-color);
		justify-content: center;
		padding: 15rpx 30rpx 15rpx 30rpx;
		margin-top: 5%;
		box-shadow: 0 8rpx 16rpx rgba(0, 0, 0, 0.1);
	}

	.site-title-box {
		align-items: center;
		justify-content: space-between;
		height: 100rpx;
		border-bottom: 1rpx dashed #ccc;
	}

	.site-title {
		color: #20a53a;
		font-size: 28rpx;
	}

	.site-switch text {
		font-size: 30rpx;
		color: #444;
	}

	.site-check-query {
		width: 100%;
		align-items: center;
		justify-content: space-between;
		flex-wrap: wrap;
		padding-bottom: 20rpx;
	}

	.site-check-query > view {
		width: 50%;
		height: 80rpx;
		align-items: center;
	}

	.check-title {
		color: var(--text-color-secondary);
		font-size: 26rpx;
		white-space: nowrap;
	}

	.check-value {
		font-size: 28rpx;
		color: var(--text-color-secondary);
	}

	/* 无数据提示 */
	.not-data {
		width: 100%;
		font-size: 30rpx;
		color: var(--text-color-secondary);
		text-align: center;
		padding: 10% 0;
	}

	.global-button {
		width: 100%;
		background-color: #fff;
		align-items: center;
		justify-content: flex-end;
		border-top: 1rpx solid #d7dde1;
	}

	.global-button > view {
		padding: 0 30rpx;
		text-align: center;
		height: 60rpx;
		line-height: 60rpx;
		color: #fff;
		font-size: 26rpx;
		margin: 22rpx 30rpx 22rpx 10rpx;
		border-radius: 6rpx;
		background-color: #20a53a;
	}
</style>
