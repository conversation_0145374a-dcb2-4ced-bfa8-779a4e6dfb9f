export const isUndefined = (val) => typeof val === 'undefined';
export const isNull = (val) => val === null;
export const isBoolean = (val) => typeof val === 'boolean';
export const isNumber = (val) => typeof val === 'number' && !isNaN(val);
export const isObject = (val) =>
  val !== null && typeof val === 'object' && !Array.isArray(val) && !(val instanceof Function);
export const isFunction = (val) => typeof val === 'function';
export const isString = (val) => typeof val === 'string';
export const isArray = (val) => Array.isArray(val);
export const isEmpty = (val) => {
  if (val === null || val === undefined) return true;
  if (typeof val === 'string' || Array.isArray(val)) return val.length === 0;
  if (typeof val === 'object') return Object.keys(val).length === 0;
  return false;
};
export const isStringNumber = (val) => /^\d+$/.test(val);
export const isDate = (val) => val instanceof Date;
export const isPromise = (val) => val instanceof Promise || (val && typeof val.then === 'function');
export const isSymbol = (val) => typeof val === 'symbol';
export const isVNode = (val) => val !== null && typeof val === 'object' && 'type' in val;
