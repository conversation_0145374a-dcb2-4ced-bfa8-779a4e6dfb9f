<template>
	<page-container ref="pageContainer" :is-back="true" title="站点配置">
		<view class="security-dashboard">
			<!-- Header -->
			<view class="header">
				<view class="header__content">
					<text class="header__title">公共配置</text>
					<view class="header__actions">
						<view class="header__icon-button" @click="menuOpen = !menuOpen">
							<uv-icon name="setting" size="24" color="#757575"></uv-icon>
						</view>
					</view>
				</view>

				<!-- Settings Menu -->
				<view v-if="menuOpen" class="settings-menu" style="background-color: var(--bg-color)">
					<view class="settings-menu__content">
						<view class="settings-menu__item">
							<view class="toggle-wrapper">
								<view class="toggle-content">
									<uv-icon name="lock-fill" size="24" color="#757575" class="toggle-icon"></uv-icon>
									<text class="toggle-text">堡塔免费恶意IP共享库</text>
								</view>
								<uv-switch
									activeColor="#20a50a"
									:model-value="settings.blockMaliciousIp"
									size="20"
									@change="
										(val) => {
											settings.blockMaliciousIp = val;
											handleBlockMaliciousIp();
										}
									"
									:loading="loadingStates.maliciousIp"
								></uv-switch>
							</view>
						</view>
						<view class="settings-menu__item">
							<view class="toggle-wrapper">
								<view class="toggle-content">
									<uv-icon name="map" size="24" color="#757575" class="toggle-icon"></uv-icon>
									<text class="toggle-text">禁国外增强封锁</text>
								</view>
								<uv-switch
									activeColor="#20a50a"
									:model-value="settings.blockForeignIp"
									size="20"
									@change="
										(val) => {
											settings.blockForeignIp = val;
											handleBlockForeignIp();
										}
									"
									:loading="loadingStates.foreignIp"
								></uv-switch>
							</view>
						</view>
						<view class="settings-menu__item">
							<view class="toggle-wrapper">
								<view class="toggle-content">
									<uv-icon
										name="warning-fill"
										size="24"
										color="#757575"
										class="toggle-icon"
									></uv-icon>
									<text class="toggle-text">堡塔恶意情报IP库</text>
								</view>
								<uv-switch
									activeColor="#20a50a"
									:model-value="settings.blockMaliciousRequests"
									size="20"
									@change="
										(val) => {
											settings.blockMaliciousRequests = val;
											handleBlockMaliciousRequests();
										}
									"
									:loading="loadingStates.maliciousRequests"
								></uv-switch>
							</view>
						</view>
					</view>
				</view>
			</view>

			<!-- Content -->
			<view class="content">
				<view class="site-list">
					<!-- 站点列表 -->
					<view v-if="siteStates.length > 0">
						<view v-for="site in siteStates" :key="site.id" class="site-card mb-20">
							<view class="site-card__header" @click="toggleSiteExpand(site.id)">
								<view class="site-info">
									<text class="site-title">{{ site.id }}</text>
								</view>
								<view class="site-card__actions">
									<uv-switch
										:model-value="site.status"
										@change="toggleSiteStatus(site.id)"
										activeColor="#20a50a"
										@click.stop
										size="22"
									></uv-switch>
									<view class="expand-btn" @click.stop="toggleSiteExpand(site.id)">
										<uv-icon
											:name="expandedSite === site.id ? 'arrow-up' : 'arrow-down'"
											size="20"
											color="#757575"
										></uv-icon>
									</view>
								</view>
							</view>

							<!-- Quick Toggle Section - Always Visible -->
							<view class="site-card__overview">
								<view class="feature-grid">
									<view
										v-for="key in quickToggleFeatures"
										:key="key"
										class="feature-button"
										:class="{
											'feature-button--active': site.features[key].enabled,
											'feature-button--loading': getFeatureLoading(site.id, key)
										}"
										@click.stop="!getFeatureLoading(site.id, key) && toggleFeature(site.id, key)"
									>
										<view class="feature-button__icon-wrapper">
											<uv-icon
												:name="getFeatureIcon(key)"
												size="24"
												:color="site.features[key].enabled ? '#ffffff' : '#9e9e9e'"
												class="feature-icon"
											></uv-icon>
										</view>
										<text class="feature-button__label">{{ featureLabels[key] }}</text>
										<view v-if="site.features[key].count > 0" class="feature-button__badge">
											{{ site.features[key].count }}
										</view>
									</view>
								</view>
							</view>

							<!-- Expanded Details -->
							<view v-if="expandedSite === site.id" class="site-card__details">
								<view class="details-header">
									<uv-icon name="setting" size="24" color="#9e9e9e"></uv-icon>
									<text class="details-title">高级设置</text>
								</view>
								<view class="feature-list">
									<view
										v-for="(value, key) in site.features"
										:key="`${site.id}-${key}-${value.enabled}`"
										class="feature-item"
										:class="{
											'feature-item--active': value.enabled,
											'feature-item--enabled': value.enabled
										}"
									>
										<view class="feature-item__content">
											<view
												class="feature-item__icon-wrapper"
												:class="{ 'feature-item__icon-wrapper--active': value.enabled }"
											>
												<uv-icon
													:name="getFeatureIcon(key)"
													size="24"
													:color="value.enabled ? '#ffffff' : '#9e9e9e'"
													class="feature-icon"
												></uv-icon>
											</view>
											<view class="feature-item__info">
												<text class="feature-item__label">{{ featureLabels[key] }}</text>
												<text v-if="value.description" class="feature-item__desc">
													{{ value.description }}
												</text>
											</view>
											<view v-if="value.count > 0" class="feature-item__count">
												{{ value.count }}
											</view>
										</view>
										<uv-switch
											activeColor="#20a50a"
											:model-value="value.enabled"
											@change="toggleFeature(site.id, key)"
											:loading="getFeatureLoading(site.id, key)"
											size="22"
										></uv-switch>
									</view>
								</view>
							</view>
						</view>
					</view>

					<!-- 空状态提示 -->
					<view v-else-if="!isLoading && siteStates.length === 0" class="empty-state">
						<view class="empty-state__content">
							<text class="empty-state__title">暂无站点配置</text>
							<text class="empty-state__desc">当前没有检测到任何站点配置信息</text>
						</view>
					</view>

					<!-- 加载状态 -->
					<view v-else-if="isLoading" class="loading-state">
						<view class="loading-state__content">
							<uv-loading-icon mode="spinner" size="40" color="#20a50a"></uv-loading-icon>
							<text class="loading-state__text">正在加载站点配置...</text>
						</view>
					</view>
				</view>
			</view>
		</view>
	</page-container>
</template>

<script setup>
	import { ref } from 'vue';
	import PageContainer from '@/components/pageContainer/index.vue';

	// 从 useController 中分别导入所需的变量和函数
	import {
		expandedSite,
		menuOpen,
		isLoading,
		siteStates,
		settings,
		loadingStates,
		featureLoadingStates,
		featureLabels,
		quickToggleFeatures,
		toggleSiteExpand,
		toggleSiteStatus,
		toggleFeature,
		getFeatureIcon,
		createHandleBlockMaliciousIp,
		createHandleBlockForeignIp,
		createHandleBlockMaliciousRequests,
		initializeController,
		pageContainer,
	} from './useController.js';

	// 创建具体的处理函数
	const handleBlockMaliciousIp = createHandleBlockMaliciousIp(pageContainer);
	const handleBlockForeignIp = createHandleBlockForeignIp(pageContainer);
	const handleBlockMaliciousRequests = createHandleBlockMaliciousRequests(pageContainer);

	// 获取功能开关的loading状态
	const getFeatureLoading = (siteId, feature) => {
		const loadingKey = `${siteId}_${feature}`;
		return featureLoadingStates.value[loadingKey] || false;
	};

	// 初始化控制器
	initializeController();
</script>

<style lang="scss" scoped>
	// Variables
	$primary-color: #20a50a;
	$primary-light: #e8f5e9;
	$primary-dark: #1a8a08;
	$primary-ultra-light: #f0f9f0;
	$gray-50: #fafafa;
	$gray-100: #f5f5f5;
	$gray-200: #eeeeee;
	$gray-300: #e0e0e0;
	$gray-400: #bdbdbd;
	$gray-500: #9e9e9e;
	$gray-600: #757575;
	$gray-700: #616161;
	$gray-800: #424242;
	$gray-900: #212121;
	$red-500: #f44336;
	$red-100: #ffebee;
	$orange-500: #ff9800;
	$blue-500: #2196f3;
	$white: var(--dialog-bg-color);
	$black: #000000;

	// Shadows
	$shadow-1:
		0 2px 8px rgba(0, 0, 0, 0.15),
		0 1px 3px rgba(0, 0, 0, 0.12);
	$shadow-2:
		0 6px 16px rgba(0, 0, 0, 0.12),
		0 3px 6px rgba(0, 0, 0, 0.08);
	$shadow-3:
		0 12px 28px rgba(0, 0, 0, 0.15),
		0 6px 12px rgba(0, 0, 0, 0.12);
	$shadow-4:
		0 25px 50px rgba(0, 0, 0, 0.25),
		0 12px 18px rgba(0, 0, 0, 0.08);

	// Border radius
	$radius-xs: 4rpx;
	$radius-sm: 6rpx;
	$radius-md: 8rpx;
	$radius-lg: 12rpx;
	$radius-xl: 16rpx;
	$radius-2xl: 20rpx;
	$radius-full: 50%;

	// Transitions
	$transition-fast: 0.15s ease-in-out;
	$transition-normal: 0.3s ease-in-out;
	$transition-slow: 0.5s ease-in-out;

	// Base Styles
	.security-dashboard {
		color: $gray-900;
		font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Hiragino Sans GB',
			'Microsoft YaHei', Arial, sans-serif;
	}

	// Header
	.header {
		background: var(--bg-color);
		box-shadow: $shadow-1;
		position: sticky;
		top: 0;
		z-index: 100;
		border-bottom: 1rpx solid $gray-200;
		animation: slideInDown 0.5s ease-out;

		&__content {
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding: 10rpx 32rpx;
			min-height: 88rpx;
		}

		&__title {
			font-size: 30rpx;
			font-weight: 600;
			color: $primary-color;
			letter-spacing: 0.5rpx;
			animation: fadeInLeft 0.6s ease-out 0.2s both;
		}

		&__actions {
			display: flex;
			gap: 8rpx;
			animation: fadeInRight 0.6s ease-out 0.3s both;
		}

		&__icon-button {
			width: 64rpx;
			height: 64rpx;
			border-radius: $radius-full;
			background: $gray-100;
			border: none;
			display: flex;
			align-items: center;
			justify-content: center;
			transition: all $transition-fast;

			&:active {
				transform: scale(0.95);
			}
		}
	}

	// Info Panel
	.info-panel {
		background: linear-gradient(135deg, $primary-ultra-light 0%, $gray-50 100%);
		border-bottom: 1rpx solid $gray-200;
		animation: fadeInDown $transition-normal ease-out;

		&__content {
			padding: 32rpx;
		}

		&__item {
			display: flex;
			align-items: center;
			margin-bottom: 24rpx;

			&:last-child {
				margin-bottom: 0;
			}

			.info-icon {
				margin-right: 16rpx;
			}

			.info-text {
				font-size: 24rpx;
				color: $gray-700;
				line-height: 1.5;
			}
		}
	}

	// Settings Menu
	.settings-menu {
		background: $white;
		border-bottom: 1rpx solid $gray-200;
		animation: fadeInDown $transition-normal ease-out;

		&__content {
			padding: 32rpx;
		}

		&__item {
			margin-bottom: 32rpx;

			&:last-child {
				margin-bottom: 0;
			}
		}

		&__button {
			width: 100%;
			height: 80rpx;
			background: linear-gradient(135deg, $primary-color 0%, $primary-dark 100%);
			color: $white;
			border: none;
			border-radius: $radius-lg;
			font-weight: 500;
			font-size: 26rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			gap: 12rpx;
			box-shadow: $shadow-2;
			transition: all $transition-fast;

			&:hover {
				transform: translateY(-2rpx);
				box-shadow: $shadow-3;
			}

			&:active {
				transform: translateY(0);
			}
		}
	}

	// Toggle Wrapper
	.toggle-wrapper {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 24rpx;
		background: var(--border-color);
		border-radius: $radius-lg;
		border: 1rpx solid $gray-200;
		transition: all $transition-fast;

		&:hover {
			background: $gray-100;
			border-color: $gray-300;
		}
	}

	.toggle-content {
		display: flex;
		align-items: center;
		gap: 20rpx;
	}

	.toggle-icon {
		width: 24rpx;
		text-align: center;
	}

	.toggle-text {
		font-size: 26rpx;
		color: $gray-800;
		font-weight: 500;
		margin-left: 16rpx;
		color: var(--text-color-primary);
	}

	// Content
	.content {
		padding: 32rpx;
		padding-bottom: 32rpx;
	}

	// Site List
	.site-list {
		display: flex;
		flex-direction: column;
		gap: 24rpx;
	}

	// Site Card
	.site-card {
		background: $white;
		border-radius: $radius-xl;
		box-shadow: $shadow-1;
		border: 1rpx solid $gray-200;
		overflow: hidden;
		transition: all $transition-fast;
		animation: fadeInUp 0.6s ease-out;

		&:nth-child(2) {
			animation-delay: 0.1s;
		}

		&:nth-child(3) {
			animation-delay: 0.2s;
		}

		&:nth-child(4) {
			animation-delay: 0.3s;
		}

		&:nth-child(5) {
			animation-delay: 0.4s;
		}

		&:hover {
			box-shadow: $shadow-2;
			transform: translateY(-2rpx);
		}

		&__header {
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding: 32rpx;
			border-bottom: 1rpx solid $gray-100;
			cursor: pointer;
			transition: background $transition-fast;
		}

		&__actions {
			display: flex;
			align-items: center;
			gap: 20rpx;
		}
	}

	// Site Info
	.site-info {
		display: flex;
		align-items: center;
		gap: 16rpx;
		flex: 1;
	}

	.site-icon {
		width: 24rpx;
		text-align: center;
	}

	.site-title {
		font-size: 28rpx;
		font-weight: 600;
		color: var(--text-color-primary);
		margin-right: 16rpx;
	}

	// Expand Button
	.expand-btn {
		width: 48rpx;
		height: 48rpx;
		border-radius: $radius-full;
		background: $gray-100;
		border: none;
		display: flex;
		align-items: center;
		justify-content: center;
		transition: all $transition-fast;

		&:hover {
			background: $gray-200;
			transform: scale(1.1);
		}
	}

	// Site Card Overview
	.site-card__overview {
		padding: 32rpx;
		background: $white;
	}

	// Feature Grid
	.feature-grid {
		display: grid;
		grid-template-columns: repeat(4, 1fr);
		gap: 16rpx;
	}

	// Feature Button
	.feature-button {
		position: relative;
		display: flex;
		flex-direction: column;
		align-items: center;
		padding: 24rpx 16rpx;
		border-radius: $radius-lg;
		background: $white;
		border: none;
		transition: all $transition-normal;
		animation: fadeInUp 0.8s ease-out;
		border: 1rpx solid $gray-400;

		&:nth-child(2) {
			animation-delay: 0.1s;
		}

		&:nth-child(3) {
			animation-delay: 0.2s;
		}

		&:nth-child(4) {
			animation-delay: 0.3s;
		}

		&:hover {
			transform: translateY(-2rpx);
			box-shadow: $shadow-1;
		}

		&:active {
			transform: translateY(0) scale(0.98);
		}

		&--active {
			background: linear-gradient(135deg, $primary-light 0%, $primary-ultra-light 100%);
			color: $primary-dark;
			transform: scale(1.02);

			.feature-button__icon-wrapper {
				background: $primary-color;
				box-shadow: 0 0 0 2rpx rgba(32, 165, 10, 0.3);
			}
		}

		&--loading {
			opacity: 0.6;
			pointer-events: none;

			.feature-button__icon-wrapper {
				animation: pulse 1.5s infinite;
			}
		}

		&__icon-wrapper {
			width: 56rpx;
			height: 56rpx;
			border-radius: $radius-full;
			background: $gray-200;
			display: flex;
			align-items: center;
			justify-content: center;
			margin-bottom: 12rpx;
			transition: all $transition-normal;
		}

		&__label {
			font-size: 20rpx;
			font-weight: 500;
			text-align: center;
			line-height: 1.3;
			transition: color $transition-normal;
		}

		&__badge {
			position: absolute;
			top: 8rpx;
			right: 13rpx;
			min-width: 36rpx;
			height: 36rpx;
			border-radius: $radius-full;
			background: $red-500;
			color: $white;
			font-size: 18rpx;
			font-weight: 600;
			display: flex;
			align-items: center;
			justify-content: center;
			box-shadow: $shadow-2;
			transition: all $transition-normal;
			animation: pulse 2s infinite;
		}
	}

	// Site Card Details
	.site-card__details {
		padding: 32rpx;
		background: $white;
		border-top: 1rpx solid $gray-100;
		animation: fadeInDown $transition-normal ease-out;
	}

	.details-header {
		display: flex;
		align-items: center;
		gap: 12rpx;
		margin-bottom: 32rpx;
	}

	.details-title {
		font-size: 24rpx;
		font-weight: 600;
		color: $gray-700;
	}

	// Feature List
	.feature-list {
		display: flex;
		flex-direction: column;
		gap: 16rpx;
	}

	// Feature Item
	.feature-item {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 32rpx;
		border-radius: $radius-lg;
		background: $white;
		border: none;
		transition: all $transition-fast;

		&:hover {
			background: var(--border-color);
		}

		&--active,
		&--enabled {
			background: linear-gradient(135deg, $primary-light 0%, $primary-ultra-light 100%) !important;

			&:hover {
				background: linear-gradient(135deg, $primary-light 0%, $primary-ultra-light 100%) !important;
			}
		}

		&__content {
			display: flex;
			align-items: center;
			gap: 24rpx;
			flex: 1;
		}

		&__icon-wrapper {
			width: 56rpx;
			height: 56rpx;
			border-radius: $radius-full;
			background: $gray-200;
			display: flex;
			align-items: center;
			justify-content: center;
			transition: all $transition-fast;

			&--active {
				background: $primary-color;
			}
		}

		&__info {
			flex: 1;
			display: flex;
			flex-direction: column;
			gap: 8rpx;
		}

		&__label {
			font-size: 26rpx;
			font-weight: 600;
			color: $gray-800;
		}

		&__desc {
			font-size: 22rpx;
			color: $gray-600;
			line-height: 1.4;
		}

		&__count {
			min-width: 36rpx;
			height: 36rpx;
			background: $red-100;
			color: $red-500;
			border-radius: $radius-full;
			font-size: 20rpx;
			font-weight: 600;
			display: flex;
			align-items: center;
			justify-content: center;
			margin-right: 8rpx;
		}
	}

	// Keyframe Animations
	@keyframes fadeInDown {
		0% {
			opacity: 0;
			transform: translateY(-20rpx);
		}
		100% {
			opacity: 1;
			transform: translateY(0);
		}
	}

	@keyframes fadeInUp {
		0% {
			opacity: 0;
			transform: translateY(20rpx);
		}
		100% {
			opacity: 1;
			transform: translateY(0);
		}
	}

	@keyframes bounce {
		0%,
		20%,
		53%,
		80%,
		100% {
			transform: translateY(0);
		}
		40%,
		43% {
			transform: translateY(-8rpx);
		}
		70% {
			transform: translateY(-4rpx);
		}
		90% {
			transform: translateY(-2rpx);
		}
	}

	@keyframes pulse {
		0% {
			box-shadow: 0 0 0 0 rgba(32, 165, 10, 0.4);
		}
		70% {
			box-shadow: 0 0 0 20rpx rgba(32, 165, 10, 0);
		}
		100% {
			box-shadow: 0 0 0 0 rgba(32, 165, 10, 0);
		}
	}

	@keyframes shake {
		0%,
		100% {
			transform: translateX(0);
		}
		10%,
		30%,
		50%,
		70%,
		90% {
			transform: translateX(-2rpx);
		}
		20%,
		40%,
		60%,
		80% {
			transform: translateX(2rpx);
		}
	}

	@keyframes slideInDown {
		0% {
			opacity: 0;
			transform: translateY(-100%);
		}
		100% {
			opacity: 1;
			transform: translateY(0);
		}
	}

	@keyframes fadeInLeft {
		0% {
			opacity: 0;
			transform: translateX(-30rpx);
		}
		100% {
			opacity: 1;
			transform: translateX(0);
		}
	}

	@keyframes fadeInRight {
		0% {
			opacity: 0;
			transform: translateX(30rpx);
		}
		100% {
			opacity: 1;
			transform: translateX(0);
		}
	}

	@keyframes glow {
		0%,
		100% {
			box-shadow: 0 0 5rpx rgba(32, 165, 10, 0.5);
		}
		50% {
			box-shadow:
				0 0 20rpx rgba(32, 165, 10, 0.8),
				0 0 30rpx rgba(32, 165, 10, 0.6);
		}
	}

	// Responsive Design
	@media (max-width: 750rpx) {
		.feature-grid {
			grid-template-columns: repeat(2, 1fr);
			gap: 12rpx;
		}

		.feature-button {
			padding: 20rpx 12rpx;

			&__icon-wrapper {
				width: 48rpx;
				height: 48rpx;
			}

			&__label {
				font-size: 18rpx;
			}
		}

		.header__icon-button {
			width: 56rpx;
			height: 56rpx;
		}
	}

	// 添加图标过渡效果
	.feature-icon {
		transition: color $transition-normal !important;
	}

	// 添加空状态样式
	.empty-state {
		display: flex;
		justify-content: center;
		align-items: center;
		min-height: 400rpx;
		padding: 80rpx 32rpx;
		animation: fadeInUp 0.8s ease-out;

		&__content {
			text-align: center;
			max-width: 480rpx;
		}

		&__icon {
			margin-bottom: 32rpx;
			animation: bounce 2s infinite;
		}

		&__title {
			display: block;
			font-size: 32rpx;
			font-weight: 600;
			color: $gray-700;
			margin-bottom: 16rpx;
			line-height: 1.4;
		}

		&__desc {
			display: block;
			font-size: 26rpx;
			color: $gray-500;
			margin-bottom: 48rpx;
			line-height: 1.5;
		}

		&__tips {
			display: flex;
			flex-direction: column;
			gap: 16rpx;
		}
	}

	.empty-tip {
		display: flex;
		align-items: center;
		justify-content: center;
		gap: 12rpx;
		padding: 16rpx 24rpx;
		background: $gray-50;
		border-radius: $radius-lg;
		border: 1rpx solid $gray-200;

		&__text {
			font-size: 22rpx;
			color: $gray-600;
			line-height: 1.4;
		}
	}

	// Loading State
	.loading-state {
		display: flex;
		justify-content: center;
		align-items: center;
		min-height: 400rpx;
		padding: 80rpx 32rpx;
		animation: fadeInUp 0.8s ease-out;

		&__content {
			text-align: center;
			max-width: 480rpx;
		}

		&__text {
			display: block;
			font-size: 26rpx;
			color: $gray-700;
			margin-top: 24rpx;
			line-height: 1.4;
		}
	}
</style>
