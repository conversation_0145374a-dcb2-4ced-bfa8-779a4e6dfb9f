//
//  MPScanCodeAdapterInterface.h
//  MPScanCodeAdapter
//
//  Created by ya<PERSON><PERSON> on 2020/7/10.
//  Copyright © 2020 Alipay. All rights reserved.
//

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN
typedef void (^MPModuleKeepAliveCallback)(id result, BOOL keepAlive);

@protocol MPScanViewControllerDelegate <NSObject>

/**
 识别本地相册图片失败时会调用该方法
 与scanPhotoFailed不同的是，此方法在SDK默认弹框之前调用，若实现此方法，错误交由delegate处理，SDK内部不再弹框
 */
- (void)scanLocalPhotoFailedBeforeDefaultAlert;

/**
 自定义关闭当前扫码页面的方式。默认为pop
 */
- (void)customCloseCurrentScanViewController:(UIViewController *)currentScanViewController;
@end

@class TBScanViewController;
@interface MPScanCodeAdapterInterface : NSObject

+ (instancetype)sharedInstance;

/*
  打开 mPaaS 默认扫码界面
* @param callback 扫码结果回调方法，主线程回调，result结果字段包括一下内容，keepAlive可忽略
 @{
 @"resp_code":@"1000",                           // 扫码结果码，1000标识s成功
 @"resp_message":@"success",                     // 扫码状态
 @"resp_result":@"http://alipay.techfin.com"     // 扫码内容
 }
 
 */
- (TBScanViewController *)createDefaultScanPageWithallback:(MPModuleKeepAliveCallback)callback;

@property (nonatomic,weak) id<MPScanViewControllerDelegate> delegate;

///<扫码光线正常时的提示文案，默认为“扫二维码/条码”
@property (nonatomic,strong) NSString *tipLabelTitle;

///<是否显示相册入口
@property (nonatomic,assign) BOOL shoulShowAlbum;

///< 闪光灯按钮正常展现时的 title，默认为“轻触照亮”
@property (nonatomic,strong) NSString *torchStateNormalTitle;

///< 闪光灯按钮被选中后状态时的 title，默认为“轻触关闭”
@property (nonatomic,strong) NSString *torchStateSelectedTitle;

// 自定义多码中间页的tips提示文案，默认为“点击蓝点，选择要扫的码”
@property(nonatomic, strong) NSString *multiCodeTipsLabelTitle;

// 自定义多码中间页的icon，默认为蓝底白色箭头图,大小请务必与 TBScanSDK.bundle/multiple_mark_blue.png 图片保持一致
@property(nonatomic, strong) NSString *multiCodeMarkerImagePath;

@end

NS_ASSUME_NONNULL_END
