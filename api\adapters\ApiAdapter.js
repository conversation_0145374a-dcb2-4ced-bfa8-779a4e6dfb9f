/**
 * API适配器基类
 * 所有具体适配器实现都应该继承此类
 */
export default class ApiAdapter {
  /**
   * 转换响应数据
   * @param {Object} response 原始响应数据
   * @returns {Object} 转换后的响应数据
   */
  transformResponse(response) {
    throw new Error('transformResponse方法必须被子类实现');
  }

  /**
   * 处理错误
   * @param {Object} error 原始错误对象
   * @returns {Object} 处理后的错误对象
   */
  handleError(error) {
    throw new Error('handleError方法必须被子类实现');
  }
}
