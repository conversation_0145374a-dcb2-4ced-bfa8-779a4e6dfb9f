<!-- AceEditor组件 -->
<template>
  <view class="ace-editor-container">
    <view
      :prop-id="editorId"
      :change:prop-id="aceRender.handlePropIdChange"
      :prop-value="modelValue"
      :change:prop-value="aceRender.handleValueChange"
      :prop-theme="theme"
      :change:prop-theme="aceRender.handleThemeChange"
      :prop-lang="lang"
      :change:prop-lang="aceRender.handleLangChange"
      :prop-font-size="fontSize"
      :change:prop-font-size="aceRender.handleFontSizeChange"
      :prop-readonly="readonly"
      :change:prop-readonly="aceRender.handleReadOnlyChange"
      :id="editorId"
      class="ace-editor"
      :style="{ height: height + 'px' }"
    >
      <text v-if="!isEditorReady" style="color: #fff; padding: 10px">加载中...</text>
    </view>
  </view>
</template>

<script>
  import { defineComponent } from 'vue';

  const component = defineComponent({
    name: 'AceEditor',
    props: {
      modelValue: {
        type: String,
        default: '',
      },
      height: {
        type: Number,
        default: 300,
      },
      lang: {
        type: String,
        default: 'javascript',
      },
      theme: {
        type: String,
        default: 'monokai',
      },
      fontSize: {
        type: Number,
        default: 14,
      },
      readonly: {
        type: Boolean,
        default: false,
      },
      options: {
        type: Object,
        default: () => ({}),
      },
    },
    data() {
      return {
        editorId: 'ace-editor-' + Date.now(),
        isEditorReady: false,
      };
    },
    emits: ['update:modelValue', 'init', 'change'],
    methods: {
      updateValue(value) {
        // this.$emit('update:modelValue', value);
        this.$emit('change', value);
      },
      handleInit(editor) {
        this.isEditorReady = true;
        this.$emit('init', editor);
      },
    },
  });

  export default component;
</script>

<script module="aceRender" lang="renderjs">
  const renderJs = {
    props: {
      propId: String,
      propTheme: String,
      propLang: String,
      propFontSize: Number,
      propReadonly: Boolean,
      propValue: String
    },
    data() {
      return {
        editor: null,
        aceInstance: null,
        currentId: null,
        currentTheme: null,
        currentLang: null,
        currentValue: null
      }
    },
    beforeDestroy() {
      if (this.editor) {
        this.editor.destroy()
        this.editor = null
      }
    },
    methods: {
      handleValueChange(newVal) {
        this.currentValue = newVal
        if (this.editor) {
          this.updateContent(newVal)
        }
      },
      handlePropIdChange(newId) {
        this.currentId = newId
        this.initAceEditor()
      },

      handleThemeChange(newTheme) {
        this.currentTheme = newTheme
        if (this.editor) {
          this.loadTheme(newTheme).then(() => {
            this.editor.setTheme('ace/theme/' + newTheme)
          })
        }
      },

      handleLangChange(newLang) {
        this.currentLang = newLang
        this.loadWorker(newLang)
        this.loadMode(newLang).then(() => {
          this.editor.session.setMode('ace/mode/' + newLang)
        })
      },

      handleFontSizeChange(newSize) {
        if (this.editor) {
          this.editor.setFontSize(newSize)
        }
      },

      handleReadOnlyChange(newValue) {
        if (this.editor) {
          this.editor.setReadOnly(newValue)
        }
      },

      async initAceEditor() {
        if (!this.currentId) {
          return
        }

        try {
          // 动态加载ace编辑器
          const ace = await this.loadAce()
          if (!ace) return

          this.aceInstance = ace

          // 等待DOM准备好
          await this.waitForElement(this.currentId)

          // 初始化编辑器
          const editor = ace.edit(this.currentId)

          // 配置编辑器
          const theme = this.propTheme || 'monokai'
          const lang = this.currentLang || 'javascript'

          this.currentTheme = theme

          await this.loadTheme(theme)
          // await this.loadMode(lang)
          // // 加载对应的 worker
          // await this.loadWorker(lang)

          editor.setTheme('ace/theme/' + theme)
          editor.session.setMode('ace/mode/' + lang)
          editor.setFontSize(this.propFontSize)
          editor.setReadOnly(this.propReadonly)
          editor.setValue(this.currentValue || '', -1)

          // 移动端优化配置
          editor.setOptions({
            ...this.options
          })

          // 设置移动端友好的滚动和边距
          editor.renderer.setScrollMargin(10, 10, 10, 10)
          editor.setOption('maxLines', 'auto')

          // 监听变化事件
          editor.on('change', () => {
            const content = editor.getValue()
            this.$ownerInstance.callMethod('updateValue', content)
          })

          this.editor = editor

          // 强制重新渲染编辑器
          setTimeout(() => {
            editor.resize(true)
            this.$ownerInstance.callMethod('handleInit', {
              ready: true,
              id: this.currentId
            })
          }, 100)
        } catch (error) {
          console.error('Ace editor initialization failed:', error)
        }
      },

      waitForElement(id) {
        return new Promise((resolve) => {
          const element = document.getElementById(id)
          if (element) {
            resolve(element)
            return
          }

          const observer = new MutationObserver((mutations, obs) => {
            const element = document.getElementById(id)
            if (element) {
              obs.disconnect()
              resolve(element)
            }
          })

          observer.observe(document.body, {
            childList: true,
            subtree: true
          })
        })
      },

      async loadAce() {
        if (window.ace) {
          return window.ace
        }

        try {
          const paths = [
            './static/ace/ace.js',
            '/static/ace/ace.js',
            '../../../static/ace/ace.js'
          ]

          let lastScript = null
          for (const path of paths) {
            try {
              const aceScript = document.createElement('script')
              aceScript.src = path
              lastScript = aceScript

              const result = await new Promise((resolve) => {
                aceScript.onload = () => {
                  resolve(true)
                }
                aceScript.onerror = () => {
                  resolve(false)
                }
                document.head.appendChild(aceScript)
              })

              if (result && window.ace) {
                return window.ace
              }

              // 如果加载失败，移除script标签
              try {
                if (aceScript.parentNode) {
                  aceScript.parentNode.removeChild(aceScript)
                }
              } catch (e) {
                console.warn('移除script标签失败:', e)
              }
            } catch (error) {
              console.error('尝试路径失败:', path, error)
            }
          }
          return null
        } catch (error) {
          console.error('加载ace.js时发生错误:', error)
          return null
        }
      },

      async loadTheme(theme) {
        if (!this.aceInstance) return

        try {
          const paths = [
            `./static/ace/theme-${theme}.js`,
            `/static/ace/theme-${theme}.js`,
            `../../../static/ace/theme-${theme}.js`
          ]

          let lastScript = null
          for (const path of paths) {
            try {
              const themeScript = document.createElement('script')
              themeScript.src = path
              lastScript = themeScript

              const result = await new Promise((resolve) => {
                themeScript.onload = () => {
                  resolve(true)
                }
                themeScript.onerror = () => {
                  resolve(false)
                }
                document.head.appendChild(themeScript)
              })

              if (result) {
                return
              }

              // 如果加载失败，移除script标签
              try {
                if (themeScript.parentNode) {
                  themeScript.parentNode.removeChild(themeScript)
                }
              } catch (e) {
                console.warn('移除主题script标签失败:', e)
              }
            } catch (error) {
              console.error('尝试主题路径失败:', path, error)
            }
          }

          console.error('所有主题路径都失败了')
        } catch (error) {
          console.error(`加载主题失败: ${theme}`, error)
        }
      },

      async loadMode(mode) {
        if (!this.aceInstance) return

        try {
          const paths = [
            `./static/ace/mode-${this.currentLang}.js`,
            `/static/ace/mode-${this.currentLang}.js`,
            `../../../static/ace/mode-${this.currentLang}.js`
          ]

          let lastScript = null
          for (const path of paths) {
            try {
              const modeScript = document.createElement('script')
              modeScript.src = path
              lastScript = modeScript

              const result = await new Promise((resolve) => {
                modeScript.onload = () => {
                  resolve(true)
                }
                modeScript.onerror = () => {
                  resolve(false)
                }
                document.head.appendChild(modeScript)
              })

              if (result) {
                return
              }

              // 如果加载失败，移除script标签
              try {
                if (modeScript.parentNode) {
                  modeScript.parentNode.removeChild(modeScript)
                }
              } catch (e) {
                console.warn('移除语言模式script标签失败:', e)
              }
            } catch (error) {
              console.error('尝试语言模式路径失败:', path, error)
            }
          }

          console.error('所有语言模式路径都失败了')
        } catch (error) {
          console.error(`加载语言模式失败: ${mode}`, error)
        }
      },

      async loadWorker(mode) {
        if (!this.aceInstance) return
        try {
          const paths = [
            `./static/ace/worker-${this.currentLang}.js`,
            `/static/ace/worker-${this.currentLang}.js`,
            `../../../static/ace/worker-${this.currentLang}.js`
          ]

          for (const path of paths) {
            try {
              const workerScript = document.createElement('script')
              workerScript.src = path
              
              const result = await new Promise((resolve) => {
                workerScript.onload = () => resolve(true)
                workerScript.onerror = () => resolve(false)
                document.head.appendChild(workerScript)
              })

              if (result) return

              if (workerScript.parentNode) {
                workerScript.parentNode.removeChild(workerScript)
              }
            } catch (error) {
              console.error('尝试加载worker失败:', path, error)
            }
          }
        } catch (error) {
          console.error(`加载worker失败: ${mode}`, error)
        }
      },

      updateContent(value) {
        if (this.editor) {
          const currentValue = this.editor.getValue()
          if (currentValue !== value) {
            this.editor.setValue(value, -1)
          }
        }
      },
    },
    watch: {
      async theme(newVal) {
        if (this.editor) {
          await this.loadTheme(newVal)
          this.editor.setTheme('ace/theme/' + newVal)
        }
      },
      async lang(newVal) {
        if (this.editor) {
          await this.loadMode(newVal)
          this.editor.session.setMode('ace/mode/' + newVal)
          await this.loadWorker(newVal)
        }
      },
      fontSize(newVal) {
        if (this.editor) {
          this.editor.setFontSize(newVal)
        }
      },
      readonly(newVal) {
        if (this.editor) {
          this.editor.setReadOnly(newVal)
        }
      },
      options: {
        handler(newVal) {
          if (this.editor) {
            this.editor.setOptions(newVal)
          }
        },
        deep: true
      }
    }
  }

  export default renderJs
</script>

<style>
  .ace-editor-container {
    width: 100%;
    position: relative;
    background: #2d2d2d;
  }
  .ace-editor {
    width: 100%;
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 1;
  }
</style>
