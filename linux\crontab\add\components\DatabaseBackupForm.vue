<template>
	<view>
		<!-- 执行周期 -->
		<CycleForm :formData="formData" @update:formData="updateFormData" :showProcessLock="true" :isEditMode="isEditMode" class="mb-32" />

		<!-- 数据库类型选择 -->
		<view class="form-group">
			<view class="form-label-row">
				<text>数据库类型</text>
			</view>
			<button class="region-select-button" @click="showDbTypePicker" :disabled="isEditMode" :style="isEditMode ? 'background: var(--bg-color-disabled)' : ''">
				<text>{{ getDbTypeLabel() || '请选择数据库类型' }}</text>
				<uv-icon name="arrow-down" size="14" :color="iconColor"></uv-icon>
			</button>
		</view>

		<!-- 数据库选择 -->
		<view class="form-group">
			<view class="form-label-row">
				<text>数据库</text>
			</view>
			<button class="region-select-button" @click="showDatabasePicker" :disabled="isEditMode" :style="isEditMode ? 'background: var(--bg-color-disabled)' : ''">
				<text>{{ getDatabaseLabel() || '请选择数据库' }}</text>
				<uv-icon name="arrow-down" size="14" :color="iconColor"></uv-icon>
			</button>
		</view>

		<!-- 表选择 (仅MySQL显示) -->
		<view
			class="form-group"
			v-if="formData.db_type === 'mysql' && formData.sName !== 'ALL' && tableOptions.length > 0"
		>
			<view class="form-label-row">
				<text>表</text>
			</view>
			<button class="region-select-button" @click="showTablePicker" :disabled="isEditMode" :style="isEditMode ? 'background: var(--bg-color-disabled)' : ''">
				<text>{{ getTableLabel() || '请选择表' }}</text>
				<uv-icon name="arrow-down" size="14" :color="iconColor"></uv-icon>
			</button>
		</view>

		<!-- 备份到选择 -->
		<view class="form-group">
			<view class="form-label-row">
				<text>备份到</text>
			</view>
			<button class="region-select-button" @click="showBackupToPicker">
				<text>{{ getBackupToLabel() || '请选择备份位置' }}</text>
				<uv-icon name="arrow-down" size="14" :color="iconColor"></uv-icon>
			</button>
		</view>

		<!-- 文件拆分和备份设置 - 仅在非本地备份时显示 -->
		<BackupSettingsForm class="mb-32" :formData="formData" @update:formData="updateFormData" :isEnterprise="false" />

		<!-- 保留最新份数 -->
		<view class="form-group">
			<view class="form-label-row">
				<text>保留最新</text>
			</view>
			<uv-input
				v-model="saveValue"
				@blur="validateSaveField"
				placeholder="请输入保存备份的数量(最大999)"
				type="number"
				border="surround"
				:customStyle="{ backgroundColor: 'var(--dialog-bg-color)', fontSize: '28rpx' }"
			>
				<template v-slot:suffix>
					<text class="save-unit">份</text>
				</template>
			</uv-input>
		</view>

		<!-- 备份路径 - 在选择服务器磁盘或开启保留本地备份时显示 -->
		<view class="form-group" v-if="formData.backupTo === 'localhost' || formData.save_local">
			<view class="form-label-row">
				<text>备份路径</text>
			</view>
			<uv-input
				v-model="backupPathValue"
				placeholder="/www/backup"
				border="surround"
				:customStyle="{ backgroundColor: 'var(--dialog-bg-color)', fontSize: '28rpx' }"
			>
				<template v-slot:suffix>
					<view class="path-select-button" @click="selectPath">
						<uv-icon name="folder" size="16" color="#ffffff"></uv-icon>
					</view>
				</template>
			</uv-input>
		</view>

		<!-- 使用数据库对应账号备份 -->
		<view class="form-group">
			<view class="form-row">
				<text>使用数据库对应账号备份</text>
				<uv-switch
					:model-value="formData.backup_mode === 1"
					@change="updateBackupMode"
					activeColor="#20a50a"
					size="22"
				></uv-switch>
			</view>
			<view class="form-help">
				<text>*注意：目前只支持mysql数据库的本地数据库备份</text>
			</view>
		</view>

		<!-- 温馨提示 -->
		<view class="form-group">
			<view class="form-label-row">
				<text>温馨提示</text>
			</view>
			<view class="help-text">
				<text>远程数据库不支持备份表</text>
			</view>
		</view>

		<!-- Picker组件 -->
		<uv-picker ref="dbTypePicker" :columns="[dbTypeOptions]" keyName="label" @confirm="onDbTypeConfirm"></uv-picker>

		<uv-picker
			ref="databasePicker"
			:columns="[databaseOptions]"
			keyName="label"
			@confirm="onDatabaseConfirm"
		></uv-picker>

		<uv-picker ref="tablePicker" :columns="[tableOptions]" keyName="label" @confirm="onTableConfirm"></uv-picker>

		<uv-picker
			ref="backupToPicker"
			:columns="[backupToOptions]"
			keyName="label"
			@confirm="onBackupToConfirm"
		></uv-picker>
	</view>
</template>

<script setup>
	import { ref, reactive, onMounted, onBeforeUnmount, nextTick, getCurrentInstance, computed, watch } from 'vue';
	import { getCrontabDataList, getDatabases } from '@/api/crontab';
	import { openFileSelector } from '@/stores/fileSelector.js';
	import { getTheme, THEME_TYPE } from '@/hooks/useTheme.js';
	import { truncateText } from '../useController';
	import CycleForm from './CycleForm.vue';
	import BackupSettingsForm from './BackupSettingsForm.vue';

	const { proxy } = getCurrentInstance();

	const props = defineProps({
		formData: {
			type: Object,
			required: true,
		},
		isEditMode: {
			type: Boolean,
			default: false,
		},
	});

	const emit = defineEmits(['update:formData']);

	// 主题相关
	const currentTheme = ref(getTheme());
	const iconColor = computed(() => {
		return currentTheme.value === THEME_TYPE.DARK ? '#cccccc' : '#666666';
	});

	// 保存数量的双向绑定计算属性
	const saveValue = computed({
		get() {
			return props.formData.save;
		},
		set(value) {
			emit('update:formData', { ...props.formData, save: value });
		}
	});

	// 备份路径的双向绑定计算属性
	const backupPathValue = computed({
		get() {
			return props.formData.db_backup_path;
		},
		set(value) {
			emit('update:formData', { ...props.formData, db_backup_path: value });
		}
	});

	// 数据选项
	const dbTypeOptions = ref([
		{ value: 'mysql', label: 'MySQL' },
		{ value: 'mongodb', label: 'MongoDB' },
		{ value: 'redis', label: 'Redis' },
		{ value: 'pgsql', label: 'PgSQL' },
	]);
	const databaseOptions = ref([{ label: '所有[所有数据]', value: 'ALL' }]);
	const tableOptions = ref([]);
	const backupToOptions = ref([{ label: '服务器磁盘', value: 'localhost' }]);

	// 更新字段
	const updateField = (field, event) => {
		const value = event.detail?.value || event.target?.value || event;
		emit('update:formData', { ...props.formData, [field]: value });
	};

	// 验证保留份数字段
	const validateSaveField = () => {
		let saveValue = props.formData.save;

		// 如果为空或undefined，设置为默认值
		if (!saveValue || saveValue === '') {
			emit('update:formData', { ...props.formData, save: '3' });
			return;
		}

		// 转换为数字并验证
		const floatValue = parseFloat(saveValue);

		// 检查是否为有效数字
		if (isNaN(floatValue) || floatValue <= 0) {
			emit('update:formData', { ...props.formData, save: '3' });
			return;
		}

		// 四舍五入到整数
		let roundedValue = Math.round(floatValue);

		// 检查最大值限制
		if (roundedValue > 999) {
			roundedValue = 999;
		}

		// 确保最小值为1
		if (roundedValue < 1) {
			roundedValue = 1;
		}

		// 更新为四舍五入后的整数值
		emit('update:formData', { ...props.formData, save: roundedValue.toString() });
	};

	// 更新表单数据
	const updateFormData = (newData) => {
		emit('update:formData', { ...props.formData, ...newData });
	};

	// 更新备份模式
	const updateBackupMode = (value) => {
		emit('update:formData', { ...props.formData, backup_mode: value ? 1 : 0 });
	};

	// 选择路径
	const selectPath = () => {
		openFileSelector('folder', false, (selectedPaths) => {
			if (selectedPaths && selectedPaths.length > 0) {
				emit('update:formData', { ...props.formData, db_backup_path: selectedPaths[0] });
			}
		});
	};

	// Picker refs
	const dbTypePicker = ref(null);
	const databasePicker = ref(null);
	const tablePicker = ref(null);
	const backupToPicker = ref(null);

	// 显示选择器
	const showDbTypePicker = () => dbTypePicker.value?.open();
	const showDatabasePicker = () => databasePicker.value?.open();
	const showTablePicker = () => tablePicker.value?.open();
	const showBackupToPicker = () => backupToPicker.value?.open();

	// 获取显示标签
	const getDbTypeLabel = () => {
		const option = dbTypeOptions.value.find((item) => item.value === props.formData.db_type);
		return option ? option.label : '';
	};

	const getDatabaseLabel = () => {
		const option = databaseOptions.value.find((item) => item.value === props.formData.sName);
		return option ? option.label : '';
	};

	const getTableLabel = () => {
		const option = tableOptions.value.find((item) => item.value === props.formData.table_list);
		return option ? truncateText(option.label) : '';
	};

	const getBackupToLabel = () => {
		const option = backupToOptions.value.find((item) => item.value === props.formData.backupTo);
		return option ? truncateText(option.label) : '';
	};

	// 确认选择
	const onDbTypeConfirm = async (e) => {
		const selectedValue = e.value[0].value;
		emit('update:formData', {
			db_type: selectedValue,
			sName: 'ALL',
			table_list: 'ALL',
			name: `备份${selectedValue}数据库[ 所有 ]`,
		});
		await loadDatabases(selectedValue);
	};

	const onDatabaseConfirm = (e) => {
		const selectedValue = e.value[0].value;

		if (selectedValue === 'ALL') {
			emit('update:formData', {
				sName: selectedValue,
				table_list: 'ALL',
				name: `备份${props.formData.db_type}数据库[ 所有 ]`,
			});
		} else {
			// 加载表列表
			const database = databaseOptions.value.find((item) => item.value === selectedValue);
			if (database && database.table_list && database.table_list.length > 0) {
				tableOptions.value = database.table_list.map((item) => ({
					label: item.tb_name,
					value: item.value === '' ? 'ALL' : item.value,
				}));
			} else {
				// 如果没有table_list或为空，清空表选项
				tableOptions.value = [];
			}

			emit('update:formData', {
				sName: selectedValue,
				table_list: 'ALL',
				name: `备份${props.formData.db_type}数据库[ ${selectedValue} ]`,
			});
		}
	};

	const onTableConfirm = (e) => {
		const selectedValue = e.value[0].value;
		const selectedLabel = e.value[0].label;
		emit('update:formData', {
			table_list: selectedValue,
			name: `备份${props.formData.db_type}数据库[ ${props.formData.sName} - ${selectedLabel} ]`,
		});
	};

	const onBackupToConfirm = (e) => {
		const selectedValue = e.value[0].value;
		emit('update:formData', { backupTo: selectedValue });
	};

	// 加载数据库列表
	const loadDatabases = async (dbType) => {
		try {
			const res = await getDatabases({ db_type: dbType });
			databaseOptions.value = [{ label: '所有[所有数据]', value: 'ALL' }].concat(
				res.map((item) => ({
					label: item.name + (item.ps ? `[${item.ps}]` : ''),
					value: item.name,
					table_list: item.table_list,
				})),
			);

			// 如果有数据库，加载第一个数据库的表列表
			if (res.length > 0 && res[0].table_list && res[0].table_list.length > 0) {
				tableOptions.value = res[0].table_list.map((item) => ({
					label: item.tb_name,
					value: item.value === '' ? 'ALL' : item.value,
				}));
			} else {
				// 如果没有table_list或为空，清空表选项
				tableOptions.value = [];
			}
		} catch (error) {
			console.error('加载数据库列表失败:', error);
		}
	};

	// 加载备份位置
	const loadBackupTo = async () => {
		try {
			const res = await getCrontabDataList({ type: 'sites' });
			// 只显示已配置的选项，过滤掉未配置的项
			backupToOptions.value = [{ label: '服务器磁盘', value: 'localhost' }].concat(
				res.orderOpt
					.filter((item) => item.status) // 只保留已配置的项
					.map((item) => ({
						label: item.name,
						value: item.value,
					})),
			);
		} catch (error) {
			console.error('加载备份位置失败:', error);
		}
	};



	// 主题变化监听
	const handleThemeChange = (event) => {
		currentTheme.value = event.theme;
	};

	onMounted(async () => {
		await loadBackupTo();

		// 只在非编辑模式下初始化默认值
		if (!props.isEditMode && !props.formData.db_type) {
			emit('update:formData', {
				...props.formData,
				db_type: 'mysql',
				sName: 'ALL',
				table_list: 'ALL',
				backupTo: 'localhost',
				save: '3',
				backup_mode: 0,
				db_backup_path: '/www/backup',
				name: '备份mysql数据库[ 所有 ]',
				// 文件拆分和备份设置默认值
				split_type: '0',
				split_value: 5,
				save_local: 0,
				// 时间相关字段
				type: props.formData.type || 'day',
				week: props.formData.week || '1',
				hour: props.formData.hour || '1',
				minute: props.formData.minute || '30',
				second: props.formData.second || '5',
				where1: props.formData.where1 || '1',
				timeSet: props.formData.timeSet || '',
				timeType: props.formData.timeType || 'sday',
				flock: true,
			});
			await loadDatabases('mysql');
		} else {
			// 编辑模式下或已有数据时，仍需要加载数据库列表
			await loadDatabases(props.formData.db_type || 'mysql');
		}

		// 监听主题变化
		uni.$on('themeChange', handleThemeChange);
	});

	onBeforeUnmount(() => {
		// 移除主题变化监听
		uni.$off('themeChange', handleThemeChange);
	});
</script>

<style lang="scss" scoped>
	@import './form-styles.scss';

	.form-row {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 20rpx;

		text {
			flex: 1;
			font-size: 28rpx;
			color: var(--text-color-primary);
		}
	}

	.form-help {
		margin-top: 10rpx;

		text {
			font-size: 24rpx;
			color: var(--text-color-tertiary);
		}
	}

	.save-unit {
		font-size: 26rpx;
		font-weight: 600;
		color: var(--text-color-primary);
		padding: 0 8rpx;
	}

	.path-select-button {
		width: 60rpx;
		height: 60rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		background: #20a50a;
		border: none;
		border-radius: 8rpx;
		transition: all 0.3s ease;

		&:active {
			transform: scale(0.95);
		}
	}

	.help-text {
		background: var(--bg-color-secondary);
		border: 1px solid var(--border-color);
		border-radius: 16rpx;
		padding: 20rpx;

		text {
			font-size: 22rpx;
			color: var(--text-color-secondary);
			line-height: 1.6;
			font-weight: 400;
		}
	}
</style>
