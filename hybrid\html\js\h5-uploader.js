var mask = document.querySelector('.mask');
var fileDom = document.querySelector('.file');

mask.addEventListener('click', () => {
	plus.webview.currentWebview().close();
});

document.addEventListener('UniAppJSBridgeReady', () => {
	fileDom.value = '';

	fileDom.addEventListener(
		'change',
		(event) => {
			uni.postMessage({
				data: {
					loading: true,
				},
			});

			var file = fileDom.files[0];

			var reader = new FileReader();
			reader.readAsDataURL(file);

			reader.onload = function (e) {
				uni.postMessage({
					data: {
						base: e.target.result,
						name: file.name,
						size: file.size,
					},
				});

				plus.webview.currentWebview().close();
			};
		},
		false,
	);
});
