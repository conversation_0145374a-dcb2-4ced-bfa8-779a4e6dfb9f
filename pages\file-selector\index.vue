<template>
  <page-container ref="fileContainer" :title="headerTitle" :is-back="true">
    <template #nav-left>
      <view class="flex items-center text-26 flex-nowrap text-secondary min-w-100 max-w-280">
        <text class="path-text">{{ navPath }}</text>
      </view>
    </template>
    <template #nav-right>
      <view class="relative">
        <uni-icons
          class="mr-20"
          color="var(--text-color-secondary)"
          type="more-filled"
          size="24"
          @tap="toggleNavMenu"
        ></uni-icons>

        <!-- 导航栏下拉菜单蒙层 -->
        <view
          class="fixed top-0 left-0 right-0 bottom-0 z-39"
          v-if="showNavMenu"
          @tap="hideNavMenu"
          @touchmove.prevent
        ></view>

        <!-- 导航栏下拉菜单 -->
        <view class="nav-dropdown-menu" v-if="showNavMenu" @tap.stop>
          <view class="menu-item" @tap="showDiskSelectorDialog">
            <uni-icons type="settings" size="16" color="#20a50a"></uni-icons>
            <text class="menu-text">切换盘符</text>
          </view>
        </view>
      </view>
    </template>

    <view class="file-selector">
      <!-- 面包屑导航 -->
      <view class="breadcrumbs">
        <text>{{ currentPath }} ></text>
      </view>

      <!-- 文件列表 -->
      <z-paging
        ref="paging"
        class="mt-260"
        :class="{ 'with-bottom-bar': hasSelection }"
        :default-page-size="100"
        use-virtual-list
        :force-close-inner-list="true"
        :auto-hide-loading-after-first-loaded="false"
        :auto-show-system-loading="true"
        @virtualListChange="virtualListChange"
        @query="queryList"
        @refresherStatusChange="reload"
        :refresher-complete-delay="200"
      >
        <view class="px-30" :id="`zp-id-${item.zp_index}`" :key="item.zp_index" v-for="item in filesList">
          <view
            class="file-item-container"
            @tap="handleItemClick(item)"
          >
            <view
              class="py-20 flex items-center file-item"
              :class="{ 'selected': isSelected(item.path) }"
            >
              <!-- 选择状态图标 -->
              <view class="flex justify-center items-center w-80 h-80">
                <uni-icons
                  v-if="isSelected(item.path)"
                  type="checkbox-filled"
                  size="24"
                  color="#20a50a"
                ></uni-icons>
                <uni-icons
                  v-else
                  fontFamily="iconfont"
                  :class="`icon-${getFileIconType(item.ext, item.icon)}`"
                  size="24"
                  :color="getFileIconColor(item.ext, item.icon)"
                ></uni-icons>
              </view>
              
              <view class="flex-1 ml-20">
                <view class="flex items-start">
                  <view 
                    class="text-primary text-26 text-ellipsis ws-nowrap overflow-hidden max-w-400 min-w-50"
                    :class="{ 'selected-name': isSelected(item.path) }"
                  >
                    {{ item.fileName + item.isLink }}
                  </view>
                  <view class="text-tertiary text-22 ml-10" v-if="item.ps">{{ item.ps }}</view>
                </view>
                <view class="text-tertiary text-22 mt-8">{{ item.time }}</view>
              </view>
              
              <view class="text-tertiary text-22 mr-10" v-if="item.ext !== 'folder'">
                {{ getByteUnit(item.size) }}
              </view>
              
              <uni-icons
                v-show="item.ext === 'folder'"
                type="right"
                color="var(--text-color-primary)"
                size="20"
                class="mx-2"
                @tap="(e) => handleFolderEnterClick(e, item)"
              ></uni-icons>
            </view>
          </view>
          <view class="file-divider h-2"></view>
        </view>
      </z-paging>
    </view>

    <!-- 底部操作栏 -->
    <view v-if="hasSelection" class="bottom-action-bar">
      <view class="selection-info">
        <text class="selection-count">
          {{ selectionSummary }}
        </text>
      </view>
      <view class="action-buttons">
        <button class="cancel-button" @tap="clearSelection">取消</button>
        <button class="confirm-button" @tap="handleConfirmSelection">确认</button>
      </view>
    </view>

    <!-- 磁盘选择器弹窗 -->
    <CustomDialog
      contentHeight="300rpx"
      v-model="showDiskSelector"
      title="选择盘符"
      :showConfirm="false"
      cancelText="取消"
    >
      <view class="disk-selector-content">
        <view
          class="disk-item"
          v-for="disk in diskList"
          :key="disk.path"
          @tap="switchDisk(disk.path)"
          :class="{ 'active': disk.path === currentDisk }"
        >
          <view class="disk-info">
            <view class="disk-path">{{ disk.path }}</view>
            <view class="disk-details">
              <text class="disk-size">{{ disk.size[0] }}</text>
              <text class="disk-usage">已用 {{ disk.size[1] }} / 可用 {{ disk.size[2] }}</text>
            </view>
            <view class="disk-type">{{ disk.type }} - {{ disk.filesystem }}</view>
          </view>
          <view class="disk-usage-bar">
            <view class="usage-bar-bg">
              <view class="usage-bar-fill" :style="{ width: disk.size[3] }"></view>
            </view>
            <text class="usage-percent">{{ disk.size[3] }}</text>
          </view>
        </view>
      </view>
    </CustomDialog>
  </page-container>
</template>

<script setup>
  import { ref, watch, onMounted, computed, getCurrentInstance } from 'vue';
  import { onLoad, onUnload, onBackPress } from '@dcloudio/uni-app';
  import PageContainer from '@/components/PageContainer/index.vue';
  import CustomDialog from '@/components/CustomDialog/index.vue';
  import {
    getFilesList,
    currentDisk,
    paging,
    getFileIconType,
    getFileIconColor,
    currentPath,
    pathList,
    cutDirPath,
    parsePath,
    // 导航菜单相关
    showNavMenu,
    toggleNavMenu,
    hideNavMenu,
    // 切换盘符相关
    showDiskSelector,
    diskList,
    showDiskSelectorDialog,
    switchDisk,
  } from '@/linux/files/useController';
  import { getByteUnit } from '@/utils/common';
  import { useFileSelectorController } from './useController';

  // 使用文件选择器控制器
  const {
    filesList,
    selectedItems,
    selectionMode,
    multipleSelection,
    pageTitle,
    originalPath,
    originalDisk,
    isConfirming,
    virtualListChange,
    queryList,
    isSelected,
    handleItemClick,
    handleFolderEnter,
    handleFolderEnterClick,
    toggleSelection,
    clearSelection,
    handleConfirmSelection,
    initializePageParams,
    restoreOriginalState,
    fileContainer
  } = useFileSelectorController();

  // 获取当前实例，用于挂载回调方法
  const instance = getCurrentInstance();

  // 计算属性
  const headerTitle = computed(() => {
    if (hasSelection.value) {
      return `${selectedCount.value} 已选择`;
    }
    return pageTitle.value;
  });

  const navPath = computed(() => {
    const maxLength = 8;
    const path =
      pathList.value[pathList.value.length - 1].name === '根目录'
        ? '/'
        : pathList.value[pathList.value.length - 1].name;

    if (path.length > maxLength) {
      return path.substring(0, maxLength) + '...';
    }

    return path;
  });

  // 选择器相关计算属性
  const selectedCount = computed(() => selectedItems.value.size);
  const hasSelection = computed(() => selectedItems.value.size > 0);
  const selectionSummary = computed(() => {
    const count = selectedItems.value.size;
    if (count === 0) return '';
    
    const typeText = selectionMode.value === 'file' ? '文件' : 
                     selectionMode.value === 'folder' ? '文件夹' : '项目';
    
    return `${count} 个${typeText}已选择`;
  });



  const reload = (reloadType) => {
    if (reloadType === 'complete') {
      fileContainer.value.notify.success('刷新成功');
    }
  };

  // 页面加载时处理参数
  onLoad((options) => {
    initializePageParams(options);
  });

  // 监听导航菜单状态
  watch(showNavMenu, (val) => {
    if (val && paging.value) {
      paging.value.lockScroll && paging.value.lockScroll(true);
    } else if (paging.value) {
      paging.value.lockScroll && paging.value.lockScroll(false);
    }
  });

  onBackPress(() => {
    // 如果正在确认选择，允许正常返回
    if (isConfirming.value) {
      return false;
    }

    // 如果导航菜单正在显示，先关闭菜单
    if (showNavMenu.value) {
      showNavMenu.value = false;
      return true;
    }

    if (pathList.value.length === 1) {
      return false;
    }
    cutDirPath(pathList.value[pathList.value.length - 2].path);
    return true;
  });

  // 页面卸载时恢复原始状态
  onUnload(() => {
    restoreOriginalState();
  });
</script>

<style lang="scss" scoped>
  .file-selector {
    display: flex;
    flex-direction: column;
    height: 100%;
  }

  /* 当有底部操作栏时，为文件列表添加底部内边距 */
  .with-bottom-bar {
    padding-bottom: calc(140rpx + env(safe-area-inset-bottom)) !important;
  }

  .breadcrumbs {
    padding: 20rpx 30rpx;
    background-color: var(--bg-color-primary);
    border-bottom: 1rpx solid var(--text-color-light);
    font-size: 26rpx;
    color: var(--text-color-secondary);
  }

  .file-divider {
    background-color: var(--text-color-light);
    opacity: 0.8;
  }

  .file-item-container {
    position: relative;
    overflow: visible;
  }

  .file-item {
    position: relative;
    z-index: 1;
    transition: background-color 0.2s ease;

    &:active {
      background-color: rgba(var(--bg-color-secondary-rgb), 0.4);
    }

    &.selected {
      background-color: rgba(32, 165, 10, 0.1);
      border-left: 6rpx solid #20a50a;
    }
  }

  .selected-name {
    color: #20a50a;
    font-weight: 500;
  }

  .path-text {
    white-space: nowrap;
    word-break: keep-all;
  }

  /* 导航栏下拉菜单 */
  .nav-dropdown-menu {
    position: absolute;
    top: 60rpx;
    right: 0;
    z-index: 40;
    background: rgba(249, 249, 249, 0.94);
    border-radius: 14rpx;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.25);
    padding: 10rpx 0;
    min-width: 280rpx;
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    animation: fadeInDown 0.2s ease;
    transform-origin: top right;

    .menu-item {
      display: flex;
      align-items: center;
      padding: 24rpx 30rpx;

      &:active {
        background-color: rgba(0, 0, 0, 0.05);
      }
    }

    .menu-text {
      margin-left: 20rpx;
      font-size: 32rpx;
      color: #666;
      font-weight: 400;
    }
  }

  @keyframes fadeInDown {
    from {
      opacity: 0;
      transform: translateY(-10rpx);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  /* 底部操作栏 */
  .bottom-action-bar {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: var(--bg-color);
    padding: 30rpx 40rpx;
    border-top: 1rpx solid var(--text-color-light);
    box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    justify-content: space-between;
    z-index: 20;
    padding-bottom: calc(30rpx + env(safe-area-inset-bottom));
  }

  .selection-info {
    flex: 1;
  }

  .selection-count {
    font-size: 28rpx;
    color: var(--text-color-secondary);
  }

  .action-buttons {
    display: flex;
    gap: 20rpx;
  }

  .cancel-button {
    background-color: transparent;
    color: var(--text-color-secondary);
    padding: 16rpx 32rpx;
    border: 1rpx solid var(--text-color-light);
    border-radius: 8rpx;
    font-size: 28rpx;
    font-weight: 400;
  }

  .confirm-button {
    background-color: #20a50a;
    color: white;
    padding: 16rpx 32rpx;
    border: none;
    border-radius: 8rpx;
    font-size: 28rpx;
    font-weight: 500;
  }

  /* 磁盘选择器样式 */
  .disk-selector-content {
    padding: 20rpx 0;
  }

  .disk-item {
    padding: 30rpx 40rpx;
    border-bottom: 1rpx solid var(--text-color-light);
    transition: background-color 0.2s ease;

    &:last-child {
      border-bottom: none;
    }

    &:active {
      background-color: rgba(var(--bg-color-secondary-rgb), 0.4);
    }

    &.active {
      background-color: rgba(32, 165, 10, 0.1);
      border-left: 6rpx solid #20a50a;
    }
  }

  .disk-info {
    margin-bottom: 20rpx;
  }

  .disk-path {
    font-size: 32rpx;
    font-weight: 600;
    color: var(--text-color-primary);
    margin-bottom: 10rpx;
  }

  .disk-details {
    display: flex;
    align-items: center;
    margin-bottom: 8rpx;
  }

  .disk-size {
    font-size: 28rpx;
    color: var(--text-color-primary);
    margin-right: 20rpx;
  }

  .disk-usage {
    font-size: 24rpx;
    color: var(--text-color-secondary);
  }

  .disk-type {
    font-size: 24rpx;
    color: var(--text-color-tertiary);
  }

  .disk-usage-bar {
    display: flex;
    align-items: center;
  }

  .usage-bar-bg {
    flex: 1;
    height: 12rpx;
    background-color: var(--text-color-light);
    border-radius: 6rpx;
    overflow: hidden;
    margin-right: 20rpx;
  }

  .usage-bar-fill {
    height: 100%;
    background: linear-gradient(90deg, #20a50a 0%, #67C23A 50%, #E6A23C 80%, #F56C6C 100%);
    border-radius: 6rpx;
    transition: width 0.3s ease;
  }

  .usage-percent {
    font-size: 24rpx;
    color: var(--text-color-secondary);
    min-width: 80rpx;
    text-align: right;
  }
</style>
