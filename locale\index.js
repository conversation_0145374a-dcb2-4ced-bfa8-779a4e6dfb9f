import { createI18n } from 'vue-i18n'
import zhCN from './zh-CN.js'
import enUS from './en.js'

// 获取系统语言
function getDefaultLocale() {
  // 优先使用环境变量中的语言设置
  const envLanguage = import.meta.env.VITE_DEFAULT_LANGUAGE
  if (envLanguage) {
    return envLanguage === 'zh' ? 'zh-CN' : 'en-US'
  }
  
  // 如果没有环境变量，则获取设备语言
  const systemLanguage = uni.getSystemInfoSync().language
  
  // uni-app中处理系统语言
  if (systemLanguage.indexOf('zh') !== -1) {
    return 'zh-CN'
  } else {
    return 'en-US'
  }
}

const i18n = createI18n({
  // 如果本地有存储的语言，则使用本地存储的，否则使用系统语言
  locale: getDefaultLocale() || uni.getStorageSync('language'),
  messages: {
    'zh-CN': zhCN,
    'en-US': enUS
  },
  // 如果不存在某个翻译，回退到指定语言
  fallbackLocale: 'zh-CN', 
  // 是否允许使用html插值
  silentTranslationWarn: true,
  legacy: false // 使用Composition API模式
})

// 导出i18n实例和翻译函数t
export const $i18n = i18n.global
// 导出t函数，用于组件内部使用
export function $t(key, options) {
  try {
    // 1. 先尝试直接使用i18n的t函数
    const result = i18n.global.t(key, options)

    // 2. 检查是否有未替换的占位符，例如 {version} 或 {anyKey}
    if (typeof result === 'string' && result.includes('{') && options) {
      // 包含未替换的占位符，进行手动替换
      let finalText = result

      // 遍历所有参数，替换对应的占位符
      Object.keys(options).forEach(paramKey => {
        const paramValue = options[paramKey]
        const placeholder = `{${paramKey}}`

        // 全局替换所有匹配的占位符
        while (finalText.includes(placeholder)) {
          finalText = finalText.replace(placeholder, paramValue)
        }
      })

      return finalText
    }

    // 如果没有未替换的占位符，直接返回结果
    return result
  } catch (error) {
    console.error('国际化翻译错误:', error)
    // 出错时尝试直接返回key
    return key
  }
}

export default i18n