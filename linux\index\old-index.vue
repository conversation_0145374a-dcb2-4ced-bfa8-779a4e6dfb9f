<template>
  <page-container :is-back="true" :title="currentServerInfo?.name">
    <view class="server-info flex items-center justify-between px-24 py-10">
      <view class="flex items-center ip-title">
        <uni-icons fontFamily="iconfont" color="var(--text-color-secondary)" class="icon-ip" size="20"></uni-icons>
        <text class="text-24">{{ currentServerInfo?.ip }}</text>
      </view>
      <bt-button
        plain
        :customStyle="{
          backgroundColor: 'var(--bg-color-secondary)',
          borderColor: 'var(--text-color-secondary)',
          color: 'var(--text-color-secondary)',
          borderRadius: '40rpx',
          height: '50rpx',
        }"
        @click="handleSwitchPanel"
      >
        <template #default>
          <view class="flex items-center text-24">
            <uni-icons
              fontFamily="iconfont"
              style="margin-right: 10rpx"
              class="icon-toggle"
              color="var(--text-color-secondary)"
              size="16"
            ></uni-icons>
            <text>{{ $t('linux.switchPanel') }}</text>
          </view>
        </template>
      </bt-button>
    </view>
    <view class="px-24 py-20">
      <view class="flex items-center justify-between">
        <view class="flex items-center">
          <uni-icons
            fontFamily="iconfont"
            style="margin-right: 10rpx"
            :class="`icon-${systemIcon}`"
            color="var(--text-color-secondary)"
            size="20"
          ></uni-icons>
          <text class="text-24 ip-title">{{ networkInfo?.simple_system || networkInfo?.system }}</text>
        </view>
        <view class="flex items-center">
          <uv-icon name="clock" size="16" :color="'var(--text-color-secondary)'" style="margin-right: 10rpx"></uv-icon>
          <text class="text-24 ip-title">{{ $t('linux.runningTime') }} {{ networkInfo?.time }}</text>
        </view>
      </view>
      <view class="mt-20 relative">
        <view class="">
          <scroll-view scroll-x class="server-item py-30 border border-gray-100 rounded-md" :show-scrollbar="false">
            <!-- 负载状态 -->
            <view class="status-item">
              <text class="ip-title text-28">{{ $t('linux.load') }}</text>
              <view class="chart-container py-20">
                <ECharts
                  canvas-id="load-chart"
                  chart-type="gauge"
                  :chart-data="getBaseChartConfig(chartMap['load']?.val, $t('linux.load'))"
                  :height="140"
                />
              </view>
              <view class="status-value">
                <text class="ip-title text-28">{{ chartMap['load']?.title }}</text>
              </view>
            </view>

            <!-- CPU状态 -->
            <view class="status-item">
              <text class="ip-title text-28">CPU</text>
              <view class="chart-container py-20">
                <ECharts
                  canvas-id="cpu-chart"
                  chart-type="gauge"
                  :chart-data="getBaseChartConfig(chartMap['cpu']?.val, 'CPU')"
                  :height="140"
                />
              </view>
              <view class="status-value">
                <text class="ip-title text-28">{{ chartMap['cpu']?.title }}</text>
              </view>
            </view>

            <!-- 内存状态 -->
            <view class="status-item">
              <text class="ip-title text-28">{{ $t('linux.memory') }}</text>
              <view class="chart-container py-20">
                <ECharts
                  canvas-id="mem-chart"
                  chart-type="gauge"
                  :chart-data="getBaseChartConfig(chartMap['mem']?.val, $t('linux.memory'))"
                  :height="140"
                />
              </view>
              <view class="status-value">
                <text class="ip-title text-28">{{ chartMap['mem']?.title }}</text>
              </view>
            </view>

            <!-- 磁盘状态 -->
            <view class="status-item" v-for="(item, index) in chartMap['disk']" :key="index">
              <text class="ip-title text-28 whitespace-nowrap">{{ $t('linux.disk') }}({{ getDiskListName(item.path) }})</text>
              <view class="chart-container py-20">
                <ECharts
                  :canvas-id="`disk-chart-${index}`"
                  chart-type="gauge"
                  :chart-data="getBaseChartConfig(item.val, $t('linux.disk'))"
                  :height="140"
                />
              </view>
              <view class="status-value">
                <text class="ip-title text-28">{{ item.title }}</text>
              </view>
            </view>
          </scroll-view>
          <text class="text-24 text-light absolute bottom-10 ml-40 right-20"> {{ $t('linux.multiDiskTips') }} </text>
        </view>
      </view>
      <view class="mt-20">
        <view class="flex gap-10">
          <!-- 使用v-for循环渲染统计卡片 -->
          <view
            v-for="(item, index) in statItems"
            :key="index"
            class="flex-1 rounded-md px-15 py-40 flex border border-gray-100 items-center justify-center"
            @click="item.action ? handleModuleAction(item.type) : null"
          >
            <view class="mr-20">
              <uni-icons fontFamily="iconfont" :class="item.icon" color="var(--primary-color)" size="24"></uni-icons>
            </view>
            <view class="flex flex-col">
              <text class="text-secondary text-24">{{ item.title }}</text>
              <text
                class="text-secondary text-24 mt-6 font-bold"
                :style="{ color: item.icon === 'icon-safetylogo' && item.value > 0 ? '#f44336' : '' }"
                >{{ item.value }}</text
              >
            </view>
          </view>
        </view>
      </view>
      <view class="mt-20 border border-gray-100 rounded-md p-20">
        <view class="flex items-center justify-between">
          <view class="w-300">
            <uv-subsection
              :list="[$t('linux.networkFlow'), $t('linux.diskIO')]"
              activeColor="#20a50a"
              :current="subsecType"
              :customItemStyle="{ backgroundColor: 'var(--bg-color)' }"
              bgColor="var(--bg-color-secondary)"
              inactiveColor="var(--text-color-secondary)"
              @change="onSectionChange"
            ></uv-subsection>
          </view>
          <view class="flex items-center">
            <button
              class="text-secondary picker-view min-w-150 max-w-280 border border-gray-200 rounded-full px-15 flex items-center justify-center text-24"
              @click="openPicker"
            >
              <text class="mr-5 text-gray-600">{{ subsecType === 0 ? networkName : ioName }}</text>
              <uni-icons class="icon-down" type="bottom" size="12" color="var(--text-color-secondary)"></uni-icons>
            </button>
          </view>
        </view>
        <view class="echart">
          <template v-if="subsecType === 0">
            <ECharts
              key="network"
              canvas-id="network-chart"
              chart-type="line"
              :chart-data="networkChartData"
              :opts="getNetworkChartOpts()"
              :height="500"
            />
          </template>
          <template v-else>
            <ECharts
              key="diskio"
              canvas-id="diskio-chart"
              chart-type="line"
              :chart-data="diskIOChartData"
              :opts="getDiskIOChartOpts()"
              :height="500"
            />
          </template>
        </view>
      </view>
      <view class="mt-20 border border-gray-100 rounded-md p-20 collapse-panel">
        <view class="flex items-center justify-between cursor-pointer collapse-header" @click="toggleFold">
          <text class="text-secondary text-28 font-bold">{{ $t('linux.modulePlugin') }}</text>
          <uni-icons
            type="bottom"
            size="16"
            color="var(--text-color-secondary)"
            class="transition-transform"
            :class="{ 'rotate-180': !isFolded }"
          ></uni-icons>
        </view>
        <view v-show="!isFolded" class="fold-container">
          <view class="grid grid-cols-3 gap-20 mt-20 fold-content" ref="foldContent">
            <view
              v-for="(module, index) in moduleItems"
              :key="index"
              class="module-item px-20 py-30"
              @click="handleModuleAction(module.type)"
            >
              <view class="module-icon-wrapper">
                <uni-icons
                  fontFamily="iconfont"
                  :class="module.icon"
                  size="24"
                  color="var(--text-color-secondary)"
                ></uni-icons>
              </view>
              <text class="text-secondary flex justify-center text-22 text-center">{{ module.title }}</text>
            </view>
          </view>
        </view>
      </view>
      <view class="mt-20 border border-gray-100 rounded-md p-20 collapse-panel">
        <view class="flex items-center justify-between cursor-pointer collapse-header" @click="toggleEnvFold">
          <text class="text-secondary text-28 font-bold">环境</text>
          <uni-icons
            type="bottom"
            size="16"
            color="var(--text-color-secondary)"
            class="transition-transform"
            :class="{ 'rotate-180': !isEnvFolded }"
          ></uni-icons>
        </view>
        <view v-show="!isEnvFolded" class="fold-container">
          <view class="grid grid-cols-3 gap-20 mt-20 fold-content" ref="foldContent">
            <view
              v-for="(module, index) in envItems"
              :key="index"
              class="module-item px-20 py-30"
              @click="handleModuleAction(module.type)"
            >
              <view class="module-icon-wrapper">
                <uni-icons
                  fontFamily="iconfont"
                  :class="module.icon"
                  size="24"
                  color="var(--text-color-secondary)"
                ></uni-icons>
              </view>
              <text class="text-secondary flex justify-center text-22 text-center">{{ module.title }}</text>
            </view>
          </view>
        </view>
      </view>
      <view class="mt-20 border border-gray-100 rounded-md p-20 collapse-panel">
        <view class="flex items-center justify-between cursor-pointer collapse-header" @click="toggleSecurityFold">
          <text class="text-secondary text-28 font-bold">{{ $t('linux.securitySetting') }}</text>
          <uni-icons
            type="bottom"
            size="16"
            color="var(--text-color-secondary)"
            class="transition-transform"
            :class="{ 'rotate-180': !isSecurityFolded }"
          ></uni-icons>
        </view>
        <view v-show="!isSecurityFolded" class="fold-container">
          <view class="grid grid-cols-3 gap-20 mt-20 fold-content" ref="foldContent">
            <view
              v-for="(module, index) in securityItems"
              :key="index"
              class="module-item px-20 py-30"
              @click="handleModuleAction(module.type)"
            >
              <view class="module-icon-wrapper">
                <uni-icons
                  fontFamily="iconfont"
                  :class="module.icon"
                  size="24"
                  color="var(--text-color-secondary)"
                ></uni-icons>
              </view>
              <text class="text-secondary flex justify-center text-22 text-center">{{ module.title }}</text>
            </view>
          </view>
        </view>
      </view>
    </view>
    <uv-picker
      :cancelText="$t('common.cancel')"
      :confirmText="$t('common.confirm')"
      ref="picker"
      keyName="title"
      :columns="[pickerList]"
      :defaultIndex="defaultIndex"
      activeColor="#20a50a"
      color="#999999"
      confirmColor="#20a50a"
      @confirm="confirm"
    ></uv-picker>
  </page-container>
</template>

<script setup>
  import { ref, onMounted, onUnmounted, watch, computed } from 'vue';
  import { onReady, onUnload, onShow, onHide, onLoad } from '@dcloudio/uni-app';
  import PageContainer from '@/components/PageContainer/index.vue';
  import BtButton from '@/components/BtButton/index.vue';
  import { useConfigStore } from '@/store/modules/config';
  import ECharts from '@/components/ECharts/index.vue';
  import { $t } from '@/locale/index.js';
  // 直接导入所需状态和方法，不再使用组合式API
  import {
    networkChartData,
    diskIOChartData,
    initData,
    startTimer,
    stopTimer,
    initChartByType,
    getNetworkChartOpts,
    getDiskIOChartOpts,
    picker,
    openPicker,
    confirm,
    onSectionChange,
    handleModuleAction,
    getNetwork,
    networkName,
    ioName,
    subsecType,
    networkInfo,
    chartMap,
    websiteTotal,
    safetyTotal,
    databaseTotal,
    networkList,
    ioList,
    getDiskListName,
    defaultIndex,
    getBaseChartConfig,
    isNodeManagementSupported,
  } from './useController';

  const { currentServerInfo } = useConfigStore().getReactiveState();

  const pickerList = computed(() => {
    if (subsecType.value === 0) {
      return networkList.value;
    } else {
      return ioList.value;
    }
  });

  // 定义统计卡片数据
  const statItems = computed(() => [
    {
      icon: 'icon-site',
      title: $t('linux.website'),
      value: websiteTotal.value,
      type: 'website',
      action: true,
    },
    {
      icon: 'icon-database',
      title: $t('linux.database'),
      value: databaseTotal.value,
      type: 'database',
      action: true,
    },
    {
      icon: 'icon-safetylogo',
      title: $t('linux.securityRisk'),
      value: safetyTotal.value,
      type: '',
      action: true,
    },
  ]);

  // 定义模块项数据
  const moduleItems = computed(() => {
    const baseItems = [
      {
        icon: 'icon-site',
        title: $t('linux.website'),
        type: 'website',
      },
      {
        icon: 'icon-database',
        title: $t('linux.database'),
        type: 'database',
      },
      {
        icon: 'icon-folder',
        title: $t('linux.fileManagement'),
        type: 'file',
      },
      {
        icon: 'icon-safetylogo',
        title: $t('linux.security'),
        type: 'security',
      },
      {
        icon: 'icon-terminal',
        title: $t('linux.terminal'),
        type: 'terminal',
      },
      {
        icon: 'icon-control',
        title: $t('linux.monitoring'),
        type: 'control',
      },
      {
        icon: 'icon-site-monitor',
        title: $t('linux.siteMonitor'),
        type: 'monitorReport',
      },
      {
        icon: 'icon-nginx',
        title: $t('linux.nginx'),
        type: 'nginx',
      },
      {
        icon: 'icon-cron',
        title: $t('linux.crontab'),
        type: 'crontab',
      },
    ];

    // 只有当版本支持时才添加节点管理模块
    if (isNodeManagementSupported.value) {
      baseItems.push({
        icon: 'icon-node',
        title: '节点管理',
        type: 'node',
      });
    }

    return baseItems;
  });

  const envItems = computed(() => [
    {
      icon: 'icon-nginx',
      title: 'nginx',
      type: 'nginxEnv',
    },
    {
      icon: 'icon-mysql',
      title: 'mysql',
      type: 'mysql',
    },
  ]);

  const securityItems = computed(() => [
    {
      icon: 'icon-SSH',
      title: $t('linux.ssh'),
      type: 'ssh',
    },
    {
      icon: 'icon-setting',
      title: $t('linux.setting'),
      type: 'setting',
    },
  ]);

  // 不再需要从组合式API中解构，已在顶部直接导入

  const isFolded = ref(false);
  const foldContent = ref(null);

  const toggleFold = () => {
    isFolded.value = !isFolded.value;
  };

  const isEnvFolded = ref(false);

  const toggleEnvFold = () => {
    isEnvFolded.value = !isEnvFolded.value;
  };

  const isSecurityFolded = ref(false);

  const toggleSecurityFold = () => {
    isSecurityFolded.value = !isSecurityFolded.value;
  };

  // 系统图标
  const systemIcon = computed(() => {
    if (!networkInfo.value.system) return '';
    return networkInfo.value.system.split(' ')[0]?.toLowerCase();
  });

  const handleSwitchPanel = () => {
    uni.navigateTo({
      url: `/pages/server-selector/index?server=${currentServerInfo.value?.ip}`,
      animationType: 'zoom-fade-out',
    });
  };

  // 生命周期钩子
  onReady(() => {
    // 初始化图表数据
    initData();
    getNetwork();
  });

  onShow(() => {
    // 设置定时器
    startTimer();
  });

  onHide(() => {
    // 清除定时器
    stopTimer();
  });

  onLoad(async (options) => {
    const { fromNode, nodeId } = options || {};

    if (fromNode && nodeId) {
      // 动态导入配置管理工具
      const { setNodeAccessFlag } = await import('@/utils/nodeConfigManager');
      // 标记这是从节点访问的
      setNodeAccessFlag(true, nodeId);
    }
  });

  onUnload(async () => {
    // 如果是从节点访问的，需要恢复原配置
    const { safeRestoreConfig } = await import('@/utils/nodeConfigManager');
    safeRestoreConfig();

    // 清除定时器
    stopTimer();
  });

  // 监听section类型变化
  watch(subsecType, () => {
    // 确保切换时数据格式正确
    initChartByType(subsecType.value);
  });
</script>

<style lang="scss" scoped>
  .server-info {
    background: var(--bg-color-secondary);
  }
  .ip-title {
    color: var(--text-color-secondary);
  }
  .status-item {
    min-width: 25%;
    max-width: 100%;
    text-align: center;
    margin-bottom: 6rpx;
    display: inline-block;
    padding: 40rpx 0;
  }

  .server-item {
    white-space: nowrap;
    background-color: var(--bg-color);
    border-radius: 16rpx;
    margin-bottom: 30rpx;
  }

  .quick-action-item {
    background: var(--bg-color);
    border: 1px solid var(--border-color);
    border-radius: 12rpx;
    padding: 20rpx;
    display: flex;
    flex-direction: column;
    gap: 16rpx;
    transition: all 0.3s;

    &:active {
      transform: scale(0.98);
    }
  }

  .icon-wrapper {
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 10rpx;
  }

  .action-content {
    display: flex;
    flex-direction: column;
    gap: 6rpx;
  }

  .action-title {
    font-size: 28rpx;
    font-weight: 500;
    color: var(--text-color);
  }

  .action-desc {
    font-size: 24rpx;
    color: var(--text-color-secondary);
  }

  .module-item {
    background: var(--bg-color);
    border: 1px solid var(--border-color);
    border-radius: 12rpx;
    display: flex;
    flex-direction: column;
    gap: 10rpx;
    transition: all 0.3s;

    &:active {
      transform: scale(0.98);
    }
  }

  .module-icon-wrapper {
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .fold-container {
    overflow: hidden;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    animation: slideDown 0.3s ease-out forwards;
  }

  @keyframes slideDown {
    from {
      opacity: 0;
      transform: translateY(-10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .transition-transform {
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .rotate-180 {
    transform: rotate(180deg);
  }

  .collapse-panel {
    overflow: hidden;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
  }

  .collapse-header {
    cursor: pointer;
    user-select: none;
    transition: background-color 0.2s;

    &:hover {
      background-color: rgba(0, 0, 0, 0.01);
    }

    &:active {
      background-color: rgba(0, 0, 0, 0.02);
    }
  }

  .fold-content {
    padding-bottom: 20rpx;
  }

  .picker-view {
    background-color: var(--dialog-bg-color);
  }
</style>
