import { ref, reactive, computed, nextTick } from 'vue';
import { getSoftStatus, serviceAdmin, getPluginInfo, installPlugin, getPluginLog } from '@/api/config';
import { getNginxStatus } from '@/api/nginx';

// 提取版本号的主版本号（xx.xx格式）进行比较
const extractMainVersion = (version) => {
	const match = version.match(/^(\d+\.\d+)/);
	return match ? match[1] : version;
};

export const pageContainer = ref(null);

let versionPicker = ref(null);

export const setVersionPickerRef = (ref) => {
	versionPicker = ref;
};

export const serviceStatus = reactive({
	isRunning: true,
	version: '获取中...',
});

export const serviceLoading = ref(false);

export const isLoadStatusExpanded = ref(false);
export const loadStatusLoading = ref(false);

export const loadStats = reactive({
	activeConnections: 1,
	accepts: 100,
	handled: 100,
	requests: 100,
	reading: 0,
	writing: 1,
	waiting: 0,
	worker: 4,
	workerCpu: 0,
	workerMem: '139MB',
});

export const quickActions = computed(() => {
	const actions = [];

	if (serviceStatus.isRunning) {
		actions.push(
			{
				id: 'stop',
				title: '停止',
				icon: 'pause-circle-fill',
				useIconfont: false,
			},
			{
				id: 'restart',
				title: '重启',
				icon: 'reload',
				useIconfont: false,
			},
			{
				id: 'reload',
				title: '重载配置',
				icon: 'checkmark-circle',
				useIconfont: false,
			},
		);
	} else {
		actions.push({
			id: 'start',
			title: '启动',
			icon: 'play-circle-fill',
			useIconfont: false,
		});
	}

	return actions;
});

export const functionModules = ref([
	{
		id: 'config',
		title: '配置文件',
		desc: '编辑nginx.conf主配置文件',
		icon: 'file-text',
		useIconfont: false,
		showData: false,
		navigatable: true,
	},
	{
		id: 'version',
		title: '版本切换',
		desc: '切换不同的nginx版本',
		icon: 'tags',
		useIconfont: false,
		showData: true,
		dataValue: '获取中...',
		dataUnit: '',
	},
	{
		id: 'status',
		title: '负载状态',
		desc: '查看连接数和请求统计',
		icon: 'grid',
		useIconfont: false,
		showData: true,
		dataValue: '1',
		dataUnit: '个活跃连接',
	},
	{
		id: 'performance',
		title: '性能调整',
		desc: '调整worker进程和连接数等参数',
		icon: 'setting',
		useIconfont: false,
		showData: false,
		navigatable: true,
	},
	{
		id: 'errorLog',
		title: '错误日志',
		desc: '查看nginx错误日志',
		icon: 'warning',
		useIconfont: false,
		showData: false,
		navigatable: true,
	},
]);

export const availableVersions = ref([]);
export const selectedVersion = ref('');

export const isNginxInstalled = ref(false);

export const versionColumns = computed(() => {
	return [availableVersions.value.map((version) => 'nginx ' + version.m_version)];
});

export const checkNginxInstalled = async () => {
	try {
		const res = await getPluginInfo({ sName: 'nginx' });
		if (!res.status && res.setup !== undefined && !res.setup) {
			isNginxInstalled.value = false;
			return;
		}
		isNginxInstalled.value = true;
	} catch (error) {
		console.error('检查nginx安装状态失败:', error);
		isNginxInstalled.value = false;
	}
};

export const getNginxVersions = async () => {
	try {
		const res = await getPluginInfo({
			sName: 'nginx',
		});
		availableVersions.value = res.versions || [];
		if (availableVersions.value.length > 0) {
			selectedVersion.value = availableVersions.value[0].m_version;
		}
	} catch (error) {
		console.error('获取nginx版本信息失败:', error);
		availableVersions.value = [
			{ m_version: '1.24' },
			{ m_version: '1.26' },
			{ m_version: '1.22' },
			{ m_version: '1.28' },
			{ m_version: '-Tengine3.1' },
			{ m_version: '1.21' },
			{ m_version: 'openresty' },
			{ m_version: '1.20' },
		];
		selectedVersion.value = availableVersions.value[0].m_version;
	}
};

export const initNginxEnvData = async () => {
	try {
		await checkNginxInstalled();
		if (isNginxInstalled.value) {
			await getNginxServiceStatus();
			await getNginxVersions();
			await getNginxLoadStatus();
		}
	} catch (error) {
		console.error('初始化Nginx环境数据失败:', error);
	}
};

export const getNginxServiceStatus = async () => {
	try {
		const res = await getSoftStatus({ name: 'web' });
		serviceStatus.isRunning = res.s_status;

		if (res.version) {
			const currentVersion = 'nginx ' + res.version;
			serviceStatus.version = currentVersion;

			const versionModule = functionModules.value.find((m) => m.id === 'version');
			if (versionModule) {
				versionModule.dataValue = currentVersion;
			}
		}
	} catch (error) {
		console.error('获取Nginx服务状态失败:', error);
		serviceStatus.isRunning = false;
	}
};

export const getNginxLoadStatus = async () => {
	try {
		const res = await getNginxStatus();

		loadStats.activeConnections = parseInt(res.active) || 0;
		loadStats.accepts = parseInt(res.accepts) || 0;
		loadStats.handled = parseInt(res.handled) || 0;
		loadStats.requests = parseInt(res.requests) || 0;
		loadStats.reading = parseInt(res.Reading) || 0;
		loadStats.writing = parseInt(res.Writing) || 0;
		loadStats.waiting = parseInt(res.Waiting) || 0;
		loadStats.worker = parseInt(res.worker) || 0;
		loadStats.workerCpu = parseFloat(res.workercpu) || 0;
		loadStats.workerMem = res.workermen || '0MB';

		const statusModule = functionModules.value.find((m) => m.id === 'status');
		if (statusModule) {
			statusModule.dataValue = loadStats.activeConnections.toString();
			statusModule.dataUnit = '个活跃连接';
		}
	} catch (error) {
		console.error('获取Nginx负载状态失败:', error);
	}
};

export const toggleService = async (value) => {
	try {
		serviceLoading.value = true;

		const action = value ? 'start' : 'stop';
		const res = await serviceAdmin({ name: 'nginx', type: action });

		if (res.status) {
			await getNginxServiceStatus();

			if (serviceStatus.isRunning) {
				pageContainer.value?.notify?.success('Nginx服务启动成功');
			} else {
				pageContainer.value?.notify?.success('Nginx服务停止成功');
			}
		} else {
			const errorMsg = res.msg || (value ? '启动失败' : '停止失败');
			pageContainer.value?.notify?.error(errorMsg);
		}
	} catch (error) {
		console.error('服务状态切换失败:', error);
		const errorMsg = error.msg || error.message || '操作失败，请重试';
		pageContainer.value?.notify?.error(errorMsg);
	} finally {
		serviceLoading.value = false;
	}
};

// 确认对话框相关状态
export const showConfirmDialog = ref(false);
export const confirmDialogConfig = ref({
	title: '',
	content: '',
	confirmText: '确认',
	action: null,
});

// 显示确认对话框
const showActionConfirm = (action) => {
	const configs = {
		start: {
			title: '启动确认',
			content: '确定要启动Nginx服务吗？',
			confirmText: '确认启动',
		},
		stop: {
			title: '停止确认',
			content: '确定要停止Nginx服务吗？停止后网站将无法访问。',
			confirmText: '确认停止',
		},
		restart: {
			title: '重启确认',
			content: '确定要重启Nginx服务吗？重启过程中网站将短暂无法访问。',
			confirmText: '确认重启',
		},
		reload: {
			title: '重载确认',
			content: '确定要重载Nginx配置吗？',
			confirmText: '确认重载',
		},
	};

	const config = configs[action.id];
	if (config) {
		confirmDialogConfig.value = {
			...config,
			action: action,
		};
		showConfirmDialog.value = true;
	}
};

// 确认执行操作
export const confirmQuickAction = async () => {
	const action = confirmDialogConfig.value.action;
	if (!action) return;

	showConfirmDialog.value = false;
	await executeQuickAction(action);
};

// 取消确认对话框
export const cancelQuickAction = () => {
	showConfirmDialog.value = false;
	confirmDialogConfig.value = {
		title: '',
		content: '',
		confirmText: '确认',
		action: null,
	};
};

// 执行具体的快速操作
const executeQuickAction = async (action) => {
	try {
		switch (action.id) {
			case 'start':
				await toggleService(true);
				break;
			case 'stop':
				await toggleService(false);
				break;
			case 'restart':
				pageContainer.value?.notify?.primary('正在重启Nginx服务...');
				serviceLoading.value = true;
				try {
					const res = await serviceAdmin({ name: 'nginx', type: 'restart' });
					if (res.status) {
						await getNginxServiceStatus();
						await getNginxLoadStatus();
						pageContainer.value?.notify?.success('Nginx服务重启成功');
					} else {
						const errorMsg = res.msg || '重启失败';
						pageContainer.value?.notify?.error(errorMsg);
					}
				} catch (error) {
					const errorMsg = error.msg || error.message || '重启失败，请重试';
					pageContainer.value?.notify?.error(errorMsg);
				} finally {
					serviceLoading.value = false;
				}
				break;
			case 'reload':
				pageContainer.value?.notify?.primary('正在重载配置...');
				serviceLoading.value = true;
				try {
					const res = await serviceAdmin({ name: 'nginx', type: 'reload' });
					if (res.status) {
						pageContainer.value?.notify?.success('配置重载成功');
					} else {
						const errorMsg = res.msg || '重载配置失败';
						pageContainer.value?.notify?.error(errorMsg);
					}
				} catch (error) {
					const errorMsg = error.msg || error.message || '重载配置失败，请重试';
					pageContainer.value?.notify?.error(errorMsg);
				} finally {
					serviceLoading.value = false;
				}
				break;
		}
	} catch (error) {
		console.error('快速操作失败:', error);
		const errorMsg = error.msg || error.message || '操作失败，请重试';
		pageContainer.value?.notify?.error(errorMsg);
		serviceLoading.value = false;
	}
};

export const handleQuickAction = async (action) => {
	// 显示确认对话框而不是直接执行操作
	showActionConfirm(action);
};

export const handleModuleClick = async (module) => {
	try {
		switch (module.id) {
			case 'config':
				openNginxConfigEditor();
				break;
			case 'version':
				showVersionSelector();
				break;
			case 'status':
				isLoadStatusExpanded.value = !isLoadStatusExpanded.value;
				if (isLoadStatusExpanded.value) {
					await refreshLoadStatus();
				}
				break;
			case 'performance':
				uni.navigateTo({
					url: '/linux/nginxEnv/performance/index',
				});
				break;
			case 'errorLog':
				uni.navigateTo({
					url: '/linux/nginxEnv/errorLog/index',
				});
				break;
		}
	} catch (error) {
		console.error('模块操作失败:', error);
		pageContainer.value?.notify?.error('操作失败，请重试');
	}
};

const showVersionSelector = () => {
	const currentVersionValue = serviceStatus.version.replace('nginx ', '');
	const currentMainVersion = extractMainVersion(currentVersionValue);

	const currentVersionIndex = availableVersions.value.findIndex((version) => {
		const versionMainVersion = extractMainVersion(version.m_version);
		return versionMainVersion === currentMainVersion;
	});

	if (currentVersionIndex !== -1) {
		nextTick(() => {
			versionPicker.value?.setIndexs([currentVersionIndex], true);
		});
	}
	versionPicker.value?.open();
};

export const versionSwitching = ref(false);

export const showVersionSwitchDialog = ref(false);
export const switchLogs = ref([]);
export const switchProgress = ref(0);
export const switchHasError = ref(false);
export const switchIsCompleted = ref(false);
let switchLogInterval = null;

const addSwitchLog = (message, type = 'info') => {
	const now = new Date();
	const timestamp = `${String(now.getHours()).padStart(2, '0')}:${String(now.getMinutes()).padStart(2, '0')}:${String(
		now.getSeconds(),
	).padStart(2, '0')}`;
	switchLogs.value.push({ timestamp, message, type });
};

const startSwitchLogPolling = () => {
	let estimateProgress = 0;
	const progressInterval = 15;
	const maxProgress = 99;

	switchLogInterval = setInterval(async () => {
		try {
			const logRes = await getPluginLog();
			if (logRes) {
				if (estimateProgress < maxProgress) {
					estimateProgress += progressInterval;
					if (estimateProgress > maxProgress) {
						estimateProgress = maxProgress;
					}
					switchProgress.value = estimateProgress;
				}

				if (logRes.title) {
					addSwitchLog(logRes.title);
				}

				if (logRes.status === false) {
					stopSwitchLogPolling();
					switchProgress.value = 100;
					switchIsCompleted.value = true;
					addSwitchLog('版本切换完成');

					await refreshAfterVersionSwitch();

					setTimeout(() => {
						showVersionSwitchDialog.value = false;
						resetSwitchState();
						versionSwitching.value = false;

						// 显示版本切换成功提示
						pageContainer.value?.notify?.success(`Nginx版本已成功切换到 ${serviceStatus.version}`);
					}, 1000);
				}
			}
		} catch (error) {
			console.error('获取切换日志失败:', error);
		}
	}, 2000);
};

const stopSwitchLogPolling = () => {
	if (switchLogInterval) {
		clearInterval(switchLogInterval);
		switchLogInterval = null;
	}
};

const resetSwitchState = () => {
	switchLogs.value = [];
	switchProgress.value = 0;
	switchHasError.value = false;
	switchIsCompleted.value = false;
	stopSwitchLogPolling();
};

const refreshAfterVersionSwitch = async () => {
	try {
		await getNginxServiceStatus();
		await getNginxVersions();
		await getNginxLoadStatus();
	} catch (error) {
		console.error('刷新状态失败:', error);
	}
};

export const onVersionSwitchComplete = () => {
	showVersionSwitchDialog.value = false;
	resetSwitchState();
	versionSwitching.value = false;
};

export const onVersionConfirm = async (e) => {
	const selectedVersionDisplay = e.value[0];
	const selectedVersionValue = selectedVersionDisplay.replace('nginx ', '');

	// 获取当前服务版本号（去掉nginx前缀）
	const currentVersionValue = serviceStatus.version.replace('nginx ', '');

	const selectedMainVersion = extractMainVersion(selectedVersionValue);
	const currentMainVersion = extractMainVersion(currentVersionValue);

	// 比较主版本号
	if (selectedMainVersion === currentMainVersion) {
		pageContainer.value?.notify?.warning('当前已是此版本');
		return;
	}

	try {
		resetSwitchState();
		versionSwitching.value = true;

		showVersionSwitchDialog.value = true;
		addSwitchLog(`开始切换到 ${selectedVersionDisplay}`);
		addSwitchLog('正在准备切换环境...');

		const res = await installPlugin({
			sName: 'nginx',
			version: selectedVersionValue,
			min_version: '',
		});

		if (res.status) {
			startSwitchLogPolling();
		} else {
			switchHasError.value = true;
			addSwitchLog(res.msg || '版本切换失败', 'error');
		}
	} catch (error) {
		console.error('版本切换失败:', error);
		switchHasError.value = true;
		addSwitchLog('版本切换失败: ' + error.message, 'error');
	}
};

export const onVersionCancel = () => {};

export const refreshLoadStatus = async () => {
	try {
		loadStatusLoading.value = true;
		await getNginxLoadStatus();
		pageContainer.value?.notify?.success('负载状态已更新');
	} catch (error) {
		console.error('刷新负载状态失败:', error);
		pageContainer.value?.notify?.error('刷新失败，请重试');
	} finally {
		loadStatusLoading.value = false;
	}
};

export const initNginxLoadStatus = async () => {
	try {
		await getNginxLoadStatus();
	} catch (error) {
		console.error('初始化Nginx负载状态失败:', error);
	}
};

export const openNginxConfigEditor = async () => {
	try {
		const configFileItem = {
			path: '/www/server/nginx/conf/nginx.conf',
			title: 'nginx.conf',
			size: 1024,
		};

		if (configFileItem.size > 3 * 1024 * 1024) {
			pageContainer.value?.notify?.warning('配置文件过大，无法编辑');
			return;
		}

		uni.navigateTo({
			url: `/linux/files/editor/index?fileItem=${JSON.stringify(configFileItem)}`,
			animationType: 'zoom-fade-out',
		});
	} catch (error) {
		console.error('打开配置文件编辑器失败:', error);
		pageContainer.value?.notify?.error('打开配置文件失败，请重试');
	}
};
