<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Headers/MPAnalysisHelper.h</key>
		<data>
		4Y7PGH1hmojYsqwuaDGxAq4HjIU=
		</data>
		<key>Headers/MPMasAdapter.h</key>
		<data>
		jrmDwlxN9XXPrBMtWvTJ/1MxQXI=
		</data>
		<key>Headers/MPRemoteLoggingInterface.h</key>
		<data>
		V+m2qyRCjFnw2NAuWasoUixI92U=
		</data>
		<key>Info.plist</key>
		<data>
		lf1PjnZgSWb0ZaxMKecyltHfaB8=
		</data>
		<key>Modules/module.modulemap</key>
		<data>
		EEeyTPDqmhAY2kOuSN/cx+dKJ6E=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>Headers/MPAnalysisHelper.h</key>
		<dict>
			<key>hash</key>
			<data>
			4Y7PGH1hmojYsqwuaDGxAq4HjIU=
			</data>
			<key>hash2</key>
			<data>
			qlUqvmMBKCOKF4+352i37/fntJ84Ud/ObMFo+sAOpzc=
			</data>
		</dict>
		<key>Headers/MPMasAdapter.h</key>
		<dict>
			<key>hash</key>
			<data>
			jrmDwlxN9XXPrBMtWvTJ/1MxQXI=
			</data>
			<key>hash2</key>
			<data>
			ZxzWJrw99wynvdDprSQH+Ume2xt/O9lLKnn0p1APIJk=
			</data>
		</dict>
		<key>Headers/MPRemoteLoggingInterface.h</key>
		<dict>
			<key>hash</key>
			<data>
			V+m2qyRCjFnw2NAuWasoUixI92U=
			</data>
			<key>hash2</key>
			<data>
			Nl4D3ChefPMWrjkAzQaw1ZRwxb042YGPp/6DcRDgnoY=
			</data>
		</dict>
		<key>Modules/module.modulemap</key>
		<dict>
			<key>hash</key>
			<data>
			EEeyTPDqmhAY2kOuSN/cx+dKJ6E=
			</data>
			<key>hash2</key>
			<data>
			QwgYXkN8NOMleHDebQTQUTbJoQhhOZmmqF14HohMsys=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
