<template>
	<page-container
		ref="pageContainer"
		:showTabBar="true"
		:tabData="tabData"
		v-model:activeIndex="currentTab"
		@tabClick="onTabClick"
	>
		<template #title>
			<view class="custom-title-container">
				<view class="title-main text-secondary">计划任务</view>
				<view class="title-subtitle">
					<uv-icon name="info-circle" size="14" color="#999"></uv-icon>
					<text class="subtitle-text">长按列表项进行更多操作</text>
				</view>
			</view>
		</template>
		<!-- 计划任务列表 -->
		<z-paging
			ref="crontabPaging"
			class="mt-260"
			:default-page-size="50"
			use-virtual-list
			:force-close-inner-list="true"
			:auto-hide-loading-after-first-loaded="false"
			:auto-show-system-loading="true"
			@virtualListChange="virtualListChange"
			@query="queryList"
			@refresherStatusChange="reload"
			:refresher-complete-delay="200"
		>
			<view
				class="px-20 mt-20"
				v-for="item in crontabList"
				:id="`zp-id-${item.zp_index}`"
				:key="item.zp_index"
				@click="handleCrontabClick(item)"
			>
				<view
					class="crontab-item-container bg-primary"
					@touchstart="handleTouchStart($event)"
					@touchmove="handleTouchMove($event)"
					@touchend="handleTouchEnd($event)"
					@touchcancel="handleTouchCancel($event)"
					:data-index="item.zp_index"
					:data-crontab="JSON.stringify(item)"
				>
					<!-- 主要信息区域 -->
					<view class="crontab-main-info">
						<view class="crontab-name" :class="item.status == 0 ? 'text-error' : 'text-bt-primary'">
							{{ item.rname || item.name }}
						</view>
						<view class="crontab-status">
							<!-- 运行状态 -->
							<view class="status-tag run-tag" :class="item.status == 0 ? 'run-stopped' : 'run-active'">
								{{ item.status == 0 ? '停用' : '正常' }}
							</view>
						</view>
					</view>

					<!-- 详细信息区域 -->
					<view class="crontab-extra-info">
						<view class="crontab-cycle">
							<text class="info-label">执行周期:</text>
							<text class="info-value">{{ item.cycle }}</text>
						</view>
					</view>
					<view class="crontab-extra-info">
						<view class="crontab-type">
							<text class="info-label">类型:</text>
							<text class="info-value">{{ getTaskTypeDisplay(item) || '--' }}</text>
						</view>
					</view>
					<!-- 底部信息区域 -->
					<view class="crontab-bottom-info">
						<view class="crontab-backup">
							<text class="info-label">保存数量:</text>
							<text class="info-value backup-value">{{ getBackupInfo(item).text || '--' }}</text>
						</view>
						<view class="crontab-backup-to">
							<text class="info-label">备份到:</text>
							<text class="info-value">{{ getBackupTo(item) || '--' }}</text>
						</view>
					</view>

					<!-- 第二行信息 -->
					<view class="crontab-bottom-info">
						<view class="crontab-path">
							<text class="info-label">备份路径:</text>
							<text
								class="info-value path-value"
								:class="{ 'path-clickable': getBackupPath(item) && getBackupPath(item) !== '--' }"
								@click.stop="handleBackupPathClick(item)"
							>
								{{ getBackupPath(item) || '--' }}
							</text>
						</view>
					</view>

					<view class="crontab-bottom-info">
						<view class="crontab-time">
							<text class="info-label">上次执行时间:</text>
							<text class="info-value">{{ item.addtime || '--' }}</text>
						</view>
					</view>

					<!-- 第三行信息 -->
					<view class="crontab-bottom-info">
						<view class="crontab-result">
							<text class="info-label">上次定时任务执行结果:</text>
							<text class="info-value" :class="getResultClass(item)">{{ getResultText(item) }}</text>
						</view>
					</view>
				</view>
			</view>
		</z-paging>

		<!-- 添加一个隐藏的临时菜单用于测量高度 -->
		<view
			class="temp-measure-menu context-menu"
			v-if="showTempMenu"
			style="position: absolute; opacity: 0; pointer-events: none; top: -9999px"
		>
			<view class="menu-item">
				<uv-icon name="play-circle-fill" size="16" color="#20a53a"></uv-icon>
				<text class="menu-text">启动任务</text>
			</view>
			<view class="menu-divider"></view>
			<view class="menu-item">
				<uv-icon name="play-right-fill" size="16" color="#FF8C00"></uv-icon>
				<text class="menu-text">执行任务</text>
			</view>
			<view class="menu-divider"></view>
			<view class="menu-item">
				<uni-icons type="compose" size="16" color="#007AFF"></uni-icons>
				<text class="menu-text">编辑任务</text>
			</view>
			<view class="menu-divider"></view>
			<view class="menu-item">
				<uni-icons type="eye" size="16" color="#007AFF"></uni-icons>
				<text class="menu-text">查看日志</text>
			</view>
			<view class="menu-divider"></view>
			<view class="menu-item menu-delete">
				<uni-icons type="trash" size="16" color="#FF3B30"></uni-icons>
				<text class="menu-text menu-text-delete">删除任务</text>
			</view>
		</view>

		<!-- 模糊遮罩 -->
		<view v-if="showContextMenu" class="blur-overlay" @click="hideContextMenu"></view>

		<!-- 上下文菜单 -->
		<view
			v-if="showContextMenu"
			class="context-menu"
			:class="menuPosition.class"
			:style="{
				top: menuPosition.top,
				left: menuPosition.left,
			}"
		>
			<!-- 启动/停止选项 -->
			<view class="menu-item" v-if="activeCrontab?.status == 0" @click="showStartConfirm">
				<uv-icon name="play-circle-fill" size="16" color="#20a53a"></uv-icon>
				<text class="menu-text">启动任务</text>
			</view>
			<view class="menu-item" v-if="activeCrontab?.status == 1" @click="showStopConfirm">
				<uv-icon name="pause-circle-fill" size="16" color="#FF3B30"></uv-icon>
				<text class="menu-text">停止任务</text>
			</view>
			<view class="menu-divider"></view>
			<!-- 执行任务选项 -->
			<view class="menu-item" @click="handleExecuteCrontab">
				<uv-icon name="play-right-fill" size="16" color="#FF8C00"></uv-icon>
				<text class="menu-text">执行任务</text>
			</view>
			<view class="menu-divider"></view>
			<view class="menu-item" @click="handleEditCrontab">
				<uni-icons type="compose" size="16" color="#007AFF"></uni-icons>
				<text class="menu-text">编辑任务</text>
			</view>
			<view class="menu-divider"></view>
			<view class="menu-item" @click="handleViewLog">
				<uni-icons type="eye" size="16" color="#007AFF"></uni-icons>
				<text class="menu-text">查看日志</text>
			</view>
			<view class="menu-divider"></view>
			<view class="menu-item menu-delete" @click="handleDeleteCrontab">
				<uni-icons type="trash" size="16" color="#FF3B30"></uni-icons>
				<text class="menu-text menu-text-delete">删除任务</text>
			</view>
		</view>

		<!-- 克隆项容器 - 放在外层，使用fixed定位 -->
		<view class="fixed-clone-container" v-if="showContextMenu">
			<view class="item-clone-wrapper" :style="clonePosition" v-if="activeCrontab">
				<view class="crontab-item-clone bg-primary">
					<!-- 主要信息区域 -->
					<view class="crontab-main-info">
						<view
							class="crontab-name"
							:class="activeCrontab.status == 0 ? 'text-error' : 'text-bt-primary'"
						>
							{{ activeCrontab.rname || activeCrontab.name }}
						</view>
						<view class="crontab-status">
							<!-- 运行状态 -->
							<view
								class="status-tag run-tag"
								:class="activeCrontab.status == 0 ? 'run-stopped' : 'run-active'"
							>
								{{ activeCrontab.status == 0 ? '停用' : '正常' }}
							</view>
						</view>
					</view>

					<!-- 详细信息区域 -->
					<view class="crontab-extra-info">
						<view class="crontab-cycle">
							<text class="info-label">执行周期:</text>
							<text class="info-value">{{ activeCrontab.cycle }}</text>
						</view>
					</view>

          <view class="crontab-extra-info">
						<view class="crontab-type">
							<text class="info-label">类型:</text>
							<text class="info-value">{{ getTaskTypeDisplay(activeCrontab) || '--' }}</text>
						</view>
					</view>

					<!-- 底部信息区域 -->
					<view class="crontab-bottom-info">
						<view class="crontab-backup">
							<text class="info-label">保存数量:</text>
							<text class="info-value backup-value">{{ getBackupInfo(activeCrontab).text || '--' }}</text>
						</view>
						<view class="crontab-backup-to">
							<text class="info-label">备份到:</text>
							<text class="info-value">{{ getBackupTo(activeCrontab) || '--' }}</text>
						</view>
					</view>

					<!-- 第二行信息 -->
					<view class="crontab-bottom-info">
						<view class="crontab-path">
							<text class="info-label">备份路径:</text>
							<text
								class="info-value path-value"
								:class="{ 'path-clickable': getBackupPath(activeCrontab) && getBackupPath(activeCrontab) !== '--' }"
								@click.stop="handleBackupPathClick(activeCrontab)"
							>
								{{ getBackupPath(activeCrontab) || '--' }}
							</text>
						</view>
					</view>

					<view class="crontab-bottom-info">
						<view class="crontab-time">
							<text class="info-label">上次执行时间:</text>
							<text class="info-value">{{ activeCrontab.addtime || '--' }}</text>
						</view>
					</view>

					<!-- 第三行信息 -->
					<view class="crontab-bottom-info">
						<view class="crontab-result">
							<text class="info-label">上次定时任务执行结果:</text>
							<text class="info-value" :class="getResultClass(activeCrontab)">{{
								getResultText(activeCrontab)
							}}</text>
						</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 启动确认对话框 -->
		<CustomDialog
			contentHeight="200rpx"
			v-model="showStartDialog"
			title="启动确认"
			confirmText="确认启动"
			:confirmStyle="{
				backgroundColor: '#20a53a',
			}"
			@confirm="confirmStartCrontab"
		>
			<view class="text-secondary flex justify-center items-center h-full">
				该计划任务已停止，是否要启用这个计划任务？
			</view>
		</CustomDialog>

		<!-- 停止确认对话框 -->
		<CustomDialog
			contentHeight="200rpx"
			v-model="showStopDialog"
			title="停止确认"
			confirmText="确认停止"
			:confirmStyle="{
				backgroundColor: '#FF3B30',
			}"
			@confirm="confirmStopCrontab"
		>
			<view class="text-secondary flex justify-center items-center h-full">
				该计划任务正在运行，是否要停用这个计划任务？
			</view>
		</CustomDialog>

		<!-- 删除确认对话框 -->
		<CustomDialog
			contentHeight="200rpx"
			v-model="showDeleteDialog"
			title="删除确认"
			confirmText="确认删除"
			@confirm="confirmDeleteCrontab"
		>
			<view class="text-secondary flex justify-center items-center h-full delete-confirm-text">
				您确定要删除计划任务【{{ activeCrontab?.rname || activeCrontab?.name || '' }}】，是否继续？
			</view>
		</CustomDialog>

		<!-- 添加按钮 -->
		<view class="add-button" @click="handleAddCrontab">
			<uv-icon name="plus" size="24" color="#fff"></uv-icon>
		</view>
	</page-container>
</template>

<script setup>
	import { ref, watch, onMounted } from 'vue';
	import { onShow, onBackPress, onUnload } from '@dcloudio/uni-app';
	import PageContainer from '@/components/PageContainer/index.vue';
	import CustomDialog from '@/components/CustomDialog/index.vue';
	import { $t } from '@/locale/index.js';
	import { formatTime } from '@/utils/date.js';
	import {
		crontabPaging,
		showContextMenu,
		currentTab,
		getCrontabList,
		handleCrontabClick,
		showTempMenu,
		menuPosition,
		handleTouchStart,
		handleTouchMove,
		handleTouchEnd,
		handleTouchCancel,
		clonePosition,
		activeCrontab,
		hideContextMenu,
		measureMenuHeight,
		pageContainer,
		handleEditCrontab,
		handleViewLog,
		handleDeleteCrontab,
		handleExecuteCrontab,
		showFloatingMenu,
		showStartDialog,
		showStopDialog,
		showDeleteDialog,
		showStartConfirm,
		showStopConfirm,
		confirmStartCrontab,
		confirmStopCrontab,
		confirmDeleteCrontab,
		handleBackupPathClick,
		handleAddCrontab,
	} from './useController';

	const tabData = ['运行中', '已停止'];

	const crontabList = ref();

	const virtualListChange = (vList) => {
		crontabList.value = vList;
	};

	const queryList = async (page, pageSize) => {
		try {
			const res = await getCrontabList(page, pageSize);
			crontabPaging.value.complete(res);
			crontabPaging.value.updateVirtualListRender();
		} catch (error) {
			crontabPaging.value.complete([]);
			console.error(error);
		}
	};

	const reload = (reloadType) => {
		if (reloadType === 'complete') {
			pageContainer.value.notify.success('刷新成功');
		}
	};

	const onTabClick = (index) => {
		currentTab.value = index;
		crontabPaging.value.reload();
	};

	// 获取任务类型名称
	const getTaskTypeName = (type) => {
		const typeMap = {
			0: '系统任务',
			1: '备份任务',
			2: '日志切割',
			3: '释放内存',
			4: '同步时间',
			5: '重启面板',
			6: '重启系统',
			7: '切割日志',
			8: '备份网站',
			9: '备份数据库',
		};
		return typeMap[type] || '自定义任务';
	};

	// 获取任务类型显示文本
	const getTaskTypeDisplay = (item) => {
		if (item?.type_name) {
			return item.type_name;
		} else if (item?.type) {
			return getTaskTypeName(item.type);
		}
		return '--';
	};

	// 获取备份信息
	const getBackupInfo = (item) => {
		const backArr = ['site', 'database', 'enterpriseBack', 'path', 'mysql_increment_backup'];
		if (backArr.includes(item.sType) && item.save) {
			return {
				show: true,
				text: `${item.save}份`,
			};
		} else if (item.sType === 'logs' || item.sType === 'special_log') {
			return {
				show: true,
				text: item.save ? `${item.save}份` : '--',
			};
		}
		return { show: true, text: '--' };
	};

	// 获取备份路径
	const getBackupPath = (item) => {
		const backTable = ['site', 'database', 'path'];
		if (backTable.includes(item.sType) && item.db_backup_path) {
			return item.db_backup_path;
		}
		return '--';
	};

	// 获取备份到信息
	const getBackupTo = (item) => {
		if (item.sType === 'mysql_increment_backup' || item.sType === 'enterpriseBackup') {
			if (item.backupTo) {
				const data = item.backupTo.split('|');
				let str = '';
				data.forEach((backupItem) => {
					const name = cloudServiceMapping[backupItem];
					if (name) {
						str += name + ',';
					}
				});
				return str.slice(0, -1) || '--';
			}
		}
		// 使用接口返回的备份到数据
		if (item.sType === 'toShell') {
			return '--';
		}
		const name = cloudServiceMapping[item.backupTo];
		return name || '--';
	};

	// 获取执行结果文本
	const getResultText = (item) => {
		if (item.result === undefined || item.result === null) {
			return '--';
		}
		return item.result ? '正常' : '异常';
	};

	// 获取执行结果样式类
	const getResultClass = (item) => {
		if (item.result === undefined || item.result === null) {
			return '';
		}
		return item.result ? 'result-success' : 'result-error';
	};

	// 云服务映射
	const cloudServiceMapping = {
		localhost: '服务器磁盘',
		alioss: '阿里云OSS',
		txcos: '腾讯云COS',
		qiniu: '七牛云存储',
		obs: '华为云OBS',
		bos: '百度云存储',
		ftp: 'FTP存储空间',
		upyun: '又拍云存储',
		tianyiyun: '天翼云存储',
		webdav: 'WebDav存储',
		minio: 'MinIO存储',
		dogecloud: '多吉云COS',
		aws_s3: '亚马逊S3云存储',
		gdrive: '谷歌云网盘存储',
		msonedrive: '微软OneDrive存储',
		gcloud_storage: '谷歌云存储',
		jdcloud: '京东云存储',
	};

	onMounted(() => {
		// 初始化
	});

	onShow(() => {
		// 页面显示时刷新列表
		if (crontabPaging.value) {
			crontabPaging.value.reload();
		}
	});

	onBackPress(() => {
		// 如果有确认对话框显示，先关闭对话框
		if (showStartDialog.value) {
			showStartDialog.value = false;
			return true;
		}
		if (showStopDialog.value) {
			showStopDialog.value = false;
			return true;
		}
		if (showDeleteDialog.value) {
			showDeleteDialog.value = false;
			return true;
		}
		// 如果有长按菜单显示，先关闭菜单
		if (showContextMenu.value) {
			hideContextMenu();
			return true;
		}
		return false;
	});

	onUnload(() => {
		currentTab.value = 0;
	});

	// 在页面挂载时获取菜单高度
	onMounted(() => {
		measureMenuHeight();
	});

	// 监听菜单显示状态，防止页面滚动
	watch(showContextMenu, (val) => {
		if (val) {
			// 菜单显示时，禁用页面滚动
			uni.pageScrollTo({
				scrollTop: 0,
				duration: 0,
			});

			// 锁定列表滚动
			if (crontabPaging.value) {
				crontabPaging.value.lockScroll && crontabPaging.value.lockScroll(true);
			}
		} else {
			// 恢复列表滚动
			if (crontabPaging.value) {
				crontabPaging.value.lockScroll && crontabPaging.value.lockScroll(false);
			}
		}
	});
</script>

<style lang="scss" scoped>
	/* 计划任务项样式 */
	.crontab-item-container {
		padding: 30rpx;
		border-radius: 14rpx;
		border: 1rpx solid rgba(255, 255, 255, 0.3);
		box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.15);
		transition: all 0.2s ease;
	}

	.crontab-item-container:active {
		background-color: rgba(var(--bg-color-secondary-rgb), 0.4);
		transform: scale(0.98);
	}

	/* 主要信息区域 */
	.crontab-main-info {
		display: flex;
		align-items: center;
		justify-content: space-between;
		margin-bottom: 16rpx;
		padding-bottom: 12rpx;
		border-bottom: 1px solid var(--border-color);
	}

	.crontab-name {
		font-size: 30rpx;
		font-weight: bold;
		max-width: 85%;
		word-wrap: break-word;
		word-break: break-all;
		white-space: normal;
		line-height: 1.4;
	}

	.delete-confirm-text {
		white-space: normal;
		word-wrap: break-word;
		word-break: break-all;
		line-height: 1.5;
		text-align: center;
		padding: 20rpx;
		max-width: 100%;
	}

	.crontab-status {
		display: flex;
		align-items: center;
		gap: 8rpx;
	}

	.status-tag {
		padding: 4rpx 12rpx;
		border-radius: 20rpx;
		font-size: 20rpx;
		font-weight: 500;
	}

	.result-tag {
		&.result-success {
			background-color: rgba(39, 174, 96, 0.15);
			color: #27ae60;
		}
		&.result-error {
			background-color: rgba(231, 76, 60, 0.15);
			color: #e74c3c;
		}
	}

	.result-success {
		background-color: rgba(39, 174, 96, 0.15);
		color: #27ae60 !important;
	}

	.result-error {
		background-color: rgba(231, 76, 60, 0.15);
		color: #e74c3c !important;
	}

	.run-tag {
		&.run-active {
			background-color: rgba(39, 174, 96, 0.15);
			color: #27ae60;
		}
		&.run-stopped {
			background-color: rgba(231, 76, 60, 0.15);
			color: #e74c3c;
		}
	}

	/* 详细信息区域 */
	.crontab-extra-info {
		display: flex;
		justify-content: space-between;
		margin-bottom: 12rpx;
	}

	.crontab-cycle,
	.crontab-type {
		display: flex;
		align-items: center;
	}

	/* 底部信息区域 */
	.crontab-bottom-info {
		display: flex;
		justify-content: space-between;
		margin-bottom: 8rpx;
	}

	.crontab-backup,
	.crontab-backup-to,
	.crontab-time,
	.crontab-path,
	.crontab-result {
		display: flex;
		align-items: center;
	}

	/* 通用信息样式 */
	.info-label {
		font-size: 24rpx;
		color: var(--text-color-tertiary, #999);
		margin-right: 8rpx;
		flex-shrink: 0;
	}

	.info-value {
		font-size: 24rpx;
		color: var(--text-color-secondary, #666);
		background-color: rgba(0, 0, 0, 0.03);
		padding: 4rpx 12rpx;
		border-radius: 20rpx;
	}

	.backup-value {
		background-color: rgba(0, 0, 0, 0.03);
		color: #20a50a;
	}

	.path-value {
		background-color: rgba(0, 0, 0, 0.03);
		color: #20a50a;
		flex: 1;
		white-space: nowrap;
		overflow: hidden;
		text-overflow: ellipsis;
		margin-left: 8rpx;
	}

	.path-clickable {
		cursor: pointer;
		text-decoration: underline;
		transition: opacity 0.2s ease;
	}

	.path-clickable:active {
		opacity: 0.7;
	}

	/* 模糊遮罩 */
	.blur-overlay {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: rgba(255, 255, 255, 0.3);
		backdrop-filter: blur(3px);
		-webkit-backdrop-filter: blur(3px);
		z-index: 15;
		pointer-events: auto;
		touch-action: none; /* 禁止所有触摸操作 */
		user-select: none; /* 禁止选择 */
	}

	/* 上下文菜单 */
	.context-menu {
		position: fixed;
		z-index: 30;
		background: #fff;
		border-radius: 14rpx;
		box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.25);
		padding: 10rpx 0;
		transform: translate(-50%, 0);
		min-width: 340rpx;
		backdrop-filter: blur(20px);
		-webkit-backdrop-filter: blur(20px);

		&.menu-top {
			animation: fadeInTop 0.2s ease;
			transform: translate(-50%, 0);
		}

		&.menu-bottom {
			animation: fadeInBottom 0.2s ease;
			transform: translate(-50%, 0);
		}

		/* 菜单位于上方但紧贴克隆项顶部 */
		&.menu-position-bottom {
			transform: translate(-50%, 0);
		}
	}

	/* 菜单动画 */
	@keyframes fadeInTop {
		from {
			opacity: 0;
			transform: translate(-50%, -10px);
		}
		to {
			opacity: 1;
			transform: translate(-50%, 0);
		}
	}

	@keyframes fadeInBottom {
		from {
			opacity: 0;
			transform: translate(-50%, 10px);
		}
		to {
			opacity: 1;
			transform: translate(-50%, 0);
		}
	}

	.menu-item {
		display: flex;
		align-items: center;
		padding: 24rpx 30rpx;

		&:active {
			background-color: rgba(0, 0, 0, 0.05);
		}
	}

	.menu-text {
		margin-left: 16rpx;
		font-size: 28rpx;
		color: #333;
	}

	.menu-delete {
		opacity: 0.9;
	}

	.menu-text-delete {
		color: #ff3b30;
	}

	.menu-divider {
		height: 1rpx;
		background-color: rgba(0, 0, 0, 0.1);
		margin: 0 10rpx;
	}

	/* 固定克隆项容器 */
	.fixed-clone-container {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		z-index: 25;
		pointer-events: none;
	}

	/* 克隆项样式 */
	.item-clone-wrapper {
		position: absolute;
		pointer-events: none; /* 阻止触摸事件 */
	}

	/* 克隆项特殊样式 */
	.crontab-item-clone {
		padding: 30rpx;
		border-radius: 14rpx;
		transform: scale(1.02);
		box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.2);
		pointer-events: none; /* 确保克隆项不响应触摸事件 */
	}

	/* 添加按钮样式 */
	.add-button {
		position: fixed;
		bottom: 120rpx;
		right: 40rpx;
		width: 100rpx;
		height: 100rpx;
		background: #20a50a;
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
		box-shadow: 0 8rpx 24rpx rgba(32, 165, 58, 0.4);
		z-index: 20;
		transition: all 0.3s ease;
	}

	.add-button:active {
		transform: scale(0.95);
		box-shadow: 0 4rpx 12rpx rgba(32, 165, 58, 0.3);
	}
</style>
