<template>
  <page-container ref="pageContainer" :title="$t('checkAuth.title')" :is-back="false" :is-show-nav="false" bgColorPage="transparent" class="checkAuth-page-container">
		<custom-nav :is-remove-status-bar="true" :is-back="false" bg-color="transparent" class="pt-40 auth-page-nav">
			<template #left>
				<image
					src="@/static/login/home-logo.png"
					mode="scaleToFill"
					class="w-150 h-80 mt-40 mr-20"
				/>
			</template>
			<template #right>
				<view class="mt-40 mx-15" @click="startScan">
					<uv-icon name="plus-circle" color="var(--text-color-tertiary)" size="32"></uv-icon>
				</view>
			</template>
			<template #nav-title>
				<uv-input
					v-model="searchText"
					placeholder="请输入服务器或者IP地址"
					prefixIcon="search"
					prefixIconStyle="font-size: 22px;color: #909399"
					shape="circle"
					border="surround"
					:customStyle="{ backgroundColor: '#fff', height: '60rpx' }"
					class="mt-40"
				></uv-input>
			</template>
		</custom-nav>
    <!-- 背景模糊遮罩 -->
    <view class="blur-overlay" v-if="showContextMenu" @click="hideContextMenu"></view>

    <view>
      <view class="auth-list-container" v-show="filteredAccountList.length !== 0">
        <view
          class="auth-item"
          v-for="(account, index) in filteredAccountList"
          :key="index"
          @longpress="showFloatingMenu(account, index, $event)"
        >
          <!-- 长按激活时显示的克隆项（不受模糊影响） -->
          <view class="item-clone-wrapper" v-if="showContextMenu && activeIndex === index">
            <view class="auth-item-clone">
              <view class="auth-header">
                <text class="auth-title font-bold">{{ account.name }}</text>
                <text class="auth-subtitle">IP：{{ account.issuer }}</text>
              </view>
              <view class="auth-code-container">
                <text class="auth-code" :class="{ 'auth-code-expiring': timeLeft <= 10 }">{{
                  currentCode[index]
                }}</text>
                <view class="countdown-container">
                  <canvas :canvas-id="'countdown-clone-' + index" class="countdown-canvas"></canvas>
                  <text class="countdown-text">{{ timeLeft }}s</text>
                </view>
              </view>
            </view>
          </view>

          <view class="auth-header" :class="{ hidden: showContextMenu && activeIndex === index }">
            <text class="auth-title font-bold">{{ account.name }}</text>
            <text class="auth-subtitle">IP：{{ account.issuer }}</text>
          </view>
          <view class="auth-code-container" :class="{ hidden: showContextMenu && activeIndex === index }">
            <text class="auth-code" :class="{ 'auth-code-expiring': timeLeft <= 10 }">{{ currentCode[index] }}</text>
            <view class="countdown-container">
              <canvas :canvas-id="'countdown-' + index" class="countdown-canvas"></canvas>
              <text class="countdown-text">{{ timeLeft }}s</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 悬浮上下文菜单 -->
    <view class="context-menu" v-if="showContextMenu" :style="menuPosition" :class="menuPosition.class">
      <view class="menu-item" @click="copyCode">
        <uni-icons type="paperclip" size="20" color="var(--text-color-primary)"></uni-icons>
        <text class="menu-text">{{ $t('checkAuth.copyCode') }}</text>
      </view>
      <view class="menu-divider"></view>
      <view class="menu-item" @click="showRenameDialog = true">
        <uni-icons type="compose" size="20" color="var(--text-color-primary)"></uni-icons>
        <text class="menu-text">{{ $t('checkAuth.rename') }}</text>
      </view>
      <view class="menu-divider"></view>
      <view class="menu-item menu-delete" @click="deleteModel = true">
        <uni-icons type="trash" size="20" color="#FF3B30"></uni-icons>
        <text class="menu-text menu-text-delete">{{ $t('checkAuth.delete') }}</text>
      </view>
    </view>

    <view class="empty-container" v-show="accountList.length === 0">
      <view class="not-server">
        <view class="tips-container">
					<image
						src="@/static/login/home-logo.png"
						mode="scaleToFill"
						class="w-300 h-160 mt-40 mb-40 mx-auto"
					/>
          <text class="tips-text text-tertiary mb-20">{{ $t('checkAuth.emptyTip') }}</text>
          <!-- <text class="server-tips" @click="handleNavigate">{{ $t('checkAuth.checkTutorial') }}</text> -->
        </view>
        <!-- <view class="security-tip">{{ $t('checkAuth.securityTip') }}</view> -->
      </view>
      <!-- <bt-button :customStyle="{ padding: '10rpx 0' }" style="width: 320rpx" @click="startScan" plain>
        <template #default>
          <view class="scan-button">
            <uni-icons type="scan" color="20a50a" size="22"></uni-icons>
            <text>{{ $t('checkAuth.scanToAdd') }}</text>
          </view>
        </template>
      </bt-button> -->
    </view>
    <!-- <view class="tips-footer">
      <view class="tips-item">{{ $t('checkAuth.supportTip') }}</view>
      <view class="tips-item">{{ $t('checkAuth.longPressTip') }}</view>
    </view> -->
    <CustomDialog
      contentHeight="100rpx"
      v-model="showRenameDialog"
      :title="$t('checkAuth.rename')"
      :confirmText="$t('checkAuth.modify')"
      @confirm="confirmRename"
      @cancel="renameRemark = ''"
    >
      <view class="text-secondary flex justify-center items-center h-full">
        <uv-input v-model="renameRemark" :placeholder="activeAccount?.name || $t('checkAuth.inputName')" />
      </view>
    </CustomDialog>
    <CustomDialog
      v-model="deleteModel"
      contentHeight="280rpx"
      :title="$t('checkAuth.deleteConfirm')"
      @close="deleteModel = false"
      @confirm="deleteAccount"
      :confirmStyle="{
        backgroundColor: '#FF3B30',
      }"
    >
      <view class="delete-dialog">
        <text>{{ $t('checkAuth.deleteWarning', { issuer: activeAccount?.issuer }) }}</text>
      </view>
    </CustomDialog>
		<CustomDialog
      contentHeight="400rpx"
      v-model="showGuideModel"
			:showCancel="false"
      :confirmText="guideStep === guideData.length - 1 ? '完成' : '下一步'"
      @confirm="guideHandle"
			class="guide-dialog"
			:showType="'top'"
			:guideTheme="true"
    >
			<view class="guide-container">
				<view class="flex flex-row items-flex-start guide-header mb-12">
					<view class="logo">
						<image
							src="@/static/novice/guide-logo.png"
							mode="scaleToFill"
							class="w-92 h-92"
						/>
					</view>
					<view class="title text-30 flex flex-row items-flex-start">
						<view class="lh-1.5">{{ guideStep + 1 }}.</view>
						<view class="lh-1.5">{{ guideData[guideStep].title }}</view>
					</view>
				</view>
				<view class="guide-body">
					<image
						:src="guideData[guideStep].image"
						mode="scaleToFill"
					/>
				</view>
			</view>
    </CustomDialog>
  </page-container>
</template>

<script setup>
	import CustomNav from '@/components/customNav/index.vue';
  import PageContainer from '@/components/PageContainer/index.vue';
  import CustomDialog from '@/components/CustomDialog/index.vue';
  import BtButton from '@/components/BtButton/index.vue';
  import useController from './useController';
  import { onMounted, onBeforeUnmount, watch, ref } from 'vue';
  import { onBackPress } from '@dcloudio/uni-app';
  import { $t } from '@/locale/index.js';

  // 引入控制器逻辑
  const {
    accountList,
    currentCode,
    timeLeft,
    showFeedback,
    feedbackIndex,
    showContextMenu,
    activeIndex,
    activeAccount,
    menuPosition,
    showFloatingMenu,
    hideContextMenu,
    copyCode,
    deleteAccount,
    startScan,
    deleteModel,
    messageInstance,
    renameRemark,
    confirmRename,
    showRenameDialog,
		pageContainer,
		searchText,
    filteredAccountList,
  } = useController();
	const showGuideModel = ref(false)
	const guideStep = ref(0) // 当前步骤索引
	// 引导教程的数据
	const guideData = [
		{ title: '打开宝塔面板的设置，点击安全设置找到动态口令认证打开宝塔面板的设置，点击安全设置找到动态口令认证', image: '/static/novice/notive1.png' },
		{ title: '使用堡塔APP进行扫码绑定。添加口令后，下次登陆需要输入动态口令进行验证。使用堡塔APP进行扫码绑定。添加口令后，下次登陆需要输入动态口令进行验证。', image: '/static/novice/notive2.png' },
	]
  const handleNavigate = () => {
    uni.navigateTo({
      url: '/pages/novice/novice?type=auth',
      animationType: 'zoom-fade-out',
    });
  };
	const guideHandle = () => {
		if (guideStep.value < guideData.length - 1) {
			guideStep.value++
		} else {
			showGuideModel.value = false
		}
	}
  // 监听菜单显示状态，动态添加body样式
  watch(showContextMenu, (val) => {
    // 使用uni-app提供的接口禁用页面滑动
    if (val) {
      // 菜单显示时，禁用页面滚动
      // 使用uni.pageScrollTo阻止页面滚动
      uni.pageScrollTo({
        scrollTop: 0,
        duration: 0,
      });
      // 也可以通过uni.createSelectorQuery禁用页面滑动
      uni
        .createSelectorQuery()
        .selectViewport()
        .fields(
          {
            scrollOffset: true,
          },
          () => {
            // 通过空回调固定滚动位置
          },
        )
        .exec();
    }
  });
	onMounted(() => {
		if (!accountList.value.length) {
			showGuideModel.value = true;
		}
	});
  onBackPress(() => {
    if (showContextMenu.value) {
      hideContextMenu();
      return true;
    }
    return false;
  });
</script>

<style lang="scss" scoped>
	// 设置层次避免标题栏没遮罩的情况
	.auth-page-nav {
		position: relative;
		z-index: 10;
	}
		/* 采用伪类实现渐变背景 */
	.checkAuth-page-container::before{
		content: '';
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		background: linear-gradient(180.00deg, #e2ecee, #fafafa 99%);
		height: 300rpx;
	}
  .scan-icon {
    color: var(--text-color-secondary) !important;
    margin-right: 20rpx;
  }

  .scan-button {
    min-height: 76rpx;
    line-height: 1.2;
    text-align: center;
    color: var(--primary-color);
    font-size: 30rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    &.text {
      margin-left: 10rpx;
    }
  }

  .empty-container {
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  .not-server {
    width: 100%;
    align-items: center;
    justify-content: center;
    height: 30vh;
    color: var(--text-color-secondary);
    font-size: 30rpx;
    display: flex;
    flex-direction: column;
  }

  .tips-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    flex-wrap: wrap;
    justify-content: center;
    width: 100%;
    padding: 0 24rpx;
  }

  .tips-text {
    text-align: center;
  }

  .server-tips {
    padding-left: 12rpx;
    color: var(--primary-color);
    text-decoration: underline;
  }

  .security-tip {
    margin-top: 20rpx;
    text-align: center;
    padding: 0 24rpx;
    line-height: 1.5;
  }

  .auth-list-container {
    padding: 20rpx;
    position: relative;
  }

  .auth-item {
    margin-bottom: 20rpx;
    padding: 24rpx;
    border-radius: 32rpx;
    background-color: var(--bg-color);
    box-shadow: var(--box-shadow);
    position: relative;
    overflow: hidden;
  }

  // 克隆项，用于在模糊背景上显示
  .item-clone-wrapper {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 20;
    pointer-events: none; /* 阻止触摸事件 */
  }

  .auth-item-clone {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: var(--bg-color);
    border-radius: 32rpx;
    padding: 24rpx;
    box-shadow: var(--box-shadow);
    pointer-events: none; /* 确保克隆项也不响应触摸事件 */
  }

  .hidden {
    visibility: hidden;
  }

  .auth-header {
    display: flex;
    flex-direction: column;
    margin-bottom: 16rpx;
  }

  .auth-title {
    font-size: 32rpx;
    color: var(--text-color-primary);
    margin-bottom: 6rpx;
  }

  .auth-subtitle {
    font-size: 24rpx;
    color: var(--text-color-secondary);
  }

  .auth-code-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .auth-code {
    font-size: 42rpx;
    font-weight: 700;
    color: #20a50a;
    font-family:
      'SF Pro Text',
      -apple-system,
      BlinkMacSystemFont,
      'Segoe UI',
      Roboto,
      sans-serif;
    letter-spacing: 2rpx;
    transition: color 0.3s ease;
  }

  .auth-code-expiring {
    color: #ff3b30;
    animation: pulse 1s infinite;
  }

  .countdown-container {
    position: relative;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .countdown-canvas {
    width: 32px;
    height: 32px;
    position: absolute;
    top: 0;
    left: 0;
  }

  .countdown-text {
    font-size: 9px;
    color: var(--text-color-secondary);
    font-weight: 500;
    z-index: 1;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 100%;
    text-align: center;
    line-height: 1;
  }

  @keyframes pulse {
    0% {
      opacity: 1;
    }
    50% {
      opacity: 0.8;
    }
    100% {
      opacity: 1;
    }
  }

  @keyframes slideUp {
    from {
      transform: translateY(100%);
    }
    to {
      transform: translateY(0);
    }
  }

  /* 模糊遮罩 */
  .blur-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(3px);
    -webkit-backdrop-filter: blur(3px);
    z-index: 15;
    pointer-events: auto;
    overflow: hidden;
    height: 100%;
    touch-action: none; /* 禁止所有触摸操作 */
    user-select: none; /* 禁止选择 */
  }

  /* 上下文菜单 */
  .context-menu {
    position: fixed;
    z-index: 100;
    background: var(--bg-color);
    border-radius: 14rpx;
    box-shadow: var(--box-shadow);
    padding: 10rpx 0;
    transform: translate(-50%, 0); /* 不再向上偏移 */
    min-width: 240rpx;
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    animation: fadeInBottom 0.2s ease;

    &.menu-top {
      animation: fadeInTop 0.2s ease;
    }

    &.menu-bottom {
      animation: fadeInBottom 0.2s ease;
    }
  }

  .menu-item {
    display: flex;
    align-items: center;
    padding: 24rpx 30rpx;

    &:active {
      background-color: rgba(0, 0, 0, 0.05);
    }
  }

  .menu-text {
    margin-left: 20rpx;
    font-size: 28rpx;
    color: var(--text-color-primary);
    font-weight: 400;
  }

  .menu-delete {
    opacity: 0.9;
  }

  .menu-text-delete {
    color: #ff3b30;
  }

  .menu-divider {
    height: 1rpx;
    background-color: rgba(0, 0, 0, 0.1);
    margin: 0 10rpx;
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translate(-50%, -10px);
    }
    to {
      opacity: 1;
      transform: translate(-50%, 0);
    }
  }

  @keyframes fadeInTop {
    from {
      opacity: 0;
      transform: translate(-50%, 10px);
    }
    to {
      opacity: 1;
      transform: translate(-50%, 0);
    }
  }

  @keyframes fadeInBottom {
    from {
      opacity: 0;
      transform: translate(-50%, -10px);
    }
    to {
      opacity: 1;
      transform: translate(-50%, 0);
    }
  }

  .delete-dialog {
    padding: 20rpx;
    color: var(--text-color-secondary);

    &.text {
      font-size: 28rpx;
      line-height: 1.5;
    }
  }

  .tips-footer {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding-top: 50rpx;
    padding-bottom: 30rpx;
  }

  .tips-item {
    margin: 6rpx 0;
    color: var(--text-color-secondary);
    font-size: 26rpx;
    text-align: center;
  }
</style>
