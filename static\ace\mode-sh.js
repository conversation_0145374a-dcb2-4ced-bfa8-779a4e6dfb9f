ace.define(
	'ace/mode/sh_highlight_rules',
	['require', 'exports', 'module', 'ace/lib/oop', 'ace/mode/text_highlight_rules'],
	function (e, t, n) {
		'use strict'
		var r = e('../lib/oop'),
			i = e('./text_highlight_rules').TextHighlightRules,
			s = (t.reservedKeywords =
				'!|{|}|case|do|done|elif|else|esac|fi|for|if|in|then|until|while|&|;|export|local|read|typeset|unset|elif|select|set|function|declare|readonly'),
			o = (t.languageConstructs =
				'[|]|alias|bg|bind|break|builtin|cd|command|compgen|complete|continue|dirs|disown|echo|enable|eval|exec|exit|fc|fg|getopts|hash|help|history|jobs|kill|let|logout|popd|printf|pushd|pwd|return|set|shift|shopt|source|suspend|test|times|trap|type|ulimit|umask|unalias|wait'),
			u = function () {
				var e = this.createKeywordMapper(
						{ keyword: s, 'support.function.builtin': o, 'invalid.deprecated': 'debugger' },
						'identifier'
					),
					t = '(?:(?:[1-9]\\d*)|(?:0))',
					n = '(?:\\.\\d+)',
					r = '(?:\\d+)',
					i = '(?:(?:' + r + '?' + n + ')|(?:' + r + '\\.))',
					u = '(?:(?:' + i + '|' + r + ')' + ')',
					a = '(?:' + u + '|' + i + ')',
					f = '(?:&' + r + ')',
					l = '[a-zA-Z_][a-zA-Z0-9_]*',
					c = '(?:' + l + '(?==))',
					h = '(?:\\$(?:SHLVL|\\$|\\!|\\?))',
					p = '(?:' + l + '\\s*\\(\\))'
				;(this.$rules = {
					start: [
						{ token: 'constant', regex: /\\./ },
						{ token: ['text', 'comment'], regex: /(^|\s)(#.*)$/ },
						{
							token: 'string.start',
							regex: '"',
							push: [
								{ token: 'constant.language.escape', regex: /\\(?:[$`"\\]|$)/ },
								{ include: 'variables' },
								{ token: 'keyword.operator', regex: /`/ },
								{ token: 'string.end', regex: '"', next: 'pop' },
								{ defaultToken: 'string' },
							],
						},
						{
							token: 'string',
							regex: "\\$'",
							push: [
								{
									token: 'constant.language.escape',
									regex:
										/\\(?:[abeEfnrtv\\'"]|x[a-fA-F\d]{1,2}|u[a-fA-F\d]{4}([a-fA-F\d]{4})?|c.|\d{1,3})/,
								},
								{ token: 'string', regex: "'", next: 'pop' },
								{ defaultToken: 'string' },
							],
						},
						{ regex: '<<<', token: 'keyword.operator' },
						{
							stateName: 'heredoc',
							regex: '(<<-?)(\\s*)([\'"`]?)([\\w\\-]+)([\'"`]?)',
							onMatch: function (e, t, n) {
								var r = e[2] == '-' ? 'indentedHeredoc' : 'heredoc',
									i = e.split(this.splitRegex)
								return (
									n.push(r, i[4]),
									[
										{ type: 'constant', value: i[1] },
										{ type: 'text', value: i[2] },
										{ type: 'string', value: i[3] },
										{ type: 'support.class', value: i[4] },
										{ type: 'string', value: i[5] },
									]
								)
							},
							rules: {
								heredoc: [
									{
										onMatch: function (e, t, n) {
											return e === n[1]
												? (n.shift(), n.shift(), (this.next = n[0] || 'start'), 'support.class')
												: ((this.next = ''), 'string')
										},
										regex: '.*$',
										next: 'start',
									},
								],
								indentedHeredoc: [
									{ token: 'string', regex: '^	+' },
									{
										onMatch: function (e, t, n) {
											return e === n[1]
												? (n.shift(), n.shift(), (this.next = n[0] || 'start'), 'support.class')
												: ((this.next = ''), 'string')
										},
										regex: '.*$',
										next: 'start',
									},
								],
							},
						},
						{
							regex: '$',
							token: 'empty',
							next: function (e, t) {
								return t[0] === 'heredoc' || t[0] === 'indentedHeredoc' ? t[0] : e
							},
						},
						{
							token: ['keyword', 'text', 'text', 'text', 'variable'],
							regex: /(declare|local|readonly)(\s+)(?:(-[fixar]+)(\s+))?([a-zA-Z_][a-zA-Z0-9_]*\b)/,
						},
						{ token: 'variable.language', regex: h },
						{ token: 'variable', regex: c },
						{ include: 'variables' },
						{ token: 'support.function', regex: p },
						{ token: 'support.function', regex: f },
						{ token: 'string', start: "'", end: "'" },
						{ token: 'constant.numeric', regex: a },
						{ token: 'constant.numeric', regex: t + '\\b' },
						{ token: e, regex: '[a-zA-Z_][a-zA-Z0-9_]*\\b' },
						{
							token: 'keyword.operator',
							regex: '\\+|\\-|\\*|\\*\\*|\\/|\\/\\/|~|<|>|<=|=>|=|!=|[%&|`]',
						},
						{ token: 'punctuation.operator', regex: ';' },
						{ token: 'paren.lparen', regex: '[\\[\\(\\{]' },
						{ token: 'paren.rparen', regex: '[\\]]' },
						{ token: 'paren.rparen', regex: '[\\)\\}]', next: 'pop' },
					],
					variables: [
						{ token: 'variable', regex: /(\$)(\w+)/ },
						{ token: ['variable', 'paren.lparen'], regex: /(\$)(\()/, push: 'start' },
						{
							token: [
								'variable',
								'paren.lparen',
								'keyword.operator',
								'variable',
								'keyword.operator',
							],
							regex: /(\$)(\{)([#!]?)(\w+|[*@#?\-$!0_])(:[?+\-=]?|##?|%%?|,,?\/|\^\^?)?/,
							push: 'start',
						},
						{ token: 'variable', regex: /\$[*@#?\-$!0_]/ },
						{ token: ['variable', 'paren.lparen'], regex: /(\$)(\{)/, push: 'start' },
					],
				}),
					this.normalizeRules()
			}
		r.inherits(u, i), (t.ShHighlightRules = u)
	}
),
	ace.define(
		'ace/mode/folding/cstyle',
		['require', 'exports', 'module', 'ace/lib/oop', 'ace/range', 'ace/mode/folding/fold_mode'],
		function (e, t, n) {
			'use strict'
			var r = e('../../lib/oop'),
				i = e('../../range').Range,
				s = e('./fold_mode').FoldMode,
				o = (t.FoldMode = function (e) {
					e &&
						((this.foldingStartMarker = new RegExp(
							this.foldingStartMarker.source.replace(/\|[^|]*?$/, '|' + e.start)
						)),
						(this.foldingStopMarker = new RegExp(
							this.foldingStopMarker.source.replace(/\|[^|]*?$/, '|' + e.end)
						)))
				})
			r.inherits(o, s),
				function () {
					;(this.foldingStartMarker = /([\{\[\(])[^\}\]\)]*$|^\s*(\/\*)/),
						(this.foldingStopMarker = /^[^\[\{\(]*([\}\]\)])|^[\s\*]*(\*\/)/),
						(this.singleLineBlockCommentRe = /^\s*(\/\*).*\*\/\s*$/),
						(this.tripleStarBlockCommentRe = /^\s*(\/\*\*\*).*\*\/\s*$/),
						(this.startRegionRe = /^\s*(\/\*|\/\/)#?region\b/),
						(this._getFoldWidgetBase = this.getFoldWidget),
						(this.getFoldWidget = function (e, t, n) {
							var r = e.getLine(n)
							if (
								this.singleLineBlockCommentRe.test(r) &&
								!this.startRegionRe.test(r) &&
								!this.tripleStarBlockCommentRe.test(r)
							)
								return ''
							var i = this._getFoldWidgetBase(e, t, n)
							return !i && this.startRegionRe.test(r) ? 'start' : i
						}),
						(this.getFoldWidgetRange = function (e, t, n, r) {
							var i = e.getLine(n)
							if (this.startRegionRe.test(i)) return this.getCommentRegionBlock(e, i, n)
							var s = i.match(this.foldingStartMarker)
							if (s) {
								var o = s.index
								if (s[1]) return this.openingBracketBlock(e, s[1], n, o)
								var u = e.getCommentFoldRange(n, o + s[0].length, 1)
								return (
									u &&
										!u.isMultiLine() &&
										(r ? (u = this.getSectionRange(e, n)) : t != 'all' && (u = null)),
									u
								)
							}
							if (t === 'markbegin') return
							var s = i.match(this.foldingStopMarker)
							if (s) {
								var o = s.index + s[0].length
								return s[1]
									? this.closingBracketBlock(e, s[1], n, o)
									: e.getCommentFoldRange(n, o, -1)
							}
						}),
						(this.getSectionRange = function (e, t) {
							var n = e.getLine(t),
								r = n.search(/\S/),
								s = t,
								o = n.length
							t += 1
							var u = t,
								a = e.getLength()
							while (++t < a) {
								n = e.getLine(t)
								var f = n.search(/\S/)
								if (f === -1) continue
								if (r > f) break
								var l = this.getFoldWidgetRange(e, 'all', t)
								if (l) {
									if (l.start.row <= s) break
									if (l.isMultiLine()) t = l.end.row
									else if (r == f) break
								}
								u = t
							}
							return new i(s, o, u, e.getLine(u).length)
						}),
						(this.getCommentRegionBlock = function (e, t, n) {
							var r = t.search(/\s*$/),
								s = e.getLength(),
								o = n,
								u = /^\s*(?:\/\*|\/\/|--)#?(end)?region\b/,
								a = 1
							while (++n < s) {
								t = e.getLine(n)
								var f = u.exec(t)
								if (!f) continue
								f[1] ? a-- : a++
								if (!a) break
							}
							var l = n
							if (l > o) return new i(o, r, l, t.length)
						})
				}.call(o.prototype)
		}
	),
	ace.define(
		'ace/mode/sh',
		[
			'require',
			'exports',
			'module',
			'ace/lib/oop',
			'ace/mode/text',
			'ace/mode/sh_highlight_rules',
			'ace/range',
			'ace/mode/folding/cstyle',
			'ace/mode/behaviour/cstyle',
		],
		function (e, t, n) {
			'use strict'
			var r = e('../lib/oop'),
				i = e('./text').Mode,
				s = e('./sh_highlight_rules').ShHighlightRules,
				o = e('../range').Range,
				u = e('./folding/cstyle').FoldMode,
				a = e('./behaviour/cstyle').CstyleBehaviour,
				f = function () {
					;(this.HighlightRules = s), (this.foldingRules = new u()), (this.$behaviour = new a())
				}
			r.inherits(f, i),
				function () {
					;(this.lineCommentStart = '#'),
						(this.getNextLineIndent = function (e, t, n) {
							var r = this.$getIndent(t),
								i = this.getTokenizer().getLineTokens(t, e),
								s = i.tokens
							if (s.length && s[s.length - 1].type == 'comment') return r
							if (e == 'start') {
								var o = t.match(/^.*[\{\(\[:]\s*$/)
								o && (r += n)
							}
							return r
						})
					var e = { pass: 1, return: 1, raise: 1, break: 1, continue: 1 }
					;(this.checkOutdent = function (t, n, r) {
						if (r !== '\r\n' && r !== '\r' && r !== '\n') return !1
						var i = this.getTokenizer().getLineTokens(n.trim(), t).tokens
						if (!i) return !1
						do var s = i.pop()
						while (s && (s.type == 'comment' || (s.type == 'text' && s.value.match(/^\s+$/))))
						return s ? s.type == 'keyword' && e[s.value] : !1
					}),
						(this.autoOutdent = function (e, t, n) {
							n += 1
							var r = this.$getIndent(t.getLine(n)),
								i = t.getTabString()
							r.slice(-i.length) == i && t.remove(new o(n, r.length - i.length, n, r.length))
						}),
						(this.$id = 'ace/mode/sh'),
						(this.snippetFileId = 'ace/snippets/sh')
				}.call(f.prototype),
				(t.Mode = f)
		}
	)
;(function () {
	ace.require(['ace/mode/sh'], function (m) {
		if (typeof module == 'object' && typeof exports == 'object' && module) {
			module.exports = m
		}
	})
})()
