<template>
	<view class="_tab-box" :style="{fontSize: defaultConfig.fontSize + 'rpx', color: defaultConfig.color}">
		<scroll-view id="_scroll" :scroll-x="true" class="scroll-view-h" scroll-with-animation :scroll-left="slider.scrollLeft">
			<view class="_scroll-content">
				<view class="_tab-item-box" :class="[defaultConfig.itemWidth ? '_clamp' : '_flex']">
					<block v-for="(item, index) in tabList" :key="index" >
						<view 
							class="_item"
							:id="'_tab_'+index"
							:class="{ '_active': activeIndex === index }"
							:style="{color: activeIndex == index ? defaultConfig.activeColor : defaultConfig.color, 'width': defaultConfig.itemWidth ? defaultConfig.itemWidth + 'rpx' : ''}"
							@click="tabClick(index)">{{ item[defaultConfig.key] || item }}</view>
					</block>
				</view>
				<view class="_underline" :style="{
						transform: 'translateX(' + slider.left + 'px)',
						width: slider.width + 'px',
						height: defaultConfig.underLineHeight + 'rpx',
						backgroundColor: defaultConfig.underLineColor,
					}" />
			</view>
		</scroll-view>
	</view>
</template>

<script setup>
import { ref, reactive, computed, watch, nextTick, onMounted } from 'vue';

const props = defineProps({
	tabData: {
		type: Array,
		default: () => []
	},
	config: {
		type: Object,
		default: () => ({})
	}
});

const emit = defineEmits(['tabClick']);
const activeIndex = defineModel('value', {
	type: Number,
	default: 0
})

const tabList = ref([]);
const slider = reactive({
	left: 0,
	width: 0,
	scrollLeft: 0
});
const scorll = ref({});

const defaultConfig = reactive({
	// 要显示的key
	key: 'name',
	// 字体大小 rpx
	fontSize: 26,
	// 字体颜色
	color: 'var(--text-color-secondary)',
	// 激活字体颜色
	activeColor: 'var(--primary-color)',
	// item宽度 0为自动
	itemWidth: 0,
	// 下划线左右边距，文字宽度加边距 rpx
	underLinePadding: 10,
	// 下划线宽度 rpx  注意：设置了此值 underLinePadding 失效
	underLineWidth: 0,
	// 下划线高度 rpx
	underLineHeight: 4,
	// 下划线颜色
	underLineColor: 'var(--primary-color)',
});

const updateData = () => {
	let data = [];
	if (typeof(props.tabData[0]) === 'string') {
		props.tabData.forEach((item) => {
			data.push({
				name: item,
			});
		});
		defaultConfig.key = 'name';
	} else {
		data = JSON.parse(JSON.stringify(props.tabData));
	}
	
	tabList.value = data;
};

const updateConfig = () => {
	Object.assign(defaultConfig, props.config);
};

const calcScrollPosition = () => {
	const query = uni.createSelectorQuery();
	
	query.select('#_scroll').boundingClientRect((res) => {
		scorll.value = res;
		updateTabWidth();
	}).exec();
};

const updateTabWidth = (index = 0) => {
	let data = tabList.value;
	
	if (data.length === 0) return false;
	
	const query = uni.createSelectorQuery();
	
	query.select('#_tab_' + index).boundingClientRect((res) => {
		data[index]._slider = {
			width: res.width,
			left: res.left,
			scrollLeft: res.left - (data[index - 1] ? data[index - 1]._slider.width : 0),
		};
		
		if (activeIndex.value == index) {
			tabToIndex(activeIndex.value);
		}

		index++;
		if (data.length > index) {
			updateTabWidth(index);
		}
	}).exec();
};

const tabToIndex = (index) => {
	let _slider = tabList.value[index]._slider;
	
	let width = uni.upx2px(defaultConfig.underLineWidth);
	
	if (!width) {
		if (defaultConfig.itemWidth) {
			width = uni.upx2px(defaultConfig.itemWidth);
		} else {
			width = tabList.value[index][defaultConfig.key].length * uni.upx2px(defaultConfig.fontSize);
		}
		width += uni.upx2px(defaultConfig.underLinePadding) * 2;
	}
	
	let scorll_left = scorll.value.left || 0;
	
	slider.left = _slider.left - scorll_left + (_slider.width - width) / 2;
	slider.width = width;
	slider.scrollLeft = _slider.scrollLeft - scorll_left;
};

const tabClick = (index) => {
	activeIndex.value = index;
	tabToIndex(index);
	emit('tabClick', index);
};

// 监听 props 变化
watch(() => props.tabData, () => {
	updateData();
	setTimeout(() => {
		updateTabWidth();
	}, 0);
}, { deep: true });

watch(() => props.config, () => {
	updateConfig();
}, { deep: true });

watch(() => activeIndex.value, (value) => {
	tabToIndex(value);
}, { deep: true });

onMounted(() => {
	updateConfig();
	updateData();
	nextTick(() => {
		calcScrollPosition();
	});
});
</script>

<style lang="scss" scoped>
	._tab-box {
		width: 100%;
		display: flex;
		font-size: 26rpx;
		position: relative;
		height: 90rpx;
		line-height: 90rpx;
		z-index: 10;
		border-bottom: 2rpx solid var(--border-color);
		.scroll-view-h{
			white-space:nowrap;
			width: 100%;
			height: 100%;
			box-sizing: border-box;
			._scroll-content {
				width: 100%;
				height: 100%;
				position:relative;
				
				._tab-item-box {
					height: 100%;
					&._flex {
						display: flex;
						._item {
							flex: 1;
						}
					}
					&._clamp {
						._item {
							overflow:hidden;
							text-overflow:ellipsis;
							white-space:nowrap;
						}
					}
					
					
					._item { 
						height: 100%;
						display: inline-block;
						text-align: center;
						padding: 0 30rpx;
						position: relative;
						text-align: center;
						
						color: var(--text-color-secondary);
						&._active {
							color: var(--primary-color);
						}
					}
				}
				._underline {
					height: 4rpx;
					background-color: var(--primary-color);
					border-radius: 6rpx;
					transition: .5s;
					position: absolute;
					bottom: 0;
				}
			}
		}
	}
</style>
