<template>
	<view>
		<!-- 备份目录路径 -->
		<view class="form-group">
			<view class="form-label-row">
				<text>备份目录</text>
			</view>
			<uv-input
				:value="formData.sName"
				@input="updateField('sName', $event)"
				placeholder="请输入要备份的目录路径"
				border="surround"
				:customStyle="{ backgroundColor: 'var(--dialog-bg-color)', fontSize: '28rpx' }"
			>
				<template v-slot:suffix>
					<view class="path-select-button" @click="selectPath">
						<uv-icon name="folder" size="16" color="#ffffff"></uv-icon>
					</view>
				</template>
			</uv-input>
		</view>

		<!-- 备份到选择 -->
		<view class="form-group">
			<view class="form-label-row">
				<text>备份到</text>
			</view>
			<button class="region-select-button" @click="showBackupToPicker" :disabled="isEditMode">
				<text>{{ getBackupToLabel() || '请选择备份位置' }}</text>
				<uv-icon name="arrow-down" size="14" color="#666666"></uv-icon>
			</button>
		</view>

		<!-- 保留最新份数 -->
		<view class="form-group">
			<view class="form-label-row">
				<text>保留最新</text>
			</view>
			<view class="input-wrapper">
				<uv-input
					:value="formData.save"
					@input="updateField('save', $event)"
					placeholder="请输入保存备份的数量"
					type="number"
					border="surround"
					:customStyle="{ backgroundColor: 'var(--dialog-bg-color)', fontSize: '28rpx' }"
				/>
			</view>
		</view>

		<!-- 备份路径 -->
		<view class="form-group">
			<view class="form-label-row">
				<text>备份路径</text>
			</view>
			<view class="input-wrapper">
				<uv-input
					:value="formData.db_backup_path"
					@input="updateField('db_backup_path', $event)"
					placeholder="请输入备份路径"
					border="surround"
					:customStyle="{ backgroundColor: 'var(--dialog-bg-color)', fontSize: '28rpx' }"
				/>
			</view>
		</view>

		<!-- 排除目录 -->
		<view class="form-group">
			<view class="form-label-row">
				<text>排除目录</text>
			</view>
			<view class="textarea-wrapper">
				<uv-textarea
					:value="formData.sBody"
					@input="updateField('sBody', $event)"
					placeholder="每行一条规则,目录不能以/结尾，&#10; 示例：data/config.php &#10; static/upload &#10; *.log"
					height="200"
					:textStyle="{ fontSize: '28rpx', color: 'var(--text-color-primary)' }"
					:customStyle="{ backgroundColor: 'var(--dialog-bg-color)' }"
				/>
			</view>
		</view>

		<!-- 进程锁 -->
		<view class="form-group">
			<view class="form-row">
				<text>进程锁</text>
				<uv-switch :value="formData.flock" @change="updateField('flock', $event)" activeColor="#20a53a"></uv-switch>
			</view>
		</view>

		<!-- Picker组件 -->
		<uv-picker
			ref="backupToPicker"
			:columns="[backupToOptions]"
			keyName="label"
			@confirm="onBackupToConfirm"
		></uv-picker>
	</view>
</template>

<script setup>
	import { ref, onMounted, getCurrentInstance } from 'vue';
	import { getCrontabDataList } from '@/api/crontab';
	import { openFileSelector } from '@/stores/fileSelector.js';
	import { truncateText } from '../useController';

	const { proxy } = getCurrentInstance();

	const props = defineProps({
		formData: {
			type: Object,
			required: true
		},
		isEditMode: {
			type: Boolean,
			default: false
		}
	});

	const emit = defineEmits(['update:formData']);

	// 数据选项
	const backupToOptions = ref([{ label: '服务器磁盘', value: 'localhost' }]);

	// Picker引用
	const backupToPicker = ref(null);

	// 更新字段
	const updateField = (field, event) => {
		const value = event.detail?.value || event.target?.value || event;
		emit('update:formData', { [field]: value });
		
		// 如果是目录路径变化，更新任务名称
		if (field === 'sName') {
			emit('update:formData', { 
				[field]: value,
				name: `备份目录[ ${value || '未设置'} ]`
			});
		}
	};

	// 显示选择器
	const showBackupToPicker = () => {
		proxy.$refs.backupToPicker?.open();
	};

	// 获取显示标签
	const getBackupToLabel = () => {
		const option = backupToOptions.value.find(item => item.value === props.formData.backupTo);
		return option ? truncateText(option.label) : '';
	};

	// 确认选择
	const onBackupToConfirm = (e) => {
		const selectedValue = e.value[0].value;
		emit('update:formData', { backupTo: selectedValue });
	};

	// 选择路径
	const selectPath = () => {
		openFileSelector('folder', false, (selectedPaths) => {
			if (selectedPaths && selectedPaths.length > 0) {
				const selectedPath = selectedPaths[0];
				emit('update:formData', {
					sName: selectedPath,
					name: `备份目录[ ${selectedPath} ]`
				});
			}
		});
	};

	// 获取数据
	const loadData = async () => {
		try {
			const res = await getCrontabDataList({ type: 'sites' });
			
			// 设置备份位置选项
			backupToOptions.value = [{ label: '服务器磁盘', value: 'localhost' }].concat(
				res.orderOpt.map(item => ({
					label: item.name + (item.status ? '' : '[未配置]'),
					value: item.value,
					disabled: item.value !== 'localhost' && !item.status
				}))
			);

			// 初始化默认值
			if (!props.formData.sName) {
				emit('update:formData', { 
					sName: '/www/wwwroot',
					backupTo: 'localhost',
					save: '3',
					db_backup_path: '/www/backup/path',
					name: '备份目录[ /www/wwwroot ]'
				});
			}
		} catch (error) {
			console.error('加载数据失败:', error);
		}
	};

	onMounted(() => {
		loadData();
	});
</script>

<style lang="scss" scoped>
	@import './form-styles.scss';

	.form-row {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-top: 20rpx;

		text {
			font-size: 28rpx;
			font-weight: 500;
			color: var(--text-color-primary);
		}
	}

	.path-select-button {
		width: 60rpx;
		height: 60rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		background: #20a50a;
		border: none;
		border-radius: 8rpx;
		transition: all 0.3s ease;

		&:active {
			transform: scale(0.95);
		}
	}
</style>
