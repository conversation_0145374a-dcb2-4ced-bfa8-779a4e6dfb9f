<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>APProtocolBuffers-Info.plist</key>
		<data>
		Po//BA5tM1BE58borc33LNpqXyw=
		</data>
		<key>Headers/APDPBGeneratedMessage.h</key>
		<data>
		i6zxC5zpWt3b4gUm+7qep7/S7vY=
		</data>
		<key>Headers/APPBGeneratedMessage.h</key>
		<data>
		idCT/i5eR26qLCp66VlujdZ1aKQ=
		</data>
		<key>Headers/AbstractMessage.h</key>
		<data>
		e9a43rnJVFTtC9v81dAAqNlODA8=
		</data>
		<key>Headers/AbstractMessageBuilder.h</key>
		<data>
		uiRnlqBlat2XLbQHROCPlZT6/Qs=
		</data>
		<key>Headers/Bootstrap.h</key>
		<data>
		74ptHYnR4kvWVDyyLBkQhenh6BA=
		</data>
		<key>Headers/CodedInputStream.h</key>
		<data>
		Zzfj3j7angr1vMujZ9X1wZ53Khw=
		</data>
		<key>Headers/CodedOutputStream.h</key>
		<data>
		Jsq/YPuDDPCFanLTm2S18Xz0yMY=
		</data>
		<key>Headers/ExtensionField.h</key>
		<data>
		6FEm1vQYLHDaiWpOiDVNf6mxOqk=
		</data>
		<key>Headers/ExtensionRegistry.h</key>
		<data>
		lVzbZNZdp4IPXZGU4yIvHuH8vL8=
		</data>
		<key>Headers/ExternKVParam.h</key>
		<data>
		sgVT9XfEQdgVS7NArf18ATbRdG4=
		</data>
		<key>Headers/Field.h</key>
		<data>
		jYPn9NyAFkBm9N3rblGmw4IxumY=
		</data>
		<key>Headers/ForwardDeclarations.h</key>
		<data>
		XJ3sdrBJcLJDApPI+LWqWyUKp1w=
		</data>
		<key>Headers/GeneratedMessage.h</key>
		<data>
		CYD2O075S4h//FASb36i6VG7JvI=
		</data>
		<key>Headers/Message.h</key>
		<data>
		gohEthJGDzatwbaRaFbVqF7qP14=
		</data>
		<key>Headers/MessageBuilder.h</key>
		<data>
		uaHL1f7wPJkzk6d8VhZ48jtPAAI=
		</data>
		<key>Headers/MutableField.h</key>
		<data>
		RUL65TGggv0XxeHTzXIAd7kgXm0=
		</data>
		<key>Headers/PBArray.h</key>
		<data>
		WwOBqBIOSPE/SieygCbx0RoPER8=
		</data>
		<key>Headers/PBEntryStringString.h</key>
		<data>
		0sEWLzHczFdYJak7Sl6q9MDAr1o=
		</data>
		<key>Headers/PBMapStringString.h</key>
		<data>
		h+uNgef0s5f4Mz7CKwziGMZLlkg=
		</data>
		<key>Headers/ProtocolBuffers.h</key>
		<data>
		sIyBcXuvZgTshQQNuiTAeHwD4+0=
		</data>
		<key>Headers/RingBuffer.h</key>
		<data>
		zPOxAFboAkY8yZHOJ297rBHKr14=
		</data>
		<key>Headers/UnknownFieldSet.h</key>
		<data>
		d4+zpFG8PsR+KeEY3FCATwCPV34=
		</data>
		<key>Headers/UnknownFieldSetBuilder.h</key>
		<data>
		DGL4Djr0icM5QPtARyc0lVgEoZE=
		</data>
		<key>Headers/Utilities.h</key>
		<data>
		o4cXEB1NN/Y2SCAwzklcVQeAhN0=
		</data>
		<key>Headers/WireFormat.h</key>
		<data>
		WBmzYp0NiaJ/6wUXeMPwegHp39w=
		</data>
		<key>Info.plist</key>
		<data>
		ApjthHWVy+hyj/idm1rBi5D18OU=
		</data>
		<key>en.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			zmV6UqBSo6r1NOz798vd5O4zTBA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
	</dict>
	<key>files2</key>
	<dict>
		<key>APProtocolBuffers-Info.plist</key>
		<dict>
			<key>hash</key>
			<data>
			Po//BA5tM1BE58borc33LNpqXyw=
			</data>
			<key>hash2</key>
			<data>
			B2XyqSNoreUIQ8uDojzpys2o9qi8XuLbypPrkCR+l8w=
			</data>
		</dict>
		<key>Headers/APDPBGeneratedMessage.h</key>
		<dict>
			<key>hash</key>
			<data>
			i6zxC5zpWt3b4gUm+7qep7/S7vY=
			</data>
			<key>hash2</key>
			<data>
			xz63PFcmWl6mQAcb+/y1wDEH+FhEWpHFm3x3YSC3fuY=
			</data>
		</dict>
		<key>Headers/APPBGeneratedMessage.h</key>
		<dict>
			<key>hash</key>
			<data>
			idCT/i5eR26qLCp66VlujdZ1aKQ=
			</data>
			<key>hash2</key>
			<data>
			GwwYSX8Rq656kjv8nJOHe5+HAZ4PL2naJ7T1/nndWSs=
			</data>
		</dict>
		<key>Headers/AbstractMessage.h</key>
		<dict>
			<key>hash</key>
			<data>
			e9a43rnJVFTtC9v81dAAqNlODA8=
			</data>
			<key>hash2</key>
			<data>
			B1D9SYGPmU8zrjm8zlprhejQvw7gNx6io0jAZOzF5Z4=
			</data>
		</dict>
		<key>Headers/AbstractMessageBuilder.h</key>
		<dict>
			<key>hash</key>
			<data>
			uiRnlqBlat2XLbQHROCPlZT6/Qs=
			</data>
			<key>hash2</key>
			<data>
			/V7pYH9LFgEy3ifRDRhSHQd6Wct8BvLPhCddHp5uS80=
			</data>
		</dict>
		<key>Headers/Bootstrap.h</key>
		<dict>
			<key>hash</key>
			<data>
			74ptHYnR4kvWVDyyLBkQhenh6BA=
			</data>
			<key>hash2</key>
			<data>
			tM3HfR1prZVkCCvnKPNayvjYxWEoE13Nkljzj4i12FM=
			</data>
		</dict>
		<key>Headers/CodedInputStream.h</key>
		<dict>
			<key>hash</key>
			<data>
			Zzfj3j7angr1vMujZ9X1wZ53Khw=
			</data>
			<key>hash2</key>
			<data>
			nZkPzs+CmAujQb1RxlT/JlAr8odhEB69OEN3AUpHCN0=
			</data>
		</dict>
		<key>Headers/CodedOutputStream.h</key>
		<dict>
			<key>hash</key>
			<data>
			Jsq/YPuDDPCFanLTm2S18Xz0yMY=
			</data>
			<key>hash2</key>
			<data>
			E46hxH0Y/SP6rm0CK2fsqzL7yLUn9T4DXKMoaCmo6Oo=
			</data>
		</dict>
		<key>Headers/ExtensionField.h</key>
		<dict>
			<key>hash</key>
			<data>
			6FEm1vQYLHDaiWpOiDVNf6mxOqk=
			</data>
			<key>hash2</key>
			<data>
			h8Xq8lQK1UkYPv1LRopJG4VvnFii/ifWAqElEzi1E0g=
			</data>
		</dict>
		<key>Headers/ExtensionRegistry.h</key>
		<dict>
			<key>hash</key>
			<data>
			lVzbZNZdp4IPXZGU4yIvHuH8vL8=
			</data>
			<key>hash2</key>
			<data>
			G24b1bKtFSlTLqsFhI9jQYl2RkZMvdVAlmXGKp8ffX0=
			</data>
		</dict>
		<key>Headers/ExternKVParam.h</key>
		<dict>
			<key>hash</key>
			<data>
			sgVT9XfEQdgVS7NArf18ATbRdG4=
			</data>
			<key>hash2</key>
			<data>
			a/gb5wnGPiFomXE3JHxmA9l7Zc8gxcdBfilaheSDT/Y=
			</data>
		</dict>
		<key>Headers/Field.h</key>
		<dict>
			<key>hash</key>
			<data>
			jYPn9NyAFkBm9N3rblGmw4IxumY=
			</data>
			<key>hash2</key>
			<data>
			7oNAR8x+HCnMEeLv80VNtF3H5PX44FN3DSQgANubA3M=
			</data>
		</dict>
		<key>Headers/ForwardDeclarations.h</key>
		<dict>
			<key>hash</key>
			<data>
			XJ3sdrBJcLJDApPI+LWqWyUKp1w=
			</data>
			<key>hash2</key>
			<data>
			dcYqY+zruPjAiZtbo18hPR23p3WQqbT2T83t95IeQQk=
			</data>
		</dict>
		<key>Headers/GeneratedMessage.h</key>
		<dict>
			<key>hash</key>
			<data>
			CYD2O075S4h//FASb36i6VG7JvI=
			</data>
			<key>hash2</key>
			<data>
			tVpMNsAEWB1Xw6AhH3yuJXOL90IQPI18p3RPKGLTLnY=
			</data>
		</dict>
		<key>Headers/Message.h</key>
		<dict>
			<key>hash</key>
			<data>
			gohEthJGDzatwbaRaFbVqF7qP14=
			</data>
			<key>hash2</key>
			<data>
			+BkeNd7sXldN87RnMB/kpk2F4Ai4Ka0nIB5av1vHjTA=
			</data>
		</dict>
		<key>Headers/MessageBuilder.h</key>
		<dict>
			<key>hash</key>
			<data>
			uaHL1f7wPJkzk6d8VhZ48jtPAAI=
			</data>
			<key>hash2</key>
			<data>
			********************************+nxeJZasQ0w=
			</data>
		</dict>
		<key>Headers/MutableField.h</key>
		<dict>
			<key>hash</key>
			<data>
			RUL65TGggv0XxeHTzXIAd7kgXm0=
			</data>
			<key>hash2</key>
			<data>
			DEpIZXUxzJtqblU+6ZHRJBRs74Eys8VhKK0m6NmlL6g=
			</data>
		</dict>
		<key>Headers/PBArray.h</key>
		<dict>
			<key>hash</key>
			<data>
			WwOBqBIOSPE/SieygCbx0RoPER8=
			</data>
			<key>hash2</key>
			<data>
			09M9pVysVcydu6yPwCBSte5I7Ohh8eGzGIDfTded39A=
			</data>
		</dict>
		<key>Headers/PBEntryStringString.h</key>
		<dict>
			<key>hash</key>
			<data>
			0sEWLzHczFdYJak7Sl6q9MDAr1o=
			</data>
			<key>hash2</key>
			<data>
			brclCT70FyIpTqMPXCFbDCwOyLleIfL2IvW/FksqyjQ=
			</data>
		</dict>
		<key>Headers/PBMapStringString.h</key>
		<dict>
			<key>hash</key>
			<data>
			h+uNgef0s5f4Mz7CKwziGMZLlkg=
			</data>
			<key>hash2</key>
			<data>
			ZUcHuHdpEAXV7ipv4QGraTg+/aqK3d2tjV7zSqbi5tw=
			</data>
		</dict>
		<key>Headers/ProtocolBuffers.h</key>
		<dict>
			<key>hash</key>
			<data>
			sIyBcXuvZgTshQQNuiTAeHwD4+0=
			</data>
			<key>hash2</key>
			<data>
			i6Vdtf6+P21UXg/cHEkNCAFBOMfPP4ZG9n0c736Q/TI=
			</data>
		</dict>
		<key>Headers/RingBuffer.h</key>
		<dict>
			<key>hash</key>
			<data>
			zPOxAFboAkY8yZHOJ297rBHKr14=
			</data>
			<key>hash2</key>
			<data>
			QCw8PaT9BOoyaMcfi+sI4rYe0x0dB2pyWH4voaWXZCM=
			</data>
		</dict>
		<key>Headers/UnknownFieldSet.h</key>
		<dict>
			<key>hash</key>
			<data>
			d4+zpFG8PsR+KeEY3FCATwCPV34=
			</data>
			<key>hash2</key>
			<data>
			GXDUDcxdVoTNL36VW1asX5RuskPqJ20eOJE3t4Dz6Cc=
			</data>
		</dict>
		<key>Headers/UnknownFieldSetBuilder.h</key>
		<dict>
			<key>hash</key>
			<data>
			DGL4Djr0icM5QPtARyc0lVgEoZE=
			</data>
			<key>hash2</key>
			<data>
			9fZGzTyJkLsvCqE0Z7VXyySo2BKFNnEfNz1HKGVJcEM=
			</data>
		</dict>
		<key>Headers/Utilities.h</key>
		<dict>
			<key>hash</key>
			<data>
			o4cXEB1NN/Y2SCAwzklcVQeAhN0=
			</data>
			<key>hash2</key>
			<data>
			vIrYj3aCgpRHBttvKBKVHGR+wtOzDZfUwXxK4BESiwA=
			</data>
		</dict>
		<key>Headers/WireFormat.h</key>
		<dict>
			<key>hash</key>
			<data>
			WBmzYp0NiaJ/6wUXeMPwegHp39w=
			</data>
			<key>hash2</key>
			<data>
			H5rST+7GGcWDTGuOgxbtqwtaQk9sxhqPMuij42oC90E=
			</data>
		</dict>
		<key>en.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			zmV6UqBSo6r1NOz798vd5O4zTBA=
			</data>
			<key>hash2</key>
			<data>
			kmHsztpgjvF0JW5f3HdMHm49z1M0CcG8OT1JDQHHE/E=
			</data>
			<key>optional</key>
			<true/>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
