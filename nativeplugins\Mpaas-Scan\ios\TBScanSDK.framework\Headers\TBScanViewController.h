//
//  TBScanViewController.h
//  TBScanSDK
//
//  Created by <PERSON> on 15/7/24.
//  Copyright (c) 2015 Taobao.com. All rights reserved.
//

#import <UIKit/UIKit.h>
#import <Foundation/Foundation.h>
#import <TBDecodeSDK/tbItf.h>

#import <TBScanSDK/TBScanMacros.h>

extern NSString * const TBScanVCInitConfigMultipleQueueStartCamera;
extern NSString * const TBScanVCInitConfigSubClassDelegate;

@class TBScanResult;
@class TBScanPlugin;
@class AVCaptureVideoPreviewLayer;

enum{
    TorchState_On = 1,//闪光灯开
    TorchState_Off,//闪光灯关闭
    TorchState_Disable,//没有闪光灯（ipad,ipod）
};
typedef int TorchState;// 闪光灯状态

// 扫码SDK会弹出的UIAlertView的tag
enum{
    AlertType_LocalPhoto = 1688,
    AlertType_CameraDisable,
};
typedef int AlertType;

enum{
    ImageType_DidFind = 1000,
    ImageType_AIDetectedQR
};
typedef int ImageType;

@protocol TBScanViewControllerTorchDelegate <NSObject>

/**
 闪光灯提示
 黑暗环境:如果闪光灯暗，显示闪光灯按钮，提示打开闪光灯; 如果闪光灯亮，显示闪光灯按钮,提示关闭
 光线充足:如果闪光灯亮，显示闪光灯按钮，提示关闭; 如果闪光灯暗, 不显示
 @param yesOrNo 是否要展示手电筒
 @param yesOrNo 打开或关闭闪光灯
 */
- (void)showTorchView:(BOOL)yesOrNo shouldTurnOn:(BOOL)shouldTurnOn;

/**
 *
 * @discussion 闪光灯初始化、状态变更都会调用这个方法，可以在这里实现闪光灯的UI
 *
 * @param bState 闪光灯当前的状态，
 *
 */
-(void)setTorchState:(TorchState)bState;

@end

@protocol TBScanViewControllerDelegate <NSObject>

@required

/**
 *
 * @discussion 条码，快递单，二维码等扫描成功会回调这个函数
 *
 * @param result 扫描成功的结果，具体参见TBScanResult类
 *
 */
-(void)didFind:(NSArray<TBScanResult*>*)resultArray;

@optional

/**
 摄像头没有访问权限时会调用该方法
 */
-(void)cameraPermissionDenied;

/**
 *
 * @discussion 摄像头启动后的回调
 *
 */
-(void)cameraDidStart;


/**
 收到摄像头第一帧的回调
 */
-(void)frameDidReceived;

/**
 *
 * @discussion 摄像头启动失败
 *
 */
-(void)cameraStartFail;


/**
 因为收到内存警告，而释放摄像头
 */
-(void)releaseByMemoryWarning;

/**
 *
 * @discussion 识别本地相册图片失败时会调用该方法
 *
 */
-(void)scanPhotoFailed;

/////////////////////////// 新特性 ////////////////////////////
/**
 * bStableStatusMonitorEnable 为 YES 时会回调此方法
 * @discussion 机身稳定状态改变时的回调
 *
 * @param isStable YES 为变稳，NO 为变不稳，
 * @param scanFrameCount 第几帧
 *
 */
- (void)stableStatusChanged:(BOOL)isStable AtScanFrameCount:(NSInteger)scanFrameCount;

/**
 获取 AI 模型的路径，如果设置了 bAIQRImageDetectEnable 或 bAIQRCVDetectEnable 为 true, 会立即调用此方法
 */
- (NSURL *)mlmodelcURLForAIDetect;

/**
 AI 模型在第几帧探测到了二维码
*/
-(void)detectedQRByAIAtScanFrameCount:(NSInteger)frmaeCount;
///////////////////////////////埋点接口//////////////////////////////
/**
 *
 * @discussion 用接入方自己的埋点接口实现
 *
 */
-(void)userTrack:(NSString*)name;

/**
 *
 * @discussion 用接入方的埋点接口实现,可以上传数据
 *
 */
-(void)userTrack:(NSString*)name args:(NSDictionary*)data;


/**
 Alipay用于关键路径记录，目前主要用于研究主线程切换耗时
 */
-(void)alipayLog:(NSString*)content;

/**
 业务方处理扫描结果的机会
 
 @param > 真实识别结果
 @return 处理结果继续走流程
 */
-(NSArray<TBScanResult*>*)handleResultArray:(NSArray<TBScanResult*>*)resultArray;

/**
 如果 needSaveFrameASImageWhenDidFind 或 needSaveFrameASImageWhenAIDetectedQR 为ture ，这可以获取帧转化的图像
 */
-(void)handleImage:(UIImage *)image WithImageType:(ImageType)imageType;

///////////////////////////////iPad相关回调/////////////////////////
/**
 *
 * @discussion iPad识别本地相册图片失败时会调用该方法
 *
 */
-(void)scanPadPhotoFailed;

/**
 *  @discussion popover 消失通知
 */
-(void)dismissPopover;


////////////////////////////////定制

/**
 自定义界面
 */
-(void)buildContainerView:(UIView*)containerView;

/**
 自定义相册
 */
-(void)buildCustomAlbum;

/**
 自定义相机权限框
 */
-(void)buildCustomCameraPermissionAlert;

@end


@interface TBScanViewController : UIViewController <UIAlertViewDelegate>

- (instancetype)initWithConfig:(NSDictionary *)config;

@property (nonatomic,weak) id<TBScanViewControllerDelegate> delegate;

@property (nonatomic,weak) id<TBScanViewControllerTorchDelegate> torchDelegate;

/**
 *
 * @discussion scanType 是一个整型，可以用该值来确定要支持的扫码类型，可以用按位或的方式来支持多个类型，详细见ScanType定义。
 * 推荐使用最小类型，扫码类型越少识别速度越快，比如只支持条形码则使用ScanType_Barcode，只支持QR码则使用ScanType_QRCode，只支持
 * 条形码和QR码则使用ScanType_Barcode|ScanType_QRCode。请谨慎使用ScanType_All_Code类型，该类型中包含一些不常用的扫码类型。
 *
 */
@property (nonatomic,assign) ScanType scanType;
/**
 *
 * @discussion rectOfInterest 设置底层解码时的裁剪区域，如果不设置，SDK会默认一个裁剪区域
 *
 */
@property (nonatomic,assign) CGRect rectOfInterest;

/**
 *
 * @discussion cameraWidthPercent 设置屏幕中默认显示摄像头内容的百分比，设值有效范围大约为0.4~1.0，默认为0.74
 *
 */
@property (nonatomic,assign) CGFloat cameraWidthPercent;

/**
 *
 * @discussion bPlaySound 设置识别成功后是否播放声音，YES播放，NO不播放，默认是YES
 *
 */
@property (nonatomic,assign) BOOL bPlaySound;

/**
 *
 * @discussion bPreciseScan 设置扫描模式，YES能扫描很难识别的条码二维码，但是会影响扫描速度，NO对很复杂的条码二维码识别不了，但是识别速度会提升，默认是NO
 *
 */
@property (nonatomic,assign) BOOL bPreciseScan;

/**
 *
 * @discussion bResumeScanAtViewAppear 设置viewAppear的时候是否自动恢复扫描，为NO时不恢复需要手动调用resumeScan来恢复，默认为YES
 *
 */
@property (nonatomic,assign) BOOL bResumeScanAtViewAppear;

/**
 是否支持自动根据屏幕内疑似二维码size做放大缩小，默认是YES
 */
@property (nonatomic,assign,setter=setBAutoZoomEnable:) BOOL bAutoZoomEnable;           ///< 逐步淘汰掉这种以下面渐进式的为主

/**
 是否支持自动根据屏幕内疑似二维码 size 做渐进试的放大缩小，默认是YES
 */
@property (nonatomic,assign) BOOL bGraduallyAutoZoomEnable;

/**
 对焦 near 优先模式是否启用
 */
@property (nonatomic,assign) BOOL bAutoFocusRangeNearEnable;

/**
 为防止过早自动放大，默认前 35 帧不做自动放大，可调整
 */
@property (nonatomic,assign) int autoZoomDelayFrameCount;

/**
 是否支持手势,默认YES
 */
@property (nonatomic,assign) BOOL bGestureEnable;

/**
 是否监控扫码过程中机身稳定性, 默认 NO，为 YES 时会在状态变动时回调方法 - (void)stableStatusUpdate: AtScanCount:
 */
@property (nonatomic,assign) BOOL bStableStatusMonitorEnable;

/**
 是否要在收到内存警告时释放相机，默认 NO 
 */
@property (nonatomic,assign) BOOL shouldReleaseByMemoryWarning;

//////////////////////////////// 新特性 AI 相关 /////////////////////////////////////

/**
 动画框大小，用于做放大参考
 */
@property (nonatomic,assign) CGRect animationRect;

/**
 提示打开闪光灯的阈值
 */
@property (nonatomic,assign) CGFloat flashOnBrightnessThreshold;

/**
 提示关闭闪光灯的阈值
 */
@property (nonatomic,assign) CGFloat flashOffBrightnessThreshold;

/**
 拍照时设置该值为true，不主动改变闪光灯的状态
 */
@property (nonatomic,assign) BOOL bKeepTorchModeWhenPauseScan;

/////////////////////// 保存帧图像 ////////////////
/**
 是否要保存 did find 那一帧图像, 图像会通过 delegate 异步线程回传 默认 NO
 */
@property (nonatomic,assign) BOOL needSaveFrameASImageWhenDidFind;

/**
 *
 * @discussion bImpactFeedBack 设置识别成功后是否短促震动，YES播放，NO不播放，默认是YES
 *
 */
@property (nonatomic,assign) BOOL bImpactFeedBack;

////////////////////////////自定义UIAlertView的文案/////////////////////////////////
// 无摄像头权限时的AlertView的title
@property (nonatomic,strong) NSString *cameraPermissionDeniedTitle;
// 无摄像头权限时的AlertView的提示信息
@property (nonatomic,strong) NSString *cameraPermissionDeniedMsg;
// 无摄像头权限时的AlertView取消按钮的title
@property (nonatomic,strong) NSString *cameraPermissionDeniedCancelTitle;
// 相册识别失败时的AlertView的提示信息
@property (nonatomic,strong) NSString *scanPhotoFailedMsg;
// 相册识别失败时AlertView取消按钮的title
@property (nonatomic,strong) NSString *scanPhotoFailedCancelTitle;

@property (nonatomic, assign) BOOL supportSaveColorfullImage; ///配置是否支持保存彩色图片

/**
 *
 * @discussion 停止扫描并关闭摄像头，一般情况下不要直接调用该方法
 *
 */
-(void)exitScan;

/**
 *
 * @discussion 扫描本地图库，会调起本地相册
 *
 */
-(void)scanPhotoLibrary;


/**
 从相册选择的流程
 */
-(void)scanPhotoImage:(UIImage*)image;

/**
 *
 * @discussion 点击闪光灯，会调用代理接口-(void)setTorchState:(TorchState)bState，在该代理接口中根据参数bState来判断当前闪光灯的状态从而实现对应的UI
 *
 */
-(void)onTorch;

/**
 * 返回闪光灯的状态
 */
-(TorchState)torchMode;
/**
 *
 * @discussion 暂停扫码
 *
 */
-(void)pauseScan;

/**
 *
 * @discussion 继续扫码
 *
 */
-(void)resumeScan;

/**
 *
 * @discussion 暂停扫码,并停止相机(预览停止)
 *
 */
-(void)pauseCaptureSession;

/**
 *
 * @discussion 继续扫码，并恢复相机
 *
 */
-(void)resumeCaptureSession;


/**
 *
 * @discussion 暂停扫,并停止预览,相机 Session 没断（便于 resume 快速开始，不必重启相机）
 *
 */
-(void)pauseScanAndPreView;

/**
 *
 * @discussion 继续扫,并同步预览
 *
 */
-(void)resumeScanAndPreView;

/**
 *
 * @discussion 针对预留ScanType可以注册自定义的Plugin
 *
 */
-(void)registerPlugin:(TBScanPlugin*)inPlugin withType:(ScanType)type;

/**
 *
 * @discussion 对底层算法做自定义配置
 *
 */
- (void)startCameraWithConfig:(NSDictionary *)config;

/**
 *
 * @discussion 获取底层算法解码过程详细信息,  如果没有则为 nil
 */
- (NSDictionary *)getDecodeDetailInfo;



///////////////////////////////下面两个接口只在iPad下有效/////////////////////////
/**
 *
 * @discussion 对iPad相册取图弹出框有定制要求的可以使用这个接口来替换通用的相册取图scanPhotoLibrary接口
 *
 * @param rect 指定相册取图弹出框的区域
 *
 * @param arrowDirections 指定相册取图弹出框的箭头方向
 *
 * @param arrowColor 指定相册取图弹出框的背景色，nil为白色
 *
 */
-(void)scanPhotoLibrary:(CGRect)rect permittedArrowDirections:(UIPopoverArrowDirection)arrowDirections with:(UIColor*)arrowColor;

@end

@interface TBScanViewController(CaptureManager)

/**
 *
 * @discussion 判断摄像头是否支持该SessionPreset
 *
 */
-(BOOL)canSetSession:(NSString *)sessionPreset;

// 判断当前是否在使用前置摄像头
-(BOOL)isUsingFrontCamera;

-(void)setCameraCaptureFocusPoint:(CGPoint)point;

-(void)updateCodeDetectStatus;

@end
