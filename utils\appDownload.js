/* ------------------------------------------------------------------
 * 外部下载链接实现
 * - openExternalDownload: 使用uni-link打开外部下载链接
 * - saveAndOpenTextFile: 保存文本到系统公共目录（Android）或应用目录（iOS）
 * ------------------------------------------------------------------ */

/**
 * 为下载URL添加认证参数
 * @param {string} url 原始下载URL
 * @returns {Promise<string>} 带认证参数的URL
 */
async function addAuthToUrl(url) {
	try {
		// 动态导入认证相关模块
		const { UUID, SECRET, AESKEY, encrypt } = await import('@/utils/config');
		const { isFromNodeAccess } = await import('@/utils/nodeConfigManager');
		const md5Module = await import('js-md5');
		const md5 = md5Module.default;

		// 构建认证参数
		const timestamp = Date.parse(new Date());
		const request_token = md5(String(timestamp) + md5(SECRET.value));

		// 根据是否为节点访问构建不同的参数
		if (!isFromNodeAccess()) {
			// 非节点访问：使用加密的 form_data 方式
			const data = {
				request_time: timestamp,
				request_token: request_token,
			};

			const client_bind_token = UUID.value;
			const form_data = encrypt(JSON.stringify(data), AESKEY.value);

			// 检查URL是否已经有参数
			const separator = url.includes('?') ? '&' : '?';
			const authenticatedUrl =
				url + separator + `client_bind_token=${client_bind_token}&form_data=${encodeURIComponent(form_data)}`;

			return authenticatedUrl;
		} else {
			// 节点访问：直接添加参数
			const params = [];
			params.push(`request_time=${timestamp}`);
			params.push(`request_token=${request_token}`);

			// 检查URL是否已经有参数
			const separator = url.includes('?') ? '&' : '?';
			const authenticatedUrl = url + separator + params.join('&');

			return authenticatedUrl;
		}
	} catch (error) {
		// 如果添加认证失败，返回原始URL
		return url;
	}
}

/**
 * 获取带认证的下载链接
 * @param {string} url 下载链接
 * @param {string} fileName 文件名（用于提示）
 * @returns {Promise<string>} 带认证的下载链接
 */
export const getAuthenticatedDownloadUrl = async (url, fileName) => {
	try {
		// 添加认证参数到URL
		const authenticatedUrl = await addAuthToUrl(url);
		return authenticatedUrl;
	} catch (error) {
		throw new Error('生成下载链接失败：' + error.message);
	}
};

/**
 * 获取下载链接（用于uni-link组件）
 * @param {string} url 下载链接
 * @param {string} fileName 文件名
 * @returns {Promise<string>} 带认证的下载链接
 */
export const downloadFile = async (url, fileName) => {
	try {
		// 获取带认证的下载链接
		const downloadUrl = await getAuthenticatedDownloadUrl(url, fileName);
		return downloadUrl;
	} catch (error) {
		uni.showToast({
			title: error.message || '生成下载链接失败',
			icon: 'none',
			duration: 4000,
		});
		throw error;
	}
};

/**
 * 保存文本内容到文件并打开（使用 MediaStore 方案）
 * @param {string} content 文本内容
 * @param {string} fileName 文件名
 * @returns {Promise<string>} 保存后的文件路径或 URI
 */
export const saveAndOpenTextFile = (content, fileName) =>
	new Promise((resolve, reject) => {
		// #ifdef APP-PLUS
		const sys = uni.getSystemInfoSync();
		const isAndroid = sys.platform === 'android';
		const isIOS = sys.platform === 'ios';

		if (isAndroid) {
			// Android: 使用 MediaStore 保存到公共 Download 目录
			saveTextToPublicDownloadAndroid(content, fileName, resolve, reject);
		} else if (isIOS) {
			// iOS: 保存到沙盒并打开
			saveTextToIOSDocuments(content, fileName, resolve, reject);
		} else {
			reject(new Error('不支持的平台'));
		}
		// #endif

		// #ifndef APP-PLUS
		reject(new Error('该方法仅支持 App（APP-PLUS）平台'));
		// #endif
	});

/**
 * Android 使用 MediaStore 保存文本文件到公共 Download 目录
 */
function saveTextToPublicDownloadAndroid(content, fileName, resolve, reject) {
	try {
		// ---- Java 类导入 ----
		const mainAct = plus.android.runtimeMainActivity();
		const context = mainAct.getApplicationContext();
		let resolver = context.getContentResolver();
		plus.android.importClass(resolver); // ★★ 核心修正：注入实例方法
		const Build = plus.android.importClass('android.os.Build');
		const ContentValues = plus.android.importClass('android.content.ContentValues');
		const MediaStore = plus.android.importClass('android.provider.MediaStore');
		const BufferedOS = plus.android.importClass('java.io.BufferedOutputStream');

		// 1. 目标集合
		const collection =
			Build.VERSION.SDK_INT >= 29
				? MediaStore.Downloads.getContentUri(MediaStore.VOLUME_EXTERNAL_PRIMARY)
				: MediaStore.Downloads.EXTERNAL_CONTENT_URI;

		// 2. 插入一条空记录
		const values = new ContentValues();
		values.put(MediaStore.Downloads.DISPLAY_NAME, fileName);
		values.put(MediaStore.Downloads.MIME_TYPE, 'text/plain');

		const itemUri = resolver.insert(collection, values);
		if (itemUri == null) {
			reject(new Error('MediaStore 插入失败'));
			return;
		}

		// 3. 写入文本内容
		const outStream = new BufferedOS(resolver.openOutputStream(itemUri, 'w'));
		const bytes = content.getBytes('UTF-8');
		outStream.write(bytes);
		outStream.flush();
		outStream.close();

		// 4. 通知系统扫描
		plus.android
			.importClass('android.media.MediaScannerConnection')
			.scanFile(context, [itemUri.toString()], null, null);

		// 尝试打开文件
		const uriString = itemUri.toString();
		uni.openDocument({
			filePath: uriString,
			success: () => {
				uni.showToast({
					title: '文件已保存并打开',
					icon: 'success',
				});
				resolve(uriString);
			},
			fail: () => {
				uni.showToast({
					title: '文件已保存到系统 Download 目录',
					icon: 'success',
				});
				resolve(uriString);
			},
		});
	} catch (error) {
		reject(new Error('写入公共 Download 失败：' + error));
	}
}

/**
 * iOS 保存文本到沙盒文档目录
 */
function saveTextToIOSDocuments(content, fileName, resolve, reject) {
	plus.io.resolveLocalFileSystemURL(
		'documents/',
		(entry) => {
			entry.getFile(
				fileName,
				{ create: true, exclusive: false },
				(fileEntry) => {
					fileEntry.createWriter(
						(writer) => {
							writer.onwriteend = () => {
								// 尝试打开文件
								uni.openDocument({
									filePath: fileEntry.fullPath,
									success: () => {
										uni.showToast({
											title: '文件已保存并打开',
											icon: 'success',
										});
										resolve(fileEntry.fullPath);
									},
									fail: () => {
										plus.runtime.openFile(fileEntry.fullPath, {}, () => {
											uni.showToast({
												title: '文件已保存',
												icon: 'success',
											});
											resolve(fileEntry.fullPath);
										});
									},
								});
							};
							writer.onerror = () => {
								reject(new Error('文件写入失败'));
							};
							writer.write(content);
						},
						() => reject(new Error('创建文件写入对象失败')),
					);
				},
				() => reject(new Error('创建文件失败')),
			);
		},
		() => reject(new Error('获取文档目录失败')),
	);
}

// 默认导出
export default {
	getAuthenticatedDownloadUrl,
	downloadFile,
	saveAndOpenTextFile,
};
