<template>
  <page-container ref="pageContainerRef" :title="$t('linux.control.title')">
    <view class="p-20">
      <!-- 开关区域 -->
      <view class="switch-section mb-20 bg-primary">
        <view class="switch-item">
          <text class="text-primary">{{ $t('linux.control.monitorSwitch') }}</text>
          <uv-switch v-model="controlEnabled" @change="handleControlSwitch" activeColor="#20a50a"></uv-switch>
        </view>
      </view>

      <!-- 资源使用率图表 -->
      <view class="chart-container bg-primary mb-20">
        <view class="text-primary text-32 font-bold mb-20">{{ $t('linux.control.resourceUsage') }}</view>
        <view id="resourceview" class="charts" :prop="loadData" :i18n="chartI18n" :change:prop="echarts.changeLoadData" :change:i18n="echarts.updateI18n"></view>
      </view>

      <!-- 负载详情图表 -->
      <view class="chart-container bg-primary mb-20">
        <view class="text-primary text-32 font-bold mb-20">{{ $t('linux.control.loadDetails') }}</view>
        <view id="loaddetailview" class="charts" :prop="loadData" :i18n="chartI18n" :change:prop="echarts.changeLoadDetailData" :change:i18n="echarts.updateI18n"></view>
      </view>

      <view class="chart-text bg-primary mb-20">
        <view class="table-th table-title row">
          <view class="" style="color: rgb(255, 140, 0)">{{ $t('linux.control.resourceUsage') }}</view>
          <view class="text-tertiary">{{ $t('linux.control.average') }}</view>
          <view class="text-tertiary">{{ $t('linux.control.maximum') }}</view>
        </view>
        <view class="table-th row">
          <view class="table-info">
            <view class="text-tertiary mb-20">{{ $t('linux.control.today') }}</view>
            <view class="text-tertiary">{{ $t('linux.control.yesterday') }}</view>
          </view>
          <view class="table-value">
            <view class="text-secondary value-now">{{ comInfo.use.now.ave }}%</view>
            <view class="text-tertiary value-last">{{ comInfo.use.last.ave }}%</view>
          </view>
          <view class="table-value">
            <view class="text-secondary value-now">{{ comInfo.use.now.max }}%</view>
            <view class="text-tertiary value-last">{{ comInfo.use.last.max }}%</view>
          </view>
        </view>
      </view>

      <!-- CPU -->
      <view class="chart-container bg-primary mb-20">
        <view class="text-primary text-32 font-bold mb-20">CPU</view>
        <view id="cpuview" class="charts" :prop="cpuData" :i18n="chartI18n" :change:prop="echarts.changeCpuData" :change:i18n="echarts.updateI18n"></view>
      </view>

      <view class="chart-text bg-primary mb-20">
        <view class="table-th table-title row">
          <view class="" style="color: rgb(0, 153, 238)">CPU</view>
          <view class="text-tertiary">{{ $t('linux.control.average') }}</view>
          <view class="text-tertiary">{{ $t('linux.control.maximum') }}</view>
        </view>
        <view class="table-th row">
          <view class="table-info">
            <view class="text-tertiary mb-20">{{ $t('linux.control.today') }}</view>
            <view class="text-tertiary">{{ $t('linux.control.yesterday') }}</view>
          </view>
          <view class="table-value">
            <view class="text-secondary value-now">{{ comInfo.cpu.now.ave }}%</view>
            <view class="text-tertiary value-last">{{ comInfo.cpu.last.ave }}%</view>
          </view>
          <view class="table-value">
            <view class="text-secondary value-now">{{ comInfo.cpu.now.max }}%</view>
            <view class="text-tertiary value-last">{{ comInfo.cpu.last.max }}%</view>
          </view>
        </view>
      </view>

      <!-- 内存 -->
      <view class="chart-container bg-primary mb-20">
        <view class="text-primary text-32 font-bold mb-20">{{ $t('linux.memory') }}</view>
        <view id="memoryview" class="charts" :prop="memoryData" :i18n="chartI18n" :change:prop="echarts.changeMemoryData" :change:i18n="echarts.updateI18n"></view>
      </view>

      <view class="chart-text bg-primary mb-20">
        <view class="table-th table-title row">
          <view class="" style="color: rgb(0, 153, 238)">{{ $t('linux.memory') }}</view>
          <view class="text-tertiary">{{ $t('linux.control.average') }}</view>
          <view class="text-tertiary">{{ $t('linux.control.maximum') }}</view>
        </view>
        <view class="table-th row">
          <view class="table-info">
            <view class="text-tertiary mb-20">{{ $t('linux.control.today') }}</view>
            <view class="text-tertiary">{{ $t('linux.control.yesterday') }}</view>
          </view>
          <view class="table-value">
            <view class="text-secondary value-now">{{ comInfo.memory.now.ave }}%</view>
            <view class="text-tertiary value-last">{{ comInfo.memory.last.ave }}%</view>
          </view>
          <view class="table-value">
            <view class="text-secondary value-now">{{ comInfo.memory.now.max }}%</view>
            <view class="text-tertiary value-last">{{ comInfo.memory.last.max }}%</view>
          </view>
        </view>
      </view>

      <!-- 磁盘 -->
      <view class="chart-container bg-primary mb-20">
        <view class="text-primary text-32 font-bold mb-20">{{ $t('linux.diskIO') }}</view>
        <view id="diskview" class="charts" :prop="diskData" :i18n="chartI18n" :change:prop="echarts.changeDiskData" :change:i18n="echarts.updateI18n"></view>
      </view>

      <view class="chart-text bg-primary mb-20">
        <view class="table-th table-title row">
          <view class="" style="color: rgb(255, 70, 131)">{{ $t('linux.control.readBytes') }}</view>
          <view class="text-tertiary">{{ $t('linux.control.average') }}</view>
          <view class="text-tertiary">{{ $t('linux.control.maximum') }}</view>
        </view>
        <view class="table-th row">
          <view class="table-info">
            <view class="text-tertiary mb-20">{{ $t('linux.control.today') }}</view>
            <view class="text-tertiary">{{ $t('linux.control.yesterday') }}</view>
          </view>
          <view class="table-value">
            <view class="text-secondary value-now" style="font-size: 32rpx">{{ comInfo.read.now.ave }} kb/s</view>
            <view class="text-tertiary value-last" style="font-size: 32rpx">{{ comInfo.read.last.ave }} kb/s</view>
          </view>
          <view class="table-value">
            <view class="text-secondary value-now" style="font-size: 32rpx">{{ comInfo.read.now.max }} kb/s</view>
            <view class="text-tertiary value-last" style="font-size: 32rpx">{{ comInfo.read.last.max }} kb/s</view>
          </view>
        </view>
      </view>

      <view class="chart-text bg-primary mb-20">
        <view class="table-th table-title row">
          <view class="" style="color: rgba(46, 165, 186, 0.7)">{{ $t('linux.control.writeBytes') }}</view>
          <view class="text-tertiary">{{ $t('linux.control.average') }}</view>
          <view class="text-tertiary">{{ $t('linux.control.maximum') }}</view>
        </view>
        <view class="table-th row">
          <view class="table-info">
            <view class="text-tertiary mb-20">{{ $t('linux.control.today') }}</view>
            <view class="text-tertiary">{{ $t('linux.control.yesterday') }}</view>
          </view>
          <view class="table-value">
            <view class="text-secondary value-now" style="font-size: 32rpx">{{ comInfo.write.now.ave }} kb/s</view>
            <view class="text-tertiary value-last" style="font-size: 32rpx">{{ comInfo.write.last.ave }} kb/s</view>
          </view>
          <view class="table-value">
            <view class="text-secondary value-now" style="font-size: 32rpx">{{ comInfo.write.now.max }} kb/s</view>
            <view class="text-tertiary value-last" style="font-size: 32rpx">{{ comInfo.write.last.max }} kb/s</view>
          </view>
        </view>
      </view>

      <!-- 网络 -->
      <view class="chart-container bg-primary mb-20">
        <view class="text-primary text-32 font-bold mb-20">{{ $t('linux.control.networkIO') }}</view>
        <view id="networkview" class="charts" :prop="networkData" :i18n="chartI18n" :change:prop="echarts.changeNetworkData" :change:i18n="echarts.updateI18n"></view>
      </view>

      <view class="chart-text bg-primary mb-20">
        <view class="table-th table-title row">
          <view class="" style="color: rgb(255, 140, 0)">{{ $t('linux.network.up') }}</view>
          <view class="text-tertiary">{{ $t('linux.control.average') }}</view>
          <view class="text-tertiary">{{ $t('linux.control.maximum') }}</view>
        </view>
        <view class="table-th row">
          <view class="table-info">
            <view class="text-tertiary mb-20">{{ $t('linux.control.today') }}</view>
            <view class="text-tertiary">{{ $t('linux.control.yesterday') }}</view>
          </view>
          <view class="table-value">
            <view class="text-secondary value-now" style="font-size: 32rpx">{{ comInfo.up.now.ave }} kb/s</view>
            <view class="text-tertiary value-last" style="font-size: 32rpx">{{ comInfo.up.last.ave }} kb/s</view>
          </view>
          <view class="table-value">
            <view class="text-secondary value-now" style="font-size: 32rpx">{{ comInfo.up.now.max }} kb/s</view>
            <view class="text-tertiary value-last" style="font-size: 32rpx">{{ comInfo.up.last.max }} kb/s</view>
          </view>
        </view>
      </view>

      <view class="chart-text bg-primary mb-20">
        <view class="table-th table-title row">
          <view class="" style="color: rgb(30, 144, 255)">{{ $t('linux.network.down') }}</view>
          <view class="text-tertiary">{{ $t('linux.control.average') }}</view>
          <view class="text-tertiary">{{ $t('linux.control.maximum') }}</view>
        </view>
        <view class="table-th row">
          <view class="table-info">
            <view class="text-tertiary mb-20">{{ $t('linux.control.today') }}</view>
            <view class="text-tertiary">{{ $t('linux.control.yesterday') }}</view>
          </view>
          <view class="table-value">
            <view class="text-secondary value-now" style="font-size: 32rpx">{{ comInfo.down.now.ave }} kb/s</view>
            <view class="text-tertiary value-last" style="font-size: 32rpx">{{ comInfo.down.last.ave }} kb/s</view>
          </view>
          <view class="table-value">
            <view class="text-secondary value-now" style="font-size: 32rpx">{{ comInfo.down.now.max }} kb/s</view>
            <view class="text-tertiary value-last" style="font-size: 32rpx">{{ comInfo.down.last.max }} kb/s</view>
          </view>
        </view>
      </view>
    </view>
  </page-container>
</template>

<script setup>
  import PageContainer from '@/components/PageContainer/index.vue';
  import { ref, reactive, onMounted } from 'vue';
  import { $t } from '@/locale/index.js';
  import {
    getLoadData as getLoadDataApi,
    getCpuAndMemoryData as getCpuAndMemoryDataApi,
    getNetworkIoData as getNetworkIoDataApi,
    getDiskIoData as getDiskIoDataApi,
    getControlStatus as getControlStatusApi,
  } from '@/api/control';

  const pageContainerRef = ref(null);

  // 监控开关状态
  const controlEnabled = ref(false);

  // 图表数据
  const loadData = ref([]);
  const cpuData = ref([]);
  const memoryData = ref([]);
  const diskData = ref([]);
  const networkData = ref([]);

  // 添加图表多语言数据
  const chartI18n = reactive({
    resourceUsage: $t('linux.control.resourceUsage'),
    loadDetails: $t('linux.control.loadDetails'),
    average: $t('linux.control.average'),
    maximum: $t('linux.control.maximum'),
    today: $t('linux.control.today'),
    yesterday: $t('linux.control.yesterday'),
    cpu: 'CPU',
    memory: $t('linux.memory'),
    readBytes: $t('linux.control.readBytes'),
    writeBytes: $t('linux.control.writeBytes'),
    networkIO: $t('linux.control.networkIO'),
    up: $t('linux.network.up'),
    down: $t('linux.network.down'),
    percentUnit: $t('linux.control.percentUnit'),
    kbsUnit: $t('linux.unitKBS'),
    networkUnit: $t('linux.unitKBS'),
    minutes1: $t('linux.control.minutes1'),
    minutes5: $t('linux.control.minutes5'),
    minutes15: $t('linux.control.minutes15'),
    time: $t('linux.control.time')
  });

  // 日期相关
  const nowDate = ref('');
  const startDate = ref(0);
  const endDate = ref(0);

  // 图表数据信息
  const comInfo = reactive({
    use: {
      now: { ave: '...', max: '...' },
      last: { ave: '计算中', max: '计算中' },
    },
    cpu: {
      now: { ave: '...', max: '...' },
      last: { ave: '计算中', max: '计算中' },
    },
    memory: {
      now: { ave: '', max: '' },
      last: { ave: '', max: '' },
    },
    read: {
      now: { ave: '', max: '' },
      last: { ave: '', max: '' },
    },
    write: {
      now: { ave: '', max: '' },
      last: { ave: '', max: '' },
    },
    up: {
      now: { ave: '', max: '' },
      last: { ave: '', max: '' },
    },
    down: {
      now: { ave: '', max: '' },
      last: { ave: '', max: '' },
    },
  });

  // 当前标记（今天/昨天）
  const tag = ref('now');

  // 控制天数
  const controlDay = ref(30);

  onMounted(() => {
    initData();
  });

  // 初始化数据
  const initData = () => {
    // 设置开始和结束时间
    const currentTimeStamp = Math.floor(Date.now() / 1000);
    startDate.value = getZeroDateStamp(0);
    endDate.value = currentTimeStamp;

    // 获取当前日期
    const date = new Date();
    nowDate.value = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(
      date.getDate(),
    ).padStart(2, '0')}`;

    // 获取监控状态
    getControlStatus();

    // 获取图表数据
    getChartsData();
  };

  // 监控开关切换事件
  const handleControlSwitch = async (status) => {
    const paramType = status ? 1 : 0;

    const res = await getControlStatusApi({ type: paramType, day: controlDay.value });
    if (res.status) {
      pageContainerRef.value.notify.success(res.msg);
    } else {
      pageContainerRef.value.notify.error(res.msg);
    }
  };

  // 获取当前监控状态
  const getControlStatus = async () => {
    const res = await getControlStatusApi({ type: -1 });
    if (res.status) {
      controlEnabled.value = res.status;
      controlDay.value = res.day;
    }
  };

  // 获取0点时间戳
  const getZeroDateStamp = (dateNum) => {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    return Math.floor(today.getTime() / 1000) - 86400 * dateNum;
  };

  // 获取23:59:59时间戳
  const getLateDateStamp = (dateNum) => {
    const today = new Date();
    today.setHours(23, 59, 59, 0);
    return Math.floor(today.getTime() / 1000) - 86400 * dateNum;
  };

  // 获取所有图表数据
  const getChartsData = () => {
    tag.value = 'now';

    loadData.value = [];
    cpuData.value = [];
    memoryData.value = [];
    diskData.value = [];
    networkData.value = [];

    setTimeout(() => getLoadData(), 500);
    setTimeout(() => getCpuAndMemoryData(), 1000);
    setTimeout(() => getDiskData(), 1500);
    setTimeout(() => getNetworkData(), 2000);
  };

  // 获取昨天数据用于对比
  const getLastData = async () => {
    tag.value = 'last';

    const start = getZeroDateStamp(1);
    const end = getLateDateStamp(1);

    const cpuRes = await getCpuAndMemoryDataApi({
      start: start,
      end: end,
    });

    addComInfo(cpuRes, 'cpu', 'pro', tag.value);
    addComInfo(cpuRes, 'memory', 'mem', tag.value);

    const loadRes = await getLoadDataApi({
      start: start,
      end: end,
    });

    addComInfo(loadRes, 'use', 'pro', tag.value);

    const diskRes = await getDiskIoDataApi({
      start: start,
      end: end,
    });

    const processedData = JSON.parse(JSON.stringify(diskRes));
    for (let i = 0; i < processedData.length; i++) {
      processedData[i].read_bytes = processedData[i].read_bytes / 1024 / 60;
      processedData[i].write_bytes = processedData[i].write_bytes / 1024 / 60;
    }
    addComInfo(processedData, 'read', 'read_bytes', tag.value);
    addComInfo(processedData, 'write', 'write_bytes', tag.value);

    const networkRes = await getNetworkIoDataApi({
      start: start,
      end: end,
    });

    addComInfo(networkRes, 'up', 'up', tag.value);
    addComInfo(networkRes, 'down', 'down', tag.value);
  };

  // 获取负载数据
  const getLoadData = async () => {
    const res = await getLoadDataApi({
      start: startDate.value,
      end: endDate.value,
    });
    loadData.value = res;
    addComInfo(res, 'use', 'pro', tag.value);
  };

  // 获取CPU和内存数据
  const getCpuAndMemoryData = async () => {
    const res = await getCpuAndMemoryDataApi({
      start: startDate.value,
      end: endDate.value,
    });
    cpuData.value = res;
    memoryData.value = res;

    addComInfo(res, 'cpu', 'pro', tag.value);
    addComInfo(res, 'memory', 'mem', tag.value);
  };

  // 获取磁盘IO数据
  const getDiskData = async () => {
    const res = await getDiskIoDataApi({
      start: startDate.value,
      end: endDate.value,
    });

    diskData.value = JSON.parse(JSON.stringify(res));

    const processedData = JSON.parse(JSON.stringify(res));
    for (let i = 0; i < processedData.length; i++) {
      processedData[i].read_bytes = processedData[i].read_bytes / 1024 / 60;
      processedData[i].write_bytes = processedData[i].write_bytes / 1024 / 60;
    }

    addComInfo(processedData, 'read', 'read_bytes', tag.value);
    addComInfo(processedData, 'write', 'write_bytes', tag.value);
  };

  // 获取网络IO数据
  const getNetworkData = async () => {
    const res = await getNetworkIoDataApi({
      start: startDate.value,
      end: endDate.value,
    });

    networkData.value = res;

    addComInfo(res, 'up', 'up', tag.value);
    addComInfo(res, 'down', 'down', tag.value);

    // 加载昨天的数据用于对比
    getLastData();
  };

  // 计算平均值和最大值
  const addComInfo = (data, name, attr, time) => {
    if (!data || data.length === 0) return;

    let sum = 0;
    for (let i = 0; i < data.length; i++) {
      sum += data[i][attr];
    }

    const ave = (sum / data.length).toFixed(2);
    const max = Math.max(...data.map((item) => item[attr])).toFixed(2);

    comInfo[name][time] = { ave, max };
  };
</script>

<script module="echarts" lang="renderjs">
  let loadView = null,
    loadDetailView = null,
    cpuView = null,
    memoryView = null,
    diskView = null,
    networkView = null;

  export default {
    data() {
      return {
        loadData: [], // 负载
        cpuData: [], // cpu
        memoryData: [], // 内存
        diskData: [], // 磁盘IO
        networkData: [], // 网络
        i18n: {}, // 多语言文本
      }
    },

    mounted() {
      if (typeof window.echarts === 'function') {
        // echarts已经加载
      } else {
        // 动态引入大的类库，以此避免影响页面展示
        const script = document.createElement('script')
        script.src = 'static/echarts/echarts.min.js'
        document.head.appendChild(script)
      }
    },

    methods: {
      // 更新i18n数据
      updateI18n(newValue, oldValue, ownerInstance, instance) {
        this.i18n = newValue
        
        // 如果图表已初始化，更新它们的配置
        if (loadView) this.loadInit()
        if (loadDetailView) this.loadDetailInit()
        if (cpuView) this.cpuInit()
        if (memoryView) this.memoryInit()
        if (diskView) this.diskInit()
        if (networkView) this.networkInit()
      },

      /**
       * 平均负载
       */
      // 得到平均负载数据
      changeLoadData(newValue, oldValue, ownerInstance, instance) {
        this.loadData = newValue
        this.loadInit()
        this.loadDetailInit() // 同时初始化负载详情图表
      },

      // 负载详情数据变化
      changeLoadDetailData(newValue, oldValue, ownerInstance, instance) {
        // 不需要重新赋值，因为使用的是同一组数据
        this.loadDetailInit()
      },

      // 资源使用率 chart渲染
      loadInit() {
        let _self = this

        try {
          loadView = echarts.init(document.getElementById("resourceview"))
        } catch (err) {
          // eCharts还没完全引入成功，数据就已经开始加载，所以会报这个错
          if (err == "ReferenceError: Can't find variable: echarts") {
            // 延迟500ms后，eCharts已引入完成，此时可重新加载负载图表
            setTimeout(function() {
              // 告诉普通script，eCharts已加载完成，可以重新加载数据
              _self.$ownerInstance.callMethod('chartsInit', {
                data: ""
              })
            }, 500)
          }
        }

        let xData = []
        let yData = []

        for (let i = 0, leng = this.loadData.length; i < leng; i++) {
          xData.push(this.loadData[i].addtime)
          yData.push(this.loadData[i].pro)
        }

        let option = {
          title: {
            text: ' ',
            left: 'center',
            textStyle: {
              fontSize: 16,
              fontWeight: 400
            }
          },
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'cross'
            }
          },
          axisPointer: {
            lineStyle: {
              color: '#aaaa',
              width: 1
            }
          },
          grid: {
            top: '35px',
            left: '9%',
            right: '5%',
            bottom: '17%',
            containLabel: true
          },
          xAxis: {
            type: 'category',
            boundaryGap: false,
            axisLine: {
              lineStyle: {
                color: '#666'
              }
            },
            data: xData.slice().reverse()
          },
          yAxis: {
            scale: true,
            name: this.i18n.resourceUsage ? this.i18n.resourceUsage + '%' : '资源使用率%',
            splitLine: {
              show: true,
              lineStyle: {
                color: "#ddd"
              }
            },
            nameTextStyle: {
              color: 'rgb(255, 140, 0)',
              fontSize: 12,
              align: 'left'
            },
            axisLine: {
              lineStyle: {
                color: '#666',
              }
            }
          },
          dataZoom: [{
            type: 'slider',
            start: 0,
            end: 100,
            handleIcon: 'M10.7,11.9v-1.3H9.3v1.3c-4.9,0.3-8.8,4.4-8.8,9.4c0,5,3.9,9.1,8.8,9.4v1.3h1.3v-1.3c4.9-0.3,8.8-4.4,8.8-9.4C19.5,16.3,15.6,12.2,10.7,11.9z M13.3,24.4H6.7V23h6.6V24.4z M13.3,19.6H6.7v-1.4h6.6V19.6z',
            handleSize: '80%',
            handleStyle: {
              color: '#fff',
              shadowBlur: 3,
              shadowColor: 'rgba(0, 0, 0, 0.6)',
              shadowOffsetX: 2,
              shadowOffsetY: 2
            },
            left: '5%',
            right: '5%'
          }],
          series: [{
            name: this.i18n.resourceUsage || '资源使用率',
            type: 'line',
            lineStyle: {
              normal: {
                width: 2,
                color: 'rgb(255, 140, 0)'
              }
            },
            itemStyle: {
              normal: {
                color: 'rgb(255, 140, 0)'
              }
            },
            data: yData.slice().reverse()
          }],
          textStyle: {
            color: '#666',
            fontSize: 12
          }
        }

        loadView.setOption(option);
      },

      // 负载详情 chart渲染
      loadDetailInit() {
        try {
          loadDetailView = echarts.init(document.getElementById("loaddetailview"))
        } catch (err) {
          if (err == "ReferenceError: Can't find variable: echarts") {
            return; // 如果echarts还没加载完，等待loadInit中的重载触发
          }
        }

        let xData = []
        let zData = []
        let aData = []
        let bData = []

        for (let i = 0, leng = this.loadData.length; i < leng; i++) {
          xData.push(this.loadData[i].addtime)
          zData.push(this.loadData[i].one)
          aData.push(this.loadData[i].five)
          bData.push(this.loadData[i].fifteen)
        }

        let option = {
          title: {
            text: '',
            left: 'center',
            textStyle: {
              fontSize: 16,
              fontWeight: 400
            }
          },
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'cross'
            }
          },
          axisPointer: {
            lineStyle: {
              color: '#aaaa',
              width: 1
            }
          },
          grid: {
            top: '35px',
            left: '9%',
            right: '5%',
            bottom: '17%',
            containLabel: true
          },
          xAxis: {
            type: 'category',
            boundaryGap: false,
            data: xData.slice().reverse(),
            axisLine: {
              lineStyle: {
                color: '#666'
              }
            }
          },
          yAxis: {
            scale: true,
            name: this.i18n.loadDetails || '负载值',
            splitLine: {
              show: true,
              lineStyle: {
                color: "#ddd"
              }
            },
            nameTextStyle: {
              color: 'rgb(30, 144, 255)',
              fontSize: 12,
              align: 'left'
            },
            axisLine: {
              lineStyle: {
                color: '#666',
              }
            }
          },
          dataZoom: [{
            type: 'slider',
            start: 0,
            end: 100,
            handleIcon: 'M10.7,11.9v-1.3H9.3v1.3c-4.9,0.3-8.8,4.4-8.8,9.4c0,5,3.9,9.1,8.8,9.4v1.3h1.3v-1.3c4.9-0.3,8.8-4.4,8.8-9.4C19.5,16.3,15.6,12.2,10.7,11.9z M13.3,24.4H6.7V23h6.6V24.4z M13.3,19.6H6.7v-1.4h6.6V19.6z',
            handleSize: '80%',
            handleStyle: {
              color: '#fff',
              shadowBlur: 3,
              shadowColor: 'rgba(0, 0, 0, 0.6)',
              shadowOffsetX: 2,
              shadowOffsetY: 2
            },
            left: '5%',
            right: '5%',
          }],
          series: [
            {
              name: this.i18n.minutes1,
              type: 'line',
              lineStyle: {
                normal: {
                  width: 2,
                  color: 'rgb(30, 144, 255)'
                }
              },
              itemStyle: {
                normal: {
                  color: 'rgb(30, 144, 255)'
                }
              },
              data: zData.slice().reverse()
            },
            {
              name: this.i18n.minutes5,
              type: 'line',
              lineStyle: {
                normal: {
                  width: 2,
                  color: 'rgb(0, 178, 45)'
                }
              },
              itemStyle: {
                normal: {
                  color: 'rgb(0, 178, 45)'
                }
              },
              data: aData.slice().reverse()
            },
            {
              name: this.i18n.minutes15,
              type: 'line',
              lineStyle: {
                normal: {
                  width: 2,
                  color: 'rgb(147, 38, 255)'
                }
              },
              itemStyle: {
                normal: {
                  color: 'rgb(147, 38, 255)'
                }
              },
              data: bData.slice().reverse()
            }
          ],
          textStyle: {
            color: '#666',
            fontSize: 12
          }
        }

        loadDetailView.setOption(option);
      },

      /**
       * CPU
       */
      // 得到cpu数据
      changeCpuData(newValue, oldValue, ownerInstance, instance) {
        this.cpuData = newValue
        this.cpuInit()
      },

      // cpu chart渲染
      cpuInit() {
        cpuView = echarts.init(document.getElementById("cpuview"))

        let xData = []
        let yData = []

        for (let i = 0, leng = this.cpuData.length; i < leng; i++) {
          xData.push(this.cpuData[i].addtime)
          yData.push(this.cpuData[i].pro)
        }

        let option = {
          title: {
            text: ' ',
            left: 'center',
            textStyle: {
              fontSize: 16,
              fontWeight: 400
            }
          },
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'cross'
            },
            formatter: '{b}<br />{a}: {c}%'
          },
          grid: {
            top: '35px',
            left: '5%',
            right: '5%',
            bottom: '17%',
            containLabel: true
          },
          xAxis: {
            type: 'category',
            boundaryGap: false,
            data: xData.slice().reverse(),
            axisLine: {
              lineStyle: {
                color: "#666"
              }
            }
          },
          yAxis: {
            type: 'value',
            name: this.i18n.percentUnit || '百分比',
            boundaryGap: [0, '100%'],
            min: 0,
            max: 100,
            splitLine: {
              lineStyle: {
                color: "#ddd"
              }
            },
            axisLine: {
              lineStyle: {
                color: "#666"
              }
            }
          },
          dataZoom: [{
            type: 'slider',
            start: 0,
            end: 100,
            handleIcon: 'M10.7,11.9v-1.3H9.3v1.3c-4.9,0.3-8.8,4.4-8.8,9.4c0,5,3.9,9.1,8.8,9.4v1.3h1.3v-1.3c4.9-0.3,8.8-4.4,8.8-9.4C19.5,16.3,15.6,12.2,10.7,11.9z M13.3,24.4H6.7V23h6.6V24.4z M13.3,19.6H6.7v-1.4h6.6V19.6z',
            handleSize: '80%',
            handleStyle: {
              color: '#fff',
              shadowBlur: 3,
              shadowColor: 'rgba(0, 0, 0, 0.6)',
              shadowOffsetX: 2,
              shadowOffsetY: 2
            },
            left: '5%',
            right: '5%',
          }],
          series: [{
            name: 'CPU',
            type: 'line',
            smooth: true,
            symbol: 'none',
            itemStyle: {
              normal: {
                color: 'rgb(0, 153, 238)'
              }
            },
            data: yData.slice().reverse()
          }]
        }

        cpuView.setOption(option)
      },

      /**
       * 内存
       */
      // 得到内存数据
      changeMemoryData(newValue, oldValue, ownerInstance, instance) {
        this.memoryData = newValue
        this.memoryInit()
      },

      // 内存 chart渲染
      memoryInit() {
        memoryView = echarts.init(document.getElementById("memoryview"))

        let xData = []
        let yData = []

        for (let i = 0, leng = this.memoryData.length; i < leng; i++) {
          xData.push(this.memoryData[i].addtime)
          yData.push(this.memoryData[i].mem)
        }

        let option = {
          title: {
            text: ' ',
            left: 'center',
            textStyle: {
              fontSize: 16,
              fontWeight: 400
            }
          },
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'cross'
            },
            formatter: function(tips) {
              return tips[0].axisValueLabel + '</br>' + tips[0].seriesName + ':' + tips[0].value
                .toFixed(2) + '%'
            }
          },
          grid: {
            top: '35px',
            left: '5%',
            right: '5%',
            bottom: '17%',
            containLabel: true
          },
          xAxis: {
            type: 'category',
            boundaryGap: false,
            data: xData.slice().reverse(),
            axisLine: {
              lineStyle: {
                color: "#666"
              }
            }
          },
          yAxis: {
            type: 'value',
            name: this.i18n.percentUnit || '百分比',
            boundaryGap: [0, '100%'],
            min: 0,
            max: 100,
            splitLine: {
              lineStyle: {
                color: "#ddd"
              }
            },
            axisLine: {
              lineStyle: {
                color: "#666"
              }
            }
          },
          dataZoom: [{
            type: 'slider',
            start: 0,
            end: 100,
            handleIcon: 'M10.7,11.9v-1.3H9.3v1.3c-4.9,0.3-8.8,4.4-8.8,9.4c0,5,3.9,9.1,8.8,9.4v1.3h1.3v-1.3c4.9-0.3,8.8-4.4,8.8-9.4C19.5,16.3,15.6,12.2,10.7,11.9z M13.3,24.4H6.7V23h6.6V24.4z M13.3,19.6H6.7v-1.4h6.6V19.6z',
            handleSize: '80%',
            handleStyle: {
              color: '#fff',
              shadowBlur: 3,
              shadowColor: 'rgba(0, 0, 0, 0.6)',
              shadowOffsetX: 2,
              shadowOffsetY: 2
            },
            left: '5%',
            right: '5%',
          }],
          series: [{
            name: this.i18n.memory || '内存',
            type: 'line',
            smooth: true,
            symbol: 'none',
            itemStyle: {
              normal: {
                color: 'rgb(0, 153, 238)'
              }
            },
            data: yData.slice().reverse()
          }]
        };

        memoryView.setOption(option)
      },

      /**
       * 磁盘IO
       */
      // 得到磁盘IO数据
      changeDiskData(newValue, oldValue, ownerInstance, instance) {
        this.diskData = newValue
        this.diskInit()
      },

      // 磁盘IO chart渲染
      diskInit() {
        let _that = this

        diskView = echarts.init(document.getElementById("diskview"))

        let rData = []
        let wData = []
        let xData = []

        for (let i = 0, leng = this.diskData.length; i < leng; i++) {
          rData.push((this.diskData[i].read_bytes / 1024 / 60).toFixed(3))
          wData.push((this.diskData[i].write_bytes / 1024 / 60).toFixed(3))
          xData.push(this.diskData[i].addtime)
        }

        let option = {
          title: {
            text: '',
            left: 'center',
            textStyle: {
              fontSize: 16,
              fontWeight: 400
            }
          },
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'cross'
            },
            formatter: function(config) {
              let _config = config,
                _tips = '';

              for (let i = 0; i < config.length; i++) {
                _tips +=
                  '<span style="display: inline-block;width: 10px;height: 10px;margin-rigth:10px;border-radius: 50%;background: ' +
                  config[i].color + ';"></span>  ' + config[i].seriesName + '：' + parseInt(
                    config[i].data).toFixed(2) + 'KB/s' +
                  (config.length - 1 !== i ? '<br />' : '')
              }

              return _that.i18n.time + "：" + _config[0].axisValue + "<br />" + _tips
            },
          },
          xAxis: {
            type: 'category',
            boundaryGap: false,
            data: xData.slice().reverse(),
            axisLine: {
              lineStyle: {
                color: "#666"
              }
            }
          },
          yAxis: {
            type: 'value',
            name: this.i18n.kbsUnit || '单位:KB/s',
            boundaryGap: [0, '100%'],
            splitLine: {
              lineStyle: {
                color: "#ddd"
              }
            },
            axisLine: {
              lineStyle: {
                color: "#666"
              }
            }
          },
          grid: {
            top: '35px',
            left: '5%',
            right: '5%',
            bottom: '17%',
            containLabel: true
          },
          dataZoom: [{
            handleIcon: 'M10.7,11.9v-1.3H9.3v1.3c-4.9,0.3-8.8,4.4-8.8,9.4c0,5,3.9,9.1,8.8,9.4v1.3h1.3v-1.3c4.9-0.3,8.8-4.4,8.8-9.4C19.5,16.3,15.6,12.2,10.7,11.9z M13.3,24.4H6.7V23h6.6V24.4z M13.3,19.6H6.7v-1.4h6.6V19.6z',
            handleSize: '80%',
            handleStyle: {
              color: '#fff',
              shadowBlur: 3,
              shadowColor: 'rgba(0, 0, 0, 0.6)',
              shadowOffsetX: 2,
              shadowOffsetY: 2
            }
          }],
          series: [{
              name: this.i18n.readBytes || '读取字节数',
              type: 'line',
              smooth: true,
              symbol: 'none',
              itemStyle: {
                normal: {
                  color: 'rgb(255, 70, 131)'
                }
              },
              data: rData.slice().reverse()
            },
            {
              name: this.i18n.writeBytes || '写入字节数',
              type: 'line',
              smooth: true,
              symbol: 'none',
              itemStyle: {
                normal: {
                  color: 'rgba(46, 165, 186, .7)'
                }
              },
              data: wData.slice().reverse()
            }
          ]
        };

        diskView.setOption(option)
      },

      /**
       * 网络IO
       */
      // 得到网络IO数据
      changeNetworkData(newValue, oldValue, ownerInstance, instance) {
        this.networkData = newValue
        this.networkInit()
      },

      // 网络IO chart渲染
      networkInit() {
        let _that = this

        networkView = echarts.init(document.getElementById("networkview"))

        let xData = []
        let yData = []
        let zData = []

        for (let i = 0, leng = this.networkData.length; i < leng; i++) {
          xData.push(this.networkData[i].addtime)
          yData.push(this.networkData[i].up)
          zData.push(this.networkData[i].down)
        }

        let option = {
          title: {
            text: '',
            left: 'center',
            textStyle: {
              fontSize: 16,
              fontWeight: 400
            }
          },
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'cross'
            },
            formatter: function(config) {
              let _config = config,
                _tips = '';

              for (let i = 0; i < config.length; i++) {
                _tips +=
                  '<span style="display: inline-block;width: 10px;height: 10px;margin-rigth:10px;border-radius: 50%;background: ' +
                  config[i].color + ';"></span>  ' + config[i].seriesName + '：' + parseInt(
                    config[i].data).toFixed(2) + 'KB/s' +
                  (config.length - 1 !== i ? '<br />' : '')
              }

              return _that.i18n.time + "：" + _config[0].axisValue + "<br />" + _tips
            }
          },
          xAxis: {
            type: 'category',
            boundaryGap: false,
            data: xData.slice().reverse(),
            axisLine: {
              lineStyle: {
                color: "#666"
              }
            }
          },
          yAxis: {
            type: 'value',
            name: this.i18n.networkUnit || '单位：KB/s',
            boundaryGap: [0, '100%'],
            splitLine: {
              lineStyle: {
                color: "#ddd"
              }
            },
            axisLine: {
              lineStyle: {
                color: "#666"
              }
            }
          },
          grid: {
            top: '35px',
            left: '5%',
            right: '5%',
            bottom: '17%',
            containLabel: true
          },
          dataZoom: [{
            handleIcon: 'M10.7,11.9v-1.3H9.3v1.3c-4.9,0.3-8.8,4.4-8.8,9.4c0,5,3.9,9.1,8.8,9.4v1.3h1.3v-1.3c4.9-0.3,8.8-4.4,8.8-9.4C19.5,16.3,15.6,12.2,10.7,11.9z M13.3,24.4H6.7V23h6.6V24.4z M13.3,19.6H6.7v-1.4h6.6V19.6z',
            handleSize: '80%',
            handleStyle: {
              color: '#fff',
              shadowBlur: 3,
              shadowColor: 'rgba(0, 0, 0, 0.6)',
              shadowOffsetX: 2,
              shadowOffsetY: 2
            }
          }],
          series: [{
              name: this.i18n.up || '上行',
              type: 'line',
              smooth: true,
              symbol: 'none',
              itemStyle: {
                normal: {
                  color: 'rgb(255, 140, 0)'
                }
              },
              data: yData.slice().reverse()
            },
            {
              name: this.i18n.down || '下行',
              type: 'line',
              smooth: true,
              symbol: 'none',
              itemStyle: {
                normal: {
                  color: 'rgb(30, 144, 255)'
                }
              },
              data: zData.slice().reverse()
            }
          ]
        };

        networkView.setOption(option)
      }
    }
  }
</script>

<style lang="scss" scoped>
  /* 开关区域 */
  .switch-section {
    padding: 30rpx 40rpx;
    border-radius: 14rpx;
    border: 2rpx solid var(--color-border, #eee);
    box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);

    .switch-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 28rpx;
      font-weight: 500;
    }
  }

  .chart-container {
    border-radius: 14rpx;
    border: 2rpx solid var(--color-border, #eee);
    box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
    padding: 20rpx;
  }

  /* 图表相关样式 */
  .charts {
    width: 100%;
    height: 600rpx;
  }

  /* 表格样式 */
  .chart-text {
    border-radius: 14rpx;
    border: 2rpx solid var(--color-border, #eee);
    box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
    padding: 20rpx;
  }

  .row {
    display: flex;
    align-items: center;
  }

  .table-th {
    width: 100%;

    > view {
      width: 40%;
      text-align: center;
      padding: 10rpx 0;

      &:first-child {
        width: 20% !important;
      }
    }
  }

  .table-title {
    font-size: 26rpx;

    > view:first-child {
      font-weight: 600;
    }
  }

  .table-info {
    font-size: 28rpx;
    color: #666;

    > view {
      font-size: 26rpx;
    }
  }

  .value-now {
    font-size: 42rpx;
    font-weight: 600;
    padding-bottom: 10rpx;
  }

  .value-last {
    color: #666;
    font-size: 26rpx;
  }
</style>
