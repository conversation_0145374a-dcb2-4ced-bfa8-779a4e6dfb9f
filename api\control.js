import axios from '@/api/request';

const DEFAULT_API_TYPE = import.meta.env.VITE_PANEL_OWNER || 'domestic';

/**
 * @description 获取控制开关状态
 * @returns {Promise<Object>} 控制开关状态
 */
export const getControlStatus = (data) => {
    const url = DEFAULT_API_TYPE === 'domestic' ? '/config?action=SetControl' : '/v2/config?action=SetControl';
    return axios(url, data, "POST", (res) => {
        if (DEFAULT_API_TYPE === 'domestic') {
            return res;
        } else {
            if (data.type != -1) {
                return {
                    status: res?.result,
                    msg: res?.result,
                }
            } else {
                return res;
            }
        }
    });
}

/**
 * @description 获取负载数据
 */
export const getLoadData = (data) => {
    const url = DEFAULT_API_TYPE === 'domestic' ? '/ajax?action=get_load_average' : '/v2/ajax?action=get_load_average';
    return axios(url, data);
}

/**
 * @description 获取CPU和内存数据
 */
export const getCpuAndMemoryData = (data) => {
    const url = DEFAULT_API_TYPE === 'domestic' ? '/ajax?action=GetCpuIo' : '/v2/ajax?action=GetCpuIo';
    return axios(url, data);
}

/**
 * @description 获取网络IO数据
 */
export const getNetworkIoData = (data) => {
    const url = DEFAULT_API_TYPE === 'domestic' ? '/ajax?action=GetNetWorkIo' : '/v2/ajax?action=GetNetWorkIo';
    return axios(url, data);
}

/**
 * @description 获取磁盘IO数据
 */
export const getDiskIoData = (data) => {
    const url = DEFAULT_API_TYPE === 'domestic' ? '/ajax?action=GetDiskIo' : '/v2/ajax?action=GetDiskIo';
    return axios(url, data);
}

