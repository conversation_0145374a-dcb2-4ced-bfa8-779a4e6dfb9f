import { ref, computed, nextTick } from 'vue';
import { getCrontabLog, getCrontabLogPath, startTask, clearCrontabLog } from '@/api/crontab';
import { throttle } from '@/uni_modules/uni-easyinput/components/uni-easyinput/common.js';

// 页面引用 - 导出为独立的 ref，参考 nginxEnv/errorLog
export const pageContainer = ref(null);

/**
 * 日志查看器控制器
 */
export const useLogViewer = () => {
	// 响应式数据
	const logPath = ref('');
	const logSize = ref('');
	const logContent = ref('');
	const activeRange = ref('all');
	const scrollTop = ref(0);
	const loading = ref(false);
	const refreshing = ref(false);
	const clearing = ref(false);
	const executing = ref(false);
	const taskId = ref('');
	const taskName = ref('');
	const showScrollToBottom = ref(false);
	const showClearDialog = ref(false);

	// 自动刷新相关
	const autoRefreshTimer = ref(null);
	const autoRefreshInterval = ref(5000); // 5秒自动刷新
	const enableAutoRefresh = ref(false);

	// 时间范围选项
	const timeRanges = ref([
		{ id: 'all', label: '全部' },
		{ id: '7d', label: '近7天' },
		{ id: '30d', label: '近30天' },
	]);

	// 计算属性 - 简化样式，参考 nginxEnv/errorLog
	const buttonStyle = computed(() => ({
		flex: 1,
		margin: '0 4px',
	}));

	/**
	 * 获取时间范围的时间戳
	 */
	const getTimeRangeTimestamps = (rangeId) => {
		const now = Date.now();
		const nowTimestamp = Math.floor(now / 1000);

		switch (rangeId) {
			case '7d':
				return {
					start_timestamp: nowTimestamp - 604800, // 7天前
					end_timestamp: nowTimestamp,
				};
			case '30d':
				return {
					start_timestamp: nowTimestamp - 2592000, // 30天前
					end_timestamp: nowTimestamp,
				};
			case 'all':
			default:
				return {}; // 不传时间范围参数，获取全部日志
		}
	};

	/**
	 * 初始化页面数据
	 */
	const initPageData = (params = {}) => {
		// 处理传递的参数
		if (params.taskId) {
			taskId.value = params.taskId;
		}
		if (params.taskName) {
			// 对任务名称进行URL解码并限制长度
			const decodedName = decodeURIComponent(params.taskName);
			taskName.value = decodedName.length > 15 ? decodedName.substring(0, 15) + '...' : decodedName;
		}
		if (params.autoRefresh === 'true') {
			enableAutoRefresh.value = true;
		}

		// 如果有任务ID，同时获取日志路径和日志内容
		if (taskId.value) {
			// 并行执行两个请求
			Promise.all([loadLogPath(), loadLogContent()]).catch(() => {
				pageContainer.value?.notify?.error('加载日志数据失败');
			});
		} else {
			// 没有任务ID时显示提示
			pageContainer.value?.notify?.warning('缺少任务ID参数');
			logContent.value = '无法加载日志：缺少任务ID参数';
			return;
		}

		// 启动自动刷新
		if (enableAutoRefresh.value) {
			startAutoRefresh();
		}

		// 自动滚动到底部
		nextTick(() => {
			scrollToBottom();
		});
	};

	/**
	 * 加载日志路径
	 */
	const loadLogPath = async () => {
		try {
			if (!taskId.value) {
				return;
			}

			// 调用获取日志路径的API
			const response = await getCrontabLogPath({
				id: parseInt(taskId.value),
			});

			// 根据实际API响应结构，数据在msg字段中
			if (response && response.status && response.msg && response.msg.log_path) {
				logPath.value = response.msg.log_path;
				logSize.value = response.msg.size || '';
			} else {
				logPath.value = '获取日志路径失败';
				logSize.value = '--';
				pageContainer.value?.notify?.warning('获取日志路径失败');
			}
		} catch (error) {
			logPath.value = '获取日志路径失败';
			logSize.value = '--';
			pageContainer.value?.notify?.error('获取日志路径失败');
		}
	};

	/**
	 * 加载日志内容
	 */
	const loadLogContent = async () => {
		try {
			loading.value = true;

			if (!taskId.value) {
				logContent.value = '无法加载日志：缺少任务ID参数';
				return;
			}

			// 获取时间范围参数
			const timeParams = getTimeRangeTimestamps(activeRange.value);

			// 调用获取日志内容的API
			const response = await getCrontabLog({
				id: parseInt(taskId.value),
				...timeParams,
			});

			if (response && response.status) {
				const content = response.msg || '';
				logContent.value = content || '暂无日志内容';
				// 加载完成后滚动到底部 - 参考 nginxEnv/errorLog
				await scrollToBottom();
			} else {
				const errorMsg = response?.msg || '获取日志失败';
				logContent.value = errorMsg;
				// 只有在非空日志错误时才显示错误弹窗
				if (!errorMsg.includes('当前日志为空')) {
					pageContainer.value?.notify?.error(errorMsg);
				}
			}
		} catch (error) {
			const errorMsg = error.msg || error.message || '加载日志失败';
			logContent.value = '加载日志失败: ' + errorMsg;
			pageContainer.value?.notify?.error(errorMsg);
		} finally {
			loading.value = false;
		}
	};

	/**
	 * 刷新日志 - 参考 nginxEnv/errorLog 实现
	 */
	const refreshLog = async () => {
		if (refreshing.value) {
			return; // 防止重复刷新
		}

		refreshing.value = true;
		try {
			// 并行刷新日志路径和内容
			await Promise.all([loadLogPath(), loadLogContent()]);
			// 只有在手动刷新时才显示成功提示，自动刷新时不显示
			if (!enableAutoRefresh.value) {
				pageContainer.value?.notify?.success('日志刷新成功');
			}
		} catch (error) {
			// 只有在手动刷新时才显示错误提示
			if (!enableAutoRefresh.value) {
				pageContainer.value?.notify?.error('刷新失败，请重试');
			}
		} finally {
			refreshing.value = false;
		}
	};

	/**
	 * 清空日志
	 */
	const clearLogCore = () => {
		showClearDialog.value = true;
	};

	const clearLog = throttle(clearLogCore, 2000, 1);

	/**
	 * 确认清空日志
	 */
	const confirmClearLog = async (close) => {
		clearing.value = true;
		try {
			if (!taskId.value) {
				pageContainer.value?.notify?.error('任务ID不存在');
				return;
			}

			// 调用清空日志的API
			const response = await clearCrontabLog({
				id: parseInt(taskId.value),
			});

			if (response && response.status) {
				// 清空成功
				pageContainer.value?.notify?.success(response.msg || '任务日志已清空!');

				// 清空本地显示内容
				logContent.value = '';
				logSize.value = '0 B';

				// 重新加载日志路径信息
				await loadLogPath();

				// 关闭对话框
				close();
			} else {
				const errorMsg = response?.msg || '清空日志失败';
				pageContainer.value?.notify?.error(errorMsg);
			}
		} catch (error) {
			const errorMsg = error.msg || error.message || '清空失败';
			pageContainer.value?.notify?.error(errorMsg);
		} finally {
			clearing.value = false;
		}
	};

	/**
	 * 执行任务
	 */
	const executeTaskCore = async () => {
		if (!taskId.value) {
			pageContainer.value?.notify?.error('任务ID不存在');
			return;
		}

		if (executing.value) return; // 防止重复执行

		executing.value = true;
		try {
			// 调用执行任务的API
			const response = await startTask({
				id: parseInt(taskId.value),
			});

			if (response.status) {
				pageContainer.value?.notify?.success(response.msg || '执行成功');

				// 执行成功后刷新日志
				setTimeout(() => {
					refreshLog();
				}, 1000);
			} else {
				pageContainer.value?.notify?.error(response.msg || '执行失败');
			}
		} catch (error) {
			pageContainer.value?.notify?.error('执行失败');
		} finally {
			executing.value = false;
		}
	};

	const executeTask = throttle(executeTaskCore, 2000, 1);

	/**
	 * 设置时间范围
	 */
	const setActiveRange = (rangeId) => {
		if (activeRange.value === rangeId) return;

		activeRange.value = rangeId;

		// 根据时间范围重新加载日志
		loadLogContent();
	};

	/**
	 * 滚动到底部 - 参考 nginxEnv/errorLog 实现
	 */
	const scrollToBottom = async () => {
		await nextTick();
		// 设置一个很大的值来确保滚动到底部
		scrollTop.value = 999999;
		showScrollToBottom.value = false;
	};

	/**
	 * 滚动事件处理
	 */
	const onScroll = (e) => {
		const { scrollTop: currentScrollTop, scrollHeight, clientHeight } = e.detail;

		// 判断是否显示滚动到底部按钮
		const isNearBottom = scrollHeight - currentScrollTop - clientHeight < 100;
		showScrollToBottom.value = !isNearBottom && scrollHeight > clientHeight;
	};

	/**
	 * 启动自动刷新
	 */
	const startAutoRefresh = () => {
		if (autoRefreshTimer.value) {
			clearInterval(autoRefreshTimer.value);
		}

		autoRefreshTimer.value = setInterval(async () => {
			if (!loading.value && !refreshing.value) {
				try {
					// 并行刷新日志路径和内容
					await Promise.all([loadLogPath(), loadLogContent()]);
				} catch (error) {
					// 静默处理自动刷新失败
				}
			}
		}, autoRefreshInterval.value);
	};

	/**
	 * 停止自动刷新
	 */
	const stopAutoRefresh = () => {
		if (autoRefreshTimer.value) {
			clearInterval(autoRefreshTimer.value);
			autoRefreshTimer.value = null;
		}
	};

	/**
	 * 清理资源
	 */
	const cleanup = () => {
		stopAutoRefresh();
	};

	return {
		// 响应式数据
		logPath,
		logSize,
		logContent,
		activeRange,
		scrollTop,
		loading,
		refreshing,
		clearing,
		executing,
		taskId,
		taskName,
		timeRanges,
		showScrollToBottom,
		showClearDialog,

		// 计算属性
		buttonStyle,

		// 方法
		initPageData,
		loadLogPath,
		loadLogContent,
		refreshLog,
		clearLog,
		confirmClearLog,
		executeTask,
		setActiveRange,
		scrollToBottom,
		onScroll,
		startAutoRefresh,
		stopAutoRefresh,
		cleanup,
	};
};

export default useLogViewer;
