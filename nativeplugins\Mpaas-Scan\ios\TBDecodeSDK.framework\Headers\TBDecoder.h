//
//  TBDecoder.h
//  sdk_gen3_3g
//
//  Created by <PERSON> on 15/6/24.
//  Copyright (c) 2015年 Taobao.com. All rights reserved.
//

#import <UIKit/UIKit.h>
#import <CoreVideo/CoreVideo.h>

#import <TBDecodeSDK/tbItf.h>
#import <TBDecodeSDK/TBDecodeResult.h>

typedef enum {
    PreDetect_By_Normal = 0,
    PreDetect_By_xNN = 1
} PreDetectTool;

typedef struct {
    float codeProportion;           ///< 疑似二维码的宽度所占比例，用于上层放大 zoom. value ＝ 0 代表没找到
    CGPoint codeCenterInInterest;
    CGPoint sweetPointForExposure;
    PreDetectTool preDetectTool;
    float codeRadius;
} PreDetectResult;

#define MAX_HOT_NUM 10

// 模糊检查
TBEXTERN_C NSString * const TBDecodeBlurNotification;
TBEXTERN_C NSString * const TBDecodeBlurNotificationInfoKey;

TBEXTERN_C NSString * const TBDecodeLogToFileNotification;      ///<  算法层 log ,logToFile
TBEXTERN_C NSString * const TBDecodeLogToFileNotificationTagKey;
TBEXTERN_C NSString * const TBDecodeLogToFileNotificationLogKey;

TBEXTERN_C NSString * const TBDecodeTrackInfoNotification; // 解码信息埋点通知
TBEXTERN_C NSString * const TBDecodeTrackInfoNotificationSeedKey;
TBEXTERN_C NSString * const TBDecodeTrackInfoNotificationUcIdKey;
TBEXTERN_C NSString * const TBDecodeTrackInfoNotificationFlagKey;   //FLAG_DEFAULT = 0, FLAG_NEED_LSB = 1
TBEXTERN_C NSString * const TBDecodeTrackInfoNotificationParamsKey;

@interface TBDecoder : NSObject
/**
 *  解析视频中的码，先将图片根据rect裁剪，然后resample为YES时将剪裁后的图片缩放到360x360再解析，解析时的通用配置参见@TBDecodeConfig
 *
 *  @param buffer   待解析的CVPixelBufferRef
 *  @param rect     解析的图片范围，传入CGRectZero或CGRectNull时解析整张图片
 *  @param resample YES：将裁剪后的图片缩放到360x360，提高识别速度；NO：不做缩放，识别率更高但识别速度下降明显，一般情况下推荐使用YES。
 *  @param type     解析类型，可以是多种解析类型的或值，例如条形码ONED = ( EAN13 | EAN8 | UPCA | UPCE | CODE39 | CODE93 | CODE128 | ITF )
 *  @param PreDetectResult  解出码之前的探测结果
 *
 *  @return 解析结果，解析失败或者没有解析到数据时返回nil
 */
+ (NSArray<TBDecodeResult*> *)decodeCVPixelBuffer:(CVPixelBufferRef)buffer
                                   rectOfInterest:(CGRect)rect
                                         resample:(BOOL)resample
                                             type:(int)type
                                 cameraZoomFactor:(float)cameraZoomFactor
                                  preDetectResult:(PreDetectResult*)preDetectResult;

/**
 *  解析视频中的码，先将图片根据rect裁剪，然后resample为YES时将剪裁后的图片缩放到360x360再解析，解析时的通用配置参见@TBDecodeConfig
 *
 *  @param buffer   待解析的CVPixelBufferRef
 *  @param rect     解析的图片范围，传入CGRectZero或CGRectNull时解析整张图片
 *  @param resample YES：将裁剪后的图片缩放到360x360，提高识别速度；NO：不做缩放，识别率更高但识别速度下降明显，一般情况下推荐使用YES。
 *  @param type     解析类型，可以是多种解析类型的或值，例如条形码ONED = ( EAN13 | EAN8 | UPCA | UPCE | CODE39 | CODE93 | CODE128 | ITF )
 *  @param cameraZoomFactor     相机当前缩放比例，默认可以传 1
 *  @param codeProportion  疑似二维码的宽度所占比例，用于上层放大zoom.value ＝ 0代表没找到
 *
 *  @return 解析结果，解析失败或者没有解析到数据时返回nil
 */
+ (NSArray<TBDecodeResult*> *)decodeCVPixelBuffer:(CVPixelBufferRef)buffer
                         rectOfInterest:(CGRect)rect
                               resample:(BOOL)resample
                                   type:(int)type
                                 cameraZoomFactor:(float)cameraZoomFactor
                                   codeProportion:(float*)codeProportion;

/**
 传入Y通道图片数据， 原图片高宽，解析途中二维码
 
 @param data Y通道图片数据
 @param imgWidth 原图宽度
 @param imgHeight 原图高度
 @param rect 解析的图片范围，传入CGRectZero或CGRectNull时解析整张图片
 @param resample YES：将裁剪后的图片缩放到360x360，提高识别速度；NO：不做缩放，识别率更高但识别速度下降明显，一般情况下推荐使用YES。
 @param type 解析类型，可以是多种解析类型的或值，例如条形码ONED = ( EAN13 | EAN8 | UPCA | UPCE | CODE39 | CODE93 | CODE128 | ITF )
 @param codeProportion 疑似二维码的宽度所占比例，用于上层放大zoom.value ＝ 0代表没找到
 @return 解析结果，解析失败或者没有解析到数据时返回nil
 */
+ (NSArray<TBDecodeResult*> *)decodeBufferData:(NSData *)data
                                    imageWidth:(CGFloat)imgWidth
                                   imageHeight:(CGFloat)imgHeight
                                rectOfInterest:(CGRect)rect
                                      resample:(BOOL)resample
                                          type:(int)type
                                codeProportion:(float*)codeProportion;
/**
 *  解析照片中的二维码和GEN3码，和条形码，目前不支持解隐藏码，建议直接用下面支持返回图片中所有码的接口
 *
 *  @param image 待解析的UIImage
 *
 *  @return 解析结果，解析失败或者没有解析到数据时返回nil
 */
+ (TBDecodeResult *)decodeUIImage:(UIImage *)image;

/**
*  解析照片中的二维码和GEN3码，和条形码，目前不支持解隐藏码
*
*  @param image 待解析的UIImage
*
*  @return 解析结果，数组，解析失败或者没有解析到数据时返回nil
 */
+ (NSArray<TBDecodeResult*> *)overallDecodeUIImage:(UIImage *)image;

///
/// ⚠️⚠️ 由于不支持并发，以下方法都上了锁，等待其他解码器其他方法执行完再执行，所以以下不建议在主线程调用！！！！！！
///

// 算法配置。没有/nil 则默认算法，扫码初始化时调用
+ (void)startCameraWithConfig:(NSDictionary*)config;

///在 startCamera 之后设置，可以设置过滤规则及延迟策略
+ (void)setFilterConfig:(NSString *)filterConfig;

///< 支持动态调整二值化次序，
///< 每一帧用一种二值化：[ [0], [1], [2], [3], [4] ]
///< 或者每一帧用多种二值化:  [ [0,1], [2,3], [3,4] ]
+ (void)setBinarizerOrder:(NSArray *)binarizerOrder;

///<  成功返回 1、失败返回 0
+ (int)registCodeDSL:(NSString *)codeDSL IsAlbum:(BOOL)isAlbum;

+ (int)unregistCodeDSLIsAlbum:(BOOL)isAlbum;

// 获取算法解码过程信息，避免性能问题，建议不要频繁调用
+ (NSDictionary *)getDecodeInfo;

// 内存紧张，需要降级
+ (void)downGradeSdkMemoryAllocate;

// 扫码结束时调用，回异步调用清除数据
+ (void)endCamera;


@end
