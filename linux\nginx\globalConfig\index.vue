<template>
	<page-container ref="pageContainer" :is-back="true" title="全局配置">
		<view class="settings-container">
			<view class="settings-content">
				<view v-for="(group, groupIndex) in filteredSettings" :key="groupIndex" class="settings-group">
					<text class="group-title">{{ group.title }}</text>
					<view class="settings-list">
						<view
							v-for="(item, itemIndex) in group.items"
							:key="itemIndex"
							class="setting-item"
							@click="handleItemClick(item)"
						>
							<view class="setting-info">
								<text class="setting-title">{{ item.title }}</text>
								<text class="setting-description">{{ item.description }}</text>
							</view>
							<view class="setting-controls">
								<view
									class="response-value"
									@click.stop="handleResponseValueClick(item)"
									:class="{ 'has-value': item.status !== undefined && item.status !== '' }"
								>
									{{ item.status }}
									<view class="response-icon" v-if="item.status !== undefined && item.status !== ''">
										<uv-icon name="arrow-down" size="18" color="currentColor"></uv-icon>
									</view>
								</view>
								<view class="toggle-switch" v-if="item.open !== undefined && item.open !== ''">
									<uv-switch
										:model-value="item.open"
										:loading="item.loading"
										@change="handleToggleSwitchChange(item)"
										@click.stop
										activeColor="#20a50a"
										:size="22"
									></uv-switch>
								</view>
								<uv-icon
									v-if="getActionItems(item.type).length > 0"
									name="arrow-right"
									size="18"
									color="#666"
								></uv-icon>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>

		<!-- Action Sheet -->
		<uv-action-sheet
			ref="showActionSheet"
			safeAreaInsetBottom
			:title="currentItem?.title"
			:actions="actionSheetItems"
			@select="handleActionClick"
			cancelText="取消"
			:round="10"
		/>

		<!-- 响应值 picker -->
		<uv-picker
			ref="showResponseValueActionSheet"
			:columns="[responseValueActionSheetItems]"
			:defaultIndex="[currentResponseValue]"
			:keyName="'title'"
			confirmColor="#20a50a"
			activeColor="#20a50a"
			@confirm="handleResponseValueActionClick"
			:round="10"
		/>
	</page-container>
</template>

<script setup>
	import { onLoad } from '@dcloudio/uni-app';
	import PageContainer from '@/components/PageContainer/index.vue';
	import {
		getActionItems,
		currentItem,
		showActionSheet,
		actionSheetItems,
		handleItemClick,
		handleActionClick,
		filteredSettings,
		handleGetGlobalConfig,
		handleResponseValueClick,
		showResponseValueActionSheet,
		responseValueActionSheetItems,
		handleResponseValueActionClick,
		currentResponseValue,
		handleToggleSwitchChange,
		pageContainer,
	} from './useController.js';

	onLoad(() => {
		handleGetGlobalConfig();
	});
</script>

<style lang="scss" scoped>
	// 变量定义
	$primary-color: #20a50a;
	$text-color: var(--text-color-primary);
	$text-secondary: var(--text-color-secondary);
	$border-color: #eaeaea;
	$background-color: var(--bg-color-secondary);
	$white: var(--bg-color);
	$transition-speed: 0.2s;
	$border-radius: 16rpx;
	$spacing-xs: 8rpx;
	$spacing-sm: 16rpx;
	$spacing-md: 32rpx;
	$spacing-lg: 48rpx;

	:deep(.uv-action-sheet__header__title) {
		color: var(--text-color-secondary) !important;
	}

	// 全局样式
	.settings-container {
		display: flex;
		flex-direction: column;
		background-color: $background-color;
		font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans',
			'Helvetica Neue', sans-serif;
		-webkit-tap-highlight-color: transparent;
	}

	// 内容区域
	.settings-content {
		flex: 1;
		overflow-y: auto;
		padding-bottom: $spacing-lg;
		-webkit-overflow-scrolling: touch;
	}

	// 设置组
	.settings-group {
		margin-bottom: $spacing-md;

		.group-title {
			font-size: 28rpx;
			font-weight: 500;
			color: $primary-color;
			padding: $spacing-sm $spacing-md;
			background-color: $background-color;
			position: sticky;
			top: 0;
			z-index: 5;
			display: block;
		}

		.settings-list {
			background-color: $white;
			border-radius: $border-radius;
			margin: 0 $spacing-sm;
			overflow: hidden;
			box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);
		}
	}

	// 设置项
	.setting-item {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: $spacing-md;
		border-bottom: 1rpx solid $border-color;
		background-color: $white;
		transition: background-color $transition-speed;
		position: relative;

		&:last-child {
			border-bottom: none;
		}

		&:active {
			background-color: $white;
		}

		.setting-info {
			flex: 1;
			margin-right: $spacing-md;

			.setting-title {
				font-size: 30rpx;
				font-weight: 500;
				color: $text-color;
				margin-bottom: $spacing-xs;
				display: block;
			}

			.setting-description {
				font-size: 26rpx;
				color: $text-secondary;
				line-height: 1.4;
				display: block;
			}
		}

		.setting-controls {
			display: flex;
			align-items: center;

			.response-value {
				margin-right: $spacing-md;
				font-size: 28rpx;
				color: $text-secondary;
				display: flex;
				align-items: center;

				&.has-value {
					color: $primary-color;
					font-weight: 500;
				}

				.response-icon {
					margin-left: $spacing-xs;
					color: $primary-color;
				}
			}

			.toggle-switch {
				position: relative;
				margin-right: $spacing-md;
			}
		}
	}
</style>
