<template>
	<page-container ref="pageContainer" :is-back="true" title="日志查看">
		<view class="log-container">
			<!-- 日志列表 -->
			<view class="log-list">
				<z-paging
					ref="paging"
					class="mt-180"
					:default-page-size="10"
					use-virtual-list
					:force-close-inner-list="true"
					:auto-hide-loading-after-first-loaded="false"
					:auto-show-system-loading="true"
					@query="queryRules"
					@virtualListChange="virtualListChange"
					@refresherStatusChange="reload"
					:refresher-complete-delay="200"
				>
					<view class="px-30 mt-20">
						<view
							class="log-card"
							v-for="item in logData"
							:id="`zp-id-${item.zp_index}`"
							:key="item.zp_index"
						>
							<!-- 第一行：IP地址和时间 -->
							<view class="log-header">
								<view class="ip-section">
									<text class="ip-address">{{ parseLogInfo(item.log).ip }}</text>
									<text class="location-info" v-if="parseLogInfo(item.log).location">
										({{ parseLogInfo(item.log).location }})
									</text>
								</view>
								<text class="timestamp">{{ item.addtime }}</text>
							</view>

							<!-- 第二行：操作详情 -->
							<view class="log-content">
								<text class="operation-text">{{ parseLogInfo(item.log).operation }}</text>
							</view>
						</view>
					</view>
				</z-paging>
			</view>
		</view>
	</page-container>
</template>

<script setup>
	import { ref, onMounted, computed } from 'vue';
	import PageContainer from '@/components/pageContainer/index.vue';
	import { getNginxFirewallLog } from '@/api/nginx';
	import { $t } from '@/locale/index.js';

	// ===== 常量定义 =====
	const PAGE_SIZE = 20;

	// ===== 响应式状态 =====
	const pageContainer = ref(null);
	const paging = ref(null);

	// ===== 业务数据 =====
	/**
	 * 日志数据列表
	 * @type {import('vue').Ref<Array<{log: string, addtime: string}>>}
	 */
	const logData = ref();

	// ===== 分页回调函数 =====
	const queryRules = async (page, pageSize) => {
		try {
			const res = await loadLogData(page, pageSize);
			paging.value.complete(res);
			paging.value.updateVirtualListRender();
		} catch (error) {
			paging.value.complete([]);
		}
	};

	const virtualListChange = (vList) => {
		logData.value = vList;
	};

	const reload = (reloadType) => {
		if (reloadType === 'complete') {
			pageContainer.value.notify.success($t('common.refreshSuccess'));
		}
	};

	// ===== 工具函数 =====
	/**
	 * 解析日志信息，从 log 字段中提取 IP、位置和操作信息
	 * @param {string} logText - 原始日志文本
	 * @returns {{ip: string, location: string, operation: string}} 解析后的日志信息
	 */
	const parseLogInfo = (logText) => {
		if (!logText || typeof logText !== 'string') {
			return {
				ip: '未知',
				location: '',
				operation: logText || '无操作信息',
			};
		}

		// 匹配格式：IP地址(提供商 国家 省份 城市) 操作内容
		const logPattern = /^(\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})\(([^)]+)\)\s+(.+)$/;
		const match = logText.match(logPattern);

		if (match) {
			const [, ip, locationInfo, operation] = match;
			return {
				ip: ip || '本机',
				location: locationInfo || '',
				operation: operation || '无操作信息',
			};
		}

		// 如果不匹配标准格式，尝试简单提取IP
		const ipPattern = /(\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})/;
		const ipMatch = logText.match(ipPattern);

		return {
			ip: ipMatch ? ipMatch[1] : '本机',
			location: '',
			operation: logText,
		};
	};

	/**
	 * 格式化时间显示
	 * @param {string} timeString - 时间字符串
	 * @returns {string} 格式化后的时间
	 */
	const formatTime = (timeString) => {
		if (!timeString) return '';

		try {
			const date = new Date(timeString);
			const now = new Date();
			const diff = now.getTime() - date.getTime();
			const minutes = Math.floor(diff / (1000 * 60));
			const hours = Math.floor(diff / (1000 * 60 * 60));
			const days = Math.floor(diff / (1000 * 60 * 60 * 24));

			// 根据时间差显示相对时间或绝对时间
			if (minutes < 1) {
				return '刚刚';
			} else if (minutes < 60) {
				return `${minutes}分钟前`;
			} else if (hours < 24) {
				return `${hours}小时前`;
			} else if (days < 7) {
				return `${days}天前`;
			} else {
				// 超过7天显示具体日期
				return timeString.replace(/:\d{2}$/, ''); // 移除秒数
			}
		} catch (error) {
			console.warn('时间格式化失败:', error);
			return timeString;
		}
	};

	// ===== 业务逻辑函数 =====
	/**
	 * 加载日志数据
	 * @param {number} page - 页码
	 * @param {number} pageSize - 每页大小
	 * @returns {Promise<void>}
	 */
	const loadLogData = async (page = 1, pageSize = PAGE_SIZE) => {
		try {
			const res = await getNginxFirewallLog({
				p: page,
				tojs: 'bt_waf.get_gl_table_page',
			});
			return res.data ?? [];
		} catch (error) {
			console.error('加载日志数据失败:', error);
			return [];
		}
	};
</script>

<style lang="scss" scoped>
	// ===== 容器样式 =====
	.log-container {
		padding: 0;
	}

	// ===== 工具栏样式 =====
	.toolbar {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 24rpx;
		background-color: #ffffff;
		border-bottom: 1rpx solid #e5e5e7;
		margin-bottom: 0;
	}

	.toolbar-left {
		display: flex;
		align-items: center;
		gap: 16rpx;
	}

	.log-count,
	.ip-count {
		color: #666666;
		font-size: 26rpx;
		font-family:
			'SF Pro Text',
			-apple-system,
			BlinkMacSystemFont,
			'Helvetica Neue',
			sans-serif;
	}

	.log-count {
		font-weight: 600;
		color: #1a1a1a;
	}

	.toolbar-right {
		display: flex;
		align-items: center;
	}

	.toggle-btn {
		background-color: #f2f2f7;
		color: #007bff;
		border: none;
		border-radius: 20rpx;
		padding: 12rpx 24rpx;
		font-size: 24rpx;
		font-weight: 500;
		font-family:
			'SF Pro Text',
			-apple-system,
			BlinkMacSystemFont,
			'Helvetica Neue',
			sans-serif;
		transition: all 0.2s ease;

		&.active {
			background-color: #007bff;
			color: #ffffff;
		}

		&:active {
			transform: scale(0.95);
		}
	}

	.log-list {
		padding: 24rpx;
	}

	// ===== 卡片样式 =====
	.log-card {
		background-color: var(--dialog-bg-color);
		margin-bottom: 24rpx;
		padding: 32rpx 28rpx;
		border-radius: 16rpx;

		// 加深卡片阴影效果
		box-shadow:
			0 4rpx 12rpx rgba(0, 0, 0, 0.08),
			0 2rpx 6rpx rgba(0, 0, 0, 0.06),
			0 1rpx 4rpx rgba(0, 0, 0, 0.04);

		// 悬浮效果（在支持的平台上）
		transition: all 0.3s ease;

		&:hover {
			transform: translateY(-2rpx);
			box-shadow:
				0 8rpx 24rpx rgba(0, 0, 0, 0.12),
				0 4rpx 12rpx rgba(0, 0, 0, 0.08),
				0 2rpx 8rpx rgba(0, 0, 0, 0.06);
		}

		&:last-child {
			margin-bottom: 0;
		}

		&:active {
			transform: scale(0.98);
		}
	}

	// ===== 卡片内容样式 =====
	.log-header {
		display: flex;
		justify-content: space-between;
		align-items: flex-start;
		margin-bottom: 16rpx;
	}

	.ip-section {
		display: flex;
		flex-direction: column;
		flex: 1;
		gap: 6rpx;
	}

	.ip-address {
		color: var(--text-color-primary);
		font-size: 32rpx;
		font-weight: 600;
		font-family:
			'SF Pro Text',
			-apple-system,
			BlinkMacSystemFont,
			'Helvetica Neue',
			sans-serif;
		letter-spacing: 0.2rpx;
	}

	.location-info {
		color: var(--text-color-secondary);
		font-size: 26rpx;
		font-family:
			'SF Pro Text',
			-apple-system,
			BlinkMacSystemFont,
			'Helvetica Neue',
			sans-serif;
	}

	.timestamp {
		color: var(--text-color-secondary);
		font-size: 24rpx;
		font-weight: 500;
		white-space: nowrap;
		margin-left: 20rpx;
		font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
		background-color: var(--border-color);
		padding: 6rpx 12rpx;
		border-radius: 8rpx;
	}

	.log-content {
		margin-top: 12rpx;
	}

	.operation-text {
		color: var(--text-color-primary);
		font-size: 28rpx;
		line-height: 1.5;
		word-break: break-all;
		font-family:
			'SF Pro Text',
			-apple-system,
			BlinkMacSystemFont,
			'Helvetica Neue',
			sans-serif;
	}

	// ===== 原始日志样式 =====
	.raw-log {
		margin-top: 16rpx;
		padding: 16rpx;
		background-color: #f8f9fa;
		border-radius: 8rpx;
		border-left: 4rpx solid #007bff;
	}

	.raw-log-text {
		color: #495057;
		font-size: 24rpx;
		line-height: 1.4;
		font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
		word-break: break-all;
	}

	// ===== 状态样式 =====
	.empty-state,
	.loading-state {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding: 120rpx 40rpx;
		background-color: #ffffff;
		margin: 24rpx;
		border-radius: 16rpx;
		box-shadow:
			0 4rpx 12rpx rgba(0, 0, 0, 0.08),
			0 2rpx 6rpx rgba(0, 0, 0, 0.06);
	}

	.empty-text,
	.loading-text {
		color: #8e8e93;
		font-size: 28rpx;
		font-family:
			'SF Pro Text',
			-apple-system,
			BlinkMacSystemFont,
			'Helvetica Neue',
			sans-serif;
	}
</style>
