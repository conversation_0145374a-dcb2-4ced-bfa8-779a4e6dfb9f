import { ref, computed } from 'vue';
import {
	getNginxFirewallGlobalConfig,
	setNginxFirewallSiteGlobalConfig,
	setNginxFirewallSiteObjStatus,
	updateMaliciousIp,
	syncMaliciousIpLibrary,
	exportMaliciousIpLibrary,
	getNginxFirewallBatchDownload,
} from '@/api/nginx';

// 页面容器引用
export const pageContainer = ref(null);
export const formPageContainer = ref(null);

/**
 * 根据配置项类型获取对应的操作选项
 * @param {string} type - 配置项类型
 * @returns {string[]} 操作选项列表
 */
export const getActionItems = (type) => {
	switch (type) {
		case 'cc': //cc防御
		case 'cc_tolerate':
			return ['初始规则'];
		case 'get':
		case 'post':
		case 'user-agent':
		case 'cookie':
		case 'other_rule':
			return ['规则', '响应内容'];
		case 'sql_injection':
		case 'xss_injection':
			return ['响应内容'];
		case 'file_upload':
			return ['设置', '响应内容'];
		case 'spider':
			return ['同步', '设置'];
		case 'drop_abroad':
		case 'drop_china':
		case 'share_ip':
			return ['同步'];
		case 'static_code_config':
		case 'method_type':
		case 'ua_white':
		case 'ua_black':
		case 'ip_white':
		case 'ip_black':
		case 'url_white':
		case 'url_black':
		case 'scan':
		case 'set_scan_conf':
		case 'sensitive_text':
		case 'body_intercept':
		case 'api_defense':
		case 'key_words':
		case 'url_cc_param':
		case 'url_request_type_refuse':
		case 'cc_uri_frequency':
		case 'golbls_cc':
		case 'msg_send':
			return ['设置'];
		case 'btmalibrary':
			return ['导出', '同步'];
		default:
			return [];
	}
};

// 当前选中的配置项
export const currentItem = ref(null);
// action sheet 引用
export const showActionSheet = ref(null);
// action sheet 选项
export const actionSheetItems = ref([]);

/**
 * 处理点击配置项
 * @param {Object} item - 点击的配置项
 */
export const handleItemClick = (item) => {
	const actions = getActionItems(item.type);
	if (actions.length > 0) {
		currentItem.value = item;
		actionSheetItems.value = actions.map((action) => ({
			name: action,
			color: 'var(--text-color-primary)',
		}));
		showActionSheet.value.open();
	}
};

/**
 * 处理操作选项点击
 * @param {number} index - 点击的操作选项索引
 */
export const handleActionClick = async (item) => {
	// TODO: 实现具体的操作逻辑
	switch (currentItem.value.type) {
		case 'cc':
			uni.navigateTo({
				url: '/linux/nginx/globalConfig/configForm?type=cc',
				animationType: 'zoom-fade-out',
			});
			break;
		case 'cc_tolerate':
			uni.navigateTo({
				url: '/linux/nginx/globalConfig/configForm?type=cc_tolerate',
				animationType: 'zoom-fade-out',
			});
			break;
		case 'cc_uri_frequency':
			uni.navigateTo({
				url: '/linux/nginx/globalConfig/configForm?type=cc_uri_frequency',
				animationType: 'zoom-fade-out',
			});
			break;
		case 'url_cc_param':
			uni.navigateTo({
				url: '/linux/nginx/globalConfig/configForm?type=url_cc_param',
				animationType: 'zoom-fade-out',
			});
			break;
		case 'golbls_cc':
			uni.navigateTo({
				url: '/linux/nginx/globalConfig/configForm?type=golbls_cc',
				animationType: 'zoom-fade-out',
			});
			break;
		case 'btmalibrary': // 堡塔恶意情报IP库
			if (item.name === '同步') {
				await handleSyncMaliciousIpLibrary();
			} else if (item.name === '导出') {
				await handleExportMaliciousIpLibrary();
			}
			break;
		case 'share_ip': // 恶意IP共享计划
			await handleUpdateMaliciousIp();
			break;
	}
	showActionSheet.value.close();
};

// 搜索查询
export const searchQuery = ref('');

// 设置数据
export const settingsData = ref([
	{
		title: '防CC攻击',
		items: [
			{
				title: 'CC防御',
				description: '防御CC攻击，具体防御参数请到站点配置中调整',
				type: 'cc',
				open: false,
				loading: false,
				status: '',
				link: 'https://www.bt.cn/bbs/thread-57581-1-1.html',
			},
			{
				title: '攻击次数拦截',
				description: '封锁连续的恶意攻击请求，请到站点配置中调整阈值',
				type: 'cc_tolerate',
				open: false,
				loading: false,
				status: '',
				link: 'https://www.bt.cn/bbs/thread-57581-1-1.html',
			},
			// {
			//     title: '攻击告警',
			//     description: '攻击拦截通过飞书、钉钉、企业微信推送告警',
			//     type: 'msg_send',
			//     open: false,
			//     status: ''
			// },
			{
				title: '静态文件防护',
				description:
					'CC防护默认不会防护:JS、CSS、GiF、JPG、JPGE、PNG 这几种静态文件、如果不是被刷图片流量不建议一直开启。',
				type: 'static_cc',
				open: false,
				loading: false,
				status: '',
				link: 'https://www.bt.cn/bbs/thread-135541-1-1.html',
			},
			{
				title: '堡塔恶意情报IP库',
				description: '堡塔恶意IP情报库，获取精准恶意的IP，减少被攻击的风险,企业版免费使用',
				type: 'btmalibrary',
				open: false,
				loading: false,
				status: '',
				isVip: true,
			},
			{
				title: '恶意IP共享计划',
				description: '加入恶意IP共享计划后方可使用"堡塔恶意IP库"',
				type: 'share_ip',
				open: false,
				loading: false,
				status: '',
				link: 'https://www.kancloud.cn/kern123/cloudwaf/3208853',
			},
			{
				title: '单URL CC防御',
				description: '单URL的防御CC规则，优先级高于URL白名单',
				type: 'cc_uri_frequency',
				open: false,
				loading: false,
				status: '',
			},
			{
				title: 'URL增强模式',
				description: '独立设置某个URL验证规则',
				type: 'url_cc_param',
				open: false,
				loading: false,
				status: '',
			},
			{
				title: '人机验证白名单',
				description: '开启人机验证时候需要不验证某些页面时使用',
				type: 'golbls_cc',
				open: false,
				loading: false,
				status: '',
			},
		],
	},
	// {
	//     title: '黑白名单',
	//     items: [
	//         {
	//             title: 'IP白名单',
	//             description: '所有规则对IP白名单无效',
	//             type: 'ip_white',
	//             open: false,
	//             status: '',
	//             link: 'https://www.bt.cn/bbs/thread-57589-1-1.html'
	//         },
	//         {
	//             title: 'IP黑名单',
	//             description: '禁止访问的IP',
	//             type: 'ip_black',
	//             open: false,
	//             status: '',
	//             link: 'https://www.bt.cn/bbs/thread-57589-1-1.html'
	//         },
	//         {
	//             title: 'UA白名单',
	//             description: '初始化阶段User-Agent白名单',
	//             type: 'ua_white',
	//             open: false,
	//             status: '',
	//             link: 'https://www.bt.cn/bbs/thread-57589-1-1.html'
	//         },
	//         {
	//             title: 'UA黑名单',
	//             description: '初始化阶段User-Agent黑名单',
	//             type: 'ua_black',
	//             open: false,
	//             status: '',
	//             link: 'https://www.bt.cn/bbs/thread-57589-1-1.html'
	//         },
	//         {
	//             title: 'URL白名单',
	//             description: '大部分规则对URL白名单无效',
	//             type: 'url_white',
	//             open: false,
	//             status: '',
	//             link: 'https://www.bt.cn/bbs/thread-57589-1-1.html'
	//         },
	//         {
	//             title: 'URL黑名单',
	//             description: '禁止访问的URL地址',
	//             type: 'url_black',
	//             open: false,
	//             status: '',
	//             link: 'https://www.bt.cn/bbs/thread-57589-1-1.html'
	//         }
	//     ]
	// },
	// {
	//     title: '访问过滤',
	//     items: [
	//         {
	//             title: '非浏览器拦截',
	//             description: '在被非浏览器攻击时开启，将应用于所有网站（有CDN的网站可能存在部分误报）',
	//             type: 'is_browser',
	//             open: false,
	//             status: '',
	//             link: 'https://www.bt.cn/bbs/thread-57585-1-1.html'
	//         },
	//         {
	//             title: 'HTTP请求过滤',
	//             description: 'HTTP请求类型过滤/请求头过滤/语义分析开关',
	//             type: 'method_type',
	//             open: false,
	//             status: '',
	//             link: 'https://www.bt.cn/bbs/thread-57585-1-1.html'
	//         },
	//         {
	//             title: '禁止海外访问',
	//             description: '禁止中国大陆以外的地区访问站点',
	//             type: 'drop_abroad',
	//             open: false,
	//             status: '',
	//             link: 'https://www.bt.cn/bbs/thread-57588-1-1.html'
	//         },
	//         {
	//             title: '禁止境内访问',
	//             description: '禁止大陆地区访问',
	//             type: 'drop_china',
	//             open: false,
	//             status: '',
	//             link: 'https://www.bt.cn/bbs/thread-57588-1-1.html'
	//         },
	//         {
	//             title: 'URL请求类型拦截',
	//             description: '单独设置URL拦截请求类型',
	//             type: 'url_request_type_refuse',
	//             open: false,
	//             status: ''
	//         },
	//         {
	//             title: 'API接口防御',
	//             description: 'API接口防御',
	//             type: 'api_defense',
	//             open: false,
	//             status: '',
	//             link: 'https://www.bt.cn/bbs/thread-57590-1-1.html'
	//         },
	//         {
	//             title: '蜘蛛池',
	//             description: '从云端获取蜘蛛池列表',
	//             type: 'spider',
	//             open: false,
	//             status: '',
	//             link: 'https://www.bt.cn/bbs/thread-132479-1-1.html'
	//         }
	//     ]
	// },
	// {
	//     title: '网站漏洞防御',
	//     items: [
	//         {
	//             title: 'SQL注入防御',
	//             description: '检测恶意SQL语句、防止数据库因SQL注入导致的恶意篡改、删库、数据泄露',
	//             type: 'sql_injection',
	//             open: false,
	//             status: '',
	//             link: 'https://www.bt.cn/bbs/thread-57587-1-1.html'
	//         },
	//         {
	//             title: 'XSS防御',
	//             description: '检测XSS语法，防止网页被恶意篡改、用户信息泄露、权限窃取',
	//             type: 'xss_injection',
	//             open: false,
	//             status: '',
	//             link: 'https://www.bt.cn/bbs/thread-57587-1-1.html'
	//         },
	//         {
	//             title: '命令执行拦截',
	//             description: '通过语法模型判断并拦截潜在的危险命令执行，有效避免黑客通过网站入侵服务器',
	//             type: 'rce_injection',
	//             open: false,
	//             status: ''
	//         },
	//         {
	//             title: '弱密码防御',
	//             description: '实时检测弱密码登录并拦截',
	//             type: 'password',
	//             open: false,
	//             status: ''
	//         },
	//         {
	//             title: '敏感信息检测',
	//             description: '拦截报错信息中的敏感信息,包括SQL报错、PHP报错等',
	//             type: 'sensitive_info',
	//             open: false,
	//             status: ''
	//         },
	//         {
	//             title: '恶意文件上传防御',
	//             description: '检测恶意文件上传，防止被上传木马、防止服务器权限丢失',
	//             type: 'file_upload',
	//             open: false,
	//             status: '',
	//             link: 'https://www.bt.cn/bbs/thread-57590-1-1.html'
	//         },
	//         {
	//             title: '恶意下载防御',
	//             description: '检测恶意下载，防止备份文件、源代码、其他关键数据被下载',
	//             type: 'get',
	//             open: false,
	//             status: '',
	//             link: 'https://www.bt.cn/bbs/thread-57587-1-1.html'
	//         },
	//         {
	//             title: '自定义规则拦截',
	//             description: '检测php代码执行、检测目录探测、检测ssrf探测、及自定义检测',
	//             type: 'other_rule',
	//             open: false,
	//             status: '',
	//             link: 'https://www.bt.cn/bbs/thread-57589-1-1.html'
	//         },
	//         {
	//             title: '恶意爬虫防御',
	//             description: '检测恶意爬虫，防止恶意爬虫、访问网站',
	//             type: 'user-agent',
	//             open: false,
	//             status: '',
	//             link: 'https://www.bt.cn/bbs/thread-57587-1-1.html'
	//         },
	//         {
	//             title: '恶意Cookie防御',
	//             description: '检测Cookie中是否包含恶意代码、SQL注入、XSS攻击',
	//             type: 'cookie',
	//             open: false,
	//             status: '',
	//             link: 'https://www.bt.cn/bbs/thread-57587-1-1.html'
	//         },
	//         {
	//             title: '恶意扫描器防御',
	//             description: '检测恶意扫描器，防止各类扫描器、木马连接工具、访问网站',
	//             type: 'scan',
	//             open: false,
	//             status: '',
	//             link: 'https://www.bt.cn/bbs/thread-57587-1-1.html'
	//         },
	//         {
	//             title: '目录扫描防御',
	//             description: '通过访问url 产生的404链接进行防御',
	//             type: 'set_scan_conf',
	//             open: false,
	//             status: ''
	//         },
	//         {
	//             title: '木马查杀',
	//             description: '通过实时访问的文件进行查杀木马,结果在木马隔离箱页面',
	//             type: 'webshell_opens',
	//             open: false,
	//             status: ''
	//         },
	//         {
	//             title: '日志记录',
	//             description: '默认防火墙只记录1M以内的HTTP拦截数据包、如您需要记录更大的数据包则打开此功能',
	//             type: 'http_open',
	//             open: false,
	//             status: '',
	//             link: 'https://www.bt.cn/bbs/thread-57590-1-1.html'
	//         }
	//     ]
	// },
	// {
	//     title: '敏感词',
	//     items: [
	//         {
	//             title: '敏感文字替换',
	//             description: '替换设置的敏感文字',
	//             type: 'sensitive_text',
	//             open: false,
	//             status: '',
	//             link: 'https://www.bt.cn/bbs/thread-57590-1-1.html'
	//         },
	//         {
	//             title: 'URL关键词拦截',
	//             description: '从URL中拦截关键词',
	//             type: 'key_words',
	//             open: false,
	//             status: '',
	//             link: 'https://www.bt.cn/bbs/thread-57590-1-1.html'
	//         },
	//         {
	//             title: '违禁词拦截',
	//             description: '拦截文本中违禁的文字或词组',
	//             type: 'body_intercept',
	//             open: false,
	//             status: '',
	//             link: 'https://www.bt.cn/bbs/thread-57590-1-1.html'
	//         }
	//     ]
	// }
]);

/**
 * 根据搜索查询过滤设置
 */
export const filteredSettings = computed(() => {
	if (!searchQuery.value) {
		return settingsData.value;
	}

	const query = searchQuery.value.toLowerCase();
	return settingsData.value
		.map((group) => {
			const filteredItems = group.items.filter(
				(item) => item.title.toLowerCase().includes(query) || item.description.toLowerCase().includes(query),
			);

			return {
				...group,
				items: filteredItems,
			};
		})
		.filter((group) => group.items.length > 0);
});

/**
 * 导航到详情页
 * @param {Object} item - 要导航的配置项
 */
export const navigateToDetail = (item) => {
	console.log('Navigate to detail for:', item.title);
	// 实际应用中可以使用路由导航
	// router.push({ name: 'setting-detail', params: { id: item.id } })
};

/**
 * 处理获取全局配置
 */
export const handleGetGlobalConfig = async () => {
	uni.showLoading({
		title: '加载中',
		mask: true,
	});
	try {
		const overall_config = await getNginxFirewallGlobalConfig();
		for (let i = 0; i < settingsData.value.length; i++) {
			for (let j = 0; j < settingsData.value[i].items.length; j++) {
				let _type = overall_config[settingsData.value[i].items[j]['type']];
				if (settingsData.value[i].items[j]['ps'] === undefined)
					settingsData.value[i].items[j]['ps'] = _type !== undefined ? _type['ps'] : '';
				settingsData.value[i].items[j]['status'] = _type != undefined ? _type['status'] : '';
				settingsData.value[i].items[j]['open'] = _type != undefined ? _type['open'] : '';
				// 确保每个配置项都有loading状态
				if (settingsData.value[i].items[j]['loading'] === undefined) {
					settingsData.value[i].items[j]['loading'] = false;
				}
			}
		}
		settingsData.value[0].items[1].status = overall_config.cc.status; //攻击次数拦截
		// settingsData.value[3].items[12].open = overall_config.webshell_opens; //Webshell查杀
		// settingsData.value[2].items[0].open = overall_config.is_browser; //非浏览器拦截
		// settingsData.value[2].items[1].status = ''; //HTTP请求类型过滤
		// settingsData.value[2].items[1].open = '';
		// 		that.overall_show_config[13].status = '';            //状态码过滤
		// 		that.overall_show_config[13].open = '';
		// settingsData.value[1].items[2].status = ''; //UA白名单
		// settingsData.value[1].items[2].open = '';
		// settingsData.value[1].items[3].status = ''; //UA黑名单
		// settingsData.value[1].items[3].open = '';
		// settingsData.value[1].items[1].status = overall_config.cc.status; //IP黑名单
		// settingsData.value[1].items[5].status = overall_config.get.status; //URL黑名单
		// 		that.overall_show_config[22].status = that.overall_config.get.status;   //Webshell查杀
		// 		that.overall_show_config[22].open = that.overall_config.webshell_open;
		// settingsData.value[3].items[11].status = overall_config.scan.status; //目录扫描防御
		// settingsData.value[3].items[11].open = overall_config.scan_conf.open; //目录扫描防御
		// settingsData.value[3].items[5].open = overall_config.file_upload.open;
		// settingsData.value[3].items[5].status = overall_config.file_upload.status; //恶意文件上传防御
		// settingsData.value[3].items[13].open = overall_config.http_open; //日志记录
		// settingsData.value[4].items[1].status = ''; //URL关键词拦截
		// settingsData.value[4].items[1].open = '';
		// settingsData.value[2].items[4].status = ''; //URL请求类型拦截
		// settingsData.value[2].items[4].open = '';
		settingsData.value[0].items[6].status = ''; //单URL CC防御
		settingsData.value[0].items[6].open = '';
		// settingsData.value[3].items[7].status = overall_config.other_rule.status; //自定义规则拦截
		// 攻击告警
		// settingsData.value[0].items[2].open = overall_config.msg_send.open;
		// 堡塔恶意情报IP库
		settingsData.value[0].items[3].open = overall_config.btmalibrary;
		// 恶意IP共享计划
		settingsData.value[0].items[4].open = overall_config.share_ip;
		// 静态文件防护
		settingsData.value[0].items[2].open = overall_config.static_cc;
	} catch (error) {
		console.error('获取全局配置失败:', error);
	} finally {
		uni.hideLoading();
	}
};

// 响应值 Action Sheet 引用
export const showResponseValueActionSheet = ref(null);
// 响应值 Action Sheet 选项
export const responseValueActionSheetItems = ref([
	{ title: '200 正常访问', value: 200 },
	{ title: '404 文件不存在', value: 404 },
	{ title: '403 拒绝访问', value: 403 },
	{ title: '444 关闭连接', value: 444 },
	{ title: '500 应用程序错误', value: 500 },
	{ title: '502 连接超时', value: 502 },
	{ title: '503 服务器不可用', value: 503 },
]);
// 当前选中的响应值
export const currentResponseValue = ref(null);
// 当前选中的响应值信息
export const currentResponseInfo = ref(null);

/**
 * 处理响应值点击
 * @param {Object} item - 点击的配置项
 */
export const handleResponseValueClick = (item) => {
	if (item.loading) {
		return;
	}
	currentResponseValue.value = responseValueActionSheetItems.value.findIndex(
		(response) => response.value == item.status,
	);
	currentResponseInfo.value = item;
	showResponseValueActionSheet.value.open();
};

/**
 * 处理响应值 Action Sheet 点击
 * @param {number} index - 点击的操作选项索引
 */
export const handleResponseValueActionClick = async (e) => {
	showResponseValueActionSheet.value.close();
	currentResponseInfo.value.loading = true;
	try {
		let type = currentResponseInfo.value.type;
		if (type === 'cc_tolerate') type = 'cc';
		const res = await setNginxFirewallSiteObjStatus({
			obj: type,
			statusCode: e.value[0].value,
		});
		res.status ? pageContainer.value.notify.success(res.msg) : pageContainer.value.notify.error(res.msg);
		handleGetGlobalConfig();
	} catch (error) {
		console.error('设置响应值失败:', error);
	} finally {
		currentResponseInfo.value.loading = false;
	}
};

/**
 * 处理更新恶意IP共享计划
 */
export const handleUpdateMaliciousIp = async () => {
	uni.showLoading({
		title: '更新中...',
		mask: true,
	});
	try {
		const res = await updateMaliciousIp();
		if (res.status) {
			pageContainer.value.notify.success(res.msg);
		} else {
			pageContainer.value.notify.error(res.msg);
		}
	} catch (error) {
		console.error('更新恶意IP共享计划失败:', error);
		pageContainer.value.notify.error('更新失败，请稍后重试');
	} finally {
		uni.hideLoading();
	}
};

/**
 * 处理同步堡塔恶意情报IP库
 */
export const handleSyncMaliciousIpLibrary = async () => {
	uni.showLoading({
		title: '同步中...',
		mask: true,
	});
	try {
		const res = await syncMaliciousIpLibrary();
		if (res.status) {
			pageContainer.value.notify.success(res.msg);
		} else {
			pageContainer.value.notify.error(res.msg);
		}
	} catch (error) {
		console.error('同步堡塔恶意情报IP库失败:', error);
		pageContainer.value.notify.error('同步失败，请稍后重试');
	} finally {
		uni.hideLoading();
	}
};

/**
 * 保存内容为txt文件并打开
 * @param {string|Object} content - 要保存的内容
 * @param {string} fileName - 文件名
 */
export const saveAndOpenTextFile = async (content, fileName) => {
	try {
		// 将内容转换为字符串
		let textContent = '';
		if (typeof content === 'string') {
			textContent = content;
		} else if (typeof content === 'object') {
			textContent = JSON.stringify(content, null, 2);
		} else {
			textContent = String(content);
		}

		// #ifdef APP-PLUS
		// 移动端：使用HTML5+ io方法保存到手机下载目录并打开
		// 获取手机下载目录
		plus.io.resolveLocalFileSystemURL(
			'_downloads/',
			(entry) => {
				// 创建文件
				entry.getFile(
					fileName,
					{ create: true, exclusive: false },
					(fileEntry) => {
						// 创建文件写入对象
						fileEntry.createWriter(
							(writer) => {
								writer.onwriteend = () => {
									uni.hideLoading();
									// 使用uni.openDocument打开文件
									uni.openDocument({
										filePath: fileEntry.fullPath,
										success: () => {
											pageContainer.value.notify.success('文件已保存并打开');
										},
										fail: (err) => {
											console.error('文件打开失败:', err);
											// 如果openDocument失败，尝试使用plus.runtime.openFile
											plus.runtime.openFile(fileEntry.fullPath, {}, (e) => {
												pageContainer.value.notify.error('文件打开失败');
											});
										},
									});
								};
								writer.onerror = (e) => {
									uni.hideLoading();
									pageContainer.value.notify.error('文件保存失败');
								};
								// 写入文件内容
								writer.write(textContent);
							},
							(e) => {
								console.error('创建文件写入对象失败:', e);
								uni.hideLoading();
								uni.showToast({
									title: '文件保存失败',
									icon: 'none',
								});
							},
						);
					},
					(e) => {
						console.error('创建文件失败:', e);
						uni.hideLoading();
						pageContainer.value.notify.error('文件创建失败');
					},
				);
			},
			(e) => {
				console.error('获取下载目录失败:', e);
				uni.hideLoading();
				pageContainer.value.notify.error('获取下载目录失败');
			},
		);
		// #endif
	} catch (error) {
		console.error('保存和打开文件失败:', error);
		uni.hideLoading();
		pageContainer.value.notify.error('操作失败');
	}
};

/**
 * 处理导出堡塔恶意情报IP库
 */
export const handleExportMaliciousIpLibrary = async () => {
	uni.showLoading({
		title: '导出中...',
		mask: true,
	});
	try {
		const res = await exportMaliciousIpLibrary();
		if (res.status) {
			pageContainer.value.notify.success(res.msg);

			// #ifdef APP-PLUS
			// 获取文件内容并保存为txt文件
			const batchDownloadRes = await getNginxFirewallBatchDownload();

			// 将内容保存为txt文件并打开
			await saveAndOpenTextFile(batchDownloadRes, 'charge_malicious_ip.txt');
			// #endif
		} else {
			pageContainer.value.notify.error(res.msg);
		}
	} catch (error) {
		console.error('导出堡塔恶意情报IP库失败:', error);
		pageContainer.value.notify.error('导出失败，请稍后重试');
	} finally {
		uni.hideLoading();
	}
};

/**
 * 处理开关切换
 * @param {Object} item - 切换的配置项
 */
export const handleToggleSwitchChange = async (item) => {
	// 如果当前正在加载中，则不处理点击事件
	if (item.loading) {
		return;
	}
	// 保存当前状态，以便在请求失败时恢复
	const currentValue = item.open;
	// 先更新UI状态
	item.open = !currentValue;
	// 设置loading状态为true
	item.loading = true;
	try {
		const res = await setNginxFirewallSiteGlobalConfig({
			obj: item.type,
		});
		if (res.status) {
			// API调用成功，保持当前UI状态
			pageContainer.value.notify.success(res.msg);
		} else {
			// API调用失败，恢复原来的状态
			item.open = currentValue;
			pageContainer.value.notify.error(res.msg);
		}
	} catch (error) {
		console.error('设置全局配置失败:', error);
		// 发生错误时，恢复原来的状态
		item.open = currentValue;
		pageContainer.value.notify.error('操作失败，请稍后重试');
	} finally {
		// 无论成功失败，都将loading状态设为false
		item.loading = false;
	}
};
