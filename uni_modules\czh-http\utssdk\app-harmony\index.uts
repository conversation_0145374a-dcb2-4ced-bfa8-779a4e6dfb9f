import { rcp } from '@kit.RemoteCommunicationKit';
import { BusinessError } from '@kit.BasicServicesKit';

// 更强的SSL禁用配置
const securityConfig: rcp.SecurityConfiguration = {
    remoteValidation : 'skip'
};

// 定义结果类型
class HttpResult {
  data: string = '';
  status: number = 0;
}

// 创建全局session
let session: rcp.Session | null = null;

export const useHttp = (httpUrl: string, data: object, method: string = 'POST') => {
    return new Promise<object>((resolve, reject) => {
        try {
            // 确保session存在
            if (!session) {
                session = rcp.createSession({
                    sessionConfiguration: {
                        headers: {
                            'Content-Type': 'application/x-www-form-urlencoded',
                            'Accept': '*/*',
                            'Connection': 'keep-alive'
                        }
                    }
                } as rcp.SessionConfiguration);
            }
            
            // 处理HTTPS URL
            let secureUrl = httpUrl;
            
            // 更新头部确保内容类型正确
            const reqHeaders: rcp.RequestHeaders = {
                'Content-Type': 'application/x-www-form-urlencoded',
                'Accept': '*/*',
                'Connection': 'keep-alive'
            };
            
            // 创建表单数据
            // 将对象转换为表单格式的字符串
            let formData: string[] = [];
            // 使用 Object.entries() 替代 for...in 循环，添加明确的类型声明
            Object.entries(data).forEach((entry: [string, object]) => {
                const key: string = entry[0];
                const value: object = entry[1];
                formData.push(`${key}=${encodeURIComponent(String(value))}`);
            });
            
            const simpleForm: rcp.RequestContent = formData.join('&');
            // 使用表单作为请求内容
            let req = new rcp.Request(secureUrl, method, reqHeaders);
            req.content = simpleForm;
            req.configuration = {
                security: {
                    remoteValidation: 'skip'
                }
            }
            
            session.fetch(req).then((response: rcp.Response) => {
                let result = new HttpResult();
                
                // 获取响应体内容
                if (response.body) {
                    try {
                        // 将ArrayBuffer转换为字符串
                        const bodyBytes: Uint8Array = new Uint8Array(response.body as ArrayBuffer);
                        let bodyContent: string = '';
                        
                        // 使用Uint8Array手动构建UTF-8字符串
                        for (let i = 0; i < bodyBytes.length; i++) {
                            bodyContent += String.fromCharCode(bodyBytes[i]);
                        }
                        
                        if (bodyContent && bodyContent.length > 0) {
                            result.data = bodyContent;
                        } else {
                            result.data = '{}';
                        }
                        result.status = response.statusCode === 200 ? 1 : 0;
                    } catch (parseError) {
                        console.error('Parse error:', parseError);
                        result.data = '{}';
                        result.status = 0;
                    }
                } else {
                    result.data = '{}';
                    result.status = 0;
                }
                
                resolve(result);
            }).catch((err: BusinessError) => {
                let errorResult = new HttpResult();
                errorResult.data = JSON.stringify(err);
                errorResult.status = 0;
                console.error(`err: err code is ${err.code}, err message is ${JSON.stringify(err)}`);
                reject(errorResult);
            });
        } catch (e) {   
            let errorResult = new HttpResult();
            errorResult.data = JSON.stringify(e);
            errorResult.status = 0;
            console.error('Exception in useHttp:', e);
            reject(errorResult);
        }
    });
}