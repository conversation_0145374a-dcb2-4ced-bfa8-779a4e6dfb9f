@font-face {
	font-family: 'iconfont'; /* Project id 4697780 */
	src: url('/static/fonts/iconfont.ttf') format('truetype');
}

.iconfont {
	font-family: 'iconfont' !important;
	font-size: 16px;
	font-style: normal;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}

.icon-servicer:before {
	content: '\e939';
}

.icon-mysql:before {
	content: '\ec6d';
}

.icon-cron:before {
	content: '\e631';
}

.icon-node:before {
	content: '\e649';
}

.icon-nginx:before {
	content: '\e63c';
}

.icon-site-monitor:before {
	content: '\e640';
}

.icon-SSH:before {
	content: '\e7b6';
}

.icon-setting:before {
	content: '\e8b7';
}

.icon-control:before {
	content: '\e69e';
}

.icon-save:before {
	content: '\e611';
}

.icon-compress:before {
	content: '\e903';
}

.icon-folder-filled:before {
	content: '\e744';
}

.icon-video:before {
	content: '\e7c7';
}

.icon-file:before {
	content: '\e671';
}

.icon-image:before {
	content: '\e6cb';
}

.icon-folder:before {
	content: '\e629';
}

.icon-site:before {
	content: '\e628';
}

.icon-safetylogo:before {
	content: '\ec4d';
}

.icon-terminal:before {
	content: '\e87d';
}

.icon-database:before {
	content: '\e940';
}

.icon-insecurity:before {
	content: '\e732';
}

.icon-centos:before {
	content: '\e69a';
}

.icon-ubuntu:before {
	content: '\edd3';
}

.icon-debian:before {
	content: '\eb74';
}

.icon-ip:before {
	content: '\e67d';
}

.icon-safety:before {
	content: '\e600';
}

.icon-toggle:before {
	content: '\e606';
}
