import axios from '@/api/request';

const DEFAULT_API_TYPE = import.meta.env.VITE_PANEL_OWNER || 'domestic';

/**
 * @description 获取nginx防火墙信息
 * @returns { Promise } 返回值
 */
export const getNginxFirewallInfo = () => {
	const url =
		DEFAULT_API_TYPE === 'domestic'
			? '/plugin?action=a&name=btwaf&s=get_total_all'
			: '/v2/plugin?action=a&name=btwaf&s=get_total_all';
	return axios(url);
};

/**
 * @description 全局防火墙开关按钮
 */
export const setNginxFirewall = (data) => {
	const url =
		DEFAULT_API_TYPE === 'domestic'
			? '/plugin?action=a&name=btwaf&s=set_open'
			: '/v2/plugin?action=a&name=btwaf&s=set_open';
	return axios(url, data);
};

/**
 * @description 获取nginx防火墙全局配置
 * @returns { Promise } 返回值
 */
export const getNginxFirewallGlobalConfig = () => {
	const url =
		DEFAULT_API_TYPE === 'domestic'
			? '/plugin?action=a&name=btwaf&s=get_config'
			: '/v2/plugin?action=a&name=btwaf&s=get_config';
	return axios(url);
};

/**
 * @description 获取nginx防火墙日志
 * @returns { Promise } 返回值
 */
export const getNginxFirewallLog = (data) => {
	const url =
		DEFAULT_API_TYPE === 'domestic'
			? '/plugin?action=a&name=btwaf&s=get_gl_logs'
			: '/v2/plugin?action=a&name=btwaf&s=get_gl_logs';
	return axios(url, data);
};

/**
 * @description 获取nginx防火墙站点配置
 * @returns { Promise } 返回值
 */
export const getNginxFirewallSiteConfig = () => {
	const url =
		DEFAULT_API_TYPE === 'domestic'
			? '/plugin?action=a&name=btwaf&s=get_site_config'
			: '/v2/plugin?action=a&name=btwaf&s=get_site_config';
	return axios(url);
};

/**
 * @description 设置nginx防火墙站点全局配置
 * @param { Object } data.obj 请求参数
 * @returns { Promise } 返回值
 */
export const setNginxFirewallSiteGlobalConfig = (data) => {
	const url =
		DEFAULT_API_TYPE === 'domestic'
			? '/plugin?action=a&name=btwaf&s=set_obj_open'
			: '/v2/plugin?action=a&name=btwaf&s=set_obj_open';
	return axios(url, data);
};

/**
 * @description 设置nginx防火墙站点全局配置
 * @param { Object } data.obj 请求参数
 * @returns { Promise } 返回值
 */
export const setNginxFirewallSiteObjOpen = (data) => {
	const url =
		DEFAULT_API_TYPE === 'domestic'
			? '/plugin?action=a&name=btwaf&s=set_site_obj_open'
			: '/v2/plugin?action=a&name=btwaf&s=set_site_obj_open';
	return axios(url, data);
};

/**
 * @description 设置nginx防火墙站点配置
 * @param { string } data.obj 请求参数
 * @param { number } data.statusCode 状态码
 * @returns { Promise } 返回值
 */
export const setNginxFirewallSiteObjStatus = (data) => {
	const url =
		DEFAULT_API_TYPE === 'domestic'
			? '/plugin?action=a&name=btwaf&s=set_obj_status'
			: '/v2/plugin?action=a&name=btwaf&s=set_obj_status';
	return axios(url, data);
};

/**
 * @description 获取城市列表
 * @returns { Promise } 返回值
 */
export const getNginxFirewallSiteCity = () => {
	const url =
		DEFAULT_API_TYPE === 'domestic'
			? '/plugin?action=a&name=btwaf&s=city'
			: '/v2/plugin?action=a&name=btwaf&s=city';
	return axios(url);
};

/**
 * @description 停止CC防护
 * @returns { Promise } 返回值
 */
export const setNginxFirewallStopCcStatus = () => {
	const url =
		DEFAULT_API_TYPE === 'domestic'
			? '/plugin?action=a&name=btwaf&s=stop_cc_status'
			: '/v2/plugin?action=a&name=btwaf&s=stop_cc_status';
	return axios(url);
};

/**
 * @description 开启CC防护
 * @param { Object } data 请求参数
 * @returns { Promise } 返回值
 */
export const setNginxFirewallStartCcStatus = (data) => {
	const url =
		DEFAULT_API_TYPE === 'domestic'
			? '/plugin?action=a&name=btwaf&s=set_cc_conf'
			: '/v2/plugin?action=a&name=btwaf&s=set_cc_conf';
	return axios(url, data);
};

/**
 * @description 设置攻击次数防护
 * @param { Object } data 请求参数
 * @returns { Promise } 返回值
 */
export const setNginxFirewallCcTolerate = (data) => {
	const url =
		DEFAULT_API_TYPE === 'domestic'
			? '/plugin?action=a&name=btwaf&s=set_retry'
			: '/v2/plugin?action=a&name=btwaf&s=set_retry';
	return axios(url, data);
};

/**
 * @description 添加单URL CC防护
 * @param { Object } data 请求参数
 * @returns { Promise } 返回值
 */
export const addNginxFirewallCcUriFrequency = (data) => {
	const url =
		DEFAULT_API_TYPE === 'domestic'
			? '/plugin?action=a&name=btwaf&s=add_cc_uri_frequency'
			: '/v2/plugin?action=a&name=btwaf&s=add_cc_uri_frequency';
	return axios(url, data);
};

/**
 * @description 删除单URL CC防护
 * @param { Object } data 请求参数
 * @param { string } data.url 要删除的URL路径
 * @returns { Promise } 返回值
 */
export const deleteNginxFirewallCcUriFrequency = (data) => {
	const url =
		DEFAULT_API_TYPE === 'domestic'
			? '/plugin?action=a&name=btwaf&s=del_cc_uri_frequency'
			: '/v2/plugin?action=a&name=btwaf&s=del_cc_uri_frequency';
	return axios(url, data);
};

/**
 * @description 添加URL CC参数
 * @param { Object } data 请求参数
 * @param { string } data.uri URL路径
 * @param { Array } data.param 参数数组
 * @param { number } data.type 验证方式类型 (1-跳转验证, 2-验证码验证, 3-人机验证, 4-滑动验证)
 * @param { string } data.stype 匹配方式 ('url' 或 'regular')
 * @returns { Promise } 返回值
 */
export const addNginxFirewallUrlCcParam = (data) => {
	const url =
		DEFAULT_API_TYPE === 'domestic'
			? '/plugin?action=a&name=btwaf&s=add_url_cc_param'
			: '/v2/plugin?action=a&name=btwaf&s=add_url_cc_param';
	return axios(url, data);
};

/**
 * @description 删除URL CC参数
 * @param { Object } data 请求参数
 * @param { string } data.uri 要删除的URL路径
 * @returns { Promise } 返回值
 */
export const deleteNginxFirewallUrlCcParam = (data) => {
	const url =
		DEFAULT_API_TYPE === 'domestic'
			? '/plugin?action=a&name=btwaf&s=del_url_cc_param'
			: '/v2/plugin?action=a&name=btwaf&s=del_url_cc_param';
	return axios(url, data);
};

/**
 * @description 获取人机验证白名单列表
 * @returns { Promise } 返回值
 */
export const getGolblsCcList = () => {
	const url =
		DEFAULT_API_TYPE === 'domestic'
			? '/plugin?action=a&name=btwaf&s=get_golbls_cc'
			: '/v2/plugin?action=a&name=btwaf&s=get_golbls_cc';
	return axios(url);
};

/**
 * @description 添加人机验证白名单
 * @param { Object } data 请求参数
 * @param { string } data.text URL地址
 * @returns { Promise } 返回值
 */
export const addGolblsCc = (data) => {
	const url =
		DEFAULT_API_TYPE === 'domestic'
			? '/plugin?action=a&name=btwaf&s=golbls_cc_zeng'
			: '/v2/plugin?action=a&name=btwaf&s=golbls_cc_zeng';
	return axios(url, data);
};

/**
 * @description 删除人机验证白名单
 * @param { Object } data 请求参数
 * @param { string } data.text URL地址
 * @returns { Promise } 返回值
 */
export const deleteGolblsCc = (data) => {
	const url =
		DEFAULT_API_TYPE === 'domestic'
			? '/plugin?action=a&name=btwaf&s=del_golbls_cc'
			: '/v2/plugin?action=a&name=btwaf&s=del_golbls_cc';
	return axios(url, data);
};

/**
 * @description 更新恶意IP共享计划
 * @returns { Promise } 返回值，包含更新结果信息
 */
export const updateMaliciousIp = () => {
	const url =
		DEFAULT_API_TYPE === 'domestic'
			? '/plugin?action=a&name=btwaf&s=get_update_malicious_ip'
			: '/v2/plugin?action=a&name=btwaf&s=get_update_malicious_ip';
	return axios(url);
};

/**
 * @description 同步堡塔恶意情报IP库
 * @returns { Promise } 返回值，包含同步结果信息
 */
export const syncMaliciousIpLibrary = () => {
	const url =
		DEFAULT_API_TYPE === 'domestic'
			? '/plugin?action=a&name=btwaf&s=get_charge_malicious_ip'
			: '/v2/plugin?action=a&name=btwaf&s=get_charge_malicious_ip';
	return axios(url);
};

/**
 * @description 导出堡塔恶意情报IP库
 * @returns { Promise } 返回值，包含导出结果信息
 */
export const exportMaliciousIpLibrary = () => {
	const url =
		DEFAULT_API_TYPE === 'domestic'
			? '/plugin?action=a&name=btwaf&s=download_charge_malicious_ip'
			: '/v2/plugin?action=a&name=btwaf&s=download_charge_malicious_ip';
	return axios(url);
};

/**
 * @description 获取批量下载状态
 * @returns { Promise } 返回值，包含批量下载状态信息
 */
export const getNginxFirewallBatchDownload = () => {
	const url =
		DEFAULT_API_TYPE === 'domestic'
			? '/download?filename=/www/server/panel/data/charge_malicious_ip.txt'
			: '/v2/download?filename=/www/server/panel/data/charge_malicious_ip.txt';
	return axios(
		url,
		{
			skipDecrypt: true,
		},
		'GET',
	);
};

/**
 * @description 获取 Nginx 错误日志
 * @param { Object } data 请求参数
 * @param { string } data.path 日志文件路径，默认为 '/www/wwwlogs/nginx_error.log'
 * @returns { Promise } 返回值，包含日志内容
 */
export const getNginxErrorLog = (data = {}) => {
	const url = DEFAULT_API_TYPE === 'domestic' ? '/ajax?action=GetOpeLogs' : '/v2/ajax?action=GetOpeLogs';
	const requestData = {
		path: data.path || '/www/wwwlogs/nginx_error.log',
		...data,
	};
	return axios(url, requestData, 'POST');
};

/**
 * @description 获取 Nginx 负载状态
 * @returns { Promise } 返回值，包含负载状态信息
 * 返回结构：
 * {
 *   Reading: "0",     // 读取数
 *   Waiting: "0",     // 等待数
 *   Writing: "1",     // 响应数
 *   accepts: "44",    // 总连接数
 *   active: "1",      // 活动连接数
 *   handled: "44",    // 总握手数
 *   requests: "44",   // 总请求数
 *   worker: 4,        // 工作进程数
 *   workercpu: 0,     // CPU占用
 *   workermen: "153MB" // 内存占用
 * }
 */
export const getNginxStatus = () => {
	const url = DEFAULT_API_TYPE === 'domestic' ? '/ajax?action=GetNginxStatus' : '/v2/ajax?action=GetNginxStatus';
	return axios(url, {}, 'POST');
};

/**
 * @description 切换 Nginx 版本
 * @param { Object } data 请求参数
 * @param { string } data.version 要切换到的版本号
 * @returns { Promise } 返回值，包含切换结果
 */
export const switchNginxVersion = (data) => {
	const url = DEFAULT_API_TYPE === 'domestic' ? '/plugin?action=switch_version' : '/v2/plugin?action=switch_version';
	const requestData = {
		sName: 'nginx',
		version: data.version,
		...data,
	};
	return axios(url, requestData, 'POST');
};

/**
 * @description 获取 Nginx 性能配置
 * @returns { Promise } 返回值，包含配置信息数组
 * 返回结构：数组格式，每个元素包含：
 * {
 *   name: string,        // 配置项名称
 *   value: string,       // 配置项值
 *   unit: string,        // 单位
 *   ps: string,          // 描述
 *   max_num?: number     // 最大值（可选）
 * }
 */
export const getNginxPerformanceConfig = () => {
	const url = DEFAULT_API_TYPE === 'domestic' ? '/config?action=GetNginxValue' : '/v2/config?action=GetNginxValue';
	return axios(url, {}, 'POST');
};

/**
 * @description 保存 Nginx 性能配置
 * @param { Object } data 请求参数
 * @param { string|number } data.worker_processes Worker进程数，可以是'auto'或数字
 * @param { number } data.worker_connections Worker连接数
 * @param { number } data.keepalive_timeout 保持连接超时时间
 * @param { string } data.gzip Gzip开关，'on'或'off'
 * @param { number } data.gzip_min_length Gzip最小长度
 * @param { number } data.gzip_comp_level Gzip压缩率
 * @param { number } data.client_max_body_size 客户端最大请求体大小
 * @param { number } data.server_names_hash_bucket_size 服务器名称哈希桶大小
 * @param { number } data.client_header_buffer_size 客户端请求头缓冲区大小
 * @param { number } data.client_body_buffer_size 客户端请求体缓冲区大小
 * @returns { Promise } 返回值，包含保存结果
 */
export const saveNginxPerformanceConfig = (data) => {
	const url = DEFAULT_API_TYPE === 'domestic' ? '/config?action=SetNginxValue' : '/v2/config?action=SetNginxValue';
	return axios(url, data, 'POST');
};
