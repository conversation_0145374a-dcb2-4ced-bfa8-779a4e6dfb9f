# 翻译脚本使用说明

这是一个基于百度翻译API的自动化翻译脚本，用于将中文翻译文件转换为其他语言。

## 功能特点

- 支持将中文翻译文件转换为英文
- 自动跳过已翻译的内容
- 保持原有的文件结构和格式
- 支持嵌套对象的翻译
- 使用百度翻译API进行翻译

## 使用方法

1. 确保已安装所需依赖：
```bash
npm install axios
```

2. 在`package.json`中添加翻译命令：
```json
{
  "scripts": {
    "translate": "node scripts/translate/index.js"
  }
}
```

3. 运行翻译命令：
```bash
npm run translate
```

## 文件结构

```
project/
├── locale/
│   ├── zh-CN.js    # 中文翻译文件（源文件）
│   └── en.js       # 英文翻译文件（目标文件）
└── scripts/
    └── translate/
        └── index.js # 翻译脚本
```

## 配置说明

在`index.js`中可以修改以下配置：

- `BAIDU_APP_ID`：百度翻译API的APP ID
- `BAIDU_KEY`：百度翻译API的密钥
- `TARGET_LANGUAGES`：目标语言列表，默认为`['en']`

## 注意事项

1. 确保`locale/zh-CN.js`文件存在且格式正确
2. 翻译文件必须使用`export default`导出翻译对象
3. 需要有效的百度翻译API密钥
4. 建议在翻译前备份原始文件

## 错误处理

- 如果翻译过程中出现错误，会在控制台输出错误信息
- 对于翻译失败的内容，会保留原文
- 已翻译的内容会被跳过，不会重复翻译

## 示例

源文件格式（zh-CN.js）：
```javascript
export default {
  common: {
    submit: '提交',
    cancel: '取消'
  },
  user: {
    login: '登录',
    register: '注册'
  }
}
```

翻译后的文件（en.js）：
```javascript
export default {
  common: {
    submit: 'Submit',
    cancel: 'Cancel'
  },
  user: {
    login: 'Login',
    register: 'Register'
  }
}
``` 