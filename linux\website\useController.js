import { ref, nextTick } from 'vue';
import { getSoftStatus as getWebServerStatusApi } from '@/api/config';
import { getDataInfoNew, deleteSite, renameSite, updateRemark, startSite, stopSite } from '@/api/site';
import { triggerVibrate } from '@/utils/common';
import throttle from '@/uni_modules/uv-ui-tools/libs/function/throttle.js';
import { $t } from '@/locale/index.js';

export const websitePaging = ref(null);
export const isInstallWebServer = ref(true);
export const currentSiteType = ref(0); // 0: 运行中, 1: 已停止
export const pageContainer = ref(null);
export const showEditDialog = ref(false);
export const renameEdit = ref('');
export const editType = ref('name'); // name  ps
export const showDeleteDialog = ref(false);
export const showStartDialog = ref(false);
export const showStopDialog = ref(false);

export const getWebServerStatus = async () => {
	const res = await getWebServerStatusApi({ name: 'web' });
	isInstallWebServer.value = res.s_status;
};

export const handleDomain = (item) => {
	throttle(() => {
		const jsonstring = JSON.stringify(item);
		uni.navigateTo({
			url: `/linux/website/manage/index?item=${jsonstring}`,
			animationType: 'zoom-fade-out',
		});
	});
};

export const getSiteList = async (page, pageSize) => {
	try {
		const res = await getDataInfoNew({
			p: page,
			limit: pageSize,
			table: 'sites',
			type: -1,
		});
		// 如果currentSiteType为0，则返回status不为0的数据
		// 如果currentSiteType为1，则返回status为0的数据
		const data = res.data.filter((item) => (currentSiteType.value == 0 ? item.status != 0 : item.status == 0));
		return data;
	} catch (error) {
		console.error(error);
	}
};

// 长按菜单相关
export const showContextMenu = ref(false);
export const activeWebsite = ref(null);
export const activeIndex = ref(-1);
export const menuPosition = ref({
	top: '0px',
	left: '0px',
	class: '',
});
export const clonePosition = ref({
	top: '0px',
	left: '0px',
	width: '0px',
	height: '0px',
});

// 触摸状态管理
export const touchStartTime = ref(0);
export const touchStartPos = ref({ x: 0, y: 0 });
export const isTouchMoved = ref(false);
export const longPressTimer = ref(null);
export const LONG_PRESS_THRESHOLD = 600; // 长按阈值，单位毫秒
export const MOVE_THRESHOLD = 10; // 移动阈值，单位像素

// 菜单高度管理
export const actualMenuHeight = ref(140);
export const showTempMenu = ref(false);

// 测量菜单高度的方法
export const measureMenuHeight = () => {
	// 显示临时测量菜单
	showTempMenu.value = true;

	// 等待临时菜单渲染完成
	nextTick(() => {
		uni.createSelectorQuery()
			.select('.temp-measure-menu')
			.boundingClientRect((rect) => {
				if (rect && rect.height > 0) {
					actualMenuHeight.value = rect.height;
				}
				// 隐藏临时菜单
				showTempMenu.value = false;
			})
			.exec();
	});
};

// 触摸处理相关函数
export const handleTouchStart = (event) => {
	// 清除可能存在的定时器
	if (longPressTimer.value) {
		clearTimeout(longPressTimer.value);
	}

	// 记录触摸开始时间和位置
	touchStartTime.value = Date.now();
	touchStartPos.value = {
		x: event.touches[0].clientX,
		y: event.touches[0].clientY,
	};
	isTouchMoved.value = false;

	// 设置长按定时器
	longPressTimer.value = setTimeout(() => {
		if (!isTouchMoved.value) {
			const index = event.currentTarget.dataset.index;
			const websiteData = JSON.parse(event.currentTarget.dataset.website);
			showFloatingMenu(websiteData, event, index);
		}
	}, LONG_PRESS_THRESHOLD); // 长按阈值，单位毫秒
};

export const handleTouchMove = (event) => {
	if (!touchStartPos.value) return;

	// 计算移动距离
	const moveX = Math.abs(event.touches[0].clientX - touchStartPos.value.x);
	const moveY = Math.abs(event.touches[0].clientY - touchStartPos.value.y);

	// 如果移动超过阈值，标记为已移动并取消长按定时器
	if (moveX > MOVE_THRESHOLD || moveY > MOVE_THRESHOLD) {
		isTouchMoved.value = true;

		if (longPressTimer.value) {
			clearTimeout(longPressTimer.value);
			longPressTimer.value = null;
		}
	}
};

export const handleTouchEnd = (event) => {
	// 清除长按定时器
	if (longPressTimer.value) {
		clearTimeout(longPressTimer.value);
		longPressTimer.value = null;
	}

	// 如果未移动且是短触摸（非长按），则打开文件
	if (!isTouchMoved.value && Date.now() - touchStartTime.value < LONG_PRESS_THRESHOLD) {
		const websiteData = JSON.parse(event.currentTarget.dataset.website);
		handleDomain(websiteData);
	}
};

export const handleTouchCancel = () => {
	// 清除长按定时器
	if (longPressTimer.value) {
		clearTimeout(longPressTimer.value);
		longPressTimer.value = null;
	}
};

// 显示悬浮菜单 - 两阶段定位
export const showFloatingMenu = (website, event, index) => {
	// 触感反馈
	triggerVibrate();

	activeWebsite.value = website;
	activeIndex.value = index;

	// 获取系统信息，用于检测是否会超出屏幕
	const systemInfo = uni.getSystemInfoSync();
	const screenHeight = systemInfo.windowHeight;
	const screenWidth = systemInfo.windowWidth;

	// 获取被长按元素相对于页面的位置
	uni.createSelectorQuery()
		.selectAll('.website-item-container')
		.boundingClientRect((rects) => {
			if (!rects || !rects[index]) return;

			const rect = rects[index];

			// 设置克隆项位置
			clonePosition.value = {
				top: `${rect.top}px`,
				left: `${rect.left}px`,
				width: `${rect.width}px`,
				height: `${rect.height}px`,
			};

			// 预估参数
			const tabbarHeight = 60; // 底部导航栏高度
			const headerHeight = 50; // 顶部标题栏高度
			const menuWidth = 340; // 菜单宽度
			const edgeBuffer = 5; // 边缘安全距离 - 减小以使菜单更贴近克隆项

			// 计算菜单位置
			let menuTop,
				menuLeft,
				menuClass = '';

			// 水平定位 - 居中显示，但保持在屏幕内
			menuLeft = rect.left + rect.width / 2;
			// 防止菜单超出屏幕左侧
			if (menuLeft - menuWidth / 2 < edgeBuffer) {
				menuLeft = menuWidth / 2 + edgeBuffer;
			}
			// 防止菜单超出屏幕右侧
			if (menuLeft + menuWidth / 2 > screenWidth - edgeBuffer) {
				menuLeft = screenWidth - menuWidth / 2 - edgeBuffer;
			}

			// 垂直定位 - 智能判断上方还是下方
			// 计算下方可用空间和上方可用空间
			const spaceBelow = screenHeight - rect.bottom - tabbarHeight;
			const spaceAbove = rect.top - headerHeight;

			// 优先考虑下方显示，如果下方空间不足，再考虑上方
			if (spaceBelow >= actualMenuHeight.value + edgeBuffer) {
				// 下方有足够空间
				menuTop = rect.bottom + edgeBuffer;
				menuClass = 'menu-bottom';
			} else if (spaceAbove >= actualMenuHeight.value + edgeBuffer) {
				// 上方有足够空间 - 菜单底部紧贴克隆项顶部
				menuTop = rect.top - actualMenuHeight.value - edgeBuffer;
				menuClass = 'menu-top menu-position-bottom'; // 添加菜单位置标记
			} else {
				// 两边都没有理想空间，选择空间较大的一边
				if (spaceBelow >= spaceAbove) {
					// 使用下方剩余空间
					menuTop = rect.bottom + edgeBuffer;
					menuClass = 'menu-bottom';
				} else {
					// 使用上方剩余空间 - 菜单底部紧贴克隆项顶部
					menuTop = rect.top - actualMenuHeight.value - edgeBuffer;
					menuClass = 'menu-top menu-position-bottom';
				}
			}

			// 设置菜单初始位置和样式
			menuPosition.value = {
				top: `${menuTop}px`,
				left: `${menuLeft}px`,
				class: menuClass,
			};

			// 显示菜单
			showContextMenu.value = true;

			// 第二阶段：在菜单渲染后微调位置
			nextTick(() => {
				// 获取实际菜单高度
				uni.createSelectorQuery()
					.select('.context-menu')
					.boundingClientRect((menuRect) => {
						if (!menuRect) return;

						const actualMenuHeight = menuRect.height;

						// 如果菜单显示在上方，需要向上偏移菜单高度
						if (menuClass.includes('menu-position-bottom')) {
							// 确保菜单底部与克隆项顶部对齐
							const adjustedTop = rect.top - actualMenuHeight + edgeBuffer;
							menuPosition.value.top = `${adjustedTop}px`;
						}

						// 如果实际菜单宽度与预估不同，调整水平居中
						const actualMenuWidth = menuRect.width;
						if (Math.abs(actualMenuWidth - menuWidth) > 10) {
							// 重新计算水平位置
							let adjustedLeft = rect.left + rect.width / 2;
							if (adjustedLeft - actualMenuWidth / 2 < edgeBuffer) {
								adjustedLeft = actualMenuWidth / 2 + edgeBuffer;
							}
							if (adjustedLeft + actualMenuWidth / 2 > screenWidth - edgeBuffer) {
								adjustedLeft = screenWidth - actualMenuWidth / 2 - edgeBuffer;
							}
							menuPosition.value.left = `${adjustedLeft}px`;
						}
					})
					.exec();
			});
		})
		.exec();
};

// 隐藏悬浮菜单
export const hideContextMenu = () => {
	showContextMenu.value = false;
	activeWebsite.value = null;
	activeIndex.value = -1;
};

// 处理当前修改类型
export const handleEditType = (type) => {
	editType.value = type;
	showEditDialog.value = true;
};

export const confirmEdit = async (close) => {
	if (editType.value === 'name') {
		handleRename(close);
	} else {
		handleRemark(close);
	}
};

const handleRename = async (close) => {
	uni.showLoading({
		title: $t('website.deleting'),
	});
	try {
		const res = await renameSite({
			id: activeWebsite.value.id,
			rname: renameEdit.value,
		});
		if (res.status) {
			close && close();
			renameEdit.value = '';
			pageContainer.value.notify.success(res.msg);
			hideContextMenu();
			websitePaging.value.reload();
			websitePaging.value.updateVirtualListRender();
		} else {
			pageContainer.value.notify.error(res.msg);
		}
	} catch (error) {
		console.error(error);
	} finally {
		uni.hideLoading();
	}
};

const handleRemark = async (close) => {
	uni.showLoading({
		title: $t('website.deleting'),
	});
	try {
		const res = await updateRemark({
			id: activeWebsite.value.id,
			table: 'sites',
			ps: renameEdit.value,
		});
		if (res.status) {
			close && close();
			renameEdit.value = '';
			pageContainer.value.notify.success(res.msg);
			hideContextMenu();
			websitePaging.value.reload();
		} else {
			pageContainer.value.notify.error(res.msg);
		}
	} catch (error) {
		console.error(error);
	} finally {
		uni.hideLoading();
	}
};

export const confirmDeleteFile = async (close) => {
	uni.showLoading({
		title: $t('website.deleting'),
	});
	const res = await deleteSite({
		id: activeWebsite.value.id,
		webname: activeWebsite.value.name,
		ftp: 1,
		database: 1,
		path: 1,
	});
	if (res.status) {
		close && close();
		pageContainer.value.notify.success(res.msg);
		hideContextMenu();
		websitePaging.value.reload();
	} else {
		pageContainer.value.notify.error(res.msg);
	}
	uni.hideLoading();
};

// 启动网站
export const handleStartSite = async (website) => {
	uni.showLoading({
		title: '启动中...',
	});
	try {
		const res = await startSite({
			id: website.id,
			name: website.name,
		});
		if (res.status) {
			pageContainer.value.notify.success(res.msg);
			hideContextMenu();
			websitePaging.value.reload();
		} else {
			pageContainer.value.notify.error(res.msg);
		}
	} catch (error) {
		console.error(error);
		pageContainer.value.notify.error('启动失败');
	} finally {
		uni.hideLoading();
	}
};

// 暂停网站
export const handleStopSite = async (website) => {
	uni.showLoading({
		title: '暂停中...',
	});
	try {
		const res = await stopSite({
			id: website.id,
			name: website.name,
		});
		if (res.status) {
			pageContainer.value.notify.success(res.msg);
			hideContextMenu();
			websitePaging.value.reload();
		} else {
			pageContainer.value.notify.error(res.msg);
		}
	} catch (error) {
		console.error(error);
		pageContainer.value.notify.error('暂停失败');
	} finally {
		uni.hideLoading();
	}
};

// 显示启动确认对话框
export const showStartConfirm = () => {
	showStartDialog.value = true;
};

// 显示暂停确认对话框
export const showStopConfirm = () => {
	showStopDialog.value = true;
};

// 确认启动网站
export const confirmStartSite = async (close) => {
	close && close();
	await handleStartSite(activeWebsite.value);
};

// 确认暂停网站
export const confirmStopSite = async (close) => {
	close && close();
	await handleStopSite(activeWebsite.value);
};
