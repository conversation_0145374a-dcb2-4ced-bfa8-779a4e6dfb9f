<template>
	<view class="container">
		<view class="card">
			<view class="card-content">
				<view class="form-group">
					<view class="form-label-row">
						<text>模式</text>
					</view>
					<picker
						:range="modeList"
						range-key="label"
						:value="formatIndex"
						@change="handleFormatChange"
						class="uni-picker"
					>
						<button class="region-select-button">
							<text>{{ modeList[formatIndex]?.label }}</text>
							<uv-icon name="arrow-down" size="14" color="#666666"></uv-icon>
						</button>
					</picker>
				</view>

				<view class="form-group">
					<view class="form-label-row">
						<text>验证级别</text>
					</view>
					<picker
						:range="verificationList"
						range-key="label"
						:value="verificationIndex"
						@change="handleVerificationChange"
						class="uni-picker"
					>
						<button class="region-select-button">
							<text>{{ verificationList[verificationIndex]?.label }}</text>
							<uv-icon name="arrow-down" size="14" color="#666666"></uv-icon>
						</button>
					</picker>
				</view>

				<view class="accordion">
					<view class="accordion-header" @click="toggleTimeSettings">
						<text>时间设置</text>
						<uv-icon
							name="arrow-down"
							size="14"
							color="#666666"
							:custom-style="{ transform: timeSettingsOpen ? 'rotate(180deg)' : 'rotate(0deg)' }"
						></uv-icon>
					</view>
					<view class="accordion-content" v-show="timeSettingsOpen">
						<view class="form-group">
							<view class="form-row">
								<text>访问时间</text>
								<view class="input-with-unit">
									<input
										type="number"
										v-model="formData.cycle"
										placeholder="请输入访问时间"
										@blur="validateIntegerField('cycle', '访问时间')"
										:class="{ 'input-error': errors.cycle }"
									/>
									<text class="unit">秒</text>
								</view>
							</view>
							<text v-if="errors.cycle" class="error-text">{{ errors.cycle }}</text>
						</view>

						<view class="form-group">
							<view class="form-row">
								<text>访问次数</text>
								<view class="input-with-unit">
									<input
										type="number"
										v-model="formData.limit"
										placeholder="请输入访问次数"
										@blur="validateIntegerField('limit', '访问次数')"
										:class="{ 'input-error': errors.limit }"
									/>
									<text class="unit">次</text>
								</view>
							</view>
							<text v-if="errors.limit" class="error-text">{{ errors.limit }}</text>
						</view>

						<view class="form-group">
							<view class="form-row">
								<text>封锁时间</text>
								<view class="input-with-unit">
									<input
										type="number"
										v-model="formData.endtime"
										placeholder="请输入封锁时间"
										@blur="validateIntegerField('endtime', '封锁时间')"
										:class="{ 'input-error': errors.endtime }"
									/>
									<text class="unit">秒</text>
								</view>
							</view>
							<text v-if="errors.endtime" class="error-text">{{ errors.endtime }}</text>
						</view>
					</view>
				</view>

				<view class="form-group" v-if="formatIndex === 0">
					<view class="form-label-row">
						<text>URL级CC防御</text>
					</view>
					<picker
						:range="urlProtectionList"
						range-key="label"
						:value="urlProtectionIndex"
						@change="handleUrlProtectionChange"
						class="uni-picker"
					>
						<button class="region-select-button">
							<text>{{ urlProtectionList[urlProtectionIndex]?.label }}</text>
							<uv-icon name="arrow-down" size="14" color="#666666"></uv-icon>
						</button>
					</picker>
				</view>

				<view class="form-group">
					<view class="form-label-row">
						<text>单IP防御</text>
					</view>
					<picker
						:range="ipProtectionList"
						range-key="label"
						:value="ipProtectionIndex"
						@change="handleIpProtectionChange"
						class="uni-picker"
					>
						<button class="region-select-button">
							<text>{{ ipProtectionList[ipProtectionIndex]?.label }}</text>
							<uv-icon name="arrow-down" size="14" color="#666666"></uv-icon>
						</button>
					</picker>
				</view>

				<view class="form-group" v-if="formatIndex === 1">
					<view class="form-label-row">
						<text>验证方式</text>
					</view>
					<picker
						:range="verificationTypeList"
						range-key="label"
						:value="verificationTypeIndex"
						@change="handleVerificationTypeChange"
						class="uni-picker"
					>
						<button class="region-select-button">
							<text>{{ verificationTypeList[verificationTypeIndex]?.label }}</text>
							<uv-icon name="arrow-down" size="14" color="#666666"></uv-icon>
						</button>
					</picker>
				</view>

				<view class="form-group" v-if="formatIndex === 0">
					<view class="form-label-row">
						<text>地区人机验证</text>
					</view>
					<button class="region-select-button" @click="showRegionSelector = true">
						<text v-if="selectedRegions.length > 0"> 已选择 {{ selectedRegions.length }} 个地区 </text>
						<text v-else>请选择地区...</text>
						<uv-icon name="arrow-down" size="14" color="#666666"></uv-icon>
					</button>
				</view>

				<view class="info-section">
					<text>• 此处设置的是初始值，新添加站点时将继承。</text>
					<text
						>• 当IP {{ formData.cycle }} 秒内累计请求超过URL超过 {{ formData.limit }} 次触发CC防护封锁IP
						{{ formData.endtime }} 秒</text
					>
					<!-- <text
						>• 地区人机验证：当访问地区与选中的地区匹配时，触发验证。失败次数：15，访问时间：60
						秒，如验证失败：20 次，封锁时间：1800 秒</text
					>
					<text class="policy-link">《具体防御策略》</text> -->
				</view>
			</view>
			<view class="card-footer pt-20">
				<button class="submit-button" @click="handleSubmitForm">全局应用</button>
			</view>
		</view>

		<!-- 地区选择器底部弹出层 -->
		<view class="region-sheet" v-if="showRegionSelector">
			<view class="region-sheet-overlay" @click="showRegionSelector = false"></view>
			<view class="region-sheet-container">
				<view class="region-sheet-header">
					<text class="sheet-title">选择地区</text>
					<text class="sheet-subtitle">请选择需要进行人机验证的地区</text>
				</view>
				<view class="region-sheet-actions">
					<button @click="clearRegions" class="text-button">清空</button>
					<button @click="selectAllRegions" class="text-button">全选</button>
				</view>
				<scroll-view scroll-y class="region-sheet-content">
					<checkbox-group @change="handleRegionChange">
						<view v-for="region in regions" :key="region" class="region-item">
							<label class="checkbox-container">
								<checkbox
									:value="region"
									:checked="selectedRegions.includes(region)"
								/>
								<text>{{ region }}</text>
							</label>
						</view>
					</checkbox-group>
				</scroll-view>
				<view class="region-sheet-footer">
					<button class="submit-button" @click="showRegionSelector = false"> 确定 </button>
				</view>
			</view>
		</view>
	</view>
</template>

<script setup>
	import { ref } from 'vue';
	import {
		formData,
		formatIndex,
		verificationIndex,
		urlProtectionIndex,
		ipProtectionIndex,
		timeSettingsOpen,
		showRegionSelector,
		selectedRegions,
		regions,
		modeList,
		verificationList,
		urlProtectionList,
		ipProtectionList,
		handleFormatChange,
		handleVerificationTypeChange,
		handleUrlProtectionChange,
		handleIpProtectionChange,
		handleVerificationChange,
		handleRegionChange,
		toggleTimeSettings,
		clearRegions,
		selectAllRegions,
		submitForm,
		initializeCcConfig,
		verificationTypeList,
		verificationTypeIndex,
	} from './useCcConfigController.js';

	// 表单验证错误状态
	const errors = ref({
		cycle: '',
		limit: '',
		endtime: ''
	});

	/**
	 * 验证整数字段
	 * @param {string} field - 字段名
	 * @param {string} fieldName - 字段显示名称
	 */
	const validateIntegerField = (field, fieldName) => {
		const value = formData[field];

		// 清除之前的错误
		errors.value[field] = '';

		// 检查是否为空
		if (!value && value !== 0) {
			errors.value[field] = `${fieldName}不能为空`;
			return false;
		}

		// 检查是否为数字
		if (isNaN(value)) {
			errors.value[field] = `${fieldName}必须是数字`;
			return false;
		}

		// 检查是否为整数
		if (!Number.isInteger(Number(value))) {
			errors.value[field] = `${fieldName}必须是整数，不能包含小数点`;
			return false;
		}

		// 检查是否为正数
		if (Number(value) <= 0) {
			errors.value[field] = `${fieldName}必须大于0`;
			return false;
		}

		return true;
	};

	/**
	 * 验证所有表单字段
	 */
	const validateAllFields = () => {
		const cycleValid = validateIntegerField('cycle', '访问时间');
		const limitValid = validateIntegerField('limit', '访问次数');
		const endtimeValid = validateIntegerField('endtime', '封锁时间');

		return cycleValid && limitValid && endtimeValid;
	};

	/**
	 * 增强的提交表单函数
	 */
	const handleSubmitForm = () => {
		if (validateAllFields()) {
			submitForm();
		}
	};

	const showHelp = (type) => {
		// 这里可以实现不同类型的帮助提示
		console.log(`显示${type}的帮助信息`);
	};

	// 初始化组件
	initializeCcConfig();
</script>

<style lang="scss" scoped>
	// 主题色变量
	$primary-color: #20a50a;
	$primary-light: lighten($primary-color, 45%);
	$text-color: var(--text-color-primary);
	$border-color: #ddd;
	$bg-color: var(--dialog-bg-color);
	$muted-color: #666;
	$light-bg: #f5f5f5;

	.container {
		padding: 20rpx;
	}

	.card {
		background: $bg-color;
		border-radius: 20rpx;
		box-shadow: 0 2rpx 16rpx rgba(0, 0, 0, 0.1);
		overflow: hidden;
	}

	.card-title {
		font-size: 32rpx;
		font-weight: 500;
		border-bottom: 1px solid $border-color;
		background-color: $bg-color;
	}

	.card-footer {
		border-top: 1px solid $border-color;
	}

	.form-group {
		margin-bottom: 30rpx;

		&:last-child {
			margin-bottom: 0;
		}
	}

	.form-label-row {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 20rpx;

		text {
			font-size: 28rpx;
			font-weight: 500;
			color: $text-color;
		}
	}

	.form-row {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 20rpx;

		text {
			flex: 1;
			font-size: 28rpx;
			color: $text-color;
		}

		input {
			color: $text-color;
		}
	}

	.select-wrapper {
		position: relative;
		width: 100%;
		height: 80rpx;
		border: 1px solid $border-color;
		border-radius: 12rpx;
		background-color: $bg-color;
		display: flex;
		align-items: center;
		padding: 0 30rpx;

		text {
			flex: 1;
			font-size: 28rpx;
			color: $text-color;
		}

		.select-icon {
			color: $muted-color;
		}
	}

	.input-with-unit {
		display: flex;
		width: 240rpx;

		input {
			width: 100%;
			height: 80rpx;
			padding: 0 20rpx;
			border: 1px solid $border-color;
			border-right: none;
			border-top-left-radius: 12rpx;
			border-bottom-left-radius: 12rpx;
			font-size: 28rpx;

			&.input-error {
				border-color: #e53935;
			}
		}

		.unit {
			display: flex;
			align-items: center;
			padding: 0 20rpx;
			background-color: $light-bg;
			border: 1px solid $border-color;
			border-top-right-radius: 12rpx;
			border-bottom-right-radius: 12rpx;
			font-size: 24rpx;
			color: $muted-color;
		}
	}

	.error-text {
		color: #e53935;
		font-size: 24rpx;
		margin-top: 8rpx;
		display: block;
	}

	.icon-button {
		background: none;
		border: none;
		padding: 10rpx;
		margin-left: 10rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.accordion {
		border: 1px solid $border-color;
		border-radius: 12rpx;
		margin-bottom: 30rpx;
		overflow: hidden;
	}

	.accordion-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 30rpx;
		background-color: $light-bg;

		text {
			font-size: 28rpx;
			font-weight: 500;
		}
	}

	.accordion-icon {
		transition: transform 0.2s ease;

		&.is-open {
			transform: rotate(180deg);
		}
	}

	.accordion-content {
		padding: 30rpx;
		border-top: 1px solid $border-color;
	}

	.region-select-button {
		width: 100%;
		height: 80rpx;
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 0 30rpx;
		background-color: $bg-color;
		border: 1px solid $border-color;
		border-radius: 12rpx;

		text {
			font-size: 28rpx;
			color: $text-color;
		}
	}

	.info-section {
		margin-top: 40rpx;

		text {
			display: block;
			font-size: 24rpx;
			color: $muted-color;
			line-height: 1.6;
			margin-bottom: 16rpx;

			&:last-child {
				margin-bottom: 0;
			}
		}

		.policy-link {
			color: $primary-color;
		}
	}

	.submit-button {
		width: 100%;
		height: 88rpx;
		background-color: $primary-color;
		color: white;
		border: none;
		border-radius: 12rpx;
		font-size: 32rpx;
		font-weight: 500;

		&:active {
			background-color: darken($primary-color, 5%);
		}
	}

	// 地区选择器底部弹出层样式
	.region-sheet {
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		z-index: 100;
	}

	.region-sheet-overlay {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background-color: rgba(0, 0, 0, 0.5);
		z-index: -1;
	}

	.region-sheet-container {
		background-color: $bg-color;
		border-top-left-radius: 24rpx;
		border-top-right-radius: 24rpx;
		max-height: 70vh;
		display: flex;
		flex-direction: column;
		animation: slideUp 0.3s ease;
	}

	@keyframes slideUp {
		from {
			transform: translateY(100%);
		}
		to {
			transform: translateY(0);
		}
	}

	.region-sheet-header {
		padding: 30rpx;
		border-bottom: 1px solid $border-color;

		.sheet-title {
			display: block;
			margin: 0 0 16rpx;
			font-size: 32rpx;
			font-weight: 500;
		}

		.sheet-subtitle {
			display: block;
			font-size: 24rpx;
			color: $muted-color;
		}
	}

	.region-sheet-actions {
		display: flex;
		justify-content: space-between;
		padding: 20rpx 30rpx;
		border-bottom: 1px solid $border-color;
	}

	.text-button {
		background: none;
		border: none;
		padding: 0 30rpx;
		font-size: 28rpx;
		color: $primary-color;

		&:active {
			opacity: 0.8;
		}
	}

	.region-sheet-content {
		padding: 20rpx 30rpx;
		overflow-y: auto;
		flex: 1;
		height: 40vh;
	}

	.region-item {
		padding: 20rpx 0;
	}

	.checkbox-container {
		display: flex;
		align-items: center;
		position: relative;
		padding-left: 60rpx;
		font-size: 28rpx;

		checkbox {
			transform: scale(0.8);
			margin-right: 20rpx;
		}

		text {
			flex: 1;
			color: $text-color;
		}
	}

	.region-sheet-footer {
		padding: 30rpx;
		border-top: 1px solid $border-color;
	}

	// 适配暗黑模式
	@media (prefers-color-scheme: dark) {
		.container {
			background-color: #1a1a1a;
		}

		.card {
			background-color: #2a2a2a;
		}

		.select-wrapper,
		.input-with-unit input,
		.region-select-button {
			background-color: #333;
			border-color: #444;
		}

		text {
			color: #fff;
		}

		.unit,
		.muted-text {
			color: #999;
		}
	}
</style>
