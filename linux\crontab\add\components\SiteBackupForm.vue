<template>
	<view>
		<!-- 执行周期 -->
		<CycleForm
			:formData="formData"
			@update:formData="updateFormData"
			:showProcessLock="true"
			:isEditMode="isEditMode"
			class="mb-32"
		/>

		<!-- 备份网站选择 -->
		<view class="form-group">
			<view class="form-label-row">
				<text>备份网站</text>
			</view>
			<button class="region-select-button" @click="showSitePicker" :disabled="isEditMode" :style="isEditMode ? 'background: var(--bg-color-disabled)' : ''">
				<text>{{ getSiteLabel() || '请选择备份网站' }}</text>
				<uv-icon name="arrow-down" size="14" :color="iconColor"></uv-icon>
			</button>
		</view>

		<!-- 备份到选择 -->
		<view class="form-group">
			<view class="form-label-row">
				<text>备份到</text>
			</view>
			<button class="region-select-button" @click="showBackupToPicker">
				<text>{{ getBackupToLabel() || '请选择备份位置' }}</text>
				<uv-icon name="arrow-down" size="14" :color="iconColor"></uv-icon>
			</button>
		</view>

		<!-- 文件拆分和备份设置 - 仅在非本地备份时显示 -->
		<BackupSettingsForm
			class="mb-32"
			:formData="formData"
			@update:formData="updateFormData"
			:isEnterprise="false"
		/>

		<!-- 保留最新份数 -->
		<view class="form-group">
			<view class="form-label-row">
				<text>* 保留最新</text>
			</view>
			<view class="save-row">
				<uv-input
					v-model="saveValue"
					@blur="validateSaveField"
					placeholder="最大999"
					type="number"
					border="surround"
					:customStyle="{ width: '240rpx', fontSize: '28rpx' }"
				>
					<template v-slot:suffix>
						<text class="save-unit">份</text>
					</template>
				</uv-input>
			</view>
		</view>

		<!-- 备份路径 - 在选择服务器磁盘或开启保留本地备份时显示 -->
		<view class="form-group" v-if="formData.backupTo === 'localhost' || formData.save_local">
			<view class="form-label-row">
				<text>* 备份路径</text>
			</view>
			<uv-input
				v-model="backupPathValue"
				placeholder="/www/backup"
				border="surround"
				:customStyle="{ backgroundColor: 'var(--dialog-bg-color)', fontSize: '28rpx' }"
			>
				<template v-slot:suffix>
					<view class="path-select-button" @click="selectPath">
						<uv-icon name="folder" size="16" color="#ffffff"></uv-icon>
					</view>
				</template>
			</uv-input>
		</view>

		<!-- 排除规则 -->
		<view class="form-group">
			<view class="form-label-row">
				<text>排除规则</text>
			</view>
			<view class="textarea-wrapper">
				<uv-textarea
					v-model="formData.sBody"
					@input="updateField('sBody', $event)"
					placeholder="每行一条规则,目录不能以/结尾，&#10; 示例：data/config.php &#10; static/upload &#10; *.log"
					height="200"
					:textStyle="{ fontSize: '28rpx', color: 'var(--text-color-primary)' }"
					:customStyle="{ backgroundColor: 'var(--dialog-bg-color)' }"
				/>
			</view>
		</view>

		<!-- Picker组件 -->

		<uv-picker ref="sitePicker" :columns="[siteOptions]" keyName="label" @confirm="onSiteConfirm"></uv-picker>

		<uv-picker
			ref="backupToPicker"
			:columns="[backupToOptions]"
			keyName="label"
			@confirm="onBackupToConfirm"
		></uv-picker>
	</view>
</template>

<script setup>
	import { ref, onMounted, onBeforeUnmount, getCurrentInstance, computed, watch } from 'vue';
	import { getCrontabDataList } from '@/api/crontab';
	import { openFileSelector } from '@/stores/fileSelector.js';
	import { getTheme, THEME_TYPE } from '@/hooks/useTheme.js';
	import { truncateText } from '../useController';
	import CycleForm from './CycleForm.vue';
	import BackupSettingsForm from './BackupSettingsForm.vue';

	const { proxy } = getCurrentInstance();

	const props = defineProps({
		formData: {
			type: Object,
			required: true,
		},
		isEditMode: {
			type: Boolean,
			default: false,
		},
	});

	const emit = defineEmits(['update:formData']);

	// 主题相关
	const currentTheme = ref(getTheme());
	const iconColor = computed(() => {
		return currentTheme.value === THEME_TYPE.DARK ? '#cccccc' : '#666666';
	});

	// 保存数量的双向绑定计算属性
	const saveValue = computed({
		get() {
			return props.formData.save;
		},
		set(value) {
			emit('update:formData', { ...props.formData, save: value });
		},
	});

	// 备份路径的双向绑定计算属性
	const backupPathValue = computed({
		get() {
			return props.formData.db_backup_path;
		},
		set(value) {
			emit('update:formData', { ...props.formData, db_backup_path: value });
		},
	});

	// 数据选项
	const siteOptions = ref([{ label: '所有[所有网站]', value: 'ALL' }]);
	const backupToOptions = ref([{ label: '服务器磁盘', value: 'localhost' }]);

	// 更新字段
	const updateField = (field, event) => {
		const value = event.detail?.value || event.target?.value || event;
		emit('update:formData', { ...props.formData, [field]: value });
	};

	// 验证保留份数字段
	const validateSaveField = () => {
		let saveValue = props.formData.save;

		// 如果为空或undefined，设置为默认值
		if (!saveValue || saveValue === '') {
			emit('update:formData', { ...props.formData, save: '3' });
			return;
		}

		// 转换为数字并验证
		const floatValue = parseFloat(saveValue);

		// 检查是否为有效数字
		if (isNaN(floatValue) || floatValue <= 0) {
			emit('update:formData', { ...props.formData, save: '3' });
			return;
		}

		// 四舍五入到整数
		let roundedValue = Math.round(floatValue);

		// 检查最大值限制
		if (roundedValue > 999) {
			roundedValue = 999;
		}

		// 确保最小值为1
		if (roundedValue < 1) {
			roundedValue = 1;
		}

		// 更新为四舍五入后的整数值
		emit('update:formData', { ...props.formData, save: roundedValue.toString() });
	};

	// 更新表单数据
	const updateFormData = (newData) => {
		emit('update:formData', { ...props.formData, ...newData });
	};

	// Picker refs
	const sitePicker = ref(null);
	const backupToPicker = ref(null);

	// 显示选择器
	const showSitePicker = () => {
		sitePicker.value?.open();
	};

	const showBackupToPicker = () => {
		backupToPicker.value?.open();
	};

	// 选择路径
	const selectPath = () => {
		openFileSelector('folder', false, (selectedPaths) => {
			if (selectedPaths && selectedPaths.length > 0) {
				emit('update:formData', { db_backup_path: selectedPaths[0] });
			}
		});
	};

	// 获取显示标签
	const getSiteLabel = () => {
		const option = siteOptions.value.find((item) => item.value === props.formData.siteName);
		return option ? truncateText(option.label) : '';
	};

	const getBackupToLabel = () => {
		const option = backupToOptions.value.find((item) => item.value === props.formData.backupTo);
		return option ? truncateText(option.label) : '';
	};

	// 确认选择

	const onSiteConfirm = (e) => {
		const selectedValue = e.value[0].value;
		emit('update:formData', {
			...props.formData,
			siteName: selectedValue,
			sName: selectedValue, // sName 应该是网站名称，用于 API 参数
			name: `备份网站[ ${selectedValue === 'ALL' ? '所有' : selectedValue} ]`,
		});
	};

	const onBackupToConfirm = (e) => {
		const selectedValue = e.value[0].value;
		emit('update:formData', { ...props.formData, backupTo: selectedValue });
	};

	// 获取数据
	const loadData = async () => {
		try {
			const res = await getCrontabDataList({ type: 'sites' });

			// 设置网站选项
			siteOptions.value = [{ label: '所有[所有网站]', value: 'ALL' }].concat(
				res.data.map((item) => ({
					label: item.ps ? `${item.name}[${item.ps}]` : item.name,
					value: item.name,
				})),
			);

			// 设置备份位置选项 - 只显示已配置的选项
			backupToOptions.value = [{ label: '服务器磁盘', value: 'localhost' }].concat(
				res.orderOpt
					.filter((item) => item.status) // 只保留已配置的项
					.map((item) => ({
						label: item.name,
						value: item.value,
					})),
			);

			// 只在非编辑模式下初始化默认值
			if (!props.isEditMode) {
				const needsDefaults = !props.formData.siteName || props.formData.siteName === '';

				if (needsDefaults) {
					const updateData = {
						...props.formData,
						sType: 'site', // 设置任务类型为网站备份
						siteName: props.formData.siteName || 'ALL',
						sName: props.formData.sName || 'ALL', // sName 是网站名称，用于 API 参数
						backupTo: props.formData.backupTo || 'localhost',
						save: props.formData.save || '3',
						flock: props.formData.flock !== undefined ? props.formData.flock : false,
						name: props.formData.name || '备份网站[ 所有 ]',
						more: props.formData.more || 'ALL', // 设置 more 字段默认值
						// 文件拆分和备份设置默认值
						split_type: props.formData.split_type || '0',
						split_value: props.formData.split_value || 5,
						save_local: props.formData.save_local !== undefined ? props.formData.save_local : 0,
					};

					// 设置备份路径的默认值
					if (!props.formData.db_backup_path) {
						updateData.db_backup_path = '/www/backup';
					}

					emit('update:formData', updateData);
				}
			}
		} catch (error) {
			console.error('加载数据失败:', error);
		}
	};

	// 初始化默认值
	const initializeDefaults = () => {
		// 只在非编辑模式下初始化默认值
		if (!props.isEditMode) {
			// 检查是否需要设置默认值
			const needsDefaults = !props.formData.siteName || props.formData.siteName === '';

			if (needsDefaults) {
				const updateData = {
					...props.formData,
					sType: 'site', // 设置任务类型为网站备份
					siteName: props.formData.siteName || 'ALL',
					sName: props.formData.sName || 'ALL', // sName 是网站名称，用于 API 参数
					backupTo: props.formData.backupTo || 'localhost',
					save: props.formData.save || '3',
					flock: props.formData.flock !== undefined ? props.formData.flock : false,
					name: props.formData.name || '备份网站[ 所有 ]',
					more: props.formData.more || 'ALL', // 设置 more 字段默认值
					db_backup_path: props.formData.db_backup_path || '/www/backup',
					// 文件拆分和备份设置默认值
					split_type: props.formData.split_type || '0',
					split_value: props.formData.split_value || 5,
					save_local: props.formData.save_local !== undefined ? props.formData.save_local : 0,
				};

				emit('update:formData', updateData);
			}
		}
	};

	// 主题变化监听
	const handleThemeChange = (event) => {
		currentTheme.value = event.theme;
	};

	onMounted(() => {
		initializeDefaults();
		loadData();
		// 监听主题变化
		uni.$on('themeChange', handleThemeChange);
	});

	onBeforeUnmount(() => {
		// 移除主题变化监听
		uni.$off('themeChange', handleThemeChange);
	});
</script>

<style lang="scss" scoped>
	@import './form-styles.scss';

	.cycle-container {
		display: flex;
		flex-direction: column;
		gap: 16rpx;
	}

	.cycle-type-row {
		display: flex;
		align-items: center;
		gap: 20rpx;
		flex-wrap: wrap;
	}

	.cycle-select-button {
		flex: 1;
		height: 90rpx !important;
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 0 24rpx;
		background: var(--bg-color);
		border: 1px solid var(--border-color);
		border-radius: 16rpx;
		transition: all 0.3s ease;

		&:active {
			transform: scale(0.98);
			box-shadow: var(--box-shadow);
		}

		text {
			font-size: 26rpx;
			font-weight: 500;
			color: var(--text-color-primary);
		}
	}

	.week-select-button {
		width: 200rpx;
		height: 90rpx !important;
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 0 24rpx;
		background: var(--bg-color);
		border: 1px solid var(--border-color);
		border-radius: 16rpx;
		transition: all 0.3s ease;

		&:active {
			transform: scale(0.98);
			box-shadow: var(--box-shadow);
		}

		text {
			font-size: 26rpx;
			font-weight: 500;
			color: var(--text-color-primary);
		}
	}

	.custom-cycle {
		display: flex;
		flex-direction: column;
		gap: 12rpx;
	}

	.custom-type-row {
		display: flex;
		align-items: center;
	}

	.custom-type-button {
		width: 200rpx;
		height: 80rpx !important;
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 0 20rpx;
		background: var(--bg-color);
		border: 1px solid var(--border-color);
		border-radius: 12rpx;
		transition: all 0.3s ease;

		&:active {
			transform: scale(0.98);
			box-shadow: var(--box-shadow);
		}

		text {
			font-size: 24rpx;
			font-weight: 500;
			color: var(--text-color-primary);
		}
	}

	.week-multi-select,
	.month-multi-select,
	.hour-multi-select,
	.minute-multi-select {
		display: flex;
		align-items: center;
	}

	.multi-select-button {
		flex: 1;
		min-height: 80rpx !important;
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 16rpx 20rpx;
		background: var(--bg-color);
		border: 1px solid var(--border-color);
		border-radius: 12rpx;
		transition: all 0.3s ease;

		&:active {
			transform: scale(0.98);
			box-shadow: var(--box-shadow);
		}

		text {
			font-size: 24rpx;
			font-weight: 500;
			color: var(--text-color-primary);
			line-height: 1.4;
			word-break: break-all;
		}
	}

	.time-inputs {
		display: flex;
		align-items: center;
		gap: 12rpx;
		flex-wrap: wrap;
	}

	.time-input {
		width: 100rpx;
		height: 60rpx;
		border: 1px solid var(--border-color);
		border-radius: 12rpx;
		padding: 0 12rpx;
		text-align: center;
		font-size: 26rpx;
		font-weight: 500;
		color: var(--text-color-primary);
		background: var(--bg-color-secondary);
		box-shadow: var(--box-shadow);
		transition: all 0.3s ease;

		&:focus {
			border-color: var(--primary-color);
			box-shadow: 0 0 0 4rpx rgba(32, 165, 10, 0.1);
			outline: none;
		}
	}

	.time-unit {
		font-size: 26rpx;
		font-weight: 500;
		color: var(--text-color-secondary);
		margin: 0 4rpx;
	}

	.cycle-description {
		padding: 12rpx 16rpx;

		text {
			font-size: 22rpx;
			line-height: 1.4;
			color: var(--text-color-secondary);
		}
	}

	.save-row {
		display: flex;
		align-items: center;
		gap: 12rpx;
	}

	.save-unit {
		font-size: 26rpx;
		font-weight: 600;
		color: var(--text-color-primary);
		padding: 0 8rpx;
	}

	.path-select-button {
		width: 60rpx;
		height: 60rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		background: #20a50a;
		border: none;
		border-radius: 8rpx;
		transition: all 0.3s ease;

		&:active {
			transform: scale(0.95);
		}
	}

	// 重写基础样式，使其更精致
	.form-group {
		margin-bottom: 32rpx;
		padding: 24rpx;
		background: var(--bg-color);
		border-radius: 20rpx;
		box-shadow: var(--box-shadow);
		border: 1px solid var(--border-color);

		&:last-child {
			margin-bottom: 0;
		}
	}

	.form-label-row {
		margin-bottom: 16rpx;

		text {
			font-size: 28rpx;
			font-weight: 600;
			color: var(--text-color-primary);
			position: relative;

			&::before {
				content: '';
				position: absolute;
				left: -12rpx;
				top: 50%;
				transform: translateY(-50%);
				width: 6rpx;
				height: 20rpx;
				background: var(--primary-color);
				border-radius: 3rpx;
			}
		}
	}

	.region-select-button {
		background: var(--bg-color);
		border: 1px solid var(--border-color);
		border-radius: 16rpx;
		transition: all 0.3s ease;

		&:active {
			transform: scale(0.98);
			box-shadow: var(--box-shadow);
		}

		text {
			font-size: 26rpx;
			font-weight: 500;
			color: var(--text-color-primary);
		}
	}

	.form-input {
		border: 1px solid var(--border-color);
		border-radius: 16rpx;
		background: var(--bg-color-secondary);
		font-size: 26rpx;
		font-weight: 500;
		color: var(--text-color-primary);
		transition: all 0.3s ease;

		&:focus {
			border-color: var(--primary-color);
			box-shadow: 0 0 0 4rpx rgba(32, 165, 10, 0.1);
			outline: none;
		}

		&::placeholder {
			color: var(--text-color-tertiary);
			font-weight: 400;
		}
	}

	.form-textarea {
		border: 1px solid var(--border-color);
		border-radius: 16rpx;
		background: var(--bg-color-secondary);
		box-shadow: var(--box-shadow);
		font-size: 26rpx;
		font-weight: 400;
		color: var(--text-color-primary);
		line-height: 1.6;
		transition: all 0.3s ease;

		&:focus {
			border-color: var(--primary-color);
			box-shadow: 0 0 0 4rpx rgba(32, 165, 10, 0.1);
			outline: none;
		}

		&::placeholder {
			color: var(--text-color-tertiary);
			line-height: 1.6;
		}
	}

	.form-row {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-top: 20rpx;
		padding-top: 20rpx;
		border-top: 1px solid var(--border-color);

		text {
			font-size: 26rpx;
			font-weight: 500;
			color: var(--text-color-primary);
		}
	}
</style>
