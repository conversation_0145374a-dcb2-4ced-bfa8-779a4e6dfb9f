<template>
    <view class="theme-switch" @click="handleToggleTheme">
        <view class="switch-container" :class="{ 'is-dark': isDarkMode }">
            <view class="switch-thumb"></view>
            <view class="switch-icon light-icon">🌞</view>
            <view class="switch-icon dark-icon">🌙</view>
        </view>
    </view>
</template>

<script setup>
import { ref, onMounted, computed, onBeforeUnmount, watch } from 'vue';
import { getTheme, toggleTheme, THEME_TYPE } from '@/hooks/useTheme.js';

// 定义组件名称
defineOptions({
    name: 'ThemeSwitch'
});

// 定义props
const props = defineProps({
    isDark: {
        type: Boolean,
        default: null
    }
});

// 定义事件
const emit = defineEmits(['theme-changed', 'click']);

// 响应式状态
const isDarkMode = ref(false);

// 监听外部传入的isDark值变化
watch(() => props.isDark, (newVal) => {
    if (newVal !== null) {
        isDarkMode.value = newVal;
    }
}, { immediate: true });

// 主题相关颜色
const themeColors = computed(() => {
    return isDarkMode.value ? {
        backgroundColor: '#333',
        thumbColor: '#fff',
        shadowColor: 'rgba(255, 255, 255, 0.2)'
    } : {
        backgroundColor: '#f0f0f0',
        thumbColor: '#fff',
        shadowColor: 'rgba(0, 0, 0, 0.2)'
    };
});

// 切换主题
const handleToggleTheme = () => {
    // 如果有外部传入的切换功能，只触发click事件，不执行内部切换
    if (props.isDark !== null) {
        emit('click');
        return;
    }
    
    // 否则使用内部切换逻辑
    const newTheme = toggleTheme();
    isDarkMode.value = newTheme === THEME_TYPE.DARK;

    // 触发主题变更事件
    emit('theme-changed', newTheme);
};

// 主题变化监听
const themeChangeHandler = (event) => {
    // 只有当没有外部控制时才更新内部状态
    if (props.isDark === null) {
        isDarkMode.value = event.theme === THEME_TYPE.DARK;
    }
};

// 更新主题状态
const updateThemeState = () => {
    // 只有当没有外部控制时才更新内部状态
    if (props.isDark === null) {
        const currentTheme = getTheme();
        isDarkMode.value = currentTheme === THEME_TYPE.DARK;
    }
};

// 组件挂载时初始化主题状态并添加监听
onMounted(() => {
    updateThemeState();
    uni.$on('themeChange', themeChangeHandler);
});

// 组件销毁前移除监听
onBeforeUnmount(() => {
    uni.$off('themeChange', themeChangeHandler);
});
</script>

<style lang="scss">
.theme-switch {
    display: inline-block;

    .switch-container {
        position: relative;
        width: 60px;
        height: 30px;
        border-radius: 15px;
        background-color: var(--switch-bg-color, #f0f0f0);
        cursor: pointer;
        transition: all 0.3s ease;
        box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);

        &.is-dark {
            background-color: var(--switch-dark-bg-color, #333);

            .switch-thumb {
                transform: translateX(30px);
            }
        }

        .switch-thumb {
            position: absolute;
            top: 3px;
            left: 3px;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            background-color: var(--switch-thumb-color, #fff);
            box-shadow: 0 1px 3px var(--switch-shadow-color, rgba(0, 0, 0, 0.2));
            transition: transform 0.3s ease;
            z-index: 2;
        }

        .switch-icon {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            font-size: 14px;
            z-index: 1;
        }

        .light-icon {
            left: 8px;
        }

        .dark-icon {
            right: 8px;
        }
    }
}
</style>