import axios from '@/api/request';

/**
 * @fileoverview 监控报告相关的 API 接口
 * @module monitorReport
 */

/**
 * 获取插件信息
 * @param {string} sName - 插件名称
 * @returns {Promise<Object>} 返回插件信息的 Promise
 */
export const getSoftInfo = (sName) =>
    axios('/plugin?action=get_soft_find', { sName }, 'POST');

/**
 * 安装插件
 * @param {Object} params - 插件参数对象
 * @returns {Promise<Object>} 返回安装结果的 Promise
 */
export const installPlugin = (params) =>
    axios('/plugin?action=install_plugin', params, 'POST');

/**
 * 导入插件包
 * @param {Object} params - 插件包参数对象
 * @returns {Promise<Object>} 返回导入结果的 Promise
 */
export const inputPackage = (params) =>
    axios('/plugin?action=input_package', params, 'POST');

/**
 * 获取站点总数列表（自定义）
 * @param {Object} params - 查询参数对象
 * @returns {Promise<Object>} 返回站点列表的 Promise
 */
export const getSiteTotalListCustom = (params) =>
    axios('/monitor/get_site_total_list_custom.json', params, 'POST');

/**
 * 获取站点名称列表
 * @returns {Promise<Object>} 返回站点名称列表的 Promise
 */
export const getSiteNames = () =>
    axios('/monitor/get_site_names.json', {}, 'POST');

/**
 * 设置默认站点
 * @param {Object} params - 设置参数对象
 * @returns {Promise<Object>} 返回设置结果的 Promise
 */
export const setDefaultSite = (params) =>
    axios('/monitor/set_default_site.json', params, 'POST');

/**
 * 获取站点列表
 * @param {Object} params - 查询参数对象
 * @returns {Promise<Object>} 返回站点列表的 Promise
 */
export const getSiteList = (params) =>
    axios('/monitor/get_site_list.json', params, 'POST');

/**
 * 获取站点配置信息
 * @param {Object} params - 查询参数对象
 * @returns {Promise<Object>} 返回站点配置信息的 Promise
 */
export const getSiteConfigInfo = (params) =>
    axios('/monitor/get_site_config_info.json', params, 'POST');

/**
 * 设置站点配置
 * @param {Object} params - 设置参数对象
 * @returns {Promise<Object>} 返回设置结果的 Promise
 */
export const setSiteConfig = (params) =>
    axios('/monitor/set_site_config.json', params, 'POST');

/**
 * 获取站点IP统计信息
 * @param {Object} params - 查询参数对象
 * @returns {Promise<Object>} 返回站点IP统计信息的 Promise
 */
export const getSiteIpTotal = (params) =>
    axios('/monitor/get_site_ip_total.json', params, 'POST');

/**
 * 解封IP
 * @param {Object} params - 查询参数对象
 * @returns {Promise<Object>} 返回解封IP的 Promise
 */
export const unshieldIp = (params) =>
    axios('/monitor/unshield_ip.json', params, 'POST');

/**
 * 封禁IP
 * @param {Object} params - 查询参数对象
 * @returns {Promise<Object>} 返回封禁IP的 Promise
 */
export const shieldIp = (params) =>
    axios('/monitor/shield_ip.json', params, 'POST');

/**
 * 获取站点蜘蛛统计信息
 * @param {Object} params - 查询参数对象
 * @returns {Promise<Object>} 返回站点蜘蛛统计信息的 Promise
 */
export const getSiteSpiderTotalByNDay = (params) =>
    axios('/monitor/get_site_spider_total_by_nday.json', params, 'POST');