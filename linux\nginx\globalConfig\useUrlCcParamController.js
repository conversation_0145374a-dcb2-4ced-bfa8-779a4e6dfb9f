import { ref, reactive, computed } from 'vue';
import {
	getNginxFirewallGlobalConfig,
	addNginxFirewallUrlCcParam,
	deleteNginxFirewallUrlCcParam,
} from '@/api/nginx.js';
import { formPageContainer } from './useController.js';

/**
 * URL CC 参数控制器
 * 管理 URL CC 参数配置的状态和逻辑
 */

// Picker数据
export const matchModes = ['URL', '正则'];
export const verifyModes = ['跳转验证', '验证码验证', '人机验证', '滑动验证'];

// State
export const activeTab = ref('list');
export const urlItems = ref([]);

export const newItem = reactive({
	url: '',
	params: '',
	matchMode: 'URL',
	verifyMode: '跳转验证',
});

// 表单验证错误状态
export const errors = ref({
	url: '',
	params: '',
});

// 计算picker当前索引
export const matchModeIndex = computed(() => matchModes.indexOf(newItem.matchMode));
export const verifyModeIndex = computed(() => verifyModes.indexOf(newItem.verifyMode));

/**
 * 将 API 返回的数据转换为组件需要的格式
 * @param {Object} apiData - API 返回的 url_cc_param 数据
 * @returns {Array} 转换后的数组格式数据
 */
const transformApiDataToList = (apiData) => {
	if (!apiData || typeof apiData !== 'object') {
		return [];
	}

	const result = [];
	try {
		Object.entries(apiData).forEach(([url, config], index) => {
			// 确保 url 和 config 都有效
			if (!url || !config || typeof config !== 'object') {
				console.warn('跳过无效的数据项:', { url, config });
				return; // 跳过这一项
			}

			// 根据旧版代码的数据结构进行转换
			const item = {
				id: (index + 1).toString(),
				url: url,
				params: formatParamsDisplay(config.param),
				// 匹配方式：stype 为 'regular' 表示正则，否则为 URL
				matchMode: config.stype === 'regular' ? '正则' : 'URL',
				// 验证方式：根据 type 数值转换
				verifyMode: getVerifyModeText(config.type),
			};

			// 确保生成的项目有必要的属性
			if (item.url && item.id) {
				result.push(item);
			} else {
				console.warn('跳过无效的转换结果:', item);
			}
		});
	} catch (error) {
		console.error('转换数据时发生错误:', error);
		return [];
	}

	return result;
};

/**
 * 根据 type 数值获取验证方式文本
 * @param {number} type - 验证方式类型
 * @returns {string} 验证方式文本
 */
const getVerifyModeText = (type) => {
	switch (parseInt(type)) {
		case 1:
			return '跳转验证';
		case 2:
			return '验证码验证';
		case 3:
			return '人机验证';
		case 4:
			return '滑动验证';
		default:
			return '跳转验证';
	}
};

/**
 * 根据验证方式文本获取 type 数值
 * @param {string} verifyModeText - 验证方式文本
 * @returns {number} 验证方式类型
 */
const getVerifyModeType = (verifyModeText) => {
	switch (verifyModeText) {
		case '跳转验证':
			return 1;
		case '验证码验证':
			return 2;
		case '人机验证':
			return 3;
		case '滑动验证':
			return 4;
		default:
			return 1;
	}
};

/**
 * 根据匹配方式文本获取 stype 值
 * @param {string} matchModeText - 匹配方式文本
 * @returns {string} stype 值
 */
const getMatchModeType = (matchModeText) => {
	switch (matchModeText) {
		case '正则':
			return 'regular';
		case 'URL':
		default:
			return 'url';
	}
};

/**
 * 处理参数数组，转换为逗号分隔的字符串用于显示
 * @param {string|Array} params - 参数数据
 * @returns {string} 逗号分隔的参数字符串
 */
const formatParamsDisplay = (params) => {
	if (!params) {
		return '';
	}

	try {
		// 如果是字符串，尝试解析为数组
		if (typeof params === 'string') {
			// 如果已经是逗号分隔的字符串，直接返回
			if (params.includes(',') && !params.includes('[')) {
				return params;
			}
			// 尝试解析 JSON 数组字符串
			const parsed = JSON.parse(params);
			if (Array.isArray(parsed)) {
				return parsed.join(',');
			}
			// 如果不是数组，直接返回原字符串
			return params;
		}

		// 如果是数组，用逗号连接
		if (Array.isArray(params)) {
			return params.join(',');
		}

		// 其他情况转为字符串
		return String(params);
	} catch (error) {
		// 解析失败时，直接返回原始值的字符串形式
		return String(params);
	}
};

/**
 * 获取 URL CC 参数数据
 */
export const getUrlCcParamData = async () => {
	try {
		const res = await getNginxFirewallGlobalConfig();

		// 转换数据格式
		if (res && res.url_cc_param) {
			const transformedData = transformApiDataToList(res.url_cc_param);
			urlItems.value = transformedData;
		} else {
			urlItems.value = [];
		}
	} catch (error) {
		console.error('获取 URL CC 参数数据失败:', error);
		urlItems.value = [];

		// 显示错误提示
		try {
			if (formPageContainer.value && formPageContainer.value.notify) {
				formPageContainer.value.notify.error('获取数据失败，请稍后重试');
			} else {
				// 如果 formPageContainer 不可用，使用 uni.showToast
				uni.showToast({
					title: '获取数据失败',
					icon: 'error',
					duration: 2000,
				});
			}
		} catch (notifyError) {
			// 静默处理提示错误
			console.error('显示错误提示失败:', notifyError);
		}
	}
};

// Picker事件处理
export const onMatchModeChange = (e) => {
	newItem.matchMode = matchModes[e.detail.value];
};

export const onVerifyModeChange = (e) => {
	newItem.verifyMode = verifyModes[e.detail.value];
};

/**
 * 验证URL字段
 */
export const validateUrlField = () => {
	const value = newItem.url.trim();

	// 清除之前的错误
	errors.value.url = '';

	// 检查是否为空
	if (!value) {
		errors.value.url = 'URL不能为空';
		return false;
	}

	// 检查URL格式（简单验证，必须以/开头）
	if (!value.startsWith('/')) {
		errors.value.url = 'URL必须以 / 开头';
		return false;
	}

	return true;
};

/**
 * 验证所有表单字段
 */
export const validateAllFields = () => {
	const urlValid = validateUrlField();

	return urlValid;
};

/**
 * 将多行参数字符串转换为参数数组格式（用于提交到API）
 * @param {string} paramsStr - 多行参数字符串，一行一个参数
 * @returns {Array} 参数数组，空数组表示匹配所有参数
 */
export const formatParamsForApi = (paramsStr) => {
	// 如果参数为空或无效，返回空数组（表示匹配所有参数）
	if (!paramsStr || typeof paramsStr !== 'string') {
		return [];
	}

	// 去除首尾空白字符
	const trimmedStr = paramsStr.trim();

	// 如果去除空白后为空，返回空数组（表示匹配所有参数）
	if (!trimmedStr) {
		return [];
	}

	// 按行分割，去除空白字符，过滤空值
	const paramsArray = trimmedStr
		.split(/\r?\n/) // 按换行符分割（支持 \n 和 \r\n）
		.map((param) => param.trim())
		.filter((param) => param);

	// 返回参数数组，空数组表示匹配所有参数
	return paramsArray;
};

// Methods
export const addItem = async () => {
	// 验证表单
	if (!validateAllFields()) {
		return;
	}

	try {
		// 准备API参数 - 按照旧版逻辑处理
		let paramValue;
		if (newItem.params.trim() !== '') {
			// 将换行符替换为逗号，然后分割为数组，最后转为JSON字符串
			const paramArray = newItem.params
				.replace(/\n/g, ',')
				.split(',')
				.map((p) => p.trim())
				.filter((p) => p);
			paramValue = JSON.stringify(paramArray);
		} else {
			// 空参数转为空数组的JSON字符串
			paramValue = JSON.stringify([]);
		}

		const apiParams = {
			uri: newItem.url,
			param: paramValue, // JSON字符串格式
			type: getVerifyModeType(newItem.verifyMode), // 转换为数字
			stype: getMatchModeType(newItem.matchMode), // 转换为API格式
		};

		// 调用API添加
		const res = await addNginxFirewallUrlCcParam(apiParams);

		if (res.status) {
			// API添加成功
			formPageContainer.value.notify.success(res.msg || '添加成功');

			// Reset form
			newItem.url = '';
			newItem.params = '';
			newItem.matchMode = 'URL';
			newItem.verifyMode = '跳转验证';

			// 清除错误状态
			errors.value = { url: '', params: '' };

			// Switch to list view
			activeTab.value = 'list';

			// 重新获取数据更新列表
			getUrlCcParamData();
		} else {
			// API添加失败
			formPageContainer.value.notify.error(res.msg || '添加失败');
		}
	} catch (error) {
		console.error('添加URL CC参数时发生错误:', error);
		formPageContainer.value.notify.error('添加失败，请稍后重试');
	}
};

/**
 * 删除URL CC参数项
 * @param {string} id - 要删除的项目ID
 */
export const deleteItem = async (id) => {
	try {
		// 根据ID找到对应的项目
		const itemToDelete = urlItems.value.find((item) => item.id === id);
		if (!itemToDelete) {
			console.warn('未找到要删除的项目，ID:', id);
			formPageContainer.value?.notify?.error('未找到要删除的项目');
			return;
		}

		// 确保 itemToDelete 有 url 属性
		if (!itemToDelete.url) {
			console.warn('要删除的项目缺少URL属性:', itemToDelete);
			formPageContainer.value?.notify?.error('项目数据异常，无法删除');
			return;
		}

		// 调用API删除
		const res = await deleteNginxFirewallUrlCcParam({
			uri: itemToDelete.url, // API要求的参数名是uri
		});

		if (res.status) {
			// API删除成功，显示成功提示
			formPageContainer.value?.notify?.success(res.msg || '删除成功');
			// 重新获取数据更新列表
			getUrlCcParamData();
		} else {
			// API删除失败，显示错误信息
			formPageContainer.value?.notify?.error(res.msg || '删除失败');
		}
	} catch (error) {
		console.error('删除URL CC参数时发生错误:', error);
		formPageContainer.value?.notify?.error('删除失败，请稍后重试');
	}
};
